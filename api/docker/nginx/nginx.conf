worker_processes  auto;

pid        /var/cache/nginx/nginx.pid;

events { worker_connections 1024; }

error_log  /var/log/nginx/error.log notice;

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server_tokens off;

    include /etc/nginx/conf.d/*.conf;

    log_format json_combined escape=json '{'
        '"time":"$time_iso8601",'
        '"microseconds processing":"$request_time",'
        '"remoteIP":"$remote_addr",'
        '"host":"$host",'
        '"request":"$uri",'
        '"query":"$is_args$args",'
        '"method":"$request_method",'
        '"status":"$status",'
        '"userAgent":"$http_user_agent",'
        '"referer":"$http_referer",'
        '"internalTracing":{"uuid":"$http_x_flow_id"}'
    '}';

    access_log /var/log/nginx/access.log json_combined;

    server {
        listen 8080;
        server_name localhost;

        root /var/www/api/site/public;
        index index.php index.html;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass <PHP_HOSTNAME>:<PHP_PORT>;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME /var/www/api/site/public$fastcgi_script_name;
        }
    }
}