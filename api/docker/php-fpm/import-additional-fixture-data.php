<?php
require_once "/setup.php";


if (!isset($argv[1])) {
    log_error("File to be executed not found");
    exit(1);
}
$sqlFilePath = $argv[1];

try {
    // Connect to MySQL
    $mysqli = new mysqli($databaseHost, $databaseUser, $databasePassword, $databaseSchema, $databasePort);
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }

    // Check if file exists
    if (!file_exists($sqlFilePath)) {
        throw new Exception("SQL file not found: " . $sqlFilePath);
    }

    log_message("Importing additional fixture data from: " . $sqlFilePath);

    // Read the SQL file
    $sqlContent = file_get_contents($sqlFilePath);
    if ($sqlContent === false) {
        throw new Exception("Failed to read SQL file: " . $sqlFilePath);
    }

    // Split SQL statements (basic splitting by semicolon)
    $statements = array_filter(array_map('trim', explode(';', $sqlContent)));

    // Execute each statement
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            log_message("Executing SQL: " . substr($statement, 0, 100) . "...");
            
            if (!$mysqli->query($statement)) {
                throw new Exception("SQL execution failed: " . $mysqli->error . "\nStatement: " . $statement);
            }
        }
    }

    log_message("Additional fixture data imported successfully");

} catch (Exception $e) {
    log_error("Error importing additional fixture data: " . $e->getMessage());
    exit(1);
} finally {
    if (isset($mysqli)) {
        $mysqli->close();
    }
}