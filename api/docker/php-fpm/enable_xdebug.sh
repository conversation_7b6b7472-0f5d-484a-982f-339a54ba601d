#!/bin/bash
set -e

PORT=$1
MODE=$2
START_WITH_REQUEST=$3

echo "Enabling xdebug w/ port: $PORT, mode: $MODE, start_with_request: $START_WITH_REQUEST"

pecl install xdebug-3.3.2 && \
printf "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)\n" > /usr/local/etc/php/conf.d/xdebug.ini

echo ""
echo 'zend_extension=xdebug.so' > /usr/local/etc/php/conf.d/xdebug.ini
echo 'xdebug.idekey = "xdebug"' >> /usr/local/etc/php/conf.d/xdebug.ini
echo "xdebug.client_port = $PORT" >> /usr/local/etc/php/conf.d/xdebug.ini
echo "xdebug.client_host = host.docker.internal" >> /usr/local/etc/php/conf.d/xdebug.ini
echo "xdebug.mode = $MODE" >> /usr/local/etc/php/conf.d/xdebug.ini
echo "xdebug.start_with_request = $START_WITH_REQUEST" >> /usr/local/etc/php/conf.d/xdebug.ini
echo "xdebug.log_level = 0" >> /usr/local/etc/php/conf.d/xdebug.ini
sed -i 's/\r$//' /usr/local/etc/php/conf.d/xdebug.ini