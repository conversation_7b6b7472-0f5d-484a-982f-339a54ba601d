#!/bin/bash
set -e

source /support.sh

check_db_is_up

OUT_MESSAGE=$(php /init_db.php 2>&1) || OUT_EXIT_CODE=$?
log "init_db OUTPUT: $OUT_MESSAGE"
if [[ $OUT_EXIT_CODE -ne 0 ]]; then
    log_error "Database initialization failed with exit code $OUT_EXIT_CODE"
    exit $OUT_EXIT_CODE
fi

TENANT_ROW_EXISTS=$(php /check_tenant_exists.php 2>&1) || TENANT_EXIT_CODE=$?
log "check_tenant_exists output: $TENANT_ROW_EXISTS"

if [[ $TENANT_EXIT_CODE -ne 0 ]]; then
    log_error "Tenant check failed with exit code $TENANT_EXIT_CODE"
    exit 1
fi

log "Clearing application cache"
phing clear-cache

log "Creating proxy files..."
phing doctrine-generate-proxies

if [ "$TENANT_ROW_EXISTS" == "1" ]; then
    log "Running doctrine-migrate"
    phing doctrine-migrate

    log "Running import-metadata"
    phing import-metadata
else
    log "Running doctrine-rebuild"
    phing doctrine-rebuild
fi

php-fpm