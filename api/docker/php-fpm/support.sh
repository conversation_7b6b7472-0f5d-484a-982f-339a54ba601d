log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log_section() {
    echo -e "\n======================================================================"
    log "$1"
    echo "======================================================================"
}

log_error() {
    echo -e "\e[31m[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1\e[0m"
}

log_success() {
    echo -e "\e[32m[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1\e[0m"
}

log_warning() {
    echo -e "\e[33m[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1\e[0m"
}

check_db_is_up() {
    OUT_MESSAGE=$(php /check_database_is_up.php 2>&1) || OUT_EXIT_CODE=$?
    log "check_database_is_up output: $OUT_MESSAGE"

    if [[ $OUT_EXIT_CODE -ne 0 ]]; then
        log_error "Database check failed with exit code $OUT_EXIT_CODE"
        exit $OUT_EXIT_CODE
    fi

    if [[ "${OUT_MESSAGE: -6}" == "FAILED" ]]; then
       exit 1
    fi
}