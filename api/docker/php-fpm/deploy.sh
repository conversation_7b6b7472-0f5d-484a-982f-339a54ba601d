#!/bin/bash
set -e

source /support.sh

check_db_is_up

migration_output=$(/var/www/api/site/vendor/bin/doctrine-module migrations:list --no-interaction --no-ansi 2>&1)
migration_exit_code=$?

if [ $migration_exit_code -ne 0 ]; then
    log_error "Doctrine migrations:list command failed!"
    exit 1
fi

if grep -q "not migrated" <<< "$migration_output"; then
	log "Migration has not run. Please ensure the migration has been run before attempting to start again!"
  echo "Full migration output:"
  echo "$migration_output"
	exit 1
fi

log "Migrations are up to date..... Starting web server!"

log "Clearing application cache"
phing clear-cache

log "Creating proxy files..."
phing doctrine-generate-proxies

if [[ $CONTAINER_MODE == "CRON" ]]
then
  log "Starting in CRON mode..."
  log "Using cron script: $CRON_PATH"
  exec $CRON_PATH
fi

php-fpm