<?php
require_once "/setup.php";

$mysqli = new mysqli($databaseHost, $databaseUser, $databasePassword, $databaseSchema, $databasePort);
if ($mysqli->connect_error) {
    throw new Exception("Connection failed: " . $mysqli->connect_error);
}

try {
    $adminLogin = getenv('ADMIN_LOGIN');
    $mysqli->query("UPDATE identity SET identity = '" . $mysqli->real_escape_string($adminLogin) . "' WHERE identity = 'admin_login'");
} finally {
    $mysqli->close();
}
