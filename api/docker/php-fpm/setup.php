<?php 
function log_message($message) {
    echo "[PHP] $message\n";
}

function log_error($message) {
    echo "\e[31m[PHP]ERROR: $message\e[0m\n";
}

function log_warning($message) {
    echo "\e[33m[PHP]WARNING: $message\e[0m\n";
}

function log_success($message) {
    echo "\e[32m[PHP]SUCCESS: $message\e[0m\n";
}

$databaseHost = getenv("MYSQL_HOST") ?? '';
$databaseUser = getenv("MYSQL_USER") ?? '';
$databasePassword = getenv("MYSQL_PASSWORD") ?? '';
$databaseSchema = getenv("MYSQL_DATABASE") ?? '';
$databasePort = (int)getenv("MYSQL_PORT") ?? 3306;