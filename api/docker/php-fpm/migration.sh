#!/bin/bash
set -e

source /support.sh

check_db_is_up

log "Clearing application cache"
phing clear-cache

OUT_MESSAGE=$(php /init_db.php 2>&1) || OUT_EXIT_CODE=$?
log "init_db OUTPUT: $OUT_MESSAGE"
if [[ $OUT_EXIT_CODE -ne 0 ]]; then
    log_error "Database initialization failed with exit code $OUT_EXIT_CODE"
    exit $OUT_EXIT_CODE
fi

if [ "${OUT_MESSAGE: -11}" == "CREATED_PRD" ]; then
    phing doctrine-rebuild-prod
elif [ "${OUT_MESSAGE: -11}" == "CREATED_DEV" ]; then
    phing doctrine-rebuild
else
    log "Creating proxy files..."
    phing doctrine-generate-proxies

    echo -e "\n\n"
    echo -e "***********************************************************************************"
    echo -e "*                                                                                 *"
    echo -e "*                            Using existing database                              *"
    echo -e "*                                                                                 *"
    echo -e "***********************************************************************************\n\n\n"
    log "Migrating metadata storage"
    /var/www/api/site/vendor/bin/doctrine-module migrations:sync-metadata-storage --no-interaction
    
    log "Migrating database"
    phing doctrine-migrate
    log "Importing metadata"
    phing import-metadata
fi
exit 0