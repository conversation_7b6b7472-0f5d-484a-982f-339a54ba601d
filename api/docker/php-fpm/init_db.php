<?php
require_once "/setup.php";

function db_exists($host, $port, $user, $pass, $db) {
    $conn = new mysqli($host, $user, $pass, '', $port);
    if ($conn->connect_error) {
        return false;
    }

    try {
        $res = $conn->query("SHOW DATABASES LIKE '" . $conn->real_escape_string($db) . "'");
        $exists = $res && $res->num_rows > 0;
    } finally {
        $conn->close();
    }

    return $exists;
}

if (!db_exists($databaseHost, $databasePort, $databaseUser, $databasePassword, $databaseSchema)) {
    $isProd = strtoupper($mode) === 'PRODUCTION';

    echo "\n\n";
    echo str_repeat("*", 83) . "\n";
    echo "* " . str_pad("Creating new " . ($isProd ? "PROD" : "DEV") . " database if not exists", 81) . " *\n";
    echo str_repeat("*", 83) . "\n\n\n";

    $conn = new mysqli($databaseHost, $databaseUser, $databasePassword, '', $databasePort);
    if ($conn->connect_error) {
        log_error("MySQL connection failed: " . $conn->connect_error);
        exit(1);
    }

    try {
        $sql = "CREATE DATABASE IF NOT EXISTS `$databaseSchema` CHARACTER SET utf8 COLLATE utf8_general_ci";
        if (!$conn->query($sql)) {
            log_error("Database creation failed: " . $conn->error);
            exit(1);
        }
    } finally {
        $conn->close();
    }
    
    echo $isProd ? "CREATED_PRD" : "CREATED_DEV";
}