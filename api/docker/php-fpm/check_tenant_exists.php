<?php
require_once "/setup.php";

$mysqli = new mysqli($databaseHost, $databaseUser, $databasePassword, $databaseSchema, $databasePort);
if ($mysqli->connect_error) {
    throw new Exception("Connection failed: " . $mysqli->connect_error);
}

try {
    $tableCheck = $mysqli->query("SHOW TABLES LIKE 'tenant'");
    
    if (!$tableCheck || $tableCheck->num_rows === 0) {
        exit;
    }

    $result = $mysqli->query("SELECT 1 FROM tenant WHERE id = 1 LIMIT 1");

    echo $result && $result->num_rows > 0 ? "1\n" : "";
} finally {
    $mysqli->close();
}
