<?php
require_once "/setup.php";

log_message("Waiting for MySQL host and port to be available {$databaseHost}:{$databasePort}");

do {
    $connection = @fsockopen($databaseHost, $databasePort, $errno, $errstr, 5);
    if ($connection) {
        fclose($connection);
        log_message('MySQL host and port are available');
        exit(0);
    } else {
        log_message("Waiting... (Error: {$errno}:{$errstr})");
        sleep(2);
    }
} while (!$connection);