<?php
require_once "/setup.php";

$mysqli = new mysqli($databaseHost, $databaseUser, $databasePassword, $databaseSchema, $databasePort);
if ($mysqli->connect_error) {
    throw new Exception("Connection failed: " . $mysqli->connect_error);
}

try {
    $database = getenv('MYSQL_DATABASE');
    $sql = "CREATE DATABASE IF NOT EXISTS `{$database}`";
    
    $result = $mysqli->query($sql);
    if (!$result) {
        throw new Exception('Database creation failed: ' . $mysqli->error);
    }
} finally {
    $mysqli->close();
}
