#!/bin/bash
set -e
# Updates memory_limit in php.ini if CUSTOM_PHP_MEM_LIMIT has a value.
# E.g. CUSTOM_PHP_LIMIT=256 -> memory_limit = 256M
# Default value is 128M

echo "memory_limit = ${CUSTOM_PHP_MEM_LIMIT:-512}M" > /usr/local/etc/php/conf.d/memory.ini
echo "max_execution_time = ${CUSTOM_PHP_MAX_EXEC_TIME:-30}" > /usr/local/etc/php/conf.d/execution_time.ini

cd /var/www/api

#If anything other than expected env vars are provided, script presumed dev mode to ensure no outages! Migrations will be run inside each container then the web server started.
case $CONTAINER_MODE in
    DEV)
        ( exec "/dev.sh" )
        ;;
    MIGRATE)
        ( exec "/migration.sh" )
        ;;
    PROD)
        ( exec "/deploy.sh" )
        ;;
    CRON)
        ( exec "/deploy.sh" )
        ;;
    *)
		( exec "/dev.sh" )
		;;
esac

