<project>
    <target name="doctrine-cache-clear" description="Clear all Doctrine caches">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:clear-cache:metadata" checkreturn="true" />
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:clear-cache:query" checkreturn="true" />
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:clear-cache:result" checkreturn="true" />
    </target>

    <target name="doctrine-create" description="Create Doctrine Schema">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:schema-tool:create" checkreturn="true" />
    </target>

    <target name="doctrine-update" description="Update Doctrine Schema">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:schema-tool:update --force" checkreturn="true" />
    </target>

    <target name="doctrine-migrate" description="Migrate Doctrine Schema">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module migrations:migrate --no-interaction" checkreturn="true" />
    </target>

    <target name="doctrine-generate-proxies" description="Generate Doctrine Proxies">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module orm:generate:proxies" checkreturn="true" />
    </target>

    <target name="doctrine-fixture-import" hidden="true" description="Import Doctrine Data Fixtures">
        <exec passthru="true" dir="${build.site}" command="vendor/bin/doctrine-module data-fixture:import" checkreturn="true" />
    </target>

    <target name="tenant-create" hidden="true" description="Create new tenant with dev data">
        <exec passthru="true" dir="${build.site}" command="php console tenant:create default --include-test-data" checkreturn="true" />
    </target>

    <target name="tenant-create-prod" hidden="true" description="Create new tenant with prod data">
        <exec passthru="true" dir="${build.site}" command="php console tenant:create prod" checkreturn="true" />
    </target>

    <target name="import-metadata" hidden="true" description="Create new tenant with prod data">
        <exec passthru="true" dir="${build.site}" command="php console import-metadata" checkreturn="true" />
    </target>

    <target name="import-additional-fixture-data" hidden="true" description="Import the additional SQL based fixture data">
        <exec executable="php" passthru="true" checkreturn="true">
            <arg value="/import-additional-fixture-data.php" />
            <arg value="${build.db}/additional_fixture_data.sql" />
        </exec>
        <echo message="Additional fixture data imported from ${build.db}/additional_fixture_data.sql" />
    </target>

    <!-- Create database schema instance if it does not already exist -->
    <target name="create-database" hidden="true" description="Create database schema instance if it does not already exist">
    <exec
        passthru="true"
        dir="${build.db}"
        command="php /create_database.php"
        checkreturn="true"
    />
    </target>

    <!-- Migrate the database to the state expected by the current checked out Git branch -->
    <target name="doctrine-rebuild" description="Drop schema, create schema and apply dev data fixtures (use on git pull)">

        <!-- Ensure all existing caches are wiped -->
        <phingcall target="doctrine-cache-clear" />

        <!-- Create database if not exists -->
        <phingcall target="create-database" />

        <!-- Generate Doctrine Proxies -->
        <phingcall target="doctrine-generate-proxies" />

        <!-- Execute all migrations present to the latest migration in this Git branch -->
        <phingcall target="doctrine-migrate" />

        <!-- Now we'll execute the Tenant Import CLI script to import the test tenant its data -->
        <phingcall target="tenant-create" />

        <!-- Any additional SQL fixes - this needs revisiting / deprecating when we can -->
        <phingcall target="import-additional-fixture-data" />

        <!-- Complete! -->

    </target>

    <target name="doctrine-rebuild-prod" description="Drop schema, create schema and apply prod data fixtures">

        <!-- Ensure all existing caches are wiped -->
        <phingcall target="doctrine-cache-clear" />

        <!-- Create database if not exists -->
        <phingcall target="create-database" />

        <!-- Generate Doctrine Proxies -->
        <phingcall target="doctrine-generate-proxies" />

        <!-- Execute all migrations present to the latest migration in this Git branch -->
        <phingcall target="doctrine-migrate" />

        <!-- Now we'll execute the Tenant Import CLI script to import the test tenant its data -->
        <phingcall target="tenant-create-prod" />

        <!-- Any additional SQL fixes - this needs revisiting / deprecating when we can -->
        <phingcall target="import-additional-fixture-data" />

        <!-- Create the first admin user based on ENV vars -->
        <phingcall target="update-prod-tenant-data" />

        <!-- Complete! -->

    </target>

    <target name="test-tenant-imports" description="Runs both a dev and prod tenant import to test updated fixtures">
        <phingcall target="doctrine-rebuild"/>
        <phingcall target="doctrine-rebuild-prod"/>
    </target>

    <!-- update prod tenant data based on ENV vars -->
    <target name="update-prod-tenant-data" hidden="true" description="Update the first admin user based on ENV vars">
        <exec
                passthru="true"
                dir="${build.db}"
                command="php /update_prod_tenant_data.php"
                escape="false"
                checkreturn="true"
        />
    </target>


</project>
