<project>

    <target name="change-branch" description="Change branch and associated setup">
        <exec passthru="true" dir="" command="git fetch" checkreturn="true" />
        <exec passthru="true" command="git checkout ${branch}" checkreturn="true" />
        <phingcall target="doctrine-rebuild" />
        <exec passthru="true" dir="${harness.site.client.path}" command="grunt compass:dev" checkreturn="true" />
    </target>

    <target name="list" description="List all current Phing commands.">
        <exec command="phing -f ${phing.file} -l" outputProperty="phing_targets" />
        <echo>Please select a target.</echo>
        <echo>${phing_targets}</echo>
    </target>

</project>
