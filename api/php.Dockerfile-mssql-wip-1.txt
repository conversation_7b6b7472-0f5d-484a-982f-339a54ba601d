FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/php:8.3.22 AS builder

ARG USE_ZSCALER_CERT
ARG ENABLE_XDEBUG
ARG XDEBUG_IDE_PORT
ARG XDEBUG_MODE
ARG XDEBUG_START_WITH_REQUEST

USER root

# Add Zscaler Root Certificate
COPY docker/ZscalerRootCertificate-2048-SHA256.crt /etc/pki/ca-trust/source/anchors/
RUN if [ "$USE_ZSCALER_CERT" = "1" ]; then update-ca-trust extract; fi

RUN dnf upgrade -y && \
    dnf install -y --nodocs \
      autoconf \
      automake \
      bzip2-devel \
      diffutils \
      file \
      fontconfig-devel \
      freetype-devel \
      make \
      libtool \
      gcc \
      gcc-c++ \
      ghostscript \
      glib2-devel \
      libcurl-devel \
      libicu-devel \
      libtiff-devel \
      libX11-devel \
      libxml2-devel \
      libzip-devel \
      libwebp-devel \
      openldap-devel \
      openldap \
      openssl-devel \
      zlib-devel \
      openldap \
      openldap-devel \
      unixODBC unixODBC-devel && \
    dnf clean all

# Add Microsoft repo & install ODBC driver
RUN curl https://packages.microsoft.com/keys/microsoft.asc | tee /etc/pki/rpm-gpg/microsoft.asc && \
    curl https://packages.microsoft.com/config/rhel/9/prod.repo -o /etc/yum.repos.d/mssql-release.repo && \
    ACCEPT_EULA=Y dnf install -y msodbcsql18 mssql-tools18 && \
    echo 'export PATH="$PATH:/opt/mssql-tools18/bin"' >> /etc/bashrc

# Install PHP sqlsrv + pdo_sqlsrv extensions
RUN pecl install sqlsrv pdo_sqlsrv && \
    echo "extension=sqlsrv.so" > /usr/local/etc/php/conf.d/sqlsrv.ini && \
    echo "extension=pdo_sqlsrv.so" > /usr/local/etc/php/conf.d/pdo_sqlsrv.ini

RUN pecl install mongodb-1.19.4 && \
    echo "extension=mongodb.so"  > /usr/local/etc/php/conf.d/mongodb.ini

RUN pecl install opentelemetry && \
    echo "extension=opentelemetry.so" > /usr/local/etc/php/conf.d/opentelemetry.ini

RUN pecl install protobuf && \
    echo "extension=protobuf.so" > /usr/local/etc/php/conf.d/protobuf.ini

RUN curl http://www.phing.info/get/phing-3.0.0-RC1.phar -LSs > /usr/local/bin/phing && \
    chmod +x /usr/local/bin/phing

COPY docker/php-fpm/enable_xdebug.sh /tmp/enable_xdebug.sh
RUN if [ "$ENABLE_XDEBUG" = "1" ]; then chmod +x /tmp/enable_xdebug.sh && /tmp/enable_xdebug.sh $XDEBUG_IDE_PORT $XDEBUG_MODE $XDEBUG_START_WITH_REQUEST; fi

FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/php:8.3.22 AS prod

USER root

COPY --from=builder --chown=root:root /usr/local/bin /usr/local/bin
COPY --from=builder --chown=root:root /usr/local/php /usr/local/php
COPY --from=builder --chown=root:root /usr/local/etc/php/conf.d/ /usr/local/etc/php/conf.d/
COPY --from=builder --chown=root:root /usr/local/include/php /usr/local/include/php
COPY --from=builder --chown=root:root /usr/local/lib/php /usr/local/lib/php

COPY . /var/www/api

RUN test -d /var/www/api/site/vendor || exit 1

WORKDIR /
COPY ./docker/php-fpm/*.sh ./
COPY ./docker/php-fpm/*.php ./

RUN chmod +x deploy.sh dev.sh migration.sh && \
    rm -rf /var/www/html && ln -s /var/www/api/site/public /var/www/html && \
    mkdir -p /var/www/api/site/data/uploads/temp && \
    chmod -R 755 /var/www/api/site/data/uploads && \
    chown -R php-fpm:php-fpm /var/www/api/site/data/uploads && \
    chmod -R 755 /var/www/api/site/data/cache && \
    chown -R php-fpm:php-fpm /var/www/api/site/data/cache && \
    chmod -R 755 /var/www/api/site/data/DoctrineORMModule/Proxy && \
    chown -R php-fpm:php-fpm /var/www/api/site/data/DoctrineORMModule/Proxy && \
    chmod -R 755 /var/www/api/site/data/DoctrineORMModule/Cache && \
    chown -R php-fpm:php-fpm /var/www/api/site/data/DoctrineORMModule/Cache && \
    chmod -R 755 /usr/local/etc/php/conf.d && \
    chown -R php-fpm:php-fpm /var/www/api/site/vendor/bin && \
    chmod -R 777 /var/www/api/site/vendor/bin && \
    chown -R php-fpm:php-fpm /usr/local/etc/php/conf.d && \
    chown -R php-fpm:php-fpm /var/log/httpd/

RUN echo 'log_errors = On' >> /usr/local/etc/php/conf.d/error.ini && \
    echo 'error_log = /proc/self/fd/2' >> /usr/local/etc/php/conf.d/error.ini && \
    echo 'display_errors = Off' >> /usr/local/etc/php/conf.d/error.ini && \
    echo 'upload_max_filesize = 50M' >> /usr/local/etc/php/conf.d/upload.ini

RUN sed -i 's|listen = 9000|listen = 9099|' /usr/local/etc/php-fpm.d/zz-docker.conf
RUN sed -i 's/use SplQueue/use Laminas\\Stdlib\\SplQueue/' /var/www/api/site/vendor/open-telemetry/sdk/Logs/Processor/BatchLogRecordProcessor.php

EXPOSE 9099

USER php-fpm

ENTRYPOINT ["./start.sh"]
