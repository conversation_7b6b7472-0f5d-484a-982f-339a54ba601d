<?php

declare(strict_types=1);

namespace Application\Controller;

use Application\Exception\ModuleLicenseException;
use Application\Service\PermissionService;
use Application\Service\SystemConfigurationService;
use Application\Traits\AccessPermissionTrait;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Form\Service\DatasourceService;
use I18n\Service\LanguageService;
use I18n\Service\TranslationService;
use Laminas\ApiTools\ApiProblem\ApiProblem;
use Laminas\ApiTools\ApiProblem\ApiProblemResponse;
use Laminas\Db\Adapter\Adapter;
use Laminas\Db\Adapter\Driver\Pdo\Pdo;
use Laminas\Mvc\Controller\AbstractRestfulController;
use Laminas\View\Model\JsonModel;
use Teapot\StatusCode\Http;
use Tenant\Entity\Config;
use User\Service\UserService;

class SystemConfigurationController extends AbstractRestfulController
{
    use AccessPermissionTrait;
    private SystemConfigurationService $systemConfigurationService;
    private LanguageService $languageService;
    private DatasourceService $datasourceService;
    private PermissionService $permissionService;

    private UserService $userService;

    public function __construct(
        SystemConfigurationService $systemConfigurationService,
        LanguageService $languageService,
        DatasourceService $datasourceService,
        PermissionService $permissionService,
        UserService $userService
    ) {
        $this->systemConfigurationService = $systemConfigurationService;
        $this->languageService = $languageService;
        $this->datasourceService = $datasourceService;
        $this->permissionService = $permissionService;
        $this->userService = $userService;
    }

    public function getList(): JsonModel
    {
        try {
            $defaultModule = $this->systemConfigurationService->getDefaultModule()->getId();
        } catch (NoResultException $e) {
            $defaultModule = null;
        }
//
//        echo "SystemConfigurationController::getList called\n";
//        $mssqlHost = 'mssql';
//        $mssqlPort = '1433';
//        $mssqlUser = 'sa';
//        $mssqlPassword = 'C1aws0nite';
//        $mssqlDatabase = 'carlton';
//
//    // MSSQL connection using PDO
//        $dsn = 'sqlsrv:Server=' . $mssqlHost . ',' . $mssqlPort . ';Database=' . $mssqlDatabase;
//        // Create adapter using Laminas\Db
//        $config = [
//            'driver'   => 'Pdo', // important: just 'Pdo'
//            'dsn'      => $dsn,
//            'username' => $mssqlUser,
//            'password' => $mssqlPassword,
//            'driver_options' => [
//                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
//            ],
//        ];
//
//        $adapter = new Adapter($config);
//
//        // Test connection
//        try {
//            $connection = $adapter->getDriver()->getConnection();
//            $connection->connect();
//
//            echo "===================================================\n";
//            echo "MSSQL connection successful via Laminas Db Adapter\n";
//            echo "===================================================\n";
//
//            // Example query
//            $result = $adapter->query('SELECT name FROM sys.databases', Adapter::QUERY_MODE_EXECUTE);
//            foreach ($result as $row) {
//                echo "Database: " . $row['name'] . "\n";
//            }
//
//            $result = $adapter->query(
//                'SELECT id, forename, surname FROM carlton.carlton.[user]',
//                Adapter::QUERY_MODE_EXECUTE
//            );
//
//            foreach ($result as $row) {
//                echo "ID: " . $row['id'] . " | Name: " . $row['forename'] . " | Surname: " . $row['surname'] . "\n";
//            }
//
//        } catch (\Exception $e) {
//            echo "MSSQL connection failed: " . $e->getMessage() . "\n";
//        }

        return new JsonModel([
            'data' => [
                'defaultModule' => $defaultModule,
                'loginMessage' => $this->systemConfigurationService->getLoginMessage(),
                'logoutRedirectTarget' => $this->systemConfigurationService->getLogoutRedirectTarget(),
                'agreementMessage' => $this->systemConfigurationService->getAgreementMessage(),
                'loginMessageEnabled' => $this->systemConfigurationService->isLoginMessageEnabled(),
                'agreementMessageEnabled' => $this->systemConfigurationService->isAgreementMessageEnabled(),
                'mandatoryDelegationDatesEnabled' => $this->systemConfigurationService->isMandatoryDelegationDatesEnabled(),
                'nonCaptureReportingStatus' => $this->systemConfigurationService->getNonCaptureReportingStatus(),
                'languages' => $this->languageService->getTenantLanguages(),
                'logoUrl' => $this->systemConfigurationService->getLogoUrl(),
                'utcOffset' => $this->systemConfigurationService->getSystemTimezone(),
                'reportEmail' => $this->systemConfigurationService->getReportEmail(),
                'reportDownloadLimit' => $this->systemConfigurationService->getReportDownloadLimit(),
                'reportExpiryHours' => $this->systemConfigurationService->getReportExpiryHours(),
                'deleteContactsEnabled' => $this->systemConfigurationService->isDeleteContactsEnabled(),
                'deleteRisksEnabled' => $this->systemConfigurationService->isDeleteRisksEnabled(),
                'deleteSafetyAlertsEnabled' => $this->systemConfigurationService->isDeleteSafetyAlertsEnabled(),
                'deleteInvestigationsEnabled' => $this->systemConfigurationService->isdeleteInvestigationsEnabled(),
                'mySettingsEnabled' => $this->systemConfigurationService->isMySettingsEnabled(),
                'maintenanceModeEnabled' => $this->systemConfigurationService->getIsMaintenanceModeEnabled(),
                'localAdminMaintenanceMode' => $this->systemConfigurationService->getIsLocalAdminMaintenanceMode(),
                'contactMergingEnabled' => $this->systemConfigurationService->getIsContactMergingEnabled(),
                'copyLocationServiceFromSourceToAction' => $this->systemConfigurationService->isCopySourceLocationAndServiceToAction(),
                'inactivityTimeout' => [
                    'value' => $this->systemConfigurationService->getInactivityTimeout(),
                    'minimum' => Config::INACTIVITY_TIMEOUT_MIN,
                    'maximum' => Config::INACTIVITY_TIMEOUT_MAX,
                ],
                'localAdminLocationRestricted' => $this->systemConfigurationService->isLocalAdminLocationRestricted(),
                'localAdminServiceRestricted' => $this->systemConfigurationService->isLocalAdminServiceRestricted(),
                'displayPreviousLoginAttemptsEnabled' => $this->systemConfigurationService->isDisplayPreviousLoginAttemptsEnabled(),
                'enableFooterText' => $this->systemConfigurationService->isEnabledFooterText(),
                'footerText' => $this->systemConfigurationService->getFooterText(),
                'relabelIncidentsToEventsEnabled' => $this->systemConfigurationService->isRelabelIncidentsToEventsEnabled(),
                'maxFileUploadLimit' => [
                    'value' => $this->systemConfigurationService->getMaxFileUploadLimit(),
                    'minimum' => Config::FILE_UPLOAD_LIMIT_MIN,
                    'maximum' => Config::FILE_UPLOAD_LIMIT_MAX,
                ],
                'systemLanguage' => $this->systemConfigurationService->getSystemLanguage(),
            ],
        ]);
    }

    /**
     * @param array $data
     * @return JsonModel|ApiProblemResponse
     * @throws NonUniqueResultException
     */
    public function replaceList($data)
    {
        $apiProblem = $this->isAdmin($this->permissionService);
        if ($apiProblem instanceof ApiProblem) {
            return new ApiProblemResponse($apiProblem);
        }

        try {
            $this->systemConfigurationService->updateConfig($data);

            return $this->getList();
        } catch (ModuleLicenseException $e) {
            $this->getResponse()->setStatusCode(Http::FORBIDDEN);

            return new JsonModel([
                'title' => 'Module Licensing Error',
                'type' => 'moduleLicensingError',
                'detail' => $e->getMessage(),
            ]);
        }
    }

    public function utcOffsetsAction(): JsonModel
    {
        return new JsonModel([
            'data' => $this->datasourceService->findItemsByDatasourceKey(
                SystemConfigurationService::UTC_OFFSETS_DATASOURCE_KEY,
            ),
        ]);
    }

    public function deleteRisksEnabledAction(): JsonModel
    {
        return new JsonModel([
            'isEnabled' => $this->systemConfigurationService->isDeleteRisksEnabled(),
        ]);
    }

    public function deleteSafetyAlertsEnabledAction(): JsonModel
    {
        return new JsonModel([
            'isEnabled' => $this->systemConfigurationService->isDeleteSafetyAlertsEnabled(),
        ]);
    }

    public function deleteInvestigationsEnabledAction(): JsonModel
    {
        return new JsonModel([
            'isEnabled' => $this->systemConfigurationService->isdeleteInvestigationsEnabled(),
        ]);
    }

    public function getMaintenanceModeAction(): JsonModel
    {
        return new JsonModel([
            'isEnabled' => $this->systemConfigurationService->getTenantService()->getTenantConfig()->getMaintenanceModeEnabled(),
        ]);
    }

    public function copyLocationServiceFromSourceToActionAction(): JsonModel
    {
        return new JsonModel([
            'isEnabled' => $this->systemConfigurationService->getTenantService()->getTenantConfig()->isCopySourceLocationAndServiceToAction(),
        ]);
    }

    public function footerTextAction(): JsonModel
    {
        return new JsonModel([
            'footerTextEnabled' => $this->systemConfigurationService->isEnabledFooterText(),
            'footerText' => $this->systemConfigurationService->getFooterText(),
        ]);
    }

    public function relabelIncidentsToEventsEnabledAction(): JsonModel
    {
        $isEnabled = $this->systemConfigurationService->isRelabelIncidentsToEventsEnabled();
        $user = $this->userService->getCurrent();

        return new JsonModel([
            'relabelIncidentsToEventsEnabled' =>
                $isEnabled &&
                in_array($user->getLanguageCode(), TranslationService::LANGUAGE_CODES_TO_RELABEL_INCIDENTS_TO_EVENTS),
        ]);
    }
}
