<?php

namespace RiskRegister\V1\Controller;

use Application\Log\Facade\Log;
use Application\Query\PaginatorParameters;
use Laminas\ApiTools\ApiProblem\ApiProblem;
use Laminas\ApiTools\ApiProblem\ApiProblemResponse;
use Laminas\Mvc\Controller\AbstractActionController;
use Laminas\View\Model\JsonModel;
use RiskRegister\Entity\Risk;
use RiskRegister\Service\RiskEmailNotificationService;
use RiskRegister\Service\RiskService;
use Teapot\StatusCode\Http;

use function getenv;

class RiskNotificationController extends AbstractActionController
{
    private const DEFAULT_PAGE_SIZE = 10;
    private RiskEmailNotificationService $riskEmailNotificationService;
    private RiskService $riskService;

    public function __construct(
        RiskEmailNotificationService $riskEmailNotificationService,
        RiskService $riskService
    ) {
        $this->riskEmailNotificationService = $riskEmailNotificationService;
        $this->riskService = $riskService;
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function overdueAction()
    {
        if ((int) getenv('NOTIFICATIONCENTRE_ENABLED') === 0) {
            return new ApiProblemResponse(
                new ApiProblem(Http::FORBIDDEN, 'Notification Centre is not enabled on Carlton'),
            );
        }
        if ((int) getenv('ENABLE_NOTIFICATION_CENTRE_FOR_ERM') === 0) {
            return new JsonModel(['feature_flag_disabled' => 'ENABLE_NOTIFICATION_CENTRE_FOR_ERM']);
        }
        $request = $this->getRequest();
        $overdueOffsetDays = $request->getQuery('overdue-days', Risk::DEFAULT_OVERDUE_DAYS);
        if (!$overdueOffsetDays) {
            $overdueOffsetDays = Risk::DEFAULT_OVERDUE_DAYS;
        }

        $page = $request->getQuery('page', 1);
        $pagination = new PaginatorParameters($page, self::DEFAULT_PAGE_SIZE);

        $risks = $this->riskService->findRisksByOverdueReviewDate($pagination, $overdueOffsetDays);
        $data = $this->riskEmailNotificationService->buildBulkNotification(
            $risks['_embedded']['data'],
            RiskEmailNotificationService::TEMPLATE_TYPE_OVERDUE,
        );

        unset($risks['_embedded']['data']);
        $risks['_embedded']['notifications'] = $data;

        Log::info('Risk overdue review date notification data', $data);

        return new JsonModel($risks);
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function reminderAction()
    {
        if ((int) getenv('NOTIFICATIONCENTRE_ENABLED') === 0) {
            return new ApiProblemResponse(
                new ApiProblem(Http::FORBIDDEN, 'Notification Centre is not enabled on Carlton'),
            );
        }
        if ((int) getenv('ENABLE_NOTIFICATION_CENTRE_FOR_ERM') === 0) {
            return new JsonModel(['feature_flag_disabled' => 'ENABLE_NOTIFICATION_CENTRE_FOR_ERM']);
        }
        $request = $this->getRequest();
        $page = $request->getQuery('page', 1);
        $pagination = new PaginatorParameters($page, self::DEFAULT_PAGE_SIZE);

        $risks = $this->riskService->findRisksForReminderOfReviewDate($pagination, Risk::DEFAULT_REMINDER_DAYS);
        $data = $this->riskEmailNotificationService->buildBulkNotification(
            $risks['_embedded']['data'],
            RiskEmailNotificationService::TEMPLATE_TYPE_REVIEW_REMINDER,
        );

        unset($risks['_embedded']['data']);
        $risks['_embedded']['notifications'] = $data;

        Log::info('Risk review date reminder notification data', $data);

        return new JsonModel($risks);
    }
}
