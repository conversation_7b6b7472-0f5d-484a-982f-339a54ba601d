<?php

declare(strict_types=1);

namespace User\Repository;

use Action\Dto\UsersWithOverdueActions;
use Action\Entity\Action;
use Application\Query\PaginatorParameters;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\ORM\EntityRepository;

use function array_map;
use function implode;

/**
 * @extends EntityRepository<User>
 */
class UserRepository extends EntityRepository
{
    public function getRiskOwnerUsers(int $riskId): array
    {
        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder();

        return $qb->addSelect('rro.user_id')
            ->from('risk_record_owner', 'rro')
            ->andWhere($qb->expr()->isNotNull('rro.user_id'))
            ->andWhere($qb->expr()->eq('rro.risk_id', ':risk_id'))
            ->setParameter('risk_id', $riskId)
            ->addGroupBy('rro.user_id')
            ->execute()
            ->fetchFirstColumn();
    }

    public function getUsersWithOverdueActions(PaginatorParameters $pagination, string $overdueDate, bool $getAssigners): UsersWithOverdueActions
    {
        $qbAssignee = $this->getActionUserQueryBuilderSetup('user_id');
        $qbAssignee->where($qbAssignee->expr()->lt('a.dueDate', ':date'))
            ->andWhere($qbAssignee->expr()->eq('a.status', Action::STATUS_ACTIVE));

        if ($getAssigners) {
            $qbAssigner = $this->getActionUserQueryBuilderSetup('assigner_id');
            $qbAssigner->where($qbAssigner->expr()->lt('a.dueDate', ':date'))
                ->andWhere($qbAssigner->expr()->eq('a.status', Action::STATUS_ACTIVE));

            $query = $this->createUnion($qbAssignee, $qbAssigner);
        } else {
            $query = $qbAssignee->getSQL();
        }

        $limit = $pagination->getPageSize();
        $offset = ($pagination->getPage() - 1) * $pagination->getPageSize();

        $totalItems = $this->getEntityManager()
            ->getConnection()
            ->executeQuery(
                'SELECT COUNT(id) FROM (' . $query . ') AS u',
                ['date' => $overdueDate],
            )
            ->fetchOne();

        $data = $this->getEntityManager()
            ->getConnection()
            ->executeQuery(
                $query . ' ORDER BY id ASC LIMIT ' . $limit . ' OFFSET ' . $offset,
                ['date' => $overdueDate],
            )
            ->fetchAllAssociative();

        // Cannot DI UsersWithOverdueActions while the class is extending EntityRepository|AbstractEntityRepository
        return new UsersWithOverdueActions($data, (int) $totalItems, $limit);
    }

    public function getUsersWithReminderActions(PaginatorParameters $pagination, string $startDate, string $endDate, bool $getAssigners): UsersWithOverdueActions
    {
        $qbAssignee = $this->getActionUserQueryBuilderSetup('user_id');
        $qbAssignee->where($qbAssignee->expr()->isNull('a.completed'))
            ->andWhere('a.dueDate BETWEEN :assigneeStartDate AND :assigneeEndDate');

        if ($getAssigners) {
            $qbAssigner = $this->getActionUserQueryBuilderSetup('assigner_id');
            $qbAssigner->where('a.dueDate BETWEEN :assignerStartDate AND :assignerEndDate')
                ->andWhere($qbAssigner->expr()->isNull('a.completed'));

            $query = $this->createUnion($qbAssignee, $qbAssigner);
        } else {
            $query = $qbAssignee->getSQL();
        }

        $limit = $pagination->getPageSize();
        $offset = ($pagination->getPage() - 1) * $pagination->getPageSize();

        $totalItems = $this->getEntityManager()
            ->getConnection()
            ->executeQuery(
                'SELECT COUNT(id) FROM (' . $query . ') AS u',
                [
                    'assigneeStartDate' => $startDate,
                    'assigneeEndDate' => $endDate,
                    'assignerStartDate' => $startDate,
                    'assignerEndDate' => $endDate,
                ],
            )
            ->fetchOne();

        $data = $this->getEntityManager()
            ->getConnection()
            ->executeQuery(
                $query . ' ORDER BY id ASC LIMIT ' . $limit . ' OFFSET ' . $offset,
                [
                    'assigneeStartDate' => $startDate,
                    'assigneeEndDate' => $endDate,
                    'assignerStartDate' => $startDate,
                    'assignerEndDate' => $endDate,
                ],
            )
            ->fetchAllAssociative();

        // Cannot DI UsersWithOverdueActions while the class is extending EntityRepository|AbstractEntityRepository
        return new UsersWithOverdueActions($data, (int) $totalItems, $limit);
    }

    private function createUnion(QueryBuilder ...$queryBuilders): string
    {
        $imploded = implode(') UNION (', array_map(static function (QueryBuilder $q) {
            return $q->getSQL();
        }, $queryBuilders));

        return '(' . $imploded . ')';
    }

    private function getActionUserQueryBuilderSetup(string $userTypeColumn): QueryBuilder
    {
        $query = $this->getEntityManager()->getConnection()->createQueryBuilder();

        // Use quoted identifier for user table to handle MSSQL reserved keyword
        $userTable = $this->getQuotedTableName('user');

        $query->select('u.id')
            ->from($userTable, 'u')
            ->innerJoin('u', 'user_email', 'pe', 'u.primary_email_id = pe.id')
            ->innerJoin('u', 'action_user', 'au', 'au.' . $userTypeColumn . ' = u.id')
            ->innerJoin('au', 'action', 'a', 'au.action_id = a.id');

        return $query;
    }

    /**
     * Get properly quoted table name for cross-database compatibility
     */
    private function getQuotedTableName(string $tableName): string
    {
        $platform = $this->getEntityManager()->getConnection()->getDatabasePlatform();
        return $platform->quoteSingleIdentifier($tableName);
    }
}
