<?php

namespace User\V1\Controller;

use Application\Interfaces\EntityManagerAwareInterface;
use Application\Service\PermissionService;
use Application\Service\SystemConfigurationService;
use Application\Traits\AccessPermissionTrait;
use Application\Traits\EntityManagerAwareTrait;
use Doctrine\Laminas\Hydrator\DoctrineObject;
use Form\Entity\FormType;
use Form\Service\FilterService;
use Form\Service\SchemaService;
use GuzzleHttp\Exception\ClientException;
use I18n\Service\TranslationService;
use Laminas\ApiTools\ApiProblem\ApiProblem;
use Laminas\ApiTools\ApiProblem\ApiProblemResponse;
use Laminas\ApiTools\ContentNegotiation\Request;
use Laminas\Hydrator\HydratorInterface;
use Laminas\Mvc\Controller\AbstractActionController;
use Laminas\View\Model\JsonModel;
use Teapot\StatusCode\Http;
use Throwable;
use User\Entity\Email;
use User\Entity\User as UserEntity;
use User\Filter\UserFilters;
use User\Helper\UserHelper;
use User\Service\KeycloakService;
use User\Service\UserService;
use User\ServiceManager\UserServiceAwareInterface;
use User\ServiceManager\UserServiceTrait;

use function array_flip;
use function array_intersect_key;
use function explode;
use function json_decode;
use function strlen;

class UserController extends AbstractActionController implements EntityManagerAwareInterface, UserServiceAwareInterface
{
    use AccessPermissionTrait;
    use EntityManagerAwareTrait;
    use UserServiceTrait;
    private const INACTIVITY_TRANSLATION = [
        'message' => 'USER.INACTIVITY_TIMEOUT.MESSAGE',
        'button' => [
            'ok' => 'USER.INACTIVITY_TIMEOUT.BUTTON.OK',
            'cancel' => 'USER.INACTIVITY_TIMEOUT.BUTTON.CANCEL',
        ],
    ];

    /** @var DoctrineObject */
    private $hydrator;

    /** @var KeycloakService */
    private $keycloakService;

    /** @var FilterService */
    private $filterService;

    /** @var SchemaService */
    private $schemaService;

    /** @var PermissionService */
    private $permissionService;
    private TranslationService $translationService;
    private SystemConfigurationService $systemConfigurationService;

    public function __construct(
        DoctrineObject $doctrineObject,
        KeycloakService $keycloakService,
        FilterService $filterService,
        SchemaService $schemaService,
        PermissionService $permissionService,
        TranslationService $translationService,
        SystemConfigurationService $systemConfigurationService

    ) {
        $this->hydrator = $doctrineObject;
        $this->keycloakService = $keycloakService;
        $this->filterService = $filterService;
        $this->schemaService = $schemaService;
        $this->permissionService = $permissionService;
        $this->translationService = $translationService;
        $this->systemConfigurationService = $systemConfigurationService;
    }

    /**
     * Returns a collection of User resources
     *
     * @return JsonModel
     */
    public function indexAction()
    {
        $params = $this->params()->fromQuery('where');
        $filters = new UserFilters();

        if (isset($params['forename'])) {
            $filters->setForename($params['forename']);
        }

        if (isset($params['surname'])) {
            $filters->setSurname($params['surname']);
        }

        if (isset($params['name'])) {
            $filters->setName($params['name']);
        }

        if (isset($params['excludedIds'])) {
            $filters->setExcludedIds($params['excludedIds']);
        }

        if (isset($params['isStaff'])) {
            $filters->setIsStaff($params['isStaff'] === '1');
        }

        $users = $this->getUserService()->findByFilters($filters);

        return new JsonModel([
            '_embedded' => [
                'users' => $users,
            ],
        ]);
    }

    public function rpcAction()
    {
        /** @var Request $request */
        $request = $this->getRequest();

        $userCriteria = $this->getUserService()->resolveUserCriteriaHeader($request->getHeaders()->toArray());
        $data = json_decode($request->getContent(), true);

        $findCriteriaValue = $data[$userCriteria['dataObjectKey']] ?? null;

        $userId = null;
        $userDraftId = null;

        try {
            $userId = $this->getUserService()->findUser($userCriteria['dataObjectKey'], $findCriteriaValue);
        } catch (Throwable $ex) {
            $this->getResponse()->setStatusCode(Http::BAD_REQUEST);

            return new JsonModel(['error' => $ex->getMessage()]);
        }

        $userData = array_intersect_key($data, array_flip(UserHelper::PROTECTED_ENTITY_VARIABLES));

        if ($userId) {
            switch ($userCriteria['dataMethod']) {
                case UserService::UPDATE:
                    $this->getUserService()->setHydrator($this->getHydrator());
                    $this->getUserService()->update($userId, $userData);

                    break;
                case UserService::DELETE:
                    $this->getUserService()->delete($userId);

                    break;
            }

            return new JsonModel([
                'id' => $userId,
                'type' => 'user',
            ]);
        }

        if ($userCriteria['dataMethod'] === UserService::UPDATE) {
            $this->getUserService()->setHydrator($this->getHydrator());
            $user = $this->getUserService()->create($userData + ['identity' => $data['identity']]);

            $this->getResponse()->setStatusCode(Http::CREATED);

            return new JsonModel([
                'id' => $user->getId(),
                'type' => 'draft-user',
            ]);
        }

        $this->getResponse()->setStatusCode(Http::BAD_REQUEST);

        return new JsonModel(['error' => 'No user found']);
    }

    /**
     * Returns a list of all available module permissions, grouped by module ID.
     * Module label is provided in 'title' property, permissions in 'perms'.
     */
    public function getPermissionsAction(): JsonModel
    {
        return new JsonModel($this->getUserService()->getPermissions());
    }

    /**
     * Returns a list of module permissions the specified user has.
     */
    public function getUserPermissionsAction(): JsonModel
    {
        $userId = (int) $this->params()->fromRoute('user');

        return new JsonModel($this->getUserService()->getUserSpecificPermissions($userId));
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function putUserPermissionsAction()
    {
        $apiProblem = $this->isAdmin($this->permissionService);
        if ($apiProblem instanceof ApiProblem) {
            return new ApiProblemResponse($apiProblem);
        }

        $request = $this->getRequest();
        $data = json_decode($request->getContent(), true);
        if (!isset($data['permissions'])) {
            return new ApiProblemResponse(new ApiProblem(Http::BAD_REQUEST, 'Missing permissions'));
        }

        $userId = (int) $this->params()->fromRoute('user');

        $result = ['success' => $this->getUserService()->updateUserSpecificPermissions($userId, $data['permissions'])];

        return new JsonModel($result);
    }

    /**
     * Returns a list of module permissions the specified group has.
     */
    public function getGroupPermissionsAction(): JsonModel
    {
        $groupId = (int) $this->params()->fromRoute('group');

        return new JsonModel($this->getUserService()->getGroupSpecificPermissions($groupId));
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function putGroupPermissionsAction()
    {
        $apiProblem = $this->isAdmin($this->permissionService);
        if ($apiProblem instanceof ApiProblem) {
            return new ApiProblemResponse($apiProblem);
        }

        $request = $this->getRequest();
        $data = json_decode($request->getContent(), true);
        if (!isset($data['permissions'])) {
            return new ApiProblemResponse(new ApiProblem(Http::BAD_REQUEST, 'Missing permissions'));
        }

        $groupId = (int) $this->params()->fromRoute('group');

        $result = ['success' => $this->getUserService()->updateGroupSpecificPermissions($groupId, $data['permissions'])];

        return new JsonModel($result);
    }

    private function getHydrator(): HydratorInterface
    {
        return $this->hydrator;
    }

    /**
     * @return ApiProblemResponse|JsonModel
     * @throws Throwable
     */
    public function localAction()
    {
        $apiProblem = $this->isAdmin($this->permissionService);
        if ($apiProblem instanceof ApiProblem) {
            return new ApiProblemResponse($apiProblem);
        }

        /** @var Request $request */
        $request = $this->getRequest();

        $data = json_decode($request->getContent(), true);

        if (empty($data['username'])) {
            return new ApiProblemResponse(new ApiProblem(Http::BAD_REQUEST, 'Missing username'));
        }

        if (strlen($data['username']) < 4) {
            return new ApiProblemResponse(
                new ApiProblem(
                    Http::BAD_REQUEST,
                    'Username must be at least 4 characters long',
                ),
            );
        }

        if (empty($data['email'])) {
            return new ApiProblemResponse(new ApiProblem(Http::BAD_REQUEST, 'Missing email address'));
        }

        $cleanUp = function (?string $userId): void {
            $this->getEntityManager()->rollback();
            if ($userId) {
                $this->keycloakService->deleteUser($userId);
            }
        };

        $this->getEntityManager()->beginTransaction();

        $data['emails'] = [['email' => $data['email'], 'type' => Email::WORK]];

        try {
            /** @var UserEntity $user */
            $user = $this->getUserService()->getHydrator()->hydrate($data, new UserEntity());
            $remoteUserId = $this->keycloakService->createUser($user);

            $data['identity']['identity'] = $remoteUserId;
            $data['identity']['local'] = true;
            $data['identity']['active'] = true;

            $user = $this->getUserService()->create($data);
        } catch (ClientException $ex) {
            $cleanUp($remoteUserId ?? null);

            $message = $ex->getMessage();
            if ($ex->hasResponse()) {
                $content = json_decode($ex->getResponse()->getBody()->getContents(), true);
                if (!empty($content['errorMessage'])) {
                    $message = $content['errorMessage'];
                }
            }

            return new ApiProblemResponse(new ApiProblem(Http::BAD_REQUEST, $message, null, 'COMMON.EXCEPTIONS.VALIDATION_ERROR'));
        } catch (Throwable $ex) {
            $cleanUp($remoteUserId ?? null);

            throw $ex;
        }

        $this->getEntityManager()->commit();

        return new JsonModel($user->toArray(1));
    }

    public function userFilterAction(): JsonModel
    {
        $formType = $this->getFilterFormType();
        $filterForm = $this->filterService->getFilterFormByFormType($formType);

        $schema = $this->schemaService->getSchemaForForm($filterForm);
        $schema['submit'] = false;

        return new JsonModel($schema);
    }

    private function getFilterFormType(): ?FormType
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('ft')
            ->from(FormType::class, 'ft')
            ->andWhere('ft.key = :formTypeKey');

        $qb->setParameter('formTypeKey', 'user_dashboard_filter');

        return $qb->getQuery()->getOneOrNullResult();
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function apiUserAction()
    {
        $uuid = (string) $this->params()->fromRoute('uuid');
        $user = $this->getUserService()->findByApiKey($uuid);
        if (!$user) {
            return new ApiProblemResponse(
                new ApiProblem(Http::NOT_FOUND, 'User not found'),
            );
        }

        return new JsonModel([
            'user' => [
                'id' => (int) $user->getId(),
                'title' => $user->getTitle(),
                'forename' => $user->getForename(),
                'surname' => $user->getSurname(),
            ],
            'isAdmin' => $this->permissionService->userIsFullAdmin($user),
            'permissions' => $this->getUserService()->getEncodedTokenPermissions($user, $this->translationService),
        ]);
    }

    /**
     * @return ApiProblemResponse|JsonModel
     */
    public function getEmailsAction()
    {
        $ids = (string) $this->params()->fromQuery('ids');
        $ids = explode(',', $ids);

        if (empty($ids)) {
            return new ApiProblemResponse(
                new ApiProblem(Http::BAD_REQUEST, 'ids parameter missing'),
            );
        }

        return new JsonModel($this->userService->getEmailsByUserIds($ids));
    }

    public function getInactivityTimeoutConfigAction(): JsonModel
    {
        $language = $this->userService->getAuthenticatedUserLanguage();

        $translations = $this->translationService->getPartialTranslations('inactivity_timeout', $language);

        $timeout = $this->systemConfigurationService->getInactivityTimeout();

        $timeoutMilliseconds = $timeout * 60000;

        return new JsonModel([
            'timeoutValueMilliseconds' => $timeoutMilliseconds,
            'timeoutMessage' => str_replace('{{timeout}}', $timeout, $translations[self::INACTIVITY_TRANSLATION['message']]),
            'okButton' => $translations[self::INACTIVITY_TRANSLATION['button']['ok']],
            'cancelButton' => $translations[self::INACTIVITY_TRANSLATION['button']['cancel']],
        ]);
    }

    public function getLockoutSettingsAction(): JsonModel
    {
        return new JsonModel($this->systemConfigurationService->getLockoutSettings());
    }

    public function setLockoutSettingsAction(): JsonModel
    {
        $request = $this->getRequest();
        $data = json_decode($request->getContent(), true);

        return new JsonModel(['success' => $this->systemConfigurationService->setLockoutSettings($data)]);
    }
}
