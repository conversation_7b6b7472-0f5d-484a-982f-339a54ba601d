<?php

namespace User\Service;

use DateTime;
use Generator;
use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use InvalidArgumentException;
use RuntimeException;
use Teapot\StatusCode\Http;
use User\Dto\Keycloak\PasswordPolicy;
use User\Entity\Email;
use User\Entity\User as UserEntity;

use function array_keys;
use function array_map;
use function array_reduce;
use function array_search;
use function count;
use function current;
use function explode;
use function getenv;
use function implode;
use function json_decode;
use function json_encode;
use function preg_match_all;
use function sprintf;
use function str_replace;
use function strrchr;
use function substr;

class KeycloakService
{
    public const DEFAULT_DAYS_OF_INACTIVITY = 180;

    private Client $httpClient;
    
    private string $url;
    
    private string $realmAdmin;

    private string $realmUsers;

    public function __construct(Client $client)
    {
        $this->httpClient = $client;

        $this->url = getenv('KEYCLOAK_URL');
        $this->realmAdmin = getenv('KEYCLOAK_REALM_ADMIN');
        $this->realmUsers = getenv('KEYCLOAK_REALM_USERS');
    }

    private function appendUserData(array $data, UserEntity $user): array
    {
        if (!isset($data['username'])) {
            $data['username'] = $user->getUsername();
        }
        if (!isset($data['firstName'])) {
            $data['firstName'] = $user->getForename();
        }
        if (!isset($data['lastName'])) {
            $data['lastName'] = $user->getSurname();
        }
        if (!isset($data['email'])) {
            $email = $user->getPrimaryEmail() ?: $user->getEmails()->first();
            $email = $email instanceof Email ? $email->getEmail() : null;

            $data['email'] = $email;
        }
        
        return $data;
    }

    public function createUser(UserEntity $user): string
    {
        $authToken = $this->login();
        
        $response = $this->httpClient->post(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users',
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
                RequestOptions::JSON => $this->appendUserData([
                    'enabled' => true,
                    'emailVerified' => true
                ], $user),
            ],
        );

        return substr(strrchr($response->getHeaderLine('Location'), '/'), 1);
    }

    public function deleteUser(string $userId): bool
    {
        $authToken = $this->login();

        $response = $this->httpClient->delete(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users/' . $userId,
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
            ],
        );

        return $response->getStatusCode() === Http::NO_CONTENT;
    }

    public function activateUser(string $userId): void
    {
        $authToken = $this->login();

        $this->sendActivationEmail($authToken, $userId);
        $this->setUserUpdatePassword($authToken, $userId);
    }

    /**
     * @throws RuntimeException when provided user does not have an email address
     */
    public function updateUser(UserEntity $user): bool
    {
        $authToken = $this->login();

        $identity = $user->getIdentity()->getIdentity();

        $keycloakUser = $this->getUserFromKeycloak($authToken, $identity);
        $attributes = $keycloakUser['attributes'] ?? [];

        unset(
            $attributes['activeFrom'],
            $attributes['activeTo'],
            $attributes['lockOutDate'],
            $attributes['lockOutReason']
        );

        if ($user->getActiveFrom() !== null) {
            $attributes['activeFrom'] = $user->getActiveFrom()->format('c');
        }

        if ($user->getActiveTo() !== null) {
            $attributes['activeTo'] = $user->getActiveTo()->format('c');
        }

        if ($user->getLockOutDate() !== null) {
            $attributes['lockOutDate'] = $user->getLockOutDate()->format('c');
        }

        if ($user->getLockOutReason() !== null) {
            $attributes['lockOutReason'] = $user->getLockOutReason();
        }

        $data = $this->appendUserData([
            'enabled' => true,
            'attributes' => $attributes
        ], $user);

        if ($data['email'] === null) {
            throw new RuntimeException(sprintf(
                'Missing email address for user ID %d',
                $user->getId(),
            ));
        }

        return $this->updateUserInKeycloak($authToken, $identity, $data);
    }

    private function getUserFromKeycloak(string $authToken, string $identity): array
    {
        $response = $this->httpClient->get(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users/' . $identity,
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
            ],
        );

        $data = $response->getBody()->getContents();

        return json_decode($data, true);
    }

    private function login(): string
    {
        $response = $this->httpClient->post(
            $this->url . '/auth/realms/' . $this->realmAdmin . '/protocol/openid-connect/token',
            [
                RequestOptions::FORM_PARAMS => [
                    'username' => getenv('KEYCLOAK_USER'),
                    'password' => getenv('KEYCLOAK_PASS'),
                    'grant_type' => 'password',
                    'client_id' => 'admin-cli',
                ],
            ],
        );

        $json = json_decode($response->getBody()->getContents(), true);

        if (empty($json['access_token'])) {
            throw new RuntimeException('Keycloak authorization failed');
        }

        return $json['access_token'];
    }

    private function setUserUpdatePassword(string $authToken, string $userId): void
    {
        $this->updateUserInKeycloak($authToken, $userId, [
            'requiredActions' => [
                'UPDATE_PASSWORD',
            ],
        ]);
    }

    private function updateUserInKeycloak(string $authToken, string $userId, array $data): bool
    {
        $response = $this->httpClient->put(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users/' . $userId,
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
                RequestOptions::JSON => $data,
            ],
        );

        return $response->getStatusCode() === Http::NO_CONTENT;
    }

    private function sendActivationEmail(string $authToken, string $userId): void
    {
        $this->httpClient->put(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users/' . $userId . '/execute-actions-email',
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
                RequestOptions::JSON => [
                    'UPDATE_PASSWORD',
                ],
                RequestOptions::QUERY => [
                    'lifespan' => getenv('KEYCLOAK_LIFESPAN'),
                ],
            ],
        );
    }

    private function getRealmSettings(string $authToken): array
    {
        $response = $this->httpClient->get(
            $this->url . '/auth/admin/realms/' . $this->realmUsers,
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
            ],
        );

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getPasswordPolicy(): PasswordPolicy
    {
        $authToken = $this->login();
        $json = $this->getRealmSettings($authToken);

        $passwordPolicy = new PasswordPolicy();
        if (preg_match_all('/(?P<name>[a-z]+)(?:\((?P<value>\d+)\))?/i', $json['passwordPolicy'] ?? '', $matches)) {
            foreach ($matches['name'] as $i => $name) {
                if ($name === 'and') {
                    continue;
                }

                $property = array_search($name, PasswordPolicy::PROPERTY_TO_KEYCLOAK_MAP, true);
                if ($property !== false) {
                    $passwordPolicy->{$property} = (int) $matches['value'][$i];
                }
            }
        }

        return $passwordPolicy;
    }

    public function updatePasswordPolicy(PasswordPolicy $passwordPolicy): bool
    {
        $authToken = $this->login();
        $json = $this->getRealmSettings($authToken);

        $policies = empty($json['passwordPolicy']) ? [] : array_reduce(
            explode(' and ', $json['passwordPolicy']),
            static function (array $policies, string $policy): array {
                [$name, $value] = explode('(', str_replace(')', '', $policy));
                $policies[$name] = (int) $value;

                return $policies;
            },
            [],
        );

        foreach (PasswordPolicy::PROPERTY_TO_KEYCLOAK_MAP as $property => $keycloak) {
            if ($passwordPolicy->{$property} === 0) {
                unset($policies[$keycloak]);
            } else {
                $policies[$keycloak] = $passwordPolicy->{$property};
            }
        }

        $update['passwordPolicy'] = implode(
            ' and ',
            array_map(
                static function (string $name, int $value): string {
                    return $name . '(' . $value . ')';
                },
                array_keys($policies),
                $policies,
            ),
        );

        $response = $this->httpClient->put(
            $this->url . '/auth/admin/realms/' . $this->realmUsers,
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
                RequestOptions::JSON => $update,
            ],
        );

        return $response->getStatusCode() === Http::NO_CONTENT;
    }

    /**
     * @throws InvalidArgumentException when non-local user has been provided
     */
    public function updateUserDetailsFromKeycloak(UserEntity $user): bool
    {
        $identity = $user->getIdentity();
        if (!$identity->isLocal()) {
            throw new InvalidArgumentException(sprintf(
                'User ID %d is not a local user.',
                $user->getId(),
            ));
        }

        $authToken = $this->login();

        try {
            $userDetails = $this->getUserFromKeycloak($authToken, $identity->getIdentity());
        } catch (ClientException $exception) {
            if ($exception->getCode() === Http::NOT_FOUND) {
                return false;
            }

            throw $exception;
        }

        if ($userDetails['enabled'] === true
            || !empty($userDetails['attributes']['lockOutDate'])
        ) {
            return false;
        }

        $user->setActive(false);
        $user->setLockOutDate(new DateTime());
        $user->setLockOutReason(UserEntity::LOCK_OUT_REASON_MANUAL);

        $this->updateUser($user);

        return true;
    }

    /**
     * @throws InvalidArgumentException when non-local user has been provided
     */
    public function updateLoginDate(UserEntity $user): void
    {
        if (!$user->getIdentity()->isLocal()) {
            throw new InvalidArgumentException(sprintf(
                'User ID %d is not a local user',
                $user->getId(),
            ));
        }

        $identity = $user->getIdentity()->getIdentity();

        $authToken = $this->login();
        $userDetails = $this->getUserFromKeycloak($authToken, $identity);

        $attributes = $userDetails['attributes'] ?? [];
        $attributes['lastLoginDate'] = (new DateTime())->format('c');

        $this->updateUserInKeycloak(
            $authToken,
            $identity,
            $this->appendUserData(['attributes' => $attributes], $user),
        );
    }

    /**
     * @return array[] User details
     */
    public function getUsers(): Generator
    {
        $first = 0;
        $max = 100;

        do {
            $users = $this->getUsersCollection($first, $max);
            $first += $max;

            foreach ($users as $user) {
                yield $user;
            }
        } while (count($users) === $max);
    }

    private function getUsersCollection(int $first, int $max): array
    {
        $authToken = $this->login();

        $response = $this->httpClient->get(
            $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users',
            [
                RequestOptions::HEADERS => [
                    'Authorization' => 'Bearer ' . $authToken,
                ],
                RequestOptions::QUERY => [
                    'first' => $first,
                    'max' => $max,
                ],
            ],
        );

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getDaysOfInactivityToLockOut(): int
    {
        $authToken = $this->login();
        $data = $this->getRealmSettings($authToken);

        return (int) ($data['attributes']['daysOfInactivityLockOut'] ?? self::DEFAULT_DAYS_OF_INACTIVITY);
    }

    public function hasBeenActive(array $userDetails, int $daysOfInactivityToLockOut): bool
    {
        // if not configured -> user always active
        if ($daysOfInactivityToLockOut === 0) {
            return true;
        }

        $date = new DateTime('-' . $daysOfInactivityToLockOut . ' days');

        // For default user there is no createdTimestamp
        if (!empty($userDetails['createdTimestamp'])) {
            $createdAt = new DateTime('@' . substr($userDetails['createdTimestamp'], 0, -3));

            // User account has been created in last $daysOfInactivityToLockOut days
            if ($createdAt >= $date) {
                return true;
            }
        }

        $activeFrom = empty($userDetails['attributes']['activeFrom'])
            ? null
            : new DateTime(current($userDetails['attributes']['activeFrom']));

        // User has active from date, cannot be classified as inactive yet
        if ($activeFrom !== null && $activeFrom >= $date) {
            return true;
        }

        if (!empty($userDetails['attributes']['lastLoginDate'])) {
            $lastLoginDate = new DateTime(current($userDetails['attributes']['lastLoginDate']));

            return $lastLoginDate > $date;
        }

        return true;
    }

    /**
     * @throws GuzzleException
     */
    public function setDaysOfInactivityToLockOut($value): bool
    {
        $data['attributes']['daysOfInactivityLockOut'] = $value;

        try {
            $authToken = $this->login();

            $this->httpClient->put(
                $this->url . '/auth/admin/realms/' . $this->realmUsers,
                [
                    RequestOptions::HEADERS => [
                        'Authorization' => 'Bearer ' . $authToken,
                    ],
                    RequestOptions::JSON => $data,
                ],
            );

            return true;
        } catch (GuzzleException $e) {
            throw new $e();
        }
    }

    public function logoutUser(string $userId): bool
    {
        $authToken = $this->login();

        try {
            $this->httpClient->post(
                $this->url . '/auth/admin/realms/' . $this->realmUsers . '/users/' . $userId . '/logout',
                [
                    RequestOptions::HEADERS => [
                        'Authorization' => 'Bearer ' . $authToken,
                    ],
                ]
            );

            return true;
        } catch (GuzzleException $e) {
            throw new RuntimeException('Failed to log out user from Keycloak: ' . $e->getMessage());
        }
    }
}
