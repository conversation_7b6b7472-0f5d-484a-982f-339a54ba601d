<?php

namespace User\Entity;

use Acl\Entity\RuleUser;
use Application\Mapping\Annotation\EntityConfig;
use DateTime;
use Datix\Entity\AbstractEntity;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Criteria;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use I18n\Entity\Language;
use Identity\Entity\Identity;
use SafetyAlert\Entity\DistributionUser;
use Tenant\Entity\TenantAwareInterface;
use Tenant\Traits\TenantAware;

use Application\Entity\Log\ApplicationLog;
use User\Repository\UserRepository;

use function getenv;

#[
    ORM\Table(name: '`user`'),
    ORM\Entity(repositoryClass: UserRepository::class),
    ORM\HasLifecycleCallbacks,
    Gedmo\Loggable(logEntryClass: ApplicationLog::class),
    EntityConfig(
        tableName: '`user`',
        tableAlias: 'u',
        module: 'user',
        isAdmin: true
    )
]
class User extends AbstractEntity implements TenantAwareInterface
{
    use TenantAware;
    public const TABLE_NAME = 'user';
    public const LOCK_OUT_REASON_MANUAL = 'manual';
    public const LOCK_OUT_REASON_INACTIVITY = 'inactivity';
    public const LOCK_OUT_REASON_OUTSIDE_DATES = 'outside-dates';
    public const FIELD_FORENAME = 'forename';
    public const FIELD_SURNAME = 'surname';
    public const FIELD_EMAILS = 'emails';

    public const ADMIN_USER_ID = 1;

    /** @var Collection<Service> */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: Service::class,
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $services;

    /** @var Collection<Location> */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: Location::class,
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $locations;

    /** @var Collection<HierarchyNode> */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: HierarchyNode::class,
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $registerHierarchyNodes;

    #[
        ORM\OneToOne(targetEntity: Identity::class, cascade: ['persist', 'remove']),
        ORM\JoinColumn(name: 'identity_id', referencedColumnName: 'id')
    ]
    protected Identity $identity;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    protected ?string $title = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    protected ?string $forename = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    protected ?string $surname = null;

    /**
     * Email Addresses
     * @var Collection<Email>
     */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: Email::class,
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $emails;

    #[
        ORM\ManyToOne(targetEntity: Email::class),
        ORM\JoinColumn(name: 'primary_email_id', referencedColumnName: 'id', onDelete: 'SET NULL')
    ]
    protected ?Email $primaryEmail = null;

    /**
     * Addresses
     * @var Collection<Address>
     */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: 'Address',
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $addresses;

    /**
     * Telephone numbers
     * @var Collection<Number>
     */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: 'Number',
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $numbers;

    #[ORM\Column(name: 'job_title', type: 'string', length: 255, nullable: true)]
    protected ?string $jobTitle = null;

    #[ORM\Column(name: 'staff_number', type: 'string', length: 255, nullable: true)]
    protected ?string $staffNumber = null;

    #[
        ORM\ManyToOne(targetEntity: 'Group', inversedBy: 'users'),
        ORM\JoinColumn(name: 'group_id', referencedColumnName: 'id')
    ]
    protected ?Group $group = null;

    /** @var Collection<RuleUser> */
    #[ORM\OneToMany(mappedBy: 'user', targetEntity: RuleUser::class, cascade: ['persist'])]
    protected Collection $ruleUsers;

    #[
        ORM\Column(type: 'datetime'),
        Gedmo\Timestampable(on: 'create')
    ]
    protected DateTime $created;

    #[
        ORM\Column(type: 'datetime'),
        Gedmo\Timestampable(on: 'update')
    ]
    protected DateTime $updated;

    #[ORM\Column(type: 'datetime', nullable: true)]
    protected ?DateTime $closed = null;

    #[
        ORM\OneToOne(
            inversedBy: 'user',
            targetEntity: Setting::class,
            cascade: ['persist', 'remove']
        ),
        ORM\JoinColumn(
            name: 'settings_id',
            referencedColumnName: 'id',
            onDelete: 'CASCADE'
        )
    ]
    protected Setting $settings;

    /** @var Collection<SystemPermission> */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: 'SystemPermission',
        cascade: ['persist', 'remove'],
        orphanRemoval: true
    )]
    protected Collection $systemPermissions;
    protected ?User $systemUser = null;

    #[ORM\Column(type: 'boolean', nullable: false)]
    protected bool $reviewed = false;

    /** @var Collection<UserForm> */
    #[ORM\OneToMany(mappedBy: 'user', targetEntity: 'UserForm', cascade: ['all'], orphanRemoval: true)]
    protected Collection $forms;

    /** @var string[]|null */
    #[ORM\Column(type: 'json', nullable: true)]
    protected ?array $positions = null;

    #[ORM\Column(name: 'employee_status', type: 'string', nullable: true)]
    protected ?string $employeeStatus = null;

    #[ORM\OneToMany(mappedBy: 'targetUser', targetEntity: 'Delegation')]
    protected Collection $delegations;

    #[ORM\Column(name: 'local_admin', type: 'boolean', options: ['default' => 0])]
    protected bool $localAdmin = false;

    #[ORM\Column(name: 'central_admin', type: 'boolean', options: ['default' => 0])]
    protected bool $centralAdmin = false;

    #[ORM\Column(name: 'contact_merge_admin', type: 'boolean', options: ['default' => 0])]
    protected bool $contactMergeAdmin = false;

    #[ORM\Column(name: 'allow_self_delegation', type: 'boolean', options: ['default' => 0])]
    protected bool $allowSelfDelegation = false;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    protected ?string $username = null;

    #[ORM\Column(type: 'boolean', options: ['default' => 1])]
    protected bool $active = true;

    #[ORM\Column(name: 'active_from', type: 'datetime', nullable: true)]
    protected ?DateTime $activeFrom = null;

    #[ORM\Column(name: 'active_to', type: 'datetime', nullable: true)]
    protected ?DateTime $activeTo = null;

    #[ORM\Column(name: 'lock_out_date', type: 'datetime', nullable: true)]
    protected ?DateTime $lockOutDate = null;

    #[ORM\Column(name: 'lock_out_reason', type: 'string', length: 16, nullable: true)]
    protected ?string $lockOutReason = null;

    #[ORM\Column(name: 'is_staff', type: 'boolean', options: ['default' => 0])]
    protected bool $isStaff = false;

    /** @var Collection<DistributionUser"> */
    #[ORM\OneToMany(
        mappedBy: 'user',
        targetEntity: DistributionUser::class,
        fetch: 'LAZY',
    )]
    protected Collection $distributionUsers;

    #[ORM\Column(name: 'local_admin_location_restricted', type: 'integer', nullable: true, options: ['default' => null])]
    protected ?int $localAdminLocationRestricted = null;

    #[ORM\Column(name: 'local_admin_service_restricted', type: 'integer', nullable: true, options: ['default' => null])]
    protected ?int $localAdminServiceRestricted = null;

    #[ORM\Column(name: 'last_unlocked_at', type: 'datetime', nullable: true)]
    protected ?DateTime $lastUnlockedAt = null;

    /**
     * User constructor.
     */
    public function __construct()
    {
        $this->addresses = new ArrayCollection();
        $this->emails = new ArrayCollection();
        $this->locations = new ArrayCollection();
        $this->numbers = new ArrayCollection();
        $this->ruleUsers = new ArrayCollection();
        $this->services = new ArrayCollection();
        $this->systemPermissions = new ArrayCollection();
        $this->registerHierarchyNodes = new ArrayCollection();
        $this->forms = new ArrayCollection();
        $this->delegations = new ArrayCollection();
        $this->distributionUsers = new ArrayCollection();
    }

    public function toArray(int $depth = 5, array $visited = []): array
    {
        $data = parent::toArray($depth, $visited);

        if ($this->getPrimaryEmail()) {
            $data['primaryEmail'] = $this->getPrimaryEmail()->toArray(1);
        }

        return $data;
    }

    public function getIdentity(): Identity
    {
        return $this->identity;
    }

    public function setIdentity(Identity $identity): void
    {
        $this->identity = $identity;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    public function getForename(): ?string
    {
        return $this->forename;
    }

    public function setForename(?string $forename): void
    {
        $this->forename = $forename;
    }

    public function getSurname(): ?string
    {
        return $this->surname;
    }

    public function setSurname(?string $surname): void
    {
        $this->surname = $surname;
    }

    /**
     * @return Collection<Location>
     */
    public function getLocations(): Collection
    {
        return $this->locations;
    }

    /**
     * @param Collection<Location> $locations
     */
    public function setLocations(Collection $locations): self
    {
        $this->locations = $locations;

        return $this;
    }

    public function addLocation(Location $location): self
    {
        $location->setUser($this);
        $this->locations->add($location);

        return $this;
    }

    public function removeLocation(Location $location): self
    {
        $this->locations->removeElement($location);

        return $this;
    }

    /**
     * @param Collection<Location> $locations
     */
    public function addLocations(Collection $locations): self
    {
        foreach ($locations as $location) {
            $this->addLocation($location);
        }

        return $this;
    }

    /**
     * @param Collection<Location> $locations
     */
    public function removeLocations(Collection $locations): self
    {
        foreach ($locations as $location) {
            $this->removeLocation($location);
        }

        return $this;
    }

    /**
     * @return Collection<HierarchyNode>
     */
    public function getRegisterHierarchyNodes(): Collection
    {
        return $this->registerHierarchyNodes;
    }

    /**
     * @param Collection<HierarchyNode> $registerHierarchyNodes
     */
    public function setRegisterHierarchyNodes(Collection $registerHierarchyNodes): self
    {
        $this->registerHierarchyNodes = $registerHierarchyNodes;

        return $this;
    }

    public function addRegisterHierarchyNode(HierarchyNode $hierarchyNode): self
    {
        $hierarchyNode->setUser($this);
        $this->registerHierarchyNodes->add($hierarchyNode);

        return $this;
    }

    public function removeRegisterHierarchyNode(HierarchyNode $hierarchyNode): self
    {
        $this->registerHierarchyNodes->removeElement($hierarchyNode);

        return $this;
    }

    public function addRegisterHierarchyNodes(Collection $hierarchyNodes): self
    {
        foreach ($hierarchyNodes as $hierarchyNode) {
            $this->addRegisterHierarchyNode($hierarchyNode);
        }

        return $this;
    }

    public function removeRegisterHierarchyNodes(Collection $hierarchyNodes): self
    {
        foreach ($hierarchyNodes as $hierarchyNode) {
            $this->removeRegisterHierarchyNode($hierarchyNode);
        }

        return $this;
    }

    /**
     * @return Collection<Service>
     */
    public function getServices(): Collection
    {
        return $this->services;
    }

    /**
     * @param Collection<Service> $services
     */
    public function setServices(Collection $services): self
    {
        $this->services = $services;

        return $this;
    }

    public function addService(Service $service): self
    {
        $service->setUser($this);

        $this->services->add($service);

        return $this;
    }

    public function removeService(Service $service): self
    {
        $this->services->removeElement($service);

        return $this;
    }

    /**
     * @param Collection<Service> $services
     */
    public function addServices(Collection $services): self
    {
        foreach ($services as $service) {
            $this->addService($service);
        }

        return $this;
    }

    /**
     * @param Collection<Service> $services
     */
    public function removeServices(Collection $services): self
    {
        foreach ($services as $service) {
            $this->removeService($service);
        }

        return $this;
    }

    public function getJobTitle(): ?string
    {
        return $this->jobTitle;
    }

    public function setJobTitle(?string $jobTitle): self
    {
        $this->jobTitle = $jobTitle;

        return $this;
    }

    public function getStaffNumber(): ?string
    {
        return $this->staffNumber;
    }

    public function setStaffNumber(?string $staffNumber): self
    {
        $this->staffNumber = $staffNumber;

        return $this;
    }

    public function getGroup(): ?Group
    {
        return $this->group;
    }

    public function setGroup(Group $group): void
    {
        $this->group = $group;
    }

    /**
     * @param bool $includeArchived default true, all rules attached to the user
     *                              are returned, also these archived
     * @return Collection<RuleUser>
     */
    public function getRuleUsers(bool $includeArchived = true): Collection
    {
        if (!$includeArchived) {
            $criteria = Criteria::create()
                ->where(Criteria::expr()->isNull('archivedAt'));

            return $this->ruleUsers->matching($criteria);
        }

        return $this->ruleUsers;
    }

    /**
     * @param Collection<RuleUser> $ruleUsers
     */
    public function setRuleUsers(Collection $ruleUsers): void
    {
        $this->ruleUsers = $ruleUsers;
    }

    public function addRuleUser(RuleUser $ruleUser): void
    {
        $ruleUser->setUser($this);
        $this->ruleUsers->add($ruleUser);
    }

    public function removeRuleUser(RuleUser $ruleUser): void
    {
        $this->ruleUsers->removeElement($ruleUser);
    }

    /**
     * @param iterable<RuleUser> $ruleUsers
     */
    public function addRuleUsers(iterable $ruleUsers): void
    {
        foreach ($ruleUsers as $ruleUser) {
            $this->addRuleUser($ruleUser);
        }
    }

    /**
     * @param iterable<RuleUser> $ruleUsers
     */
    public function removeRuleUsers(iterable $ruleUsers): void
    {
        foreach ($ruleUsers as $ruleUser) {
            $this->removeRuleUser($ruleUser);
        }
    }

    public function getSettings(): Setting
    {
        return $this->settings;
    }

    public function setSettings(Setting $settings): self
    {
        $this->settings = $settings;

        return $this;
    }

    /**
     * @return Collection<Email>
     */
    public function getEmails(): Collection
    {
        return $this->emails;
    }

    /**
     * @param Collection<Email> $emails
     */
    public function setEmails(Collection $emails): self
    {
        $this->emails = $emails;

        return $this;
    }

    public function addEmail(Email $email): self
    {
        $email->setUser($this);
        $this->emails->add($email);

        return $this;
    }

    public function removeEmail(Email $email): self
    {
        $this->emails->removeElement($email);

        return $this;
    }

    /**
     * @param Collection<Email> $collection
     */
    public function addEmails(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->addEmail($entity);
        }

        return $this;
    }

    /**
     * @param Collection<Email> $collection
     */
    public function removeEmails(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->removeEmail($entity);
        }

        return $this;
    }

    /**
     * @return Collection<Address>
     */
    public function getAddresses(): Collection
    {
        return $this->addresses;
    }

    /**
     * @param Collection<Address> $addresses
     */
    public function setAddresses(Collection $addresses): self
    {
        $this->addresses = $addresses;

        return $this;
    }

    public function addAddress(Address $address): self
    {
        $address->setUser($this);
        $this->addresses->add($address);

        return $this;
    }

    /**
     * @param Collection<Address> $collection
     */
    public function addAddresses(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->addAddress($entity);
        }

        return $this;
    }

    /**
     * @param Collection<Address> $collection
     */
    public function removeAddresses(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->removeAddress($entity);
        }

        return $this;
    }

    public function removeAddress(Address $address): self
    {
        $this->addresses->removeElement($address);

        return $this;
    }

    /**
     * @return Collection<Number>
     */
    public function getNumbers(): Collection
    {
        return $this->numbers;
    }

    /**
     * @param Collection<Number> $numbers
     */
    public function setNumbers(Collection $numbers): self
    {
        $this->numbers = $numbers;

        return $this;
    }

    public function addNumber(Number $number): self
    {
        $number->setUser($this);
        $this->numbers->add($number);

        return $this;
    }

    public function removeNumber(Number $number): self
    {
        $this->numbers->removeElement($number);

        return $this;
    }

    /**
     * @param Collection<Number> $collection
     */
    public function addNumbers(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->addNumber($entity);
        }

        return $this;
    }

    /**
     * @param Collection<Number> $collection
     */
    public function removeNumbers(Collection $collection): self
    {
        foreach ($collection as $entity) {
            $this->removeNumber($entity);
        }

        return $this;
    }

    public function getCreated(): DateTime
    {
        return $this->created;
    }

    public function setCreated(DateTime $created): self
    {
        $this->created = $created;

        return $this;
    }

    public function getUpdated(): DateTime
    {
        return $this->updated;
    }

    public function setUpdated(DateTime $updated): self
    {
        $this->updated = $updated;

        return $this;
    }

    public function getClosed(): ?DateTime
    {
        return $this->closed;
    }

    public function setClosed(?DateTime $closed): self
    {
        $this->closed = $closed;

        return $this;
    }

    public function getPrimaryEmail(): ?Email
    {
        return $this->primaryEmail;
    }

    public function setPrimaryEmail(?Email $primaryEmail): void
    {
        $this->primaryEmail = $primaryEmail;
    }

    /**
     * @return Collection<SystemPermission>
     */
    public function getSystemPermissions(): Collection
    {
        return $this->systemPermissions;
    }

    /**
     * @param Collection<SystemPermission> $systemPermissions
     */
    public function setSystemPermissions(Collection $systemPermissions): void
    {
        $this->systemPermissions = $systemPermissions;
    }

    public function addSystemPermission(SystemPermission $permission): void
    {
        $permission->setUser($this);
        $this->systemPermissions->add($permission);
    }

    public function removeSystemPermission(SystemPermission $permission): void
    {
        $this->systemPermissions->removeElement($permission);
    }

    /**
     * @param iterable<SystemPermission> $permissions
     */
    public function addSystemPermissions(iterable $permissions): void
    {
        foreach ($permissions as $permission) {
            $this->addSystemPermission($permission);
        }
    }

    /**
     * @param iterable<SystemPermission> $permissions
     */
    public function removeSystemPermissions(iterable $permissions): void
    {
        foreach ($permissions as $permission) {
            $this->removeSystemPermission($permission);
        }
    }

    public function getSystemUser(): ?self
    {
        return $this->systemUser;
    }

    public function setSystemUser(?self $systemUser): self
    {
        $this->systemUser = $systemUser;

        return $this;
    }

    public function getReviewed(): bool
    {
        return $this->reviewed;
    }

    public function setReviewed(bool $reviewed): void
    {
        $this->reviewed = $reviewed;
    }

    /**
     * @return Collection<UserForm>
     */
    public function getForms(): Collection
    {
        return $this->forms;
    }

    /**
     * @param Collection<UserForm> $forms
     */
    public function setForms(Collection $forms): self
    {
        $this->forms = $forms;

        return $this;
    }

    public function addForm(UserForm $form): self
    {
        $this->forms->add($form);

        return $this;
    }

    /**
     * @param Collection<UserForm> $forms
     */
    public function addForms(Collection $forms): self
    {
        foreach ($forms as $form) {
            $this->addForm($form);
        }

        return $this;
    }

    public function removeForm(UserForm $form): self
    {
        $this->forms->removeElement($form);

        return $this;
    }

    /**
     * @param Collection<UserForm> $forms
     */
    public function removeForms(Collection $forms): self
    {
        foreach ($forms as $form) {
            $this->removeForm($form);
        }

        return $this;
    }

    public function getPositions(): ?array
    {
        return $this->positions;
    }

    public function setPositions(?array $positions): void
    {
        $this->positions = $positions;
    }

    public function getEmployeeStatus(): ?string
    {
        return $this->employeeStatus;
    }

    public function setEmployeeStatus(?string $employeeStatus): void
    {
        $this->employeeStatus = $employeeStatus;
    }

    /**
     * @return Collection<Delegation>
     */
    public function getDelegations(): Collection
    {
        return $this->delegations;
    }

    /**
     * @param Collection<Delegation> $delegations
     */
    public function setDelegations(Collection $delegations): void
    {
        $this->delegations = $delegations;
    }

    public function addDelegation(Delegation $delegation): void
    {
        $this->delegations->add($delegation);
    }

    /**
     * @param Collection<Delegation> $delegations
     */
    public function addDelegations(Collection $delegations): void
    {
        foreach ($delegations as $delegation) {
            $this->addDelegation($delegation);
        }
    }

    public function removeDelegation(Delegation $delegation): void
    {
        $this->delegations->removeElement($delegation);
    }

    /**
     * @param Collection<Delegation> $delegations
     */
    public function removeDelegations(Collection $delegations): void
    {
        foreach ($delegations as $delegation) {
            $this->removeDelegation($delegation);
        }
    }

    public function isLocalAdmin(): bool
    {
        return (int) getenv('ENABLE_LOCAL_AUTH') === 1 ? $this->localAdmin : false;
    }

    public function setLocalAdmin(bool $localAdmin): void
    {
        $this->localAdmin = $localAdmin;
    }

    public function getLocalAdminLocationRestricted(): ?int
    {
        return $this->localAdminLocationRestricted;
    }

    public function setLocalAdminLocationRestricted(?int $value): void
    {
        $this->localAdminLocationRestricted = $value;
    }

    public function getLocalAdminServiceRestricted(): ?int
    {
        return $this->localAdminServiceRestricted;
    }

    public function setLocalAdminServiceRestricted(?int $value): void
    {
        $this->localAdminServiceRestricted = $value;
    }

    public function isCentralAdmin(): bool
    {
        return $this->centralAdmin;
    }

    public function setCentralAdmin(bool $centralAdmin): void
    {
        $this->centralAdmin = $centralAdmin;
    }

    public function isContactMergeAdmin(): bool
    {
        return $this->contactMergeAdmin;
    }

    public function setContactMergeAdmin(bool $contactMergeAdmin): void
    {
        $this->contactMergeAdmin = $contactMergeAdmin;
    }

    public function getAllowSelfDelegation(): bool
    {
        return $this->allowSelfDelegation;
    }

    public function setAllowSelfDelegation(bool $allowSelfDelegation): void
    {
        $this->allowSelfDelegation = $allowSelfDelegation;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(?string $username): void
    {
        $this->username = $username;
    }

    public function isActive(): bool
    {
        return $this->active;
    }

    public function setActive(bool $active): void
    {
        $this->active = $active;
    }

    public function getActiveFrom(): ?DateTime
    {
        return $this->activeFrom;
    }

    public function setActiveFrom(?DateTime $activeFrom): void
    {
        $this->activeFrom = $activeFrom;
    }

    public function getActiveTo(): ?DateTime
    {
        return $this->activeTo;
    }

    public function setActiveTo(?DateTime $activeTo): void
    {
        $this->activeTo = $activeTo;
    }

    public function getLockOutDate(): ?DateTime
    {
        return $this->lockOutDate;
    }

    public function setLockOutDate(?DateTime $lockOutDate): void
    {
        $this->lockOutDate = $lockOutDate;
    }

    public function getLockOutReason(): ?string
    {
        return $this->lockOutReason;
    }

    public function setLockOutReason(?string $lockOutReason): void
    {
        $this->lockOutReason = $lockOutReason;
    }

    public function getIsStaff(): bool
    {
        return $this->isStaff;
    }

    public function setIsStaff(bool $isStaff): void
    {
        $this->isStaff = $isStaff;
    }

    public function getLanguageCode(): string
    {
        $language = $this->getSettings()->getLanguage();

        return $language ? $language->getCode() : Language::DEFAULT_LANGUAGE_CODE;
    }

    public function getLastUnlockedAt(): ?DateTime
    {
        return $this->lastUnlockedAt;
    }

    public function setLastUnlockedAt(?DateTime $lastUnlockedAt): void
    {
        $this->lastUnlockedAt = $lastUnlockedAt;
    }

    public function shouldBeActiveAfterUnlock(?int $daysOfInactivity): bool
    {
        if (!$daysOfInactivity) {
            return true;
        }

        return (new DateTime())->diff($this->getLastUnlockedAt())->days < $daysOfInactivity;
    }

    public function isAdminById(): bool
    {
        return $this->getId() === self::ADMIN_USER_ID;
    }

    public function getLanguage(): string
    {
        return $this->getSettings()->getLanguage();
    }

    public function setLanguage(Language $language): void
    {
        $this->getSettings()->setLanguage($language);
    }
}
