<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20250819144523 extends TenantAwareMigration
{
    private const TRANSLATIONS = [
        'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OVERDUE.DESCRIPTION' => [
            'en_GB' => [
                'OLD' => 'The Overdue Risk email is sent to the Register Owner and Risk Reviewer when the risk review date is overdue by one day. This email only sends once. The intention of this email is to prompt the Register Owner and Risk Reviewer to conduct the review of any risks which are now outstanding.',
                'NEW' => '<p>The Overdue Risk email is sent to the Register Owner and Risk Reviewer {{overdueDays}} days after the overdue date set on a Risk Review record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email only sends once. The intention of this email is to prompt the Register Owner and Risk Reviewer to conduct the review of any risks which are now outstanding.</p>',
            ],
        ],
    ];

    public function getDescription(): string
    {
        return '318295 Amending translation text for Overdue Risk email in Notification Centre.';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];
        foreach (self::TRANSLATIONS as $placeholder => $operation) {
            foreach ($operation as $language => $values) {
                $this->updateTranslation($values['OLD'], $values['NEW'], $language, $placeholder, $tenantId);
            }
        }
    }

    protected function tenantPostDown(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];
        foreach (self::TRANSLATIONS as $placeholder => $operation) {
            foreach ($operation as $language => $values) {
                $this->updateTranslation($values['NEW'], $values['OLD'], $language, $placeholder, $tenantId);
            }
        }
    }

    private function updateTranslation(
        string $from,
        string $to,
        string $languageCode,
        string $placeholder,
        int $tenantId
    ): void {
        $this->connection->executeQuery(
            <<<'SQL'
                UPDATE i18n_translation t
                INNER JOIN i18n_placeholder p ON p.id = t.placeholder_id
                INNER JOIN i18n_language l ON l.id = t.language_id
                SET
                    t.value = :valueTo
                WHERE
                    t.tenant_id = :tenantId
                    AND p.placeholder = :placeholder
                    AND l.code = :languageCode
                    AND t.value = :valueFrom
                SQL,
            [
                'valueFrom' => $from,
                'valueTo' => $to,
                'tenantId' => $tenantId,
                'placeholder' => $placeholder,
                'languageCode' => $languageCode,
            ],
        );
    }
}
