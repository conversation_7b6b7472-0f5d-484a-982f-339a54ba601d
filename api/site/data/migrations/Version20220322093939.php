<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Schema\Schema;
use RuntimeException;

use function sprintf;

final class Version20220322093939 extends TenantAwareMigration
{
    private const TABLE_TRANSLATION = 'i18n_translation';

    private const TABLE_PLACEHOLDER = 'i18n_placeholder';

    private const LANG_CODE = 'ar';

    private const TRANSLATIONS = [
        'NAV.ACL_GROUPS' => 'مجموعات مستوى التحكم بالدخول',
        'NAV.ACL_RULES' => 'قواعد مستوى التحكم بالدخول',
        'NAV.ACTIONS_DASHBOARD' => 'لوحة التحكم بالإجراءات',
        'NAV.ADMINISTRATION' => 'الإدارة',
        'NAV.BACK_TO_DASHBOARD' => 'الرجوع إلى لوحة المعلومات',
        'NAV.CAPTURE.ADMIN' => 'إدارة نظام جمع و إدارة التقارير',
        'NAV.CAPTURE.USER.SETTINGS' => 'إعدادات الإلتقاط',
        'NAV.CHECKLISTS' => 'قائمة التدقيق',
        'NAV.CLAIMS' => 'المطالبات المالية',
        'NAV.CONTACTS' => 'جهات الإتصال',
        'NAV.CONTROLS_AND_RECOMMENDATIONS' => 'التوصيات و التحكم',
        'NAV.DASHBOARD' => 'إدارة التقارير',
        'NAV.FEEDBACK' => 'تجربة المريض',
        'NAV.FORM-FIELDS' => 'الحقول المخصصة لنموذج',
        'NAV.FORMS' => 'النماذج',
        'NAV.INCIDENTS' => 'الأحداث',
        'NAV.INVESTIGATION' => 'التحقيق',
        'NAV.INVESTIGATIONS' => 'التحقيقات',
        'NAV.LOCATIONS' => 'المواقع',
        'NAV.MORTALITY' => 'مراجعة حالات الوفيات',
        'NAV.RISK_REGISTER' => 'سجل المخاطر',
        'NAV.REPORTABLE_INCIDENTS' => 'حدث يمكن الإبلاغ عنه',
        'NAV.ROI_ASSESSMENT' => 'تقييم العائد على الاستثمار',
        'NAV.SERVICES' => 'الخدمات',
    ];

    public function getDescription(): string
    {
        return 'IQ-33299 Update Arabic navigation translations';
    }

    /**
     * @throws Exception if translation table update fails
     */
    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $languageId = $this->getLanguageId(self::LANG_CODE, $tenantId);

        foreach (self::TRANSLATIONS as $placeholder => $translation) {
            $placeholderId = $this->getPlaceholderId($placeholder, $tenantId);
            $this->connection->update(
                self::TABLE_TRANSLATION,
                [
                    'value' => $translation,
                ],
                [
                    'placeholder_id' => $placeholderId,
                    'language_id' => $languageId,
                    'tenant_id' => $tenantId,
                ],
            );
        }
    }

    /**
     * @throws Exception if query to retrieve language id fails
     */
    private function getLanguageId(string $code, int $tenantId): int
    {
        $maybe = $this->connection->fetchOne(
            'SELECT id FROM i18n_language WHERE code = :code AND tenant_id = :tenantId LIMIT 1',
            [
                'code' => $code,
                'tenantId' => $tenantId,
            ],
        );

        $id = $maybe === false ? false : (int) $maybe;

        if ($id === false) {
            throw new RuntimeException(sprintf('Language "%s" not found for tenant %s!', $code, $tenantId));
        }

        return $id;
    }

    /**
     * @throws Exception if fetching the placeholder id fails
     */
    private function getPlaceholderId(string $placeholder, int $tenantId): int
    {
        $placeholderId = $this->connection->fetchOne(
            'SELECT id FROM i18n_placeholder WHERE placeholder = :placeholder AND tenant_id = :tenant LIMIT 1',
            [
                'placeholder' => $placeholder,
                'tenant' => $tenantId,
            ],
        );

        if ($placeholderId === false) {
            return $this->createPlaceholder($placeholder, $tenantId);
        }

        return (int) $placeholderId;
    }

    /**
     * @throws Exception if inserting the placeholder fails
     */
    private function createPlaceholder(string $placeholder, int $tenantId): int
    {
        $this->connection->insert(
            self::TABLE_PLACEHOLDER,
            [
                'tenant_id' => $tenantId,
                'placeholder' => $placeholder,
                'type' => 0,
            ],
        );

        return (int) $this->connection->lastInsertId();
    }
}
