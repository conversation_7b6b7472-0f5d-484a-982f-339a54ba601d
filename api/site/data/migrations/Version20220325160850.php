<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20220325160850 extends TenantAwareMigration
{
    private const MODULE_KEY = 'risk_register_review';
    private const FORM_TYPE_KEY = 'review';
    private const STATE = 'risk_grading';

    public function getDescription(): string
    {
        return 'IQ-31975: Remove risk_grading panel from risk review forms';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $formTypeId = $this->connection->fetchOne(
            'SELECT * FROM form_type ft
            JOIN form_type_module ftm ON ft.id = ftm.type_id
            JOIN module m ON ftm.module_id = m.id
            WHERE m.`key` = :moduleKey
              AND ft.tenant_id = :tenantId
              AND ft.`key` = :formTypeKey',
            [
                'moduleKey' => self::MODULE_KEY,
                'tenantId' => $tenantId,
                'formTypeKey' => self::FORM_TYPE_KEY,
            ]
        );

        $this->connection->delete('form_panel_default', [
            'form_type_id' => $formTypeId,
            'state' => self::STATE
        ]);

        $formIds = $this->connection->fetchFirstColumn(
            'SELECT id FROM form WHERE form_type_id = :formTypeId AND tenant_id = :tenantId',
            [
                'formTypeId' => $formTypeId,
                'tenantId' => $tenantId
            ]
        );

        foreach ($formIds as $formId) {
            $this->connection->delete('form_panel', [
                'form_id' => $formId,
                'state' => self::STATE,
                'tenant_id' => $tenantId
            ]);
        }
    }
}
