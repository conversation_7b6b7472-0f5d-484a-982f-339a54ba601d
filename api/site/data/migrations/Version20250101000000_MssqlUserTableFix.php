<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Migration to handle MSSQL reserved keyword 'user' by creating a view or synonym
 */
final class Version20250101000000_MssqlUserTableFix extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Handle MSSQL reserved keyword user table by creating proper quoted identifiers';
    }

    public function up(Schema $schema): void
    {
        // Only run on MSSQL
        if ($this->connection->getDatabasePlatform()->getName() !== 'mssql') {
            return;
        }

        // Enable quoted identifiers for MSSQL
        $this->addSql('SET QUOTED_IDENTIFIER ON');
        
        // Create a synonym for the user table if it exists in a different schema
        // This allows us to reference [user] table properly
        $this->addSql('
            IF NOT EXISTS (SELECT * FROM sys.synonyms WHERE name = \'user_table\')
            BEGIN
                CREATE SYNONYM user_table FOR [user]
            END
        ');
    }

    public function down(Schema $schema): void
    {
        // Only run on MSSQL
        if ($this->connection->getDatabasePlatform()->getName() !== 'mssql') {
            return;
        }

        // Drop the synonym
        $this->addSql('
            IF EXISTS (SELECT * FROM sys.synonyms WHERE name = \'user_table\')
            BEGIN
                DROP SYNONYM user_table
            END
        ');
    }
}
