<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220323141738 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-31986: adds max results configuration to risk owner field, set to -1 meaning no limit';
    }

    public function up(Schema $schema): void
    {
        $configuration = json_encode([
            'searchSchemaUrl' => 'risks/schema/owner-filter',
            'maxResults' => -1,
        ]);

        $this->addSql(
            <<<SQL
                UPDATE form_field ff
                INNER JOIN form_type_field ftf ON ftf.field_id = ff.id
                INNER JOIN form_type ft ON ft.id = ftf.form_type_id
                SET ff.configuration = :config
                WHERE ft.key = 'risk_dashboard_filter'
                AND ff.fieldKey = 'owner';
                SQL,
            ['config' => $configuration]
        );
    }

    public function down(Schema $schema): void
    {
        $configuration = json_encode([
            'searchSchemaUrl' => 'risks/schema/owner-filter',
        ]);

        $this->addSql(
            <<<SQL
                UPDATE form_field ff
                INNER JOIN form_type_field ftf ON ftf.field_id = ff.id
                INNER JOIN form_type ft ON ft.id = ftf.form_type_id
                SET ff.configuration = :config
                WHERE ft.key = 'risk_dashboard_filter'
                AND ff.fieldKey = 'owner';
                SQL,
            ['config' => $configuration]
        );
    }
}
