<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Platforms\MySQLPlatform;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211108111907 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-30051: Add onDeletes to service and location foreign keys for RIBs';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE15689116C');
        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE1B9B9E08');
        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE1ED5CA9E6');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE15689116C FOREIGN KEY (other_service_id) REFERENCES service (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE1B9B9E08 FOREIGN KEY (other_location_id) REFERENCES location (id) ON DELETE SET NULL');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE1ED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id) ON DELETE SET NULL');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE1B9B9E08');
        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE1ED5CA9E6');
        $this->addSql('ALTER TABLE reportable_incident DROP FOREIGN KEY FK_D4837EE15689116C');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE1B9B9E08 FOREIGN KEY (other_location_id) REFERENCES location (id)');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE1ED5CA9E6 FOREIGN KEY (service_id) REFERENCES service (id)');
        $this->addSql('ALTER TABLE reportable_incident ADD CONSTRAINT FK_D4837EE15689116C FOREIGN KEY (other_service_id) REFERENCES service (id)');
    }
}
