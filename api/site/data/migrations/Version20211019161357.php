<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use <PERSON>\Uuid\Uuid;

final class Version20211019161357 extends TenantAwareMigration
{
    private const PANELS = [
        ['ERM.RISK_REVIEWS.FORM.TABS.RISK_DETAILS', 'risk_details'],
        ['ERM.RISK_REVIEWS.FORM.TABS.SERVICES_AND_LOCATIONS', 'services_and_locations'],
        ['ERM.RISK_REVIEWS.FORM.TABS.ACTIONS', 'actions'],
        ['ERM.RISK_REVIEWS.FORM.TABS.CONTROLS_AND_ASSURANCE', 'controls_and_assurance'],
        ['ERM.RISK_REVIEWS.FORM.TABS.RISK_GRADING', 'risk_grading'],
        ['ERM.RISK_REVIEWS.FORM.TABS.COMPLETE_REVIEW', 'complete_review'],
    ];

    private int $tenantId;

    public function getDescription(): string
    {
        return 'IQ-28707 - Adding Risk Review Form Panels for each risk review form.';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $this->tenantId = (int) $tenant['id'];

        foreach ($this->getForms() as $formId) {
            foreach (self::PANELS as $i => [$placeholder, $state]) {
                $this->connection->insert('form_panel', [
                    'form_id' => $formId,
                    'placeholder_id' => $this->makeNewTranslationSet($placeholder),
                    'state' => $state,
                    'priority' => $i,
                    'is_visible' => 1,
                    'current_version' => 1,
                    'import_id' => null,
                    'tenant_id' => $this->tenantId,
                ]);
            }
        }
    }

    private function makeNewTranslationSet(string $placeholder): int
    {
        $oldPlaceholderId = $this->getPlaceholderId($placeholder);
        $newPlaceholderId = $this->makeNewPlaceholder();

        $this->copyDomains($oldPlaceholderId, $newPlaceholderId);
        $translations = $this->getTranslations($oldPlaceholderId);

        foreach ($translations as $translation) {
            $this->addTranslation(
                $newPlaceholderId,
                (int) $translation['language_id'],
                $translation['value'],
            );
        }

        return $newPlaceholderId;
    }

    private function copyDomains(int $fromId, int $toId): void
    {
        $this->connection->executeStatement('INSERT INTO i18n_placeholder_domain (placeholder_id, domain_id, tenant_id)
            SELECT :new_placeholder_id AS placeholder_id, domain_id, tenant_id
            FROM i18n_placeholder_domain
            WHERE placeholder_id = :placeholder_id',
            [
                'new_placeholder_id' => $toId,
                'placeholder_id' => $fromId,
            ]);
    }

    private function getTranslations(int $placeholderId): array
    {
        $translations = $this->connection->executeQuery('SELECT id, placeholder_id, language_id, tenant_id, value
                FROM i18n_translation
                WHERE placeholder_id = :placeholderId
                AND tenant_id = :tenantId',
            [
                'placeholderId' => $placeholderId,
                'tenantId' => $this->tenantId,
            ])
            ->fetchAllAssociative();

        if (empty($translations)) {
            // No translations were found against the placeholder, maybe there's a pointer.
            $translations = $this->connection->executeQuery('SELECT t.id, t.placeholder_id, t.language_id, t.tenant_id, t.value
                    FROM i18n_translation t
                    INNER JOIN i18n_placeholder p ON t.placeholder_id = p.id
                    INNER JOIN i18n_placeholder p2 ON p.placeholder = p2.pointer
                    WHERE p2.id = :placeholderId
                    AND t.tenant_id = :tenantId
                  ',
                [
                    'placeholderId' => $placeholderId,
                    'tenantId' => $this->tenantId,
                ])
                ->fetchAllAssociative();
        }

        return $translations;
    }

    private function addTranslation(int $placeholderId, int $languageId, string $value): void
    {
        $this->connection->insert('i18n_translation', [
            'placeholder_id' => $placeholderId,
            'language_id' => $languageId,
            'value' => $value,
            'tenant_id' => $this->tenantId,
        ]);
    }

    private function getPlaceholderId(string $placeholder): int
    {
        $placeholderId = $this->connection->fetchOne(
            'SELECT id FROM i18n_placeholder WHERE placeholder = :placeholder AND tenant_id = :tenantId',
            [
                'placeholder' => $placeholder,
                'tenantId' => $this->tenantId,
            ]
        );

        if (!$placeholderId) {
            $placeholderId = $this->makeNewPlaceholder($placeholder);
        }

        return (int) $placeholderId;
    }

    private function makeNewPlaceholder(?string $placeholder = null): int
    {
        if ($placeholder === null) {
            $placeholder = $this->getNewPlaceholder();
        }

        $this->connection->insert('i18n_placeholder', [
            'placeholder' => $placeholder,
            'tenant_id' => $this->tenantId,
            'type' => 1, // Custom
        ]);

        return (int) $this->connection->lastInsertId();
    }

    private function getNewPlaceholder(): string
    {
        do {
            $placeholder = 'PLACEHOLDER.' . Uuid::uuid4();
        } while ($this->placeholderExists($placeholder));

        return $placeholder;
    }

    private function placeholderExists(string $placeholder): bool
    {
        $qb = $this->connection->createQueryBuilder();

        return (bool) $qb->select('id')
            ->from('i18n_placeholder')
            ->where('placeholder = :placeholder')
            ->setParameter('placeholder', $placeholder)
            ->execute()
            ->fetchOne();
    }


    private function getForms(): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->select('f.id')
            ->from('form', 'f')
            ->innerJoin('f', 'form_type', 'ft', 'f.form_type_id = ft.id')
            ->innerJoin('ft', 'form_type_module', 'ftm', 'ftm.type_id = ft.id')
            ->innerJoin('ftm', 'module', 'm', 'm.id = ftm.module_id')
            ->andWhere("ft.key = 'review'")
            ->andWhere("m.key = 'risk_register_review'")
            ->execute()
            ->fetchFirstColumn();
    }
}
