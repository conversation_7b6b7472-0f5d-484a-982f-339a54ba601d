<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Platforms\MySQLPlatform;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220330111052 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-34925: adds key column and rename unit_configuration to fields in investigation_clinical_measurement_type';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE investigation_clinical_measurement_type ADD `key` VARCHAR(255) NOT NULL, CHANGE unit_configuration fields JSON NOT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE investigation_clinical_measurement_type DROP `key`, CHANGE fields unit_configuration JSON NOT NULL');
    }
}
