<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220315151631 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-32044 - Update save button on the form panel admin screen to no longer reference updating the order specifically';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<SQL
            UPDATE i18n_translation t
            INNER JOIN i18n_placeholder p ON p.id = t.placeholder_id
            INNER JOIN i18n_language l ON l.id = t.language_id
            SET t.value = 'Save Changes'
            WHERE p.placeholder = 'FORMS.PANELS.BUTTON.SAVE'
            AND l.code = 'en_gb'
            SQL
        );
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<SQL
            UPDATE i18n_translation t
            INNER JOIN i18n_placeholder p ON p.id = t.placeholder_id
            INNER JOIN i18n_language l ON l.id = t.language_id
            SET t.value = 'Save New Panel Order'
            WHERE p.placeholder = 'FORMS.PANELS.BUTTON.SAVE'
            AND l.code = 'en_gb'
            SQL
        );
    }
}
