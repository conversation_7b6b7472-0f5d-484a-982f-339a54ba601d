<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Exception;

use function date;
use function is_array;
use function json_encode;
use function sprintf;

use const JSON_THROW_ON_ERROR;

/**
 * Adds risk owner field to ERM filter form
 */
class Version20220315170434 extends TenantAwareMigration
{
    public function getDescription(): string
    {
        return 'Adds risk owner field to ERM filter form';
    }

    /**
     * @throws Exception
     */
    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $formTypeId = $this->getFormTypeId('risk_dashboard_filter', $tenantId);
        $formId = $this->getFormId($formTypeId, $tenantId);
        $this->insertFormFields($this->getFieldSpec(), $tenantId, $formTypeId, $formId);
    }

    protected function tenantPostDown(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $formTypeId = $this->getFormTypeId('risk_dashboard_filter', $tenantId);
        $formId = $this->getFormId($formTypeId, $tenantId);
        $this->deleteFormFields($this->getFieldSpec(), $tenantId, $formTypeId, $formId);
    }

    private function getFormTypeId(string $key, int $tenantId): int
    {
        return (int) $this->connection->fetchOne(
            'SELECT id FROM form_type WHERE `key` = :key AND tenant_id = :tenant',
            [
                'key' => $key,
                'tenant' => $tenantId,
            ],
        );
    }

    private function getFormId(int $formTypeId, int $tenantId): int
    {
        return (int) $this->connection->fetchOne(
            'SELECT id FROM form WHERE form_type_id = :formTypeId AND tenant_id = :tenant',
            [
                'formTypeId' => $formTypeId,
                'tenant' => $tenantId,
            ],
        );
    }

    private function insertFormFields(array $fields, int $tenantId, int $formTypeId, int $formId): void
    {
        foreach ($fields as $field) {
            $fieldProperties = $field['field'];

            // Check if placeholders exist in DB (if not create them) and use these ids
            $titlePlaceholderId = $this->getPlaceholderId($fieldProperties['title'], $tenantId);
            $labelPlaceholderId = $this->getPlaceholderId($fieldProperties['label'], $tenantId);

            $fieldProperties['title'] = $titlePlaceholderId;
            $fieldProperties['label'] = $labelPlaceholderId;

            foreach ($fieldProperties as $column => $value) {
                if (is_array($value)) {
                    $fieldProperties[$column] = json_encode($value);
                }
            }

            $fieldProperties['tenant_id'] = $tenantId;
            $fieldProperties['createdAt'] = date('Y-m-d H:i:s');
            $fieldProperties['updatedAt'] = date('Y-m-d H:i:s');

            if (isset($field['datasource'])) {
                $titlePlaceholderId = $this->getPlaceholderId($field['datasource']['label'], $tenantId);
                $dataSourceId = $this->retrieveDatasource($titlePlaceholderId, $field['datasource']['key'], $tenantId, null, json_encode($field['datasource']['meta']));

                $fieldProperties['datasource_id'] = $dataSourceId;
            }

            $this->connection->insert('form_field', $fieldProperties);
            $fieldId = $this->connection->lastInsertId();

            $this->connection->insert('form_type_field', [
                'form_type_id' => $formTypeId,
                'field_id' => $fieldId,
                'created' => date('Y-m-d H:i:s'),
                'updated' => date('Y-m-d H:i:s'),
                'required' => 0,
                'tenant_id' => $tenantId,
            ]);

            // Create domain for the form field
            $domainId = $this->getDomainId('form.field.' . $fieldId, $tenantId);

            // Add form field placeholders (title, label) to the form field domain
            $this->addPlaceholderToDomain($titlePlaceholderId, $domainId, $tenantId);
            $this->addPlaceholderToDomain($labelPlaceholderId, $domainId, $tenantId);

            $formDomainId = $this->getDomainId('form.' . $formId, $tenantId);
            $this->addPlaceholderToDomain($titlePlaceholderId, $formDomainId, $tenantId);
            $this->addPlaceholderToDomain($labelPlaceholderId, $formDomainId, $tenantId);
            $sectionId = $this->getSectionId($formId, $tenantId);
            $fieldOrder = $this->getFieldOrder($sectionId, $tenantId);

            $this->connection->insert('form_section_field', [
                'tenant_id' => $tenantId,
                'section_id' => $sectionId,
                'field_id' => $fieldId,
                'field_order' => $fieldOrder,
                'required_field' => $field['required'],
                'created' => date('Y-m-d H:i:s'),
                'updated' => date('Y-m-d H:i:s'),
            ]);
        }
    }

    private function getPlaceholderId(string $placeholder, int $tenantId): int
    {
        $sql = sprintf(
            "SELECT id FROM i18n_placeholder WHERE placeholder = '%s' AND tenant_id = %d",
            $placeholder,
            $tenantId,
        );
        $placeholderId = $this->connection->fetchOne($sql);

        if (!$placeholderId) {
            $this->connection->insert('i18n_placeholder', [
                'placeholder' => $placeholder,
                'type' => 1,
                'tenant_id' => $tenantId,
            ]);

            $placeholderId = $this->connection->lastInsertId();
        }

        return (int) $placeholderId;
    }

    private function retrieveDatasource(int $dataSourceTitle, string $dataSourceKey, int $tenantId, ?string $connectionKey = null, ?string $meta = null): int
    {
        $sql = "SELECT id FROM form_datasource WHERE `key` = '" . $dataSourceKey . "' AND tenant_id = {$tenantId}";
        $datasourceId = $this->connection->fetchOne($sql);

        if ($connectionKey) {
            $this->connection->update('form_field', [
                'datasource_id' => $datasourceId,
            ], [
                'fieldKey' => $connectionKey,
            ]);
        }

        return (int) $datasourceId;
    }

    private function getDomainId(string $name, int $tenantId): int
    {
        $sql = sprintf(
            "SELECT id FROM i18n_domain WHERE name = '%s' AND tenant_id = %d",
            $name,
            $tenantId,
        );
        $domainId = $this->connection->fetchOne($sql);

        if (!$domainId) {
            $this->connection->insert('i18n_domain', [
                'type' => 1,
                'name' => $name,
                'tenant_id' => $tenantId,
            ]);

            $domainId = $this->connection->lastInsertId();
        }

        return (int) $domainId;
    }

    private function addPlaceholderToDomain(int $placeholderId, int $domainId, int $tenantId): void
    {
        $this->connection->insert('i18n_placeholder_domain', [
            'placeholder_id' => $placeholderId,
            'domain_id' => $domainId,
            'tenant_id' => $tenantId,
        ]);
    }

    private function getFieldOrder(int $sectionId, int $tenantId): int
    {
        $result = $this->connection->createQueryBuilder()
            ->select('field_order')
            ->from('form_section_field')
            ->where('section_id = :sectionId')
            ->andWhere('tenant_id = :tenantId')
            ->setParameters([
                'sectionId' => $sectionId,
                'tenantId' => $tenantId,
            ])
            ->orderBy('field_order', 'DESC')
            ->execute()
            ->fetchOne();

        return $result + 1;
    }

    private function getSectionId(int $formId, int $tenantId): ?int
    {
        $result = $this->connection->createQueryBuilder()
            ->select('id')
            ->from('form_section')
            ->where('form_id = :formId')
            ->andWhere('tenant_id = :tenantId')
            ->setParameters([
                'formId' => $formId,
                'tenantId' => $tenantId,
            ])
            ->orderBy('priority')
            ->execute()
            ->fetchOne();

        return !$result ? null : (int) $result;
    }

    private function getFieldSpec(): array
    {
        return [
            [
                'field' => [
                    'title' => 'ERM.RISK.FILTERS.OWNER',
                    'label' => 'ERM.RISK.FILTERS.OWNER.LABEL',
                    'fieldType' => 3,
                    'controlType' => 'recordSearch',
                    'configuration' => json_encode([
                        'searchSchemaUrl' => 'risks/schema/owner-filter',
                    ], JSON_THROW_ON_ERROR),
                    'mapping' => json_encode([
                        'filter' => [
                            'type' => 'in',
                            'parameters' => [
                                'alias' => 'rrowner',
                                'column' => 'user_id',
                                'property' => 'owner',
                            ],
                        ],
                        'json_path' => '$.owner',
                        'queryBuilder' => [
                            'select' => [
                                'rrowner.user_id' => 'owner',
                            ],
                        ],
                    ], JSON_THROW_ON_ERROR),
                    'validators' => null,
                    'searchable' => 1,
                    'is_filter' => 1,
                    'fieldKey' => 'owner',
                    'createdAt' => date('Y-m-d H:i:s'),
                    'updatedAt' => date('Y-m-d H:i:s'),
                ],
                'required' => 0,
            ],
        ];
    }

    private function deleteFormFields(array $fields, int $tenantId, int $formTypeId, int $formId): void
    {
        foreach ($fields as $field) {
            $fieldProperties = $field['field'];
            $titlePlaceholderId = $this->getPlaceholderId($fieldProperties['title'], $tenantId);
            $labelPlaceholderId = $this->getPlaceholderId($fieldProperties['label'], $tenantId);
            $filedFormData = [
                'fieldKey' => $fieldProperties['fieldKey'],
                'typeKey' => 'risk_dashboard_filter',
                'moduleKey' => 'risk_register_risk',
            ];
            if ($this->getFormFieldId($filedFormData, $tenantId)) {
                $formFieldId = $this->getFormFieldId($filedFormData, $tenantId);
                $this->connection->delete('form_section_field', ['field_id' => $formFieldId]);
                $this->connection->delete('form_type_field', ['field_id' => $formFieldId, 'form_type_id' => $formTypeId]);
                $this->connection->delete('form_field', ['id' => $formFieldId]);
                $this->connection->delete('i18n_translation', ['placeholder_id' => $titlePlaceholderId, 'tenant_id' => $tenantId]);
                $this->connection->delete('i18n_translation', ['placeholder_id' => $labelPlaceholderId, 'tenant_id' => $tenantId]);
                $this->connection->delete('i18n_placeholder_domain', ['placeholder_id' => $titlePlaceholderId]);
                $this->connection->delete('i18n_placeholder_domain', ['placeholder_id' => $labelPlaceholderId]);
                $this->connection->delete('i18n_placeholder', ['id' => $titlePlaceholderId]);
                $this->connection->delete('i18n_placeholder', ['id' => $labelPlaceholderId]);
                $this->connection->delete('i18n_domain', ['name' => 'form.field.' . $formFieldId]);
            }
        }
    }

    private function getFormFieldId(array $form, int $tenantId): ?int
    {
        $result = $this->connection->createQueryBuilder()
            ->select('ff.id')
            ->from('form_field', 'ff')
            ->join('ff', 'form_type_field', 'ftf', 'ftf.field_id = ff.id')
            ->join('ftf', 'form_type', 'ft', 'ft.id = ftf.form_type_id')
            ->join('ft', 'form_type_module', 'ftm', 'ftm.type_id = ft.id')
            ->join('ftm', 'module', 'm', 'm.id = ftm.module_id')
            ->where('ff.fieldKey = :fieldKey')
            ->andWhere('ft.`key` = :typeKey')
            ->andWhere('m.`key` = :moduleKey')
            ->andWhere('ff.tenant_id = :tenantId')
            ->setParameters([
                'fieldKey' => $form['fieldKey'],
                'typeKey' => $form['typeKey'],
                'moduleKey' => $form['moduleKey'],
                'tenantId' => $tenantId,
            ])
            ->execute()
            ->fetchOne();

        if (!$result) {
            return null;
        }

        return (int) $result;
    }
}
