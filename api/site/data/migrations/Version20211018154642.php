<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Ramsey\Uuid\Uuid;

final class Version20211018154642 extends TenantAwareMigration
{
    private int $tenantId;

    public function getDescription(): string
    {
        return 'IQ-28708: Generate custom translation items for all non-default form panels.';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $this->tenantId = (int)$tenant['id'];

        $panels = $this->getPanelTranslations();
        foreach ($panels as $panel) {
            $this->makeNewTranslationSet($panel);
        }
    }

    private function getPanelTranslations(): array
    {
        $qb = $this->connection->createQueryBuilder();
        $result = $qb->select('fp.id', 'fp.placeholder_id', 'language_id', 'value')
            ->from('form_panel', 'fp')
            ->innerJoin('fp', 'i18n_translation', 'it', 'fp.placeholder_id = it.placeholder_id')
            ->where('fp.tenant_id = :tenantId')
            ->setParameter('tenantId', $this->tenantId)
            ->execute()
            ->fetchAllAssociative();
        if (!$result) {
            return [];
        }
        $panels = [];
        foreach ($result as $panel) {
            if (!isset($panels[$panel['id']])) {
                $panels[$panel['id']] = [
                    'id' => (int) $panel['id'],
                    'placeholder_id' => (int) $panel['placeholder_id'],
                    'translations' => [],
                ];
            }
            $panels[$panel['id']]['translations'][(int) $panel['language_id']] = $panel['value'];
        }
        return $panels;
    }

    private function makeNewTranslationSet(array $panel): void
    {
        $placeholderId = $this->makeNewPlaceholder();
        $this->copyDomains($panel['placeholder_id'], $placeholderId);
        foreach ($panel['translations'] as $languageId => $value) {
            $this->addTranslation($placeholderId, $languageId, $value);
        }
        $this->updatePanelTitle($panel['id'], $placeholderId);
    }

    private function makeNewPlaceholder(): int
    {
        $placeholder = $this->getNewPlaceholder();
        $this->connection->insert('i18n_placeholder', [
            'placeholder' => $placeholder,
            'tenant_id' => $this->tenantId,
            'type' => 1, // Custom
        ]);
        return (int)$this->connection->lastInsertId();
    }

    private function getNewPlaceholder(): string
    {
        do {
            $placeholder = 'PLACEHOLDER.' . Uuid::uuid4();
        } while ($this->placeholderExists($placeholder));
        return $placeholder;
    }

    private function placeholderExists(string $placeholder): bool
    {
        $qb = $this->connection->createQueryBuilder();
        $result = $qb->select('id')
            ->from('i18n_placeholder')
            ->where('placeholder = :placeholder')
            ->setParameter('placeholder', $placeholder)
            ->execute()
            ->fetchOne();
        return (bool) $result;
    }

    private function copyDomains(int $fromId, int $toId): void
    {
        $this->connection->executeStatement('INSERT INTO i18n_placeholder_domain (placeholder_id, domain_id, tenant_id)
            SELECT :new_placeholder_id AS placeholder_id, domain_id, tenant_id
            FROM i18n_placeholder_domain
            WHERE placeholder_id = :placeholder_id',
            [
                'new_placeholder_id' => $toId,
                'placeholder_id' => $fromId,
            ]);
    }

    private function addTranslation(int $placeholderId, int $languageId, string $value): void
    {
        $this->connection->insert('i18n_translation', [
            'placeholder_id' => $placeholderId,
            'language_id' => $languageId,
            'value' => $value,
            'tenant_id' => $this->tenantId,
        ]);
    }

    private function updatePanelTitle($panelId, $placeholderId) {
        $this->connection->update('form_panel',
        [
            'placeholder_id' => $placeholderId,
        ],
        [
            'id' => $panelId,
        ]);
    }
}
