<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\Exception\AbortMigration;

final class Version20220324140824 extends TenantAwareMigration
{
    private const MODULE_KEY = 'risk_register_risk';
    private const FORM_TYPE_KEY = 'main';
    private const FORM_FIELD_KEY = 'riskRatings';

    public function getDescription(): string
    {
        return 'IQ-31975: Add riskRating field to all existing forms';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $formIds = $this->getFormIds($tenantId);
        $formFieldId = $this->getFormFieldId($tenantId);

        foreach ($formIds as $formId) {
            if (!$this->hasField($tenantId, (int) $formId, $formFieldId)) {
                $this->addFormField($tenantId, (int) $formId, $formFieldId);
            }
        }
    }

    private function getFormIds(int $tenantId): array
    {
        return $this->connection->fetchFirstColumn(
        'SELECT f.id FROM form f
            JOIN form_type ft ON f.form_type_id = ft.id
            JOIN form_type_module ftm ON ft.id = ftm.type_id
            JOIN module m ON ftm.module_id = m.id
            WHERE m.`key` = :moduleKey
              AND ft.`key` = :formTypeKey
              AND ft.tenant_id = :tenantId',
            [
                'moduleKey' => self::MODULE_KEY,
                'formTypeKey' => self::FORM_TYPE_KEY,
                'tenantId' => $tenantId,
            ]
        );
    }

    private function getFormFieldId(int $tenantId): int
    {
        $formFieldId = $this->connection->fetchOne(
            'SELECT ff.id FROM form_type ft
            JOIN form_type_module ftm ON ft.id = ftm.type_id
            JOIN module m ON ftm.module_id = m.id
            JOIN form_type_field ftf ON ft.id = ftf.form_type_id
            JOIN form_field ff ON ftf.field_id = ff.id
            WHERE m.`key` = :moduleKey
              AND ft.`key` = :formTypeKey
              AND ft.tenant_id = :tenantId
              AND ff.fieldKey = :fieldKey',
            [
                'moduleKey' => self::MODULE_KEY,
                'formTypeKey' => self::FORM_TYPE_KEY,
                'tenantId' => $tenantId,
                'fieldKey' => self::FORM_FIELD_KEY,
            ]
        );
        if ($formFieldId === false) {
            throw new AbortMigration('form_field not found: ' . self::FORM_FIELD_KEY);
        }

        return (int) $formFieldId;
    }

    private function hasField(int $tenantId, int $formId, int $formFieldId): bool
    {
        return (bool) $this->connection->fetchFirstColumn(
            'SELECT fsf.id FROM form_section fs
            JOIN form_section_field fsf ON fs.id = fsf.section_id
            WHERE fs.form_id = :formId
              AND fs.tenant_id = :tenantId
              AND fsf.field_id = :formFieldId',
            [
                'formId' => $formId,
                'tenantId' => $tenantId,
                'formFieldId' => $formFieldId,
            ]
        );
    }

    private function addFormField(int $tenantId, int $formId, int $formFieldId): void
    {
        $lastSection = $this->connection->fetchAssociative(
            'SELECT fs.id, MAX(fsf.field_order) AS max_field_order FROM form_section fs
            JOIN form_section_field fsf ON fs.id = fsf.section_id
            WHERE fs.form_id = :formId
              AND fs.tenant_id = :tenantId
            GROUP BY fs.id
            ORDER BY fs.priority desc',
            [
                'formId' => $formId,
                'tenantId' => $tenantId
            ]
        );

        if ($lastSection) {
            $this->connection->insert('form_section_field', [
                'section_id' => $lastSection['id'],
                'field_id' => $formFieldId,
                'tenant_id' => $tenantId,
                'field_order' => $lastSection['max_field_order'] + 1,
                'required_field' => 1,
                'created' => date('Y-m-d H:i:s'),
                'updated' => date('Y-m-d H:i:s'),
            ]);
        }
    }
}
