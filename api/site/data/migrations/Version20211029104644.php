<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20211029104644 extends TenantAwareMigration
{
    private const LABEL_MAPPINGS = [
        'ERM.RISK.RISK_TYPE.SELECT_TYPE' => 'ERM.RISK.RISK_TYPE.LABEL',
        'ERM.RISK.RISK_SUBTYPE.SELECT_SUBTYPE' => 'ERM.RISK.RISK_SUBTYPE.LABEL',
        'ERM.RISK.RISK_TERTIARY_SUBTYPE.SELECT_TERTIARY_SUBTYPE' => 'ERM.RISK.RISK_TERTIARY_SUBTYPE.LABEL',
    ];

    public function getDescription(): string
    {
        return 'IQ-29694 - Adding Labels to Risk Type Field Configuration';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $field = $this->connection->fetchAssociative(
            "SELECT id, configuration
                   FROM form_field
                   WHERE fieldKey = 'riskTypes' AND controlType = 'cascadingSelect' AND fieldType = 3 AND tenant_id = " . $tenantId
        );

        if (empty($field)) {
            return;
        }

        $config = json_decode($field['configuration'], true);

        foreach ($config['labels'] as $index => $label) {
            $config['labels'][$index]['label'] = self::LABEL_MAPPINGS[$label['default']];
        }

        $config = json_encode($config);

        $this->connection->update('form_field', ['configuration' => $config], ['id' => $field['id']]);
    }
}
