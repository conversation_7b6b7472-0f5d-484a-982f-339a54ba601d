<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20211019141258 extends TenantAwareMigration
{
    const USER_DASHBOARD_FILTER = 'user_dashboard_filter';

    public function getDescription(): string
    {
        return 'IQ-29280 - Filtering users by forename or surname fails due to an incorrect module id';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $qb = $this->connection->createQueryBuilder();
        //Retrieves the id for the user_dashboard_filter from the form_type table 
        $formTypeField = $qb->select('ft.id')
            ->from('form_type', 'ft')
            ->where('ft.key = :userDashboardFilter')
            ->setParameter('userDashboardFilter', self::USER_DASHBOARD_FILTER)
            ->execute()
            ->fetchOne();

        if (empty($formTypeField)) {
            return;
        }
        //Retrieves the id corresponding to user from the module table
        $userModule = $qb->select('m.id')
            ->from('module', 'm')
            ->where('m.key = :userKey')
            ->setParameter('userKey', 'user_instance')
            ->execute()
            ->fetchOne();

        if (empty($userModule)) {
            return;
        }
        //Retrieves the module_id for the user_dashboard_filter from the form_type_module table 
        $fieldType = $qb->select('ftm.module_id')
            ->from('form_type_module', 'ftm')
            ->where('ftm.module_id = :moduleID')
            ->setParameter('moduleID', $userModule)
            ->execute()
            ->fetchOne();

        if ($fieldType !== $userModule) {

            //Updates the form_type_module table with the correct module_id and type_id
            $qb->update('form_type_module')
                ->set('module_id', ':userModuleId')
                ->where('type_id = :formTypeFieldId')
                ->setParameter('userModuleId', $userModule)
                ->setParameter('formTypeFieldId', $formTypeField)
                ->execute();
        }
    }
}
