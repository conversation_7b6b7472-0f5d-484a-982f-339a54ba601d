<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Platforms\MySQLPlatform;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20220215135726 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-32815: Add reviewer_id column to risk_reviews table';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE risk_review ADD reviewer_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE risk_review ADD CONSTRAINT FK_B9888FA270574616 FOREIGN KEY (reviewer_id) REFERENCES user (id)');
        $this->addSql('CREATE INDEX IDX_B9888FA270574616 ON risk_review (reviewer_id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE risk_review DROP FOREIGN KEY FK_B9888FA270574616');
        $this->addSql('DROP INDEX IDX_B9888FA270574616 ON risk_review');
        $this->addSql('ALTER TABLE risk_review DROP reviewer_id');
    }
}
