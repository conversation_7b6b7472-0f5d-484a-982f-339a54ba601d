<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Platforms\MySQLPlatform;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211021092813 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-28707 ERM Form Panels - adding unique constraint to import_id';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('CREATE UNIQUE INDEX UNIQ_21EA77CCB6A263D99033212A ON form_panel (import_id, tenant_id)');
        $this->addSql('CREATE UNIQUE INDEX UNIQ_D8AE632FB6A263D9 ON form_panel_default (import_id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('DROP INDEX UNIQ_21EA77CCB6A263D99033212A ON form_panel');
        $this->addSql('DROP INDEX UNIQ_D8AE632FB6A263D9 ON form_panel_default');
    }
}
