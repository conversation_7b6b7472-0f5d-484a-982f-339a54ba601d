<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20211013140005 extends TenantAwareMigration
{
    private const PANELS = [
        ['ERM.RISK.NAV.RISK_DETAILS', 'risk_details', null],
        ['ERM.RISK.NAV.SERVICES_AND_LOCATIONS', 'service_and_locations', null],
        ['ERM.RISK.NAV.OBJECTIVES', 'objectives', null],
        ['ERM.RISK.NAV.CONTROLS_AND_ASSURANCE', 'controls_and_assurance', null],
        ['ERM.RISK.NAV.MEDICATIONS', 'medications', null],
        ['ERM.RISK.NAV.EQUIPMENT', 'equipment', null],
        ['ERM.RISK.NAV.RISK_MONITORS', 'risk_monitors', null],
        ['ERM.RISK.NAV.CONTACTS', 'contacts', null],
        ['ERM.RISK.NAV.REVIEWS', 'risk_reviews', null],
        ['ERM.RISK.NAV.ESCALATION', 'escalation', null],
        ['ERM.RISK.NAV.ACTIONS', 'actions', null],
        ['ERM.RISK.NAV.MY_ACTIONS', 'actions.my_actions', 'actions'],
        ['ERM.RISK.NAV.ALL_ACTIONS', 'actions.all_actions', 'actions'],
        ['ERM.RISK.NAV.ATTACHMENTS', 'attachments', null],
        ['ERM.RISK.NAV.COMMUNICATION', 'notifications', null],
        ['ERM.RISK.NAV.ACCESS_CONTROL', 'access_control', null],
    ];

    public function getDescription(): string
    {
        return 'IQ-28707 - Adding Risk Form Panels for each risk form.';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $forms = $this->getForms();

        foreach ($forms as $formId) {
            foreach (self::PANELS as $i => [$placeholder, $state, $parent]) {
                if ($parent !== null) {
                    $parent = $this->getParentId($parent, (int) $formId);
                }

                $this->connection->insert('form_panel', [
                    'form_id' => $formId,
                    'placeholder_id' => $this->getPlaceholderId($placeholder),
                    'parent_id' => $parent,
                    'state' => $state,
                    'priority' => $i,
                    'is_visible' => 1,
                    'current_version' => 1,
                    'import_id' => null,
                    'tenant_id' => $tenantId,
                ]);
            }
        }
    }

    private function getPlaceholderId(string $placeholder): int
    {
        $qb = $this->connection->createQueryBuilder();

        return (int) $qb->select('id')
            ->from('i18n_placeholder')
            ->where('placeholder = :placeholder')
            ->setParameter('placeholder', $placeholder)
            ->execute()
            ->fetchOne();
    }

    private function getForms(): array
    {
        $qb = $this->connection->createQueryBuilder();

        return $qb->select('f.id')
            ->from('form', 'f')
            ->innerJoin('f', 'form_type', 'ft', 'f.form_type_id = ft.id')
            ->innerJoin('ft', 'form_type_module', 'ftm', 'ftm.type_id = ft.id')
            ->innerJoin('ftm', 'module', 'm', 'm.id = ftm.module_id')
            ->andWhere("ft.key = 'main'")
            ->andWhere("m.key = 'risk_register_risk'")
            ->execute()
            ->fetchFirstColumn();
    }

    private function getParentId(string $parentState, int $formId): int
    {
        $qb = $this->connection->createQueryBuilder();

        return (int) $qb->select('fp.id')
            ->from('form_panel', 'fp')
            ->andWhere('fp.state = :parentState')
            ->andWhere('fp.form_id = :formId')
            ->setParameter('parentState', $parentState)
            ->setParameter('formId', $formId)
            ->execute()
            ->fetchOne();
    }
}
