<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\Exception\AbortMigration;

final class Version20211019145423 extends TenantAwareMigration
{
    private const PANELS = [
        ['ERM.RISK_REVIEWS.FORM.TABS.RISK_DETAILS', 'risk_details', null],
        ['ERM.RISK_REVIEWS.FORM.TABS.SERVICES_AND_LOCATIONS', 'services_and_locations', null],
        ['ERM.RISK_REVIEWS.FORM.TABS.ACTIONS', 'actions', null],
        ['ERM.RISK_REVIEWS.FORM.TABS.CONTROLS_AND_ASSURANCE', 'controls_and_assurance', null],
        ['ERM.RISK_REVIEWS.FORM.TABS.RISK_GRADING', 'risk_grading', null],
        ['ERM.RISK_REVIEWS.FORM.TABS.COMPLETE_REVIEW', 'complete_review', null],
    ];

    private const NEW_DOMAIN = 'form.panel';

    protected $tenantId;

    protected $domainId;

    public function getDescription(): string
    {
        return 'IQ-28707 - Adding Default Risk Review Form Panels';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $typeId = $this->getTypeId();
        $this->tenantId = (int)$tenant['id'];

        $this->domainId = $this->getDomainId();
        if (!$this->domainId) {
            throw new AbortMigration('Risk Review Form Panels domain not found');
        }

        foreach (self::PANELS as $i => [$placeholder, $state, $parent]) {
            if ($parent !== null) {
                $parent = $this->getParentId($parent);
            }

            $this->connection->insert('form_panel_default', [
                'id' => null,
                'form_type_id' => $typeId,
                'placeholder_id' => $this->getPlaceholderId($placeholder),
                'parent_id' => $parent,
                'state' => $state,
                'priority' => $i,
                'is_visible' => 1,
                'current_version' => 1,
                'import_id' => null,
            ]);
        }
    }

    private function getDomainId(bool $createIfMissing = true): int
    {
        $domainId = $this->connection->fetchOne(
            'SELECT id FROM i18n_domain WHERE tenant_id = :tenant_id AND name = :domain_name',
            [
                'tenant_id' => $this->tenantId,
                'domain_name' => self::NEW_DOMAIN,
            ]);
        if (!$domainId && $createIfMissing) {
            $this->connection->insert('i18n_domain',
                [
                    'tenant_id' => $this->tenantId,
                    'name' => self::NEW_DOMAIN,
                    'type' => 0,
                ]);
            $domainId = $this->connection->lastInsertId();
        }
        return (int)$domainId;
    }

    private function getPlaceholderId(string $placeholder): int
    {
        $placeholderId = $this->connection->fetchOne(
            'SELECT id FROM i18n_placeholder WHERE placeholder = :placeholder AND tenant_id = :tenantId',
            [
                'placeholder' => $placeholder,
                'tenantId' => $this->tenantId,
            ]
        );
        if (!$placeholderId) {
            $this->connection->insert('i18n_placeholder', [
                'placeholder' => $placeholder,
                'type' => 0,
                'tenant_id' => $this->tenantId,
            ]);
            $placeholderId = $this->connection->lastInsertId();
        }
        $placeholderId = (int) $placeholderId;
        $this->addPanelDomain($placeholderId);
        return $placeholderId;
    }

    private function addPanelDomain(int $placeholderId): void
    {
        // Check it doesn't exist first (if for example the translations have been processed already)
        $alreadyLinked = $this->connection->fetchOne(
            'SELECT id FROM i18n_placeholder_domain
                WHERE placeholder_id = :placeholder_id
                  AND domain_id = :domain_id
                  AND tenant_id = :tenant_id',
            [
                'placeholder_id' => $placeholderId,
                'tenant_id' => $this->tenantId,
                'domain_id' => $this->domainId,
            ]);
        if ($alreadyLinked) {
            return;
        }
        $this->connection->insert('i18n_placeholder_domain', [
            'placeholder_id' => $placeholderId,
            'domain_id' => $this->domainId,
            'tenant_id' => $this->tenantId,
        ]);
    }

    private function getTypeId(): int
    {
        $qb = $this->connection->createQueryBuilder();
        return (int)$qb->select('ft.id')
            ->from('form_type', 'ft')
            ->innerJoin('ft', 'form_type_module', 'ftm', 'ftm.type_id = ft.id')
            ->innerJoin('ftm', 'module', 'm', 'm.id = ftm.module_id')
            ->andWhere('m.key = :module')
            ->andWhere('ft.key = :form')
            ->setParameter('module', 'risk_register_review')
            ->setParameter('form', 'review')
            ->execute()
            ->fetchOne();
    }

    private function getParentId(string $parentState): int
    {
        $qb = $this->connection->createQueryBuilder();
        return (int)$qb->select('fpd.id')
            ->from('form_panel_default', 'fpd')
            ->where('fpd.state = :parentState')
            ->setParameter('parentState', $parentState)
            ->execute()
            ->fetchOne();
    }
}
