<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20211012153244 extends TenantAwareMigration
{
    private const PANELS = [
        ['ERM.RISK.NAV.RISK_DETAILS', 'risk_details', null],
        ['ERM.RISK.NAV.SERVICES_AND_LOCATIONS', 'service_and_locations', null],
        ['ERM.RISK.NAV.OBJECTIVES', 'objectives', null],
        ['ERM.RISK.NAV.CONTROLS_AND_ASSURANCE', 'controls_and_assurance', null],
        ['ERM.RISK.NAV.MEDICATIONS', 'medications', null],
        ['ERM.RISK.NAV.EQUIPMENT', 'equipment', null],
        ['ERM.RISK.NAV.RISK_MONITORS', 'risk_monitors', null],
        ['ERM.RISK.NAV.CONTACTS', 'contacts', null],
        ['ERM.RISK.NAV.REVIEWS', 'risk_reviews', null],
        ['ERM.RISK.NAV.ESCALATION', 'escalation', null],
        ['ERM.RISK.NAV.ACTIONS', 'actions', null],
        ['ERM.RISK.NAV.MY_ACTIONS', 'actions.my_actions', 'actions'],
        ['ERM.RISK.NAV.ALL_ACTIONS', 'actions.all_actions', 'actions'],
        ['ERM.RISK.NAV.ATTACHMENTS', 'attachments', null],
        ['ERM.RISK.NAV.COMMUNICATION', 'notifications', null],
        ['ERM.RISK.NAV.ACCESS_CONTROL', 'access_control', null],
    ];

    public function getDescription(): string
    {
        return 'IQ-28707 - Adding Default Risk Form Panels';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $typeId = $this->getTypeId();

        foreach (self::PANELS as $i => [$placeholder, $state, $parent]) {
            if ($parent !== null) {
                $parent = $this->getParentId($parent);
            }

            $this->connection->insert('form_panel_default', [
                'form_type_id' => $typeId,
                'placeholder_id' => $this->getPlaceholderId($placeholder),
                'parent_id' => $parent,
                'state' => $state,
                'priority' => $i,
                'is_visible' => 1,
                'current_version' => 1,
                'import_id' => null
            ]);
        }
    }

    private function getPlaceholderId(string $placeholder): int
    {
        $qb = $this->connection->createQueryBuilder();

        return (int) $qb->select('id')
            ->from('i18n_placeholder')
            ->where('placeholder = :placeholder')
            ->setParameter('placeholder', $placeholder)
            ->execute()
            ->fetchOne();
    }

    private function getTypeId(): int
    {
        $qb = $this->connection->createQueryBuilder();

        return (int) $qb->select('ft.id')
            ->from('form_type', 'ft')
            ->innerJoin('ft', 'form_type_module', 'ftm', 'ftm.type_id = ft.id')
            ->innerJoin('ftm', 'module', 'm', 'm.id = ftm.module_id')
            ->andWhere('m.key = :module')
            ->andWhere('ft.key = :form')
            ->setParameter('module', 'risk_register_risk')
            ->setParameter('form', 'main')
            ->execute()
            ->fetchOne();
    }

    private function getParentId(string $parentState): int
    {
        $qb = $this->connection->createQueryBuilder();

        return (int) $qb->select('fpd.id')
            ->from('form_panel_default', 'fpd')
            ->where('fpd.state = :parentState')
            ->setParameter('parentState', $parentState)
            ->execute()
            ->fetchOne();
    }
}
