<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Platforms\MySQLPlatform;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20211104171749 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'IQ-28713 - Adds trigger_id column to the form_panel table';
    }

    public function up(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE form_panel ADD trigger_id INT DEFAULT NULL');
        $this->addSql('ALTER TABLE form_panel ADD CONSTRAINT FK_21EA77CC5FDDDCD6 FOREIGN KEY (trigger_id) REFERENCES form_trigger (id) ON DELETE SET NULL');
        $this->addSql('CREATE INDEX IDX_21EA77CC5FDDDCD6 ON form_panel (trigger_id)');
    }

    public function down(Schema $schema): void
    {
        $this->abortIf(!$this->connection->getDatabasePlatform() instanceof MySqlPlatform, 'Migration can only be executed safely on \'mysql\'.');

        $this->addSql('ALTER TABLE form_panel DROP FOREIGN KEY FK_21EA77CC5FDDDCD6');
        $this->addSql('DROP INDEX IDX_21EA77CC5FDDDCD6 ON form_panel');
        $this->addSql('ALTER TABLE form_panel DROP trigger_id');
    }
}
