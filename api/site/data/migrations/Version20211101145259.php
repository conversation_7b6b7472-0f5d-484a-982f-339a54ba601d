<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

final class Version20211101145259 extends TenantAwareMigration
{
    public function getDescription(): string
    {
        return 'IQ-29694: Adds disabled and main labels to safety alert source cascading select field';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        $field = $this->connection->fetchAssociative(
            "SELECT id, configuration
                   FROM form_field
                   WHERE fieldKey = 'source' AND controlType = 'cascadingSelect' AND fieldType = 3 AND tenant_id = " . $tenantId
        );

        if (empty($field)) {
            return;
        }

        $config = json_decode($field['configuration'], true);
        $config['labels'] = [
            [
                'default' => 'SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SOURCE.LABEL',
                'disabled' => 'SAFETY_ALERTS.SOURCE.NO_OPTIONS',
                'label' => 'SAFETY_ALERTS.SOURCE.LABEL',
            ],
            [
                'default' => 'SAFETY_ALERTS.DEFAULT_FORM.FIELDS.TYPE.LABEL',
                'disabled' => 'SAFETY_ALERTS.TYPE.NO_OPTIONS',
                'label' => 'SAFETY_ALERTS.TYPE.LABEL',
            ],
        ];

        $config = json_encode($config);

        $this->connection->update('form_field', ['configuration' => $config], ['id' => $field['id']]);
    }
}
