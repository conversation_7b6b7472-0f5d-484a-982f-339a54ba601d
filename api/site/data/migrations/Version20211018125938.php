<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\Exception\AbortMigration;

final class Version20211018125938 extends TenantAwareMigration
{
    private const NEW_DOMAIN = 'form.panel';
    private int $tenantId;

    public function getDescription(): string
    {
        return 'IQ-28708: Add the "form panel" domain to the form panel labels.';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $this->tenantId = (int) $tenant['id'];

        $domainId = $this->getDomainId();

        if (!$domainId) {
            throw new AbortMigration('Form Panels domain not found');
        }

        $placeholders = $this->getPlaceholderIds();
        foreach ($placeholders as $placeholderId) {
            $this->addPlaceholderDomain($placeholderId, $domainId);
        }
    }

    protected function tenantPostDown(Schema $schema, array $tenant): void
    {
        $this->tenantId = (int) $tenant['id'];

        $domainId = $this->getDomainId(false);
        if (!$domainId) {
            // Domain doesn't exist so no work to do.
            return;
        }

        $this->removePlaceholderDomains($domainId);
    }

    private function getDomainId(bool $createIfMissing = true): int
    {
        $domainId = $this->connection->fetchOne(
            'SELECT id FROM i18n_domain WHERE tenant_id = :tenant_id AND name = :domain_name',
            [
                'tenant_id' => $this->tenantId,
                'domain_name' => self::NEW_DOMAIN,
            ]);

        if (!$domainId && $createIfMissing) {
            $this->connection->insert('i18n_domain',
                [
                    'tenant_id' => $this->tenantId,
                    'name' => self::NEW_DOMAIN,
                    'type' => 0,
                ]);
            $domainId = $this->connection->lastInsertId();
        }
        return (int) $domainId;
    }

    private function getPlaceholderIds(): array
    {
        $qb = $this->connection->createQueryBuilder();
        $result = $qb->select('placeholder_id')
            ->from('form_panel', 'fpd')
            ->execute()
            ->fetchFirstColumn();
        return array_map('intval', $result);
    }

    private function addPlaceholderDomain(int $placeholderId, int $domainId): void
    {
        // Check it doesn't exist first (if for example the translations have been processed already)
        $alreadyLinked = $this->connection->fetchOne(
            'SELECT id FROM i18n_placeholder_domain
                WHERE placeholder_id = :placeholder_id
                  AND domain_id = :domain_id
                  AND tenant_id = :tenant_id',
            [
                'placeholder_id' => $placeholderId,
                'tenant_id' => $this->tenantId,
                'domain_id' => $domainId,
            ]);
        if ($alreadyLinked) {
            return;
        }
        $this->connection->insert('i18n_placeholder_domain', [
            'placeholder_id' => $placeholderId,
            'domain_id' => $domainId,
            'tenant_id' => $this->tenantId,
        ]);
    }

    private function removePlaceholderDomains(int $domainId): void
    {
        $this->connection->delete('i18n_placeholder_domain', [
            'domain_id' => $domainId,
            'tenant_id' => $this->tenantId,
        ]);
    }
}
