<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;

use function json_encode;

final class Version20220330112711 extends TenantAwareMigration
{
    public function getDescription(): string
    {
        return 'IQ-34925 Write migration to include key and update json-structure for investigation_clinical_measurement_type';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $measurementTypeFields
        = [
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.BLOOD_PRESSURE' => [
                'key' => 'BLOOD_PRESSURE',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'systolic',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.MILLIMETERS_OF_MERCURY',
                        'unitAbbreviation' => 'mmHg',
                        'step' => 2,
                        'validators' => [
                            'min' => 120,
                            'max' => 180,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SYSTOLIC',
                    ],
                    [
                        'fieldKey' => 'diastolic',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.MILLIMETERS_OF_MERCURY',
                        'unitAbbreviation' => 'mmHg',
                        'step' => 2,
                        'validators' => [
                            'min' => 120,
                            'max' => 180,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.DIASTOLIC',
                    ],
                ]),
            ],
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.HEART_RATE' => [
                'key' => 'HEART_RATE',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'bpm',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BEATS_PER_MINUTE',
                        'unitAbbreviation' => 'BPM',
                        'step' => 5,
                        'validators' => [
                            'min' => 50,
                            'max' => 200,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BEATS_PER_MINUTE',
                    ],
                ]),
            ],
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.RESPIRATORY_RATE' => [
                'key' => 'RESPIRATORY_RATE',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'bpm',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BREATHS_PER_MINUTE',
                        'unitAbbreviation' => 'BPM',
                        'step' => 1,
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BREATHS_PER_MINUTE',
                    ],
                ]),
            ],
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.PAIN_SCORE' => [
                'key' => 'PAIN_SCORE',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'pain_score',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SCORE',
                        'unitAbbreviation' => 'Score',
                        'step' => 1,
                        'validators' => [
                            'min' => 0,
                            'max' => 10,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SCORE',
                    ],
                ]),
            ],
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.TEMPERATURE' => [
                'key' => 'TEMPERATURE',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'temperature',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.DEGREES_CELSIUS',
                        'unitAbbreviation' => 'Deg. C',
                        'step' => 5,
                        'validators' => [
                            'min' => 10,
                            'max' => 60,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.DEGREES_CELSIUS',
                    ],
                ]),
            ],
            'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.OXYGEN_SATURATION' => [
                'key' => 'OXYGEN_SATURATION',
                'fields' => json_encode([
                    [
                        'fieldKey' => 'oxygen_saturation ',
                        'controlType' => 'number',
                        'unitLabel' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.OXYGEN_SATURATION',
                        'unitAbbreviation' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.OXYGEN_SATURATION',
                        'step' => 1,
                        'validators' => [
                            'min' => 0,
                            'max' => 1000,
                        ],
                        'label' => 'INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.OXYGEN_SATURATION',
                    ],
                ]),
            ],
        ];

        foreach ($measurementTypeFields as $currentType => $newType) {
            $this->connection->update(
                'investigation_clinical_measurement_type',
                [
                    '`key`' => $newType['key'],
                    '`fields`' => $newType['fields'],
                ],
                [
                    'name' => $currentType,
                ],
            );
        }
    }
}
