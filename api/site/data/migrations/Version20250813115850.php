<?php

declare(strict_types=1);

namespace Datix\Migration;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250813115850 extends AbstractMigration
{
    public function getDescription(): string
    {
        return "Normalize 'USER.FILTER.FORM.LOCKED.USERS.LABEL' placeholders and delete unreferenced duplicates";
    }

    public function up(Schema $schema): void
    {
        $needle  = 'USER.FILTER.FORM.LOCKED.USERS.LABEL';
        $pattern = '%' . $needle . '%';

        $qb = $this->connection->createQueryBuilder();
        $qb->update('i18n_placeholder', 'p')
            ->set('p.placeholder', ':needle')
            ->where('p.placeholder LIKE :pattern')
            ->andWhere('p.id IN (SELECT label FROM form_field)')
            ->setParameter('needle', $needle)
            ->setParameter('pattern', $pattern)
            ->executeStatement();

        $qb2 = $this->connection->createQueryBuilder();
        $qb2->delete('i18n_placeholder')
            ->where('placeholder LIKE :pattern')
            ->andWhere('id NOT IN (SELECT label FROM form_field)')
            ->setParameter('pattern', $pattern)
            ->executeStatement();
    }
}
