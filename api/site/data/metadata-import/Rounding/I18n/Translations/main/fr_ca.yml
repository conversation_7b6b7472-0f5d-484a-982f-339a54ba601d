entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@rounding_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@rounding_form_fields_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@rounding_form_fields_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@rounding_form_fields_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@rounding_form_fields_category_sub', language: '@language_fr_ca', value: Sous-catégorie } }
  - { fields: { placeholder: '@rounding_form_fields_category_sub_label', language: '@language_fr_ca', value: Sous-catégorie } }
  - { fields: { placeholder: '@rounding_form_fields_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@rounding_form_fields_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@rounding_form_fields_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@rounding_form_fields_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@rounding_form_fields_focus', language: '@language_fr_ca', value: Foyer } }
  - { fields: { placeholder: '@rounding_form_fields_focus_label', language: '@language_fr_ca', value: Foyer } }
  - { fields: { placeholder: '@rounding_form_fields_background_information', language: '@language_fr_ca', value: 'Informations d''arrière-plan' } }
  - { fields: { placeholder: '@rounding_form_fields_background_information_label', language: '@language_fr_ca', value: 'Informations d''arrière-plan' } }
  - { fields: { placeholder: '@rounding_form_fields_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@rounding_form_fields_attachments_label', language: '@language_fr_ca', value: 'Documents de contexte' } }
  - { fields: { placeholder: '@rounding_form_fields_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@rounding_form_fields_summary_label', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@rounding_instance_fields_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@rounding_instance_fields_date_label', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@rounding_instance_fields_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@rounding_instance_fields_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@rounding_form_type_round_form', language: '@language_fr_ca', value: 'Formulaire d''arrondi' } }
  - { fields: { placeholder: '@rounding_form_type_round_summary_form', language: '@language_fr_ca', value: 'Formulaire de sommaire d''arrondi' } }
  - { fields: { placeholder: '@rounding_form_name_rounding', language: '@language_fr_ca', value: 'Formulaire d''arrondissement' } }
  - { fields: { placeholder: '@rounding_form_name_summary', language: '@language_fr_ca', value: 'Formulaire de sommaire' } }
