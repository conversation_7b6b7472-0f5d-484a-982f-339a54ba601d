entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@rounding_form_type_round_form', language: '@language_en_us', value: Round Form } }
    - { fields: { placeholder: '@rounding_form_type_round_summary_form', language: '@language_en_us', value: Round Summary Form } }
    - { fields: { placeholder: '@rounding_form_fields_attachments', language: '@language_en_us', value: Attachments } }
    - { fields: { placeholder: '@rounding_form_fields_attachments_label', language: '@language_en_us', value: Context Documents } }
    - { fields: { placeholder: '@rounding_form_fields_background_information', language: '@language_en_us', value: Background Information } }
    - { fields: { placeholder: '@rounding_form_fields_background_information_label', language: '@language_en_us', value: Background Information } }
    - { fields: { placeholder: '@rounding_form_fields_category', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@rounding_form_fields_category_sub', language: '@language_en_us', value: Sub Category } }
    - { fields: { placeholder: '@rounding_form_fields_category_sub_label', language: '@language_en_us', value: Sub Category } }
    - { fields: { placeholder: '@rounding_form_fields_category_label', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@rounding_form_fields_focus', language: '@language_en_us', value: Focus } }
    - { fields: { placeholder: '@rounding_form_fields_focus_label', language: '@language_en_us', value: Focus } }
    - { fields: { placeholder: '@rounding_form_fields_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@rounding_form_fields_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@rounding_form_fields_summary', language: '@language_en_us', value: Summary } }
    - { fields: { placeholder: '@rounding_form_fields_summary_label', language: '@language_en_us', value: Summary } }
    - { fields: { placeholder: '@rounding_form_fields_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@rounding_form_fields_title_label', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@rounding_form_fields_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@rounding_form_fields_type_label', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@rounding_form_name_rounding', language: '@language_en_us', value: Rounding Form } }
    - { fields: { placeholder: '@rounding_form_name_summary', language: '@language_en_us', value: Summary Form } }
    - { fields: { placeholder: '@rounding_instance_fields_date', language: '@language_en_us', value: Date } }
    - { fields: { placeholder: '@rounding_instance_fields_date_label', language: '@language_en_us', value: Date } }
    - { fields: { placeholder: '@rounding_instance_fields_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@rounding_instance_fields_status_label', language: '@language_en_us', value: Status } }