entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.TITLE
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_title
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.TITLE.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_title_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.CATEGORY
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_category
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.CATEGORY.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_category_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.CATEGORY_SUB
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_category_sub
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.CATEGORY_SUB.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_category_sub_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.TYPE
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_type
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.TYPE.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_type_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.STATUS
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_status
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_status_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.FOCUS
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_focus
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.FOCUS.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_focus_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.BACKGROUND_INFORMATION
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_background_information
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.BACKGROUND_INFORMATION.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_background_information_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.ATTACHMENTS
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_attachments
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.ATTACHMENTS.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_attachments_label
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.SUMMARY
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_summary
  -
    fields:
      placeholder: ROUNDING.FORM.FIELD.SUMMARY.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_form_fields_summary_label
# Rounding Instance
  -
    fields:
      placeholder: ROUNDING.INSTANCE.FIELD.DATE
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_instance_fields_date
  -
    fields:
      placeholder: ROUNDING.INSTANCE.FIELD.DATE.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_instance_fields_date_label
  -
    fields:
      placeholder: ROUNDING.INSTANCE.FIELD.STATUS
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_instance_fields_status
  -
    fields:
      placeholder: ROUNDING.INSTANCE.FIELD.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_roundings'
    ref: rounding_instance_fields_status_label
# Forms
  -
    fields:
      placeholder: ROUNDING.FORMS.FORM1.NAME
      type: 1
      domains:
      - domain: '@domain_roundings'
    ref: rounding_forms_form1_name
  -
    fields:
      placeholder: ROUNDING.FORMS.FORM2.NAME
      type: 1
      domains:
      - domain: '@domain_roundings'
    ref: rounding_forms_form2_name
# Form Types
  -
    fields:
      placeholder: ROUNDING.FORMS.TYPES.MAIN
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_forms_types_main
  -
    fields:
      placeholder: ROUNDING.FORMS.TYPES.SUMMARY
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_forms_types_summary
# Data Sources
  -
    fields:
      placeholder: ROUNDING.DATA_SOURCES.TYPE
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_data_sources_type
  -
    fields:
      placeholder: ROUNDING.DATA_SOURCES.CATEGORY
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_data_sources_category
  -
    fields:
      placeholder: ROUNDING.DATA_SOURCES.SUB_CATEGORY
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_data_sources_sub_category
  -
    fields:
      placeholder: ROUNDING.DATA_SOURCES.ROUNDING_STATUS
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_data_sources_status
  -
    fields:
      placeholder: ROUNDING.DATA_SOURCES.INSTANCE_STATUS
      type: 0
      domains:
      - domain: '@domain_roundings'
    ref: rounding_data_sources_instance_status
  -
    fields:
      placeholder: ROUNDING.FORM_TYPE.ROUND_FORM
      type: 0
      domains:
      - domain: '@domain_roundings'
      - domain: '@domain_form_types'
    ref: rounding_form_type_round_form
  -
    fields:
      placeholder: ROUNDING.FORM_TYPE.ROUND_SUMMARY_FORM
      type: 0
      domains:
      - domain: '@domain_roundings'
      - domain: '@domain_form_types'
    ref: rounding_form_type_round_summary_form
  -
    fields:
      placeholder: ROUNDING.FORM.NAME.ROUNDING
      type: 1
      domains:
      - domain: '@domain_roundings'
    ref: rounding_form_name_rounding
  -
    fields:
      placeholder: ROUNDING.FORM.NAME.SUMMARY
      type: 1
      domains:
      - domain: '@domain_roundings'
    ref: rounding_form_name_summary
