entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: INCIDENTS.INCIDENT.INCIDENT
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_incident
  - fields:
      placeholder: INCIDENTS.INCIDENT.INCIDENTS
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_incidents
  - fields:
      placeholder: INCIDENTS.INCIDENT.SEARCH_INCIDENTS
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_search_incidents
  - fields:
      placeholder: INCIDENTS.INCIDENT.ID
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_id
  - fields:
      placeholder: INCIDENTS.INCIDENT.REFERENCE
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_reference
  - fields:
      placeholder: INCIDENTS.INCIDENT.TITLE
      pointer: COMMON.TITLE
      type: 0
      domains:
        - domain: '@domain_incidents'
  - fields:
      placeholder: INCIDENTS.INCIDENT.INCIDENT_DATE
      type: 0
      domains:
        - domain: '@domain_incidents'
    ref: incidents_incident_incident_date
