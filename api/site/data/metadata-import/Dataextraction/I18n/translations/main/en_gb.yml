entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_dataextraction'
      language: '@language_en_gb'
      value: 'Data Extraction Service'
  - fields:
      placeholder: '@dataextraction_permissions_can_schedule_and_extract'
      language: '@language_en_gb'
      value: 'Can schedule and extract'
  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_success'
      language: '@language_en_gb'
      value: 'Data Extraction Completed'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_failure'
      language: '@language_en_gb'
      value: 'Data Extraction Failed'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_dataextraction_job_name'
      language: '@language_en_gb'
      value: 'Job name'

  # Module Variables
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_dataextraction_job_id'
      language: '@language_en_gb'
      value: 'Job ID'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_dataextraction_link'
      language: '@language_en_gb'
      value: 'Link'
  - fields:
      placeholder: '@placeholder_dataextraction_module_title'
      language: '@language_en_gb'
      value: 'Data Extraction'

  # Notification Centre Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_success_title'
      language: '@language_en_gb'
      value: 'Data Extraction - Job Success Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_success_description'
      language: '@language_en_gb'
      value: '<p>A Data Extraction Completed notification will be sent upon the successful completion of an extraction job and will be sent only to the user that scheduled the job. This notifies the user that the desired data can now be downloaded from the system.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_failure_title'
      language: '@language_en_gb'
      value: 'Data Extraction - Job Failure Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_failure_description'
      language: '@language_en_gb'
      value: '<p>A Data Extraction Failed notification will be sent if an error occurred whilst attempt to execute an extraction job. It will be sent only to the user that scheduled the job.<p>'
