entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - { fields: { placeholder: '@placeholder_notification_centre_module_dataextraction', language: '@language_fr_ca', value: 'Service d''extraction de données' } }
  # Template
  - { fields: { placeholder: '@dataextraction_permissions_can_schedule_and_extract', language: '@language_fr_ca', value: 'Peut planifier et extraire' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_success', language: '@language_fr_ca', value: 'Extraction de données terminée' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_dataextraction_job_failure', language: '@language_fr_ca', value: 'Échec de l''extraction de données' } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_dataextraction_job_name', language: '@language_fr_ca', value: 'Nom de la tâche' } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_dataextraction_job_id', language: '@language_fr_ca', value: 'ID de la tâche' } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_dataextraction_link', language: '@language_fr_ca', value: Lien } }
  - { fields: { placeholder: '@placeholder_dataextraction_module_title', language: '@language_fr_ca', value: 'Extraction de données' } }
