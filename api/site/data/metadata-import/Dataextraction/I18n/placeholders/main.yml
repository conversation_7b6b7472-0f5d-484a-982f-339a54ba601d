entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: DATAEXTRACTION.PERMISSIONS.CAN_SCHEDULE_AND_EXTRACT
      type: 0
      domains:
        - domain: '@domain_users'
        - domain: '@domain_acl_groups'
    ref: dataextraction_permissions_can_schedule_and_extract
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.DATAEXTRACTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_module_dataextraction
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_FAILURE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_failure
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.DATAEXTRACTION.JOB_NAME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_module_variable_dataextraction_job_name
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.DATAEXTRACTION.JOB_ID
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_module_variable_dataextraction_job_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.DATAEXTRACTION.LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_module_variable_dataextraction_link
  - fields:
      placeholder: PLACEHOLDER.DATAEXTRACTION.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_dataextraction_module_title

  # Notification Centre Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_SUCCESS.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_success_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_SUCCESS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_success_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_FAILURE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_failure_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.DATAEXTRACTION.JOB_FAILURE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_type_dataextraction_job_failure_description
