entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@locations_columns_point_of', language: '@language_en_us', value: Point Of } }
    - { fields: { placeholder: '@locations_create', language: '@language_en_us', value: Create Location } }
    - { fields: { placeholder: '@locations_edit', language: '@language_en_us', value: Edit Location } }
    - { fields: { placeholder: '@locations_location_bartholomews_clinic', language: '@language_en_us', value: Bartholomews Clinic } }
    - { fields: { placeholder: '@locations_location_buttercup_ward', language: '@language_en_us', value: Buttercup Ward } }
    - { fields: { placeholder: '@locations_location_great_eastern', language: '@language_en_us', value: Great Eastern } }
    - { fields: { placeholder: '@locations_location_great_eastern_main', language: '@language_en_us', value: Great Eastern Main } }
    - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_one', language: '@language_en_us', value: Theatre 1 } }
    - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_three', language: '@language_en_us', value: Theatre 3 } }
    - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_two', language: '@language_en_us', value: Theatre 2 } }
    - { fields: { placeholder: '@locations_location_magnolia_ward', language: '@language_en_us', value: Magnolia Ward } }
    - { fields: { placeholder: '@locations_location_north', language: '@language_en_us', value: North } }
    - { fields: { placeholder: '@locations_location_root', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@locations_location_saved_successfully', language: '@language_en_us', value: Location saved successfully } }
    - { fields: { placeholder: '@locations_location_south', language: '@language_en_us', value: South } }
    - { fields: { placeholder: '@locations_location_south_witney', language: '@language_en_us', value: South Witney } }
    - { fields: { placeholder: '@locations_location_south_witney_main', language: '@language_en_us', value: South Witney Main } }
    - { fields: { placeholder: '@locations_location_st_stephens', language: '@language_en_us', value: St Stephen's } }
    - { fields: { placeholder: '@locations_location_st_stephens_main', language: '@language_en_us', value: St Stephen's Main } }
    - { fields: { placeholder: '@locations_location_st_stephens_main_theatre_one', language: '@language_en_us', value: Theatre 1 } }
    - { fields: { placeholder: '@locations_location_st_stephens_main_theatre_two', language: '@language_en_us', value: Theatre 2 } }
    - { fields: { placeholder: '@locations_location_tag_one', language: '@language_en_us', value: Location tag one } }
    - { fields: { placeholder: '@locations_location_tag_three', language: '@language_en_us', value: Location tag three } }
    - { fields: { placeholder: '@locations_location_tag_two', language: '@language_en_us', value: Location tag two } }
    - { fields: { placeholder: '@locations_location_treatment_room', language: '@language_en_us', value: Treatment Room } }
    - { fields: { placeholder: '@locations_modal_title_create_location', language: '@language_en_us', value: 'Create sub location of:' } }
    - { fields: { placeholder: '@locations_plural', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@locations_search', language: '@language_en_us', value: Search Locations } }
    - { fields: { placeholder: '@locations_singular', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@locations_valid_location_name_required', language: '@language_en_us', value: A valid location name must be given } }
    - { fields: { placeholder: '@locations_valid_location_name_max_length', language: '@language_en_us', value: "Location name must not exceed {{length}} characters" } }
    - { fields: { placeholder: '@locations_form_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@locations_error_messages_invalid_status_change', language: '@language_en_us', value: 'You cannot enable a location if the parent location is deactivated or disabled.' } }
    - { fields: { placeholder: '@locations_error_messages_invalid_status_change_disabled', language: '@language_en_us', value: 'You cannot enable or deactivate a location if the parent location is disabled.' } }
    - { fields: { placeholder: '@locations_delete_warning_child_nodes', language: '@language_en_us', value: 'Any child locations/services will also be deleted.' } }
    - { fields: { placeholder: '@locations_delete_warning_linked_data', language: '@language_en_us', value: 'If records are linked to these locations/services, this action will result in data loss.' } }
    - { fields: { placeholder: '@locations_delete_warning_confirm_text', language: '@language_en_us', value: 'This cannot be undone - are you sure?' } }
    - { fields: { placeholder: '@locations_errors_delete_has_children', language: '@language_en_us', value: 'This location cannot be deleted as it has child locations.' } }
