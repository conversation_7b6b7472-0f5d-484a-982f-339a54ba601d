entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@locations_singular', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@locations_plural', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@locations_search', language: '@language_fr_ca', value: 'Rechercher des emplacements' } }
  - { fields: { placeholder: '@locations_create', language: '@language_fr_ca', value: 'Créer un emplacement' } }
  - { fields: { placeholder: '@locations_edit', language: '@language_fr_ca', value: 'Modifier un emplacement' } }
  - { fields: { placeholder: '@locations_columns_point_of', language: '@language_fr_ca', value: 'Point de' } }
  - { fields: { placeholder: '@locations_location_root', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@locations_location_north', language: '@language_fr_ca', value: Nord } }
  - { fields: { placeholder: '@locations_location_st_stephens', language: '@language_fr_ca', value: St-Stéphane } }
  - { fields: { placeholder: '@locations_location_st_stephens_main', language: '@language_fr_ca', value: 'St-Stéphane principale' } }
  - { fields: { placeholder: '@locations_location_st_stephens_main_theatre_one', language: '@language_fr_ca', value: 'Salle 1' } }
  - { fields: { placeholder: '@locations_location_st_stephens_main_theatre_two', language: '@language_fr_ca', value: 'Salle 2' } }
  - { fields: { placeholder: '@locations_location_bartholomews_clinic', language: '@language_fr_ca', value: 'Bartholomews Clinic' } }
  - { fields: { placeholder: '@locations_location_treatment_room', language: '@language_fr_ca', value: 'Salle d''urgence' } }
  - { fields: { placeholder: '@locations_location_south_witney', language: '@language_fr_ca', value: 'Witney Sud' } }
  - { fields: { placeholder: '@locations_location_south_witney_main', language: '@language_fr_ca', value: 'Witney Sud principale' } }
  - { fields: { placeholder: '@locations_location_buttercup_ward', language: '@language_fr_ca', value: 'Buttercup Ward' } }
  - { fields: { placeholder: '@locations_location_magnolia_ward', language: '@language_fr_ca', value: 'Magnolia Ward' } }
  - { fields: { placeholder: '@locations_location_south', language: '@language_fr_ca', value: Sud } }
  - { fields: { placeholder: '@locations_location_great_eastern', language: '@language_fr_ca', value: Grand-Est } }
  - { fields: { placeholder: '@locations_location_great_eastern_main', language: '@language_fr_ca', value: 'Grand-Est principale' } }
  - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_one', language: '@language_fr_ca', value: 'Salle 1' } }
  - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_two', language: '@language_fr_ca', value: 'Salle 2' } }
  - { fields: { placeholder: '@locations_location_great_eastern_main_theatre_three', language: '@language_fr_ca', value: 'Salle 3' } }
  - { fields: { placeholder: '@locations_location_tag_one', language: '@language_fr_ca', value: 'Emplacement balise une' } }
  - { fields: { placeholder: '@locations_location_tag_two', language: '@language_fr_ca', value: 'Emplacement balise deux' } }
  - { fields: { placeholder: '@locations_location_tag_three', language: '@language_fr_ca', value: 'Emplacement balise trois' } }
  - { fields: { placeholder: '@locations_location_saved_successfully', language: '@language_fr_ca', value: 'Emplacement enregistré avec succès' } }
  - { fields: { placeholder: '@locations_valid_location_name_required', language: '@language_fr_ca', value: 'Un nom d''emplacement valide est nécessaire' } }
  - { fields: { placeholder: '@locations_modal_title_create_location', language: '@language_fr_ca', value: "Créer un sous-emplacement de\_:" } }
  - { fields: { placeholder: '@locations_valid_location_name_max_length', language: '@language_fr_ca', value: 'Le nom d''emplacement ne peut dépasser {{length}} caractères' } }
  - { fields: { placeholder: '@locations_form_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@locations_error_messages_invalid_status_change', language: '@language_fr_ca', value: 'Vous ne pouvez pas activer un emplacement si l''emplacement parent est désactivé.' } }
  - { fields: { placeholder: '@locations_error_messages_invalid_status_change_disabled', language: '@language_fr_ca', value: 'Vous ne pouvez pas activer ou désactiver un emplacement si l''emplacement parent est désactivé.' } }
  - { fields: { placeholder: '@location_drafts_location_draft_root', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@locations_plural_draft', language: '@language_fr_ca', value: 'Emplacements d''ébauche' } }
  - { fields: { placeholder: '@locations_success_messages_published', language: '@language_fr_ca', value: 'Emplacements d''ébauche publiés avec succès' } }
  - { fields: { placeholder: '@locations_success_messages_discarded', language: '@language_fr_ca', value: 'Emplacements d''ébauche supprimés' } }
  - { fields: { placeholder: '@locations_success_messages_bjp_triggered', language: '@language_fr_ca', value: 'Mise à jour terminée' } }
  - { fields: { placeholder: '@locations_error_messages_bjp_in_progress', language: '@language_fr_ca', value: 'Mise à jour en cours' } }
  - { fields: { placeholder: '@locations_error_messages_bjp_triggered_fail', language: '@language_fr_ca', value: 'Mise à jour a échoué' } }
  - { fields: { placeholder: '@locations_success_messages_location_created', language: '@language_fr_ca', value: 'Emplacement créé avec succès' } }
  - { fields: { placeholder: '@locations_error_messages_location_creation_fail', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la création de l''emplacement' } }
  - { fields: { placeholder: '@locations_success_messages_location_deleted', language: '@language_fr_ca', value: 'Emplacement supprimé avec succès' } }
  - { fields: { placeholder: '@locations_error_messages_location_delete_fail', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression de cet emplacement' } }