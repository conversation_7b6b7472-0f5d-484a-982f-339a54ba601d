entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: LOCATIONS.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: locations_singular
  -
    fields:
      placeholder: LOCATIONS.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_locations'
        -
          domain: '@domain_users'
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_roundings'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
        -
          domain: '@domain_safety_learnings'
        -
          domain: '@domain_safety_alerts'
    ref: locations_plural
  -
    fields:
      placeholder: LOCATIONS.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_locations'
        -
          domain: '@domain_users'
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_roundings'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
        -
          domain: '@domain_safety_learnings'
        -
          domain: '@domain_safety_alerts'
    ref: locations_search
  -
    fields:
      placeholder: LOCATIONS.CREATE
      type: 0
      domains:
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_roundings'
        -
          domain: '@domain_users'
    ref: locations_create
  -
    fields:
      placeholder: LOCATIONS.EDIT
      type: 0
      domains:
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_roundings'
        -
          domain: '@domain_users'
    ref: locations_edit
  -
    fields:
      placeholder: LOCATIONS.COLUMNS.POINT_OF
      type: 0
      domains:
        -
          domain: '@domain_locations'
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_roundings'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
    ref: locations_columns_point_of
  -
    fields:
      placeholder: LOCATIONS.LOCATION_ROOT
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_root
  -
    fields:
      placeholder: LOCATIONS.LOCATION_NORTH
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_north
  -
    fields:
      placeholder: LOCATIONS.LOCATION_ST_STEPHENS
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_st_stephens
  -
    fields:
      placeholder: LOCATIONS.LOCATION_ST_STEPHENS_MAIN
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_st_stephens_main
  -
    fields:
      placeholder: LOCATIONS.LOCATION_ST_STEPHENS_MAIN.LOCATION_THEATRE_ONE
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_st_stephens_main_theatre_one
  -
    fields:
      placeholder: LOCATIONS.LOCATION_ST_STEPHENS_MAIN.LOCATION_THEATRE_TWO
      type: 0
      domains:
        -
          domain: '@domain_location_list'
    ref: locations_location_st_stephens_main_theatre_two
  -
    fields:
      placeholder: LOCATIONS.LOCATION_BARTHOLOMEWS_CLINIC
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_bartholomews_clinic
  -
    fields:
      placeholder: LOCATIONS.LOCATION_TREATMENT_ROOM
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_treatment_room
  -
    fields:
      placeholder: LOCATIONS.LOCATION_SOUTH_WITNEY
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_south_witney
  -
    fields:
      placeholder: LOCATIONS.LOCATION_SOUTH_WITNEY_MAIN
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_south_witney_main
  -
    fields:
      placeholder: LOCATIONS.LOCATION_BUTTERCUP_WARD
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_buttercup_ward
  -
    fields:
      placeholder: LOCATIONS.LOCATION_MAGNOLIA_WARD
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_magnolia_ward
  -
    fields:
      placeholder: LOCATIONS.LOCATION_SOUTH
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_south
  -
    fields:
      placeholder: LOCATIONS.LOCATION_GREAT_EASTERN
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_great_eastern
  -
    fields:
      placeholder: LOCATIONS.LOCATION_GREAT_EASTERN_MAIN
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_great_eastern_main
  -
    fields:
      placeholder: LOCATIONS.LOCATION_GREAT_EASTERN_MAIN.LOCATION_THEATRE_ONE
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_great_eastern_main_theatre_one
  -
    fields:
      placeholder: LOCATIONS.LOCATION_GREAT_EASTERN_MAIN.LOCATION_THEATRE_TWO
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_great_eastern_main_theatre_two
  -
    fields:
      placeholder: LOCATIONS.LOCATION_GREAT_EASTERN_MAIN.LOCATION_THEATRE_THREE
      type: 0
      domains:
      -
        domain: '@domain_location_list'
    ref: locations_location_great_eastern_main_theatre_three
  -
    fields:
      placeholder: LOCATIONS.LOCATION_TAG_ONE
      type: 0
      domains:
      -
        domain: '@domain_location_tag'
    ref: locations_location_tag_one
  -
    fields:
      placeholder: LOCATIONS.LOCATION_TAG_TWO
      type: 0
      domains:
      -
        domain: '@domain_location_tag'
    ref: locations_location_tag_two
  -
    fields:
      placeholder: LOCATIONS.LOCATION_TAG_THREE
      type: 0
      domains:
      -
        domain: '@domain_location_tag'
    ref: locations_location_tag_three
  -
    fields:
      placeholder: LOCATIONS.LOCATION_SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_location_saved_successfully
  -
    fields:
      placeholder: LOCATIONS.DELETE.WARNING_CHILD_NODES
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_delete_warning_child_nodes

  -
    fields:
      placeholder: LOCATIONS.DELETE.WARNING_LINKED_DATA
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_delete_warning_linked_data

  -
    fields:
      placeholder: LOCATIONS.DELETE.WARNING_CONFIRM_TEXT
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_delete_warning_confirm_text
  -
    fields:
      placeholder: LOCATIONS.LOCATION_NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_location_not_found
  -
    fields:
      placeholder: LOCATIONS.ERRORS.DELETE_HAS_CHILDREN
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_errors_delete_has_children
  -
    fields:
      placeholder: LOCATIONS.VALID_LOCATION_NAME_REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_valid_location_name_required
  -
    fields:
      placeholder: LOCATIONS.MODAL_TITLE.CREATE_LOCATION
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_modal_title_create_location
  -
    fields:
      placeholder: LOCATIONS.VALID.LOCATION_NAME.MAX_LENGTH
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_valid_location_name_max_length
  -
    fields:
      placeholder: LOCATIONS.FORM.STATUS.LABEL
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_form_status_label
  -
    fields:
      placeholder: LOCATIONS.FORM.STATUS.DATASOURCE.VALUES.ENABLED
      pointer: COMMON.COMPONENTS.TREE.STATUS.ENABLED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_form_status_datasource_values_enabled
  -
    fields:
      placeholder: LOCATIONS.FORM.STATUS.DATASOURCE.VALUES.DEACTIVATED
      pointer: COMMON.COMPONENTS.TREE.STATUS.DEACTIVATED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_form_status_datasource_values_deactivated
  -
    fields:
      placeholder: LOCATIONS.FORM.STATUS.DATASOURCE.VALUES.DISABLED
      pointer: COMMON.COMPONENTS.TREE.STATUS.DISABLED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_form_status_datasource_values_disabled
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.INVALID_STATUS_CHANGE
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_invalid_status_change
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.INVALID_STATUS_CHANGE_DISABLED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_invalid_status_change_disabled
  -
    fields:
      placeholder: LOCATIONS.PLURAL.DRAFT
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_plural_draft
  -
    fields:
      placeholder: LOCATIONS.SUCCESS_MESSAGES.PUBLISHED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_success_messages_published
  -
    fields:
      placeholder: LOCATIONS.SUCCESS_MESSAGES.DISCARDED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_success_messages_discarded
  - fields:
      placeholder: LOCATION_DRAFTS.LOCATION_DRAFT_ROOT
      type: 0
      domains:
        - domain: '@domain_draft_location_list'
    ref: location_drafts_location_draft_root
  -
    fields:
      placeholder: LOCATIONS.SUCCESS_MESSAGES.BJP_TRIGGERED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_success_messages_bjp_triggered
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.BJP_IN_PROGRESS
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_bjp_in_progress
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.BJP_TRIGGERED_FAIL
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_bjp_triggered_fail
  -
    fields:
      placeholder: LOCATIONS.SUCCESS_MESSAGES.LOCATION_CREATED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_success_messages_location_created
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.LOCATION_CREATION_FAIL
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_location_creation_fail
  -
    fields:
      placeholder: LOCATIONS.SUCCESS_MESSAGES.LOCATION_DELETED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_success_messages_location_deleted
  -
    fields:
      placeholder: LOCATIONS.ERROR_MESSAGES.LOCATION_DELETE_FAIL
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_error_messages_location_delete_fail
  -
    fields:
      placeholder: LOCATIONS.BJP_STATUS.IN_PROGRESS
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_bjp_status_in_progress
  -
    fields:
      placeholder: LOCATIONS.BJP_STATUS.FAILED
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_bjp_status_failed
  -
    fields:
      placeholder: LOCATIONS.BJP_STATUS.LAST_COMPLETION
      type: 0
      domains:
        -
          domain: '@domain_locations'
    ref: locations_bjp_status_last_completion
