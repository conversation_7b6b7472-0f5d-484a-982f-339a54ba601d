entityClass: I18n\Entity\Placeholder
priority: 10
data:
- fields:
    placeholder: NOTIFICATIONS.FIELDS.ID
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_id
- fields:
    placeholder: NOTIFICATIONS.FIELDS.AUTHOR
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_author
- fields:
    placeholder: NOTIFICATIONS.FIELDS.RECIPIENTS
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_recipients
- fields:
    placeholder: NOTIFICATIONS.FIELDS.SUBJECT
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_subject
- fields:
    placeholder: NOTIFICATIONS.FIELDS.BODY
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_body
- fields:
    placeholder: NOTIFICATIONS.FIELDS.CREATED_AT
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_fields_created_at
- fields:
    placeholder: NOTIFICATIONS.SAVED_SUCCESSFULLY
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_saved_successfully
- fields:
    placeholder: NOTIFICATIONS.SEND
    type: 0
    domains:
    - domain: '@domain_notifications'
  ref: notifications_send
