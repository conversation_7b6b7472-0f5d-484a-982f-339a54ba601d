entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notifications_fields_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@notifications_fields_author', language: '@language_fr_ca', value: Expéditeur } }
  - { fields: { placeholder: '@notifications_fields_recipients', language: '@language_fr_ca', value: <PERSON><PERSON><PERSON><PERSON> } }
  - { fields: { placeholder: '@notifications_fields_subject', language: '@language_fr_ca', value: Sujet } }
  - { fields: { placeholder: '@notifications_fields_body', language: '@language_fr_ca', value: Corps } }
  - { fields: { placeholder: '@notifications_fields_created_at', language: '@language_fr_ca', value: Envoyé } }
  - { fields: { placeholder: '@notifications_saved_successfully', language: '@language_fr_ca', value: 'Message envoyé avec succès' } }
  - { fields: { placeholder: '@notifications_send', language: '@language_fr_ca', value: <PERSON>voyer } }
