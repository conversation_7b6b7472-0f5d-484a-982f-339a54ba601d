entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_claims'
      language: '@language_en_gb'
      value: Claims

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_notification'
      language: '@language_en_gb'
      value: Notifications
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_handler'
      language: '@language_en_gb'
      value: New Handler
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_investigator'
      language: '@language_en_gb'
      value: New Investigator
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_update'
      language: '@language_en_gb'
      value: Updated
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_communication_and_feedback'
      language: '@language_en_gb'
      value: Communication and Feedback
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_acknowledgement'
      language: '@language_en_gb'
      value: Acknowledgement

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_acknowledgement_title'
      language: '@language_en_gb'
      value: Claims - Acknowledgement Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_acknowledgement_description'
      language: '@language_en_gb'
      value: <p>A claim acknowledgement email is sent out to the reporter of a claim once they have submitted it. It is intended to be used to thank them for submitting the claim and to inform them of what they should expect in terms of future engagement or follow-up from the legal team.</p><p>Studies have shown that acknowledging submission in this manner increase voluntary reporting rates by 35%</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_communication_and_feedback_title'
      language: '@language_en_gb'
      value: Claims - Communication And Feedback Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_communication_and_feedback_description'
      language: '@language_en_gb'
      value: <p>Communication and Feedback emails are sent from a claims record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section - the user will be able to edit this in the record to suit their purpose.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_handler_title'
      language: '@language_en_gb'
      value: Claims - New Handler Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_handler_description'
      language: '@language_en_gb'
      value: <p>A New Handler email is sent to a user who has just been assigned as the handler of a claims record. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to process it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new handler.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_investigator_title'
      language: '@language_en_gb'
      value: Claims - New Investigator Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_new_investigator_description'
      language: '@language_en_gb'
      value: <p>A New Investigator email is sent to a user who has just been assigned as the investigator of a claims record. It is intended to ensure that this person is aware of their new role in relation to this record to prevent delays in them starting their investigation. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new investigator.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_notification_title'
      language: '@language_en_gb'
      value: Claims - Notification Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_notification_description'
      language: '@language_en_gb'
      value: <p>A Notification email is sent to inform one or more users about a newly created claims record. The recipients will be calculated based on their membership of security groups with criteria that match the new claim. This type of email could be used to make claims managers aware of claims being recorded in their location in the organisation, to alert senior leaders to high-value claims, or to flag records to the right representative for a particular specialty or service.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_update_title'
      language: '@language_en_gb'
      value: Claims - Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_claims_update_description'
      language: '@language_en_gb'
      value: <p>A claims Update email is sent when a change has been made to a record that would have triggered a notification email to users, had those changes been present at the point the claim was created. This can be helpful when records that are logged with incorrect data but subsequently corrected so that the correct people are still notified. To prevent large numbers of identical emails from being sent, users will only receive an update email if they would not have received one the previous time the record was saved. Note that therefore this will therefore not notify users of all updates to the record.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_claims_module'
      language: '@language_en_gb'
      value: Module
