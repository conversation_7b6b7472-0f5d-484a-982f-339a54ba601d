entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_notification_centre_module_claims', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_notification', language: '@language_fr_ca', value: Notifications } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_new_handler', language: '@language_fr_ca', value: 'Nouveau gestionnaire' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_new_investigator', language: '@language_fr_ca', value: 'Nouvel enquêteur' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_update', language: '@language_fr_ca', value: 'Mis à jour' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_communication_and_feedback', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_acknowledgement', language: '@language_fr_ca', value: 'Accusé de réception' } }
