entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@notification_centre_notification_centre_module_incidents'
      language: '@language_en_gb'
      value: Incidents

  # Template Types
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_acknowledgement'
      language: '@language_en_gb'
      value: Acknowledgement
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_new_handler'
      language: '@language_en_gb'
      value: New Handler
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_new_investigator'
      language: '@language_en_gb'
      value: New Investigator
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_overdue'
      language: '@language_en_gb'
      value: Overdue
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_reporter'
      language: '@language_en_gb'
      value: Reporter
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_update'
      language: '@language_en_gb'
      value: Update
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_notification'
      language: '@language_en_gb'
      value: Notification
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_reporter_progress'
      language: '@language_en_gb'
      value: Reporter progress notification
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_communication_and_feedback'
      language: '@language_en_gb'
      value: Communication and Feedback

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_acknowledgement_title'
      language: '@language_en_gb'
      value: Incidents - Acknowledgement Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_acknowledgement_description'
      language: '@language_en_gb'
      value: <p>An incident acknowledgement email is sent to the reporter of an incident after they have submitted it. It is intended to be used to thank the reporter for submitting the incident report and to inform them of what they should expect in terms of future engagement or follow-up from the safety team. In doing so, it keeps them engaged with the feedback loop and hopes to make them more likely to contribute to any further dialogue and more likely to report future incidents.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_new_handler_title'
      language: '@language_en_gb'
      value: Incidents - New Handler Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_new_handler_description'
      language: '@language_en_gb'
      value: <p>A New Handler email is sent to a user who has just been assigned as the handler of an incident. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to process it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new handler.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_new_investigator_title'
      language: '@language_en_gb'
      value: Incidents - New Investigator Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_new_investigator_description'
      language: '@language_en_gb'
      value: <p>A New Investigator email is sent to a user who has just been assigned as the investigator of an incident. It is intended to ensure that this person is aware of their new role in relation to this record to prevent delays in them starting their investigation. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new investigator.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_notification_title'
      language: '@language_en_gb'
      value: Incidents - Notification Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_notification_description'
      language: '@language_en_gb'
      value: <p>A notification email is sent to inform one or more users about a newly created incident. The recipients will be calculated based on their membership of security groups with criteria that match the new incident. This type of email could be used to make safety managers aware of incidents being recorded in their location in the organisation, to alert senior leaders to high-severity incidents or never events, or to flag incidents to the right representative for a particular specialty or service.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_update_title'
      language: '@language_en_gb'
      value: Incidents - Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_update_description'
      language: '@language_en_gb'
      value: <p>An incident update email is sent when a change has been made to a record that would have triggered a notification email to users, had those changes been present at the point the incident was created. This can be helpful when records that are logged with incorrect data but subsequently corrected so that the correct people are still notified. To prevent large numbers of identical emails from being sent, users will only receive an update email if they would not have received one the previous time the record was saved.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_reporter_progress_title'
      language: '@language_en_gb'
      value: Incidents - Reporter Progress Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_reporter_progress_description'
      language: '@language_en_gb'
      value: <p>The reporter progress notification is an email sent to the reporter of an incident when the incident approval status is changed to "Finally Approved". The email is intended to give some information on the resolution and to thank the reporter again for submitting the report. This "closes the loop", ensuring that the user feels that their input has made an impact and encouraging them to continue to submit incidents in future.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_communication_and_feedback_title'
      language: '@language_en_gb'
      value: Incidents - Communication And Feedback Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_communication_and_feedback_description'
      language: '@language_en_gb'
      value: <p>Communication and feedback emails are sent from an incident record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_overdue_title'
      language: '@language_en_gb'
      value: Incidents - Overdue Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_incidents_overdue_description'
      language: '@language_en_gb'
      value: <p>An overdue email is sent to notify people involved in the management of an incident that the incident has been in one status for too long, or that too much time has passed since it has been created without it being marked as closed.</p>

  # Template Page Links
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_overdue_config_link'
      language: '@language_en_gb'
      value: <p>These emails will be sent to people who are marked with particular roles on the record and will be sent only for configured statuses. These emails are sent nightly until the incident is no longer overdue.</p><p><a href="{{configUrl}}">Click here to see the currently configured combinations of roles and statuses that will trigger overdue incident emails.</a> </p>
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_incidents_overdue_docs_link'
      language: '@language_en_gb'
      value: <p><a href="{{docsUrl}}">Click here to learn how an incident becomes overdue and how you can configure incident timescales.</a></p>

  # Communication & Feedback
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_incidents_cnf_fullname'
      language: '@language_en_gb'
      value:   Full Name
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_incidents_cnf_module'
      language: '@language_en_gb'
      value: Module

  # Module variables
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_incidents_max_physical'
      language: '@language_en_gb'
      value: Maximum level of physical harm incurred by persons affected in event
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_incidents_max_psychological'
      language: '@language_en_gb'
      value: Maximum level of psychological harm incurred by persons affected in event
