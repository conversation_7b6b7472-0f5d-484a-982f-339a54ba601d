entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notification_centre_notification_centre_module_incidents', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_acknowledgement', language: '@language_fr_ca', value: 'Accusé de réception' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_new_handler', language: '@language_fr_ca', value: 'Nouveau gestionnaire' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_new_investigator', language: '@language_fr_ca', value: 'Nouvel enquêteur' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_overdue', language: '@language_fr_ca', value: Échu } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_reporter', language: '@language_fr_ca', value: Rapporteur } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_update', language: '@language_fr_ca', value: 'Mettre à jour' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_notification', language: '@language_fr_ca', value: Avis } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_reporter_progress', language: '@language_fr_ca', value: 'Avis de progrès au signaleur' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_communication_and_feedback', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
