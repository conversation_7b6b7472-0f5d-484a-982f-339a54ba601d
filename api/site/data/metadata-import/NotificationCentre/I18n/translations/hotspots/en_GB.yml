entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_hotspots'
      language: '@language_en_gb'
      value: Hotspots

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_resolve'
      language: '@language_en_gb'
      value: Resolved Hotspot
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_active'
      language: '@language_en_gb'
      value: Active Hotspot
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_archived'
      language: '@language_en_gb'
      value: Archived Hotspot
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_trigger'
      language: '@language_en_gb'
      value: Triggered Hotspot
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_watchers'
      language: '@language_en_gb'
      value: Watchers

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_resolve_title'
      language: '@language_en_gb'
      value: Hotspots - Resolved Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_resolve_description'
      language: '@language_en_gb'
      value: <p>This email is sent when a hotspot instance is marked as resolved.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_active_title'
      language: '@language_en_gb'
      value: Hotspots - Active Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_active_description'
      language: '@language_en_gb'
      value: <p>This email is sent when a hotspot agent is made active.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_watchers_title'
      language: '@language_en_gb'
      value: Hotspots - Watchers Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_watchers_description'
      language: '@language_en_gb'
      value: <p>This email is sent to watchers when anything changes on the hotspot.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_archived_title'
      language: '@language_en_gb'
      value: Hotspots - Archived Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_archived_description'
      language: '@language_en_gb'
      value: <p>This email is sent when a hotspot agent is archived.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_trigger_title'
      language: '@language_en_gb'
      value: Hotspots - Trigger Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_trigger_description'
      language: '@language_en_gb'
      value: <p>This email is sent when a hotspot agent is triggered.</p>
