entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_hotspots'
      language: '@language_fr_ca'
      value: Points chauds

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_resolve'
      language: '@language_fr_ca'
      value: Point d’accès résolu
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_active'
      language: '@language_fr_ca'
      value: Point d’accès activé
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_archived'
      language: '@language_fr_ca'
      value: Point d’accès archivé
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_trigger'
      language: '@language_fr_ca'
      value: Point d’accès déclenché
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_hotspots_watchers'
      language: '@language_fr_ca'
      value: Observateurs

