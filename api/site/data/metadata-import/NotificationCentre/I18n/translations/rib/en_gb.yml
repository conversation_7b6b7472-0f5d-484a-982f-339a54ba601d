entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_rib'
      language: '@language_en_gb'
      value: Reportable Incident Briefs

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_created'
      language: '@language_en_gb'
      value: RIB Created
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_assigned'
      language: '@language_en_gb'
      value: RIB Assigned
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_report'
      language: '@language_en_gb'
      value: Reportable Incident Brief
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_a'
      language: '@language_en_gb'
      value: Reportable Incident Brief Part A
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_ab'
      language: '@language_en_gb'
      value: Reportable Incident Brief Part AB
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_success'
      language: '@language_en_gb'
      value: RIB Successfully Sent
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_failure'
      language: '@language_en_gb'
      value: RIB Failed To Send

  # Module Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_record_id'
      language: '@language_en_gb'
      value: RIB Number
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_locations'
      language: '@language_en_gb'
      value: Location Admitted
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_services'
      language: '@language_en_gb'
      value: Service Admitted
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_status'
      language: '@language_en_gb'
      value: Status
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_record_source_ref'
      language: '@language_en_gb'
      value: Record Source Ref
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_sac_score'
      language: '@language_en_gb'
      value: Sac/Harm Score
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_incident_summary'
      language: '@language_en_gb'
      value: Incident Summary
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_reason_incident_is_being_reported'
      language: '@language_en_gb'
      value: Reason Incident Is Being Reported
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_reason_for_reporting'
      language: '@language_en_gb'
      value: Reason For Reporting
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_reported_date'
      language: '@language_en_gb'
      value: Reported Date
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_incident_date'
      language: '@language_en_gb'
      value: Incident Date
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_incident_time'
      language: '@language_en_gb'
      value: Incident Time
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_category'
      language: '@language_en_gb'
      value: Category
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_action_taken_summary'
      language: '@language_en_gb'
      value: Action Taken (Summary)
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_disclosure_process_commenced'
      language: '@language_en_gb'
      value: Disclosure Process Commenced
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_commencement_date'
      language: '@language_en_gb'
      value: Commencement Date
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_reason_for_commencing_open_disclosure'
      language: '@language_en_gb'
      value: Reason For Commencing Open Disclosure
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_referral_to_other_agencies'
      language: '@language_en_gb'
      value: Referral To Other Agencies
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_other_referred_agencies'
      language: '@language_en_gb'
      value: Other Referred Agencies
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_name_of_rib_contact'
      language: '@language_en_gb'
      value: Name Of RIB Contact
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_phone_number_of_rib_contact'
      language: '@language_en_gb'
      value: Phone Number Of RIB Contact
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_email_address_of_rib_contact'
      language: '@language_en_gb'
      value: Email Address Of RIB Contact
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_link'
      language: '@language_en_gb'
      value: Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_report_link'
      language: '@language_en_gb'
      value: Brief Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_template_name'
      language: '@language_en_gb'
      value: Brief Template Name
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_rib_owner_email'
      language: '@language_en_gb'
      value: Owner Email

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_created_title'
      language: '@language_en_gb'
      value: RIB Created
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_created_description'
      language: '@language_en_gb'
      value: '<p>This email is sent to users who have been provided the following two permissions within ACL: "Email notifications for Reportable Incident Briefs at my location" and "Reportable Incident Reviewer (share Location)", when a RIB record created from an Incident or Feedback record matches the user’s location, or child locations. The RIB record will enter into Draft status in order to be reviewed.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_assigned_title'
      language: '@language_en_gb'
      value: RIB Assigned
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_assigned_description'
      language: '@language_en_gb'
      value: <p>This email is sent to the RIB Owner of a record either when an Owner is first assigned or is changed to a new Owner. The purpose of this email is to alert the RIB Owner that a RIB record requires their attention for completion in time for submission to the MOH.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_report_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_report_description'
      language: '@language_en_gb'
      value: <p>This RIB report email is sent by the RIB Owner to the designated email address within System Configuration so that the incident can be assessed and managed by the appropriate committee/organisation. This is only possible when a RIB report is within the statuses ‘Approved' or 'Submitted’. This is sent as a one-time link to the MOH. Within System Configuration, it is possible to set the expiry time of the link and a limit to the number of times the link can be accessed from the email.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_a_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief Part A
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_a_description'
      language: '@language_en_gb'
      value: <p>This RIB report email is sent by the RIB Owner to the designated email address within System Configuration so that the incident can be assessed and managed by the appropriate committee/organisation. This is only possible when a RIB report is within the statuses ‘Approved' or 'Submitted’. This is sent as a one-time link to the MOH. Within System Configuration, it is possible to set the expiry time of the link and a limit to the number of times the link can be accessed from the email.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_ab_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief Part AB
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_part_ab_description'
      language: '@language_en_gb'
      value: <p>This RIB report email is sent by the RIB Owner to the designated email address within System Configuration so that the incident can be assessed and managed by the appropriate committee/organisation. This is only possible when a RIB report is within the statuses ‘Approved' or 'Submitted’. This is sent as a one-time link to the MOH. Within System Configuration, it is possible to set the expiry time of the link and a limit to the number of times the link can be accessed from the email.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_success_title'
      language: '@language_en_gb'
      value: RIB Successfully Sent
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_success_description'
      language: '@language_en_gb'
      value: <p>This email is sent to the RIB Owner when a RIB report they have attempted to send to an external email address, such as MOH, has been successful. This ensures the RIB Owner has a sent receipt to provide confidence and assurance that their RIB report has sent from the DCIQ Application.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_failure_title'
      language: '@language_en_gb'
      value: RIB Failed to Send
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_rib_failure_description'
      language: '@language_en_gb'
      value: <p>This email is sent to the RIB Owner when a RIB report they have attempted to send to MOH has been unsuccessful. This ensures they are informed when an email submission to an external body has failed so they are able to attempt resubmission.</p>
