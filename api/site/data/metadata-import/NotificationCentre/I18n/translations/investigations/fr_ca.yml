entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_module_investigations', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_investigations_reminder', language: '@language_fr_ca', value: Rappel } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_record_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_number_of_days', language: '@language_fr_ca', value: 'Nombre de jours' } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_link', language: '@language_fr_ca', value: Lien } }
  - { fields: { placeholder: '@placeholder_notification_centre_overdue_investigations_title', language: '@language_fr_ca', value: Enquêtes } }
  # Template Variables
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_record_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_number_of_days', language: '@language_fr_ca', value: 'Nombre de jours' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_link', language: '@language_fr_ca', value: Lien } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_investigation_events_notification', language: '@language_fr_ca', value: Notification d’évènement } }
