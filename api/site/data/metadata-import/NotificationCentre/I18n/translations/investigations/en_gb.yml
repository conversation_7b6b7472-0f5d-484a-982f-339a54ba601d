entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_notification_centre_module_investigations'
      language: '@language_en_gb'
      value: Investigations
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_investigations_title'
      language: '@language_en_gb'
      value: Investigations

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigations_reminder'
      language: '@language_en_gb'
      value: Reminder
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigation_events_notification'
      language: '@language_en_gb'
      value: Event Notification

  # Template Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_record_id'
      language: '@language_en_gb'
      value: ID
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_number_of_days'
      language: '@language_en_gb'
      value: Number of Days
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_reminder_link'
      language: '@language_en_gb'
      value: Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_reference'
      language: '@language_en_gb'
      value: Reference
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_location'
      language: '@language_en_gb'
      value: Location
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_service'
      language: '@language_en_gb'
      value: Service
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_category'
      language: '@language_en_gb'
      value: Category
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_date'
      language: '@language_en_gb'
      value: Date
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_owner'
      language: '@language_en_gb'
      value: Owner
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_sac_score'
      language: '@language_en_gb'
      value: SAC Score
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_description'
      language: '@language_en_gb'
      value: Description
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_investigations_event_link'
      language: '@language_en_gb'
      value: Link

  # Module Variables
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_investigations_record_id'
      language: '@language_en_gb'
      value: ID
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_investigations_number_of_days'
      language: '@language_en_gb'
      value: Number of Days
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_investigations_link'
      language: '@language_en_gb'
      value: Link

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigations_reminder_title'
      language: '@language_en_gb'
      value: Investigations - Reminder Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigations_reminder_description'
      language: '@language_en_gb'
      value: <p>The Investigations Reminder email is sent out a specified number of days prior to the overdue date set on an Investigations record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email gives users advanced warning of their need to complete the investigation and prevent them from becoming overdue.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigation_events_notification_title'
      language: '@language_en_gb'
      value: Investigations - Events Notification
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigation_events_notification_description'
      language: '@language_en_gb'
      value: <p>This email is intended to notify relevant users when a Capture record matching their location has been flagged for an investigation. The users sent an email are subscribed to the ACL rule 'Email notifications on record submission for Investigation Events at my location'. The purpose of this email is to inform users that a response is required for an event flagged for investigation within their location which requires their review, followed by the creation of an investigation or rejection. This opens up the process for investigation, where appropriate.</p>

  # Template Page Links
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_investigations_reminder_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to see the current configuration of the Investigations Reminder email</a></p>
