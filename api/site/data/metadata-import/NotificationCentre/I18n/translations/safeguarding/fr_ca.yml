entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - { fields: { placeholder: '@notification_centre_safeguarding_module_title', language: '@language_fr_ca', value: 'Protection' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_module_variable_is_the_report_in_connection_with_a_child', language: '@language_fr_ca', value: 'Le rapport concerne-t-il un enfant'}}
  # Template Types
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_acknowledgement', language: '@language_fr_ca', value: 'Accusé de réception' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_notification', language: '@language_fr_ca', value: 'Avis' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_update', language: '@language_fr_ca', value: 'Mettre à jour' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_reporter_feedback', language: '@language_fr_ca', value: 'Commentaire du déclarant' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_communication_and_feedback', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
