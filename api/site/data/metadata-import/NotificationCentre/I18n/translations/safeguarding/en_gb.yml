entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notification_centre_safeguarding_module_variable_is_the_report_in_connection_with_a_child', language: '@language_en_gb', value: 'Is the report in connection with a child'}}
  - { fields: { placeholder: '@notification_centre_safeguarding_module_title', language: '@language_en_gb', value: 'Safeguarding' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_notification', language: '@language_en_gb', value: 'Notification' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_update', language: '@language_en_gb', value: 'Update' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_acknowledgement', language: '@language_en_gb', value: 'Acknowledgement' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_reporter_feedback', language: '@language_en_gb', value: 'Reporter Feedback' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_record_id', language: '@language_en_gb', value: 'Record ID' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_reference', language: '@language_en_gb', value: 'Reference' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_link', language: '@language_en_gb', value: 'Link' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_name', language: '@language_en_gb', value: 'Name' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_description', language: '@language_en_gb', value: 'Description' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_date_reported', language: '@language_en_gb', value: 'Date Reported' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_date_closed', language: '@language_en_gb', value: 'Date Closed' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_location', language: '@language_en_gb', value: 'Location' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_service', language: '@language_en_gb', value: 'Service' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_child_connection', language: '@language_en_gb', value: 'Child Connection' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_handler', language: '@language_en_gb', value: 'Handler' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_manager', language: '@language_en_gb', value: 'Manager' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_approval_status', language: '@language_en_gb', value: 'Approval Status' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_variable_alleged_abuse_type', language: '@language_en_gb', value: 'Alleged Abuse Type' } }
  # template page
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_notification_title', language: '@language_en_gb', value: 'Safeguarding - Notification Email' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_notification_description', language: '@language_en_gb', value: '<p>A notification email is sent to inform one or more users about a newly created record. The recipients will be calculated based on their membership of security groups with criteria that match the new record. This type of email could be used to make Safeguarding team members aware of concerns being recorded in the organisation.</p>' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_update_title', language: '@language_en_gb', value: 'Safeguarding - Update Email' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_update_description', language: '@language_en_gb', value: '<p>A Safeguarding Update email is sent when a change has been made to a record that would have triggered a notification email to users, had those changes been present at the point the feedback was created. This can be helpful when records that are logged with incorrect data but subsequently corrected so that the correct people are still notified. To prevent large numbers of identical emails from being sent, users will only receive an update email if they would not have received one the previous time the record was saved. Note that therefore this will therefore not notify users of all updates to the record.</p>' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_acknowledgement_title', language: '@language_en_gb', value: 'Safeguarding - Acknowledgement Email' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_acknowledgement_description', language: '@language_en_gb', value: '<p>A Safeguarding acknowledgement email is sent to the reporter after submission. It is intended to be used to thank the reporter for submitting the record and to inform them of what they should expect in terms of future engagement or follow-up from the Safeguarding team. In doing so, it intends to keep them engaged with the feedback loop and make them more likely to contribute to any further dialogue and more likely to report future concerns.</p>' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_reporter_feedback_title', language: '@language_en_gb', value: 'Safeguarding - Reporter Feedback Email' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_reporter_feedback_description', language: '@language_en_gb', value: '<p>The reporter feedback notification is an email sent to the reporter of a concern when the approval status is changed to "Closed". The email is intended to give some information on the resolution and to thank the reporter again for submitting the report. This "closes the loop", ensuring that the user feels that their input has made an impact and encouraging them to continue to submit concerns in future.</p>' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_communication_and_feedback', language: '@language_en_gb', value: 'Communication And Feedback' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_communication_and_feedback_title', language: '@language_en_gb', value: 'Safeguarding - Communication And Feedback Email' } }
  - { fields: { placeholder: '@notification_centre_safeguarding_template_type_communication_and_feedback_description', language: '@language_en_gb', value: '<p>Communication and Feedback emails are sent from a safeguarding record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section - the user will be able to edit this in the record to suit their purpose.</p>' } }
