entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notification_centre_redress_module_title', language: '@language_en_gb', value: 'Redress' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_notification', language: '@language_en_gb', value: 'Notification' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_case_manager', language: '@language_en_gb', value: 'New Case Manager' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_escalation_manager', language: '@language_en_gb', value: 'New Escalation Manager' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_communication_and_feedback', language: '@language_en_gb', value: 'Communication And Feedback' } }
  # Template Page
  - { fields: { placeholder: '@notification_centre_redress_template_type_notification_title', language: '@language_en_gb', value: 'Redress - Notification Email' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_notification_description', language: '@language_en_gb', value: '<p>A Notification email is sent to inform one or more users about a newly created Redress record. The recipients will be calculated based on their membership of security groups with criteria that match the record. This type of email could be used to make Case and Escalation Managers aware of Redress cases being recorded in the organisation.</p>' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_case_manager_title', language: '@language_en_gb', value: 'Redress - New Case Manager Email' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_case_manager_description', language: '@language_en_gb', value: '<p>A New Case Manager email is sent to a user who has just been assigned as the case manager of a Redress record. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to process it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new Case Manager.</p>' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_escalation_manager_title', language: '@language_en_gb', value: 'Redress - New Escalation Manager Email' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_escalation_manager_description', language: '@language_en_gb', value: '<p>A New Escalation Manager email is sent to a user who has just been assigned as the escalation manager of a Redress record. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to process it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new Escalation Manager.</p>' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_communication_and_feedback_title', language: '@language_en_gb', value: 'Redress - Communication And Feedback Email' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_communication_and_feedback_description', language: '@language_en_gb', value: '<p>Communication and Feedback emails are sent from a redress record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section - the user will be able to edit this in the record to suit their purpose.</p>' } }
  # Template variables
  - { fields: { placeholder: '@notification_centre_redress_template_variable_full_name', language: '@language_en_gb', value: 'Full name' } }
  - { fields: { placeholder: '@notification_centre_redress_template_variable_module', language: '@language_en_gb', value: 'Module' } }
  - { fields: { placeholder: '@notification_centre_redress_template_variable_link', language: '@language_en_gb', value: 'Link' } }
