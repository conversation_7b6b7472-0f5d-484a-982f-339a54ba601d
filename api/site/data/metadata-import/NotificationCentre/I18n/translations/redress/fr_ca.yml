entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notification_centre_redress_module_title', language: '@language_fr_ca', value: 'Redressement' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_notification', language: '@language_fr_ca', value: 'Avis' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_case_manager', language: '@language_fr_ca', value: 'Nouveau gestionnaire de cas' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_new_escalation_manager', language: '@language_fr_ca', value: 'Nouveau gestionnaire d’escalade' } }
  - { fields: { placeholder: '@notification_centre_redress_template_type_communication_and_feedback', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
