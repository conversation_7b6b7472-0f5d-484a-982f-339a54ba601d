entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_module_safety_alerts', language: '@language_fr_ca', value: 'Alertes de sécurité' } }
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_reminder_deadline', language: '@language_fr_ca', value: Rappel } }
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_query_notification', language: '@language_fr_ca', value: 'Avis de requête' } }
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_action_notification', language: '@language_fr_ca', value: 'Avis d''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_for_information', language: '@language_fr_ca', value: 'Pour information' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_message', language: '@language_fr_ca', value: Message } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_sender', language: '@language_fr_ca', value: Expéditeur } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_record_id', language: '@language_fr_ca', value: 'ID d''enregistrement' } }
  - { fields: { placeholder: '@placeholder_notification_centre_module_variable_link', language: '@language_fr_ca', value: Lien } }
