entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_mortality_review'
      language: '@language_en_gb'
      value: Mortality Review

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_acknowledgement'
      language: '@language_en_gb'
      value: Acknowledgement
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_notification'
      language: '@language_en_gb'
      value: Notifications
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_update'
      language: '@language_en_gb'
      value: Updated
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_reject'
      language: '@language_en_gb'
      value: Rejected
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_new_reviewer'
      language: '@language_en_gb'
      value: New Reviewer
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_communication_and_feedback'
      language: '@language_en_gb'
      value: Communication and Feedback
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_overdue'
      language: '@language_en_gb'
      value: Overdue

  # Template variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_mortality_review_module'
      language: '@language_en_gb'
      value: Module

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_acknowledgement_title'
      language: '@language_en_gb'
      value: Mortality Review - Acknowledgement Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_acknowledgement_description'
      language: '@language_en_gb'
      value: <p>A Mortality Review Acknowledgement email is sent to the reporter after they have submitted the record. It is intended to be used to thank the reporter for submitting the report and to inform them of what they should expect in terms of future engagement or follow-up from the feedback team. In doing so, it keeps them engaged with the feedback loop and hopes to make them more likely to contribute to any further dialogue and more likely to report future mortality reviews.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_communication_and_feedback_title'
      language: '@language_en_gb'
      value: Mortality Review - Communication And Feedback Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_communication_and_feedback_description'
      language: '@language_en_gb'
      value: <p>Communication and Feedback emails are sent from a mortality review record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section - the user will be able to edit this in the record to suit their purpose.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_new_reviewer_title'
      language: '@language_en_gb'
      value: Mortality Review - New Reviewer Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_new_reviewer_description'
      language: '@language_en_gb'
      value: <p>A New Reviewer email is sent to a user who has just been assigned as the handler of a mortality review. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to review it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new reviewer.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_notification_title'
      language: '@language_en_gb'
      value: Mortality Review - Notification Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_notification_description'
      language: '@language_en_gb'
      value: <p>A Notification email is sent to inform one or more users about a newly created mortality review. The recipients will be calculated based on their membership of security groups with criteria that match the new review. This type of email could be used to make safety managers aware of mortalities being recorded in their location in the organisation, to alert senior leaders to high-severity cases, or to flag reviews to the right representative for a particular specialty or service.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_overdue_title'
      language: '@language_en_gb'
      value: Mortality Review - Overdue Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_overdue_description'
      language: '@language_en_gb'
      value: <p>An Overdue email is sent when a mortality review record has become overdue. This email is sent nightly until the record is no longer overdue.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_reject_title'
      language: '@language_en_gb'
      value: Mortality Review - Rejected Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_reject_description'
      language: '@language_en_gb'
      value: <p>A Rejected email is sent when the status of a mortality review is set to 'Rejected'. It is intended to notify the reporter that the record is no longer being investigated, and provides a safeguard if this action has been taken in error.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_update_title'
      language: '@language_en_gb'
      value: Mortality Review - Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_update_description'
      language: '@language_en_gb'
      value: <p>An Update email is sent when a change has been made to a record that would have triggered a notification email to users, had those changes been present at the point the record was created. This can be helpful when records that are logged with incorrect data but subsequently corrected so that the correct people are still notified. To prevent large numbers of identical emails from being sent, users will only receive an update email if they would not have received one the previous time the record was saved. Note that therefore this will therefore not notify users of all updates to the record.</p>

  # Template Page Links
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_mortality_review_overdue_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to learn how a record becomes overdue and how you can configure timescales.</a></p>
