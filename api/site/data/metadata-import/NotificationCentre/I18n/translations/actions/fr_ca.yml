entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@notification_centre_notification_centre_module_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_new', language: '@language_fr_ca', value: Nouveau } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_update', language: '@language_fr_ca', value: 'Mettre à jour' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_reminder', language: '@language_fr_ca', value: Rappel } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assignee', language: '@language_fr_ca', value: 'En retard (cessionnaire)' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assigner', language: '@language_fr_ca', value: 'En retard (attribueur)' } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_complete', language: '@language_fr_ca', value: Terminer } }
  - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_progress_note', language: '@language_fr_ca', value: 'Note de progression' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_action_link', language: '@language_fr_ca', value: 'Lien d''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_record_link', language: '@language_fr_ca', value: 'Lien du dossier d''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_summary', language: '@language_fr_ca', value: 'Sommaire de l''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_progress_note', language: '@language_fr_ca', value: 'Note de progression' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_id', language: '@language_fr_ca', value: 'Identifiant d''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_source_module_line', language: '@language_fr_ca', value: 'Ligne du module source de l''action' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_completed_by', language: '@language_fr_ca', value: 'Action achevée par' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_assigned_by', language: '@language_fr_ca', value: 'Action attribuée par' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_due_in', language: '@language_fr_ca', value: 'Action prévue dans' } }
