entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@notification_centre_notification_centre_module_actions'
      language: '@language_en_gb'
      value: Actions

  # Template Types
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_new'
      language: '@language_en_gb'
      value: New
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_update'
      language: '@language_en_gb'
      value: Update
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_reminder'
      language: '@language_en_gb'
      value: Reminder
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assignee'
      language: '@language_en_gb'
      value: Overdue (Assignee)
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assigner'
      language: '@language_en_gb'
      value: Overdue (Assigner)
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_complete'
      language: '@language_en_gb'
      value: Complete
  - fields:
      placeholder: '@notification_centre_notification_centre_template_type_actions_progress_note'
      language: '@language_en_gb'
      value: Progress Note

  # Template Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_action_link'
      language: '@language_en_gb'
      value: Action Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_record_link'
      language: '@language_en_gb'
      value: Action Record Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_summary'
      language: '@language_en_gb'
      value: Action Summary
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_progress_note'
      language: '@language_en_gb'
      value: Progress Note
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_id'
      language: '@language_en_gb'
      value: Action Id
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_source_module_line'
      language: '@language_en_gb'
      value: Action Source Module Line
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_completed_by'
      language: '@language_en_gb'
      value: Action Completed By
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_assigned_by'
      language: '@language_en_gb'
      value: Action Assigned By
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_actions_due_in'
      language: '@language_en_gb'
      value: Action Due In

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_complete_title'
      language: '@language_en_gb'
      value: Actions - Complete Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_complete_description'
      language: '@language_en_gb'
      value: <p>Completed action emails are sent to both the Assigned By and Assigned To users when an action is completed and closed. This gives both parties a record of the completion of this record.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_new_title'
      language: '@language_en_gb'
      value: Actions - Action Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_new_description'
      language: '@language_en_gb'
      value: <p>New action emails are sent to both the Assigned By and Assigned To users when a new action is created. This gives the Assigner a record of this creation, and notifies the assignee that they have an action which requires attention.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assignee_title'
      language: '@language_en_gb'
      value: Actions - Overdue Assignee Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assignee_description'
      language: '@language_en_gb'
      value: <p>The actions Overdue (Assignee) email is sent out to the Assigned To user {{overdueDays}} days after the overdue date set on an Actions record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email notifies users that they have an overdue action which urgently requires attention. This email is sent nightly until the action is no longer overdue.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assigner_title'
      language: '@language_en_gb'
      value: Actions - Overdue Assigner Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assigner_description'
      language: '@language_en_gb'
      value: <p>The actions Overdue (Assigner) email is sent out to the Assigned By user {{overdueDays}} days after the overdue date set on an Actions record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email notifies users there is an overdue action which requires urgent attention. This Assigner email can be disabled whilst retaining the Assignee notifications&#58; this setting is found in the Overdue Notifications section of the Notification Centre. This email is sent nightly until the action is no longer overdue.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_progress_note_title'
      language: '@language_en_gb'
      value: Actions - Progress Note Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_progress_note_description'
      language: '@language_en_gb'
      value: <p>Progress Note emails are sent to both the Assigned By and Assigned To users when a progress note has been added to an actions record. This keeps those users abreast of any progress on that action.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_reminder_title'
      language: '@language_en_gb'
      value: Actions - Reminder Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_reminder_description'
      language: '@language_en_gb'
      value: <p>The Actions Reminder email is sent to the user who assigned the action and the action assignee {{reminderDays}} days prior to the overdue date set on an Actions record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email gives users advanced warning of their need to complete the actions and prevent them from becoming overdue.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_update_title'
      language: '@language_en_gb'
      value: Actions - Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_update_description'
      language: '@language_en_gb'
      value: <p>Update emails are sent to both the Assigned By and Assigned To users when changes have been made to an actions record. This keeps those users abreast of any progress on that action.</p>

  # Template Page Links
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assignee_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to see the currently configured overdue days</a></p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_overdue_assigner_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to see the currently configured overdue days</a></p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_actions_reminder_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to see the currently configured reminder days</a></p>
