entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@notification_centre_module_title', language: '@language_en_us', value: Notification Centre } }
    - { fields: { placeholder: '@notification_centre_notification_centre_module_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_claims', language: '@language_en_us', value: Claims } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_feedback', language: '@language_en_us', value: Feedback } }
    - { fields: { placeholder: '@notification_centre_notification_centre_module_incidents', language: '@language_en_us', value: Incidents } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_mortality_review', language: '@language_en_us', value: Mortality Review } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_complete', language: '@language_en_us', value: Complete } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_new', language: '@language_en_us', value: New } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assignee', language: '@language_en_us', value: Overdue (Assignee) } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_overdue_assigner', language: '@language_en_us', value: Overdue (Assigner) } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_progress_note', language: '@language_en_us', value: Progress Note } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_reminder', language: '@language_en_us', value: Reminder } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_actions_update', language: '@language_en_us', value: Update } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_communication_and_feedback', language: '@language_en_us', value: Communication and Feedback } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_new_handler', language: '@language_en_us', value: New Handler } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_new_investigator', language: '@language_en_us', value: New Investigator } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_notification', language: '@language_en_us', value: Notifications } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_claims_update', language: '@language_en_us', value: Updated } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_acknowledgement', language: '@language_en_us', value: Acknowledgement } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_communication_and_feedback', language: '@language_en_us', value: Communication and Feedback } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_new_handler', language: '@language_en_us', value: New Handler } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_new_investigator', language: '@language_en_us', value: New Investigator } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_notification', language: '@language_en_us', value: Notifications } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_overdue', language: '@language_en_us', value: Overdue } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_update', language: '@language_en_us', value: Updated } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_acknowledgement', language: '@language_en_us', value: Acknowledgement } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_new_handler', language: '@language_en_us', value: New Handler } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_new_investigator', language: '@language_en_us', value: New Investigator } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_overdue', language: '@language_en_us', value: Overdue } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_reporter', language: '@language_en_us', value: Reporter } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_update', language: '@language_en_us', value: Notifications/Updated } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_acknowledgement', language: '@language_en_us', value: Acknowledgement } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_communication_and_feedback', language: '@language_en_us', value: Overdue } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_new_reviewer', language: '@language_en_us', value: New Reviewer } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_notification', language: '@language_en_us', value: Notifications } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_overdue', language: '@language_en_us', value: Communication and Feedback } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_reject', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_mortality_review_update', language: '@language_en_us', value: Updated } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_action_link', language: '@language_en_us', value: Action Link } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_assigned_by', language: '@language_en_us', value: Action Assigned By } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_completed_by', language: '@language_en_us', value: Action Completed By } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_due_in', language: '@language_en_us', value: Action Due In } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_id', language: '@language_en_us', value: Action Id } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_progress_note', language: '@language_en_us', value: Progress Note } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_record_link', language: '@language_en_us', value: Action Record Link } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_source_module_line', language: '@language_en_us', value: Action Source Module Line } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_actions_summary', language: '@language_en_us', value: Action Summary } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_cannot_delete_default', language: '@language_en_us', value: Default templates cannot be deleted } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_columns_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_columns_name', language: '@language_en_us', value: Template Name } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_contents_body', language: '@language_en_us', value: Body } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_contents_one_language_required', language: '@language_en_us', value: Template subject and body must be provided in at least one language } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_contents_subject', language: '@language_en_us', value: Subject } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_delete', language: '@language_en_us', value: Delete Template } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_delete_success', language: '@language_en_us', value: Template deleted successfully } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_edit_template', language: '@language_en_us', value: Edit Template } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_fields_description', language: '@language_en_us', value: Template Description } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_fields_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_fields_module', language: '@language_en_us', value: Module } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_fields_name', language: '@language_en_us', value: Template Name } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_fields_template_type', language: '@language_en_us', value: Template Type } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_new_template', language: '@language_en_us', value: New Template } }
    - { fields: { placeholder: '@notification_centre_templates_plural', language: '@language_en_us', value: Email Templates } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_save', language: '@language_en_us', value: Save Template } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_save_success', language: '@language_en_us', value: Template saved successfully } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_template_location_exists_error', language: '@language_en_us', value: A template of this type already exists for the selected location } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_template_location_exists_link_to_record', language: '@language_en_us', value: View template } }
    - { fields: { placeholder: '@notification_centre_notification_centre_templates_template_location_exists_popup_error', language: '@language_en_us', value: A template of this type already exists for the selected location } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_module_safety_alerts', language: '@language_en_us', value: 'Safety Alerts' } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_reminder_deadline', language: '@language_en_us', value: Reminder } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_query_notification', language: '@language_en_us', value: 'Query Notification' } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_action_notification', language: '@language_en_us', value: 'Action Notification' } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_template_type_safety_alerts_for_information', language: '@language_en_us', value: 'For Information' } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_message', language: '@language_en_us', value: Message } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_sender', language: '@language_en_us', value: Sender } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_record_id', language: '@language_en_us', value: 'Record ID' } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_link', language: '@language_en_us', value: Link } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_module_surveys', language: '@language_en_us', value: Surveys } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_surveys_distribution', language: '@language_en_us', value: 'Surveys Distribution' } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_surveys_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_surveys_description', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_surveys_sub_category', language: '@language_en_us', value: 'Sub Category' } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_surveys_response_due_date', language: '@language_en_us', value: 'Response Due Date' } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_surveys_link', language: '@language_en_us', value: Link } }
    - { fields: { placeholder: '@placeholder_notification_centre_notification_centre_module_investigations', language: '@language_en_us', value: Investigations } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_type_investigations_reminder', language: '@language_en_us', value: Reminder } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_record_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_number_of_days', language: '@language_en_us', value: 'Number of Days' } }
    - { fields: { placeholder: '@placeholder_notification_centre_module_variable_investigations_link', language: '@language_en_us', value: Link } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_investigations_title', language: '@language_en_us', value: Investigations } }
    - { fields: { placeholder: '@notification_centre_notification_centre_template_type_incidents_notification', language: '@language_en_us', value: Notification } }
    - { fields: { placeholder: '@placeholder_notification_centre_notifications_overdue', language: '@language_en_us', value: 'Configure Overdue Notifications' } }
    - { fields: { placeholder: '@placeholder_notification_centre_nav_view_and_create', language: '@language_en_us', value: 'View and Create Email Templates' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_roles_title', language: '@language_en_us', value: Roles } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_statuses_title', language: '@language_en_us', value: Statuses } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_overdue_messages_heading', language: '@language_en_us', value: 'Send Overdue Reminder Messages' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_role_heading', language: '@language_en_us', value: 'Who will receive overdue email notifications' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_role_label', language: '@language_en_us', value: 'Select the roles who will receive overdue email notifications' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_status_heading', language: '@language_en_us', value: 'Statuses for overdue e-mail notifications' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_status_label', language: '@language_en_us', value: 'Select the statuses who will receive overdue email notifications' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_rule_save_success', language: '@language_en_us', value: 'Overdue rule successfully added' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_rule_removal_success', language: '@language_en_us', value: 'Overdue rule successfully removed' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_invalid_rule_missing_value', language: '@language_en_us', value: 'An overdue rule needs a role and a status' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_invalid_rule_duplicate_rule', language: '@language_en_us', value: 'This rule is already active' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_action_assigners_label', language: '@language_en_us', value: 'Disable sending emails to action assigners' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_no_rules', language: '@language_en_us', value: 'No current Overdue rules' } }
    - { fields: { placeholder: '@placeholder_notification_centre_filter_modules_label', language: '@language_en_us', value: Modules } }
    - { fields: { placeholder: '@placeholder_notification_centre_filter_modules_default_option', language: '@language_en_us', value: 'Select a Module' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_reminder_days_label', language: '@language_en_us', value: 'Reminder Days' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_overdue_days_label', language: '@language_en_us', value: 'Overdue Days' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_reminder_days_error', language: '@language_en_us', value: 'Value must be above zero' } }
    - { fields: { placeholder: '@placeholder_notification_centre_overdue_overdue_days_error', language: '@language_en_us', value: 'Value must be above zero' } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_record_id', language: '@language_en_us', value: 'Feedback ID' } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_description', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_grade', language: '@language_en_us', value: Grade } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_handler', language: '@language_en_us', value: Handler } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_manager', language: '@language_en_us', value: Manager } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_link', language: '@language_en_us', value: 'Feedback Record Link' } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_date_received', language: '@language_en_us', value: 'Date Received' } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_date_opened', language: '@language_en_us', value: Opened } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_ourref', language: '@language_en_us', value: Ref } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_method', language: '@language_en_us', value: Method } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_type', language: '@language_en_us', value: Sub-Type } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_subject', language: '@language_en_us', value: Sub-Subject } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_summary', language: '@language_en_us', value: Summary } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_feedback', language: '@language_en_us', value: Feedback } }
    - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_full_name', language: '@language_en_us', value: 'Full Name' } }
    - { fields: { placeholder: '@placeholder_notification_centre_domain_whitelist', language: '@language_en_us', value: 'Domain whitelist' } }
    - { fields: { placeholder: '@placeholder_nav_notification_centre_domain_whitelist', language: '@language_en_us', value: 'Domain whitelist' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_errors_create_batch_duplicate', language: '@language_en_us', value: 'Duplicate domain entry' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_errors_create_batch_unknown_error', language: '@language_en_us', value: 'Unknown error' } }
    - { fields: { placeholder: '@placeholder_notification_centre_domain_whitelist_domain', language: '@language_en_us', value: 'Domain' } }
    - { fields: { placeholder: '@placeholder_notification_centre_domain_whitelist_action', language: '@language_en_us', value: 'Action' } }
    - { fields: { placeholder: '@placeholder_notification_centre_domain_whitelist_help_message', language: '@language_en_us', value: 'Note: Multiple domains should be separated by using Enter' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_filter_title', language: '@language_en_us', value: 'Filter Domains' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_saving_network_error', language: '@language_en_us', value: 'Error saving entry ( {{rootError}} )' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_num_domain_saved', language: '@language_en_us', value: 'Successfully saved {{numSaved}} domains' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_save_message', language: '@language_en_us', value: 'Error while saving the domain list' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_delete_success_message', language: '@language_en_us', value: 'Domain deleted successfully' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_delete_message', language: '@language_en_us', value: 'Error while deleting the domain' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_invalid_domain', language: '@language_en_us', value: 'Invalid domain entered' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_domain_invalid_characters', language: '@language_en_us', value: 'Domain must not contain invalid characters like forward slashes, colon, question marks, hashes, etc' } }
    - { fields: { placeholder: '@notification_centre_domain_whitelist_error_domain_invalid_format', language: '@language_en_us', value: 'URL does not match the expected format for a valid domain' } }
