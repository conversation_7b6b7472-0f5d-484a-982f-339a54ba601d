entityClass: I18n\Entity\Translation
priority: 15
data:
  - fields:
      placeholder: '@notification_centre_module_title'
      language: '@language_en_gb'
      value: Notification Centre
  - fields:
      placeholder: '@notification_centre_templates_plural'
      language: '@language_en_gb'
      value: Email Templates
  - fields:
      placeholder: '@notification_centre_templates_description'
      language: '@language_en_gb'
      value: Select a module and template type below to see the list of email templates that have been designed, see the locations that templates are linked to, and to make changes to the subject and body text of those templates.
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_columns_name'
      language: '@language_en_gb'
      value: Template Name
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_columns_location'
      language: '@language_en_gb'
      value: Location
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_new_template'
      language: '@language_en_gb'
      value: New Template
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_edit_template'
      language: '@language_en_gb'
      value: Edit Template
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_fields_name'
      language: '@language_en_gb'
      value: Template Name
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_fields_description'
      language: '@language_en_gb'
      value: Template Description
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_fields_location'
      language: '@language_en_gb'
      value: Location
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_fields_module'
      language: '@language_en_gb'
      value: Module
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_fields_template_type'
      language: '@language_en_gb'
      value: Template Type
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_contents_subject'
      language: '@language_en_gb'
      value: Subject
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_contents_body'
      language: '@language_en_gb'
      value: Body
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_save'
      language: '@language_en_gb'
      value: Save Template
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_delete'
      language: '@language_en_gb'
      value: Delete Template
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_contents_one_language_required'
      language: '@language_en_gb'
      value: Template subject and body must be provided in at least one language
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_template_location_exists_popup_error'
      language: '@language_en_gb'
      value: A template of this type already exists for the selected location
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_template_location_exists_error'
      language: '@language_en_gb'
      value: A template of this type already exists for the selected location
  - fields:
      placeholder: '@notification_centre_notification_centre_templates_template_location_exists_link_to_record'
      language: '@language_en_gb'
      value: View template
  - fields:
      placeholder: '@placeholder_notification_centre_template_delete_success'
      language: '@language_en_gb'
      value: Template deleted successfully
  - fields:
      placeholder: '@placeholder_notification_centre_template_cannot_delete_default'
      language: '@language_en_gb'
      value: Default templates cannot be deleted
  - fields:
      placeholder: '@placeholder_notification_centre_template_save_success'
      language: '@language_en_gb'
      value: Template saved successfully
  - fields:
      placeholder: '@placeholder_notification_centre_notifications_overdue'
      language: '@language_en_gb'
      value: Configure Overdue Notifications
  - fields:
      placeholder: '@placeholder_notification_centre_nav_view_and_create'
      language: '@language_en_gb'
      value: View and Create Email Templates

  #Overdue
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_roles_title'
      language: '@language_en_gb'
      value: Roles
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_statuses_title'
      language: '@language_en_gb'
      value: Statuses
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_overdue_messages_heading'
      language: '@language_en_gb'
      value: Send Overdue Reminder Messages
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_role_heading'
      language: '@language_en_gb'
      value: Who will receive overdue email notifications
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_role_label'
      language: '@language_en_gb'
      value: Select the roles who will receive overdue email notifications
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_status_heading'
      language: '@language_en_gb'
      value: Statuses for overdue e-mail notifications
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_status_label'
      language: '@language_en_gb'
      value: Select the statuses who will receive overdue email notifications
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_rule_save_success'
      language: '@language_en_gb'
      value: Overdue rule successfully added
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_rule_removal_success'
      language: '@language_en_gb'
      value: Overdue rule successfully removed
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_invalid_rule_missing_value'
      language: '@language_en_gb'
      value: An overdue rule needs a role and a status
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_invalid_rule_duplicate_rule'
      language: '@language_en_gb'
      value: This rule is already active
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_action_assigners_label'
      language: '@language_en_gb'
      value: Disable sending emails to action assigners
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_no_rules'
      language: '@language_en_gb'
      value: No current Overdue rules
  - fields:
      placeholder: '@placeholder_notification_centre_filter_modules_label'
      language: '@language_en_gb'
      value: Modules
  - fields:
      placeholder: '@placeholder_notification_centre_filter_modules_default_option'
      language: '@language_en_gb'
      value: Select a Module
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_reminder_days_label'
      language: '@language_en_gb'
      value: Reminder Days
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_overdue_days_label'
      language: '@language_en_gb'
      value: Overdue Days
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_reminder_days_error'
      language: '@language_en_gb'
      value: Value must be above zero
  - fields:
      placeholder: '@placeholder_notification_centre_overdue_overdue_days_error'
      language: '@language_en_gb'
      value: Value must be above zero

#audit
  - fields:
      placeholder: '@notification_centre_notification_centre_audit'
      language: '@language_en_gb'
      value: Email Audit
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_success'
      language: '@language_en_gb'
      value: Successful emails
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_failure'
      language: '@language_en_gb'
      value: Unsuccessful emails
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_columns_recipient'
      language: '@language_en_gb'
      value: Recipient
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_columns_module'
      language: '@language_en_gb'
      value: Module
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_columns_type'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_columns_date_of_email'
      language: '@language_en_gb'
      value: Date of email
  - fields:
      placeholder: '@notification_centre_notification_centre_audit_columns_error'
      language: '@language_en_gb'
      value: Error

# Domain whitelist
  -
    fields:
      placeholder: '@placeholder_notification_centre_domain_whitelist'
      language: '@language_en_gb'
      value: Domain whitelist
  -
    fields:
      placeholder: '@placeholder_nav_notification_centre_domain_whitelist'
      language: '@language_en_gb'
      value: Domain whitelist
  - fields:
      placeholder: '@notification_centre_domain_whitelist_errors_create_batch_duplicate'
      language: '@language_en_gb'
      value: Duplicate domain entry
  - fields:
      placeholder: '@notification_centre_domain_whitelist_errors_create_batch_unknown_error'
      language: '@language_en_gb'
      value: Unknown error
  -
    fields:
      placeholder: '@placeholder_notification_centre_domain_whitelist_domain'
      language: '@language_en_gb'
      value: Domain

  -
    fields:
      placeholder: '@placeholder_notification_centre_domain_whitelist_action'
      language: '@language_en_gb'
      value: Action

  -
    fields:
      placeholder: '@placeholder_notification_centre_domain_whitelist_help_message'
      language: '@language_en_gb'
      value: 'Note: Multiple domains should be separated by using Enter'

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_filter_title'
      language: '@language_en_gb'
      value: 'Filter Domains'
  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_saving_network_error'
      language: '@language_en_gb'
      value: 'Error saving entry ( {{rootError}} )'

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_num_domain_saved'
      language: '@language_en_gb'
      value: Successfully saved {{numSaved}} domains

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_save_message'
      language: '@language_en_gb'
      value: Error while saving the domain list

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_delete_success_message'
      language: '@language_en_gb'
      value: Domain deleted successfully

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_delete_message'
      language: '@language_en_gb'
      value: Error while deleting the domain

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_invalid_domain'
      language: '@language_en_gb'
      value: Invalid domain entered

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_domain_invalid_characters'
      language: '@language_en_gb'
      value: Domain must not contain invalid characters like forward slashes, colon, question marks, hashes, etc

  -
    fields:
      placeholder: '@notification_centre_domain_whitelist_error_domain_invalid_format'
      language: '@language_en_gb'
      value: URL does not match the expected format for a valid domain
