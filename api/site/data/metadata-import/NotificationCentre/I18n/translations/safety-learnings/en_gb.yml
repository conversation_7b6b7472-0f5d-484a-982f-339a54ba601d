entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_safety_learnings'
      language: '@language_en_gb'
      value: 'Safety Learnings'

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_rejected'
      language: '@language_en_gb'
      value: 'Rejected'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_update_to_draft'
      language: '@language_en_gb'
      value: 'Update to Draft'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_access_to_record'
      language: '@language_en_gb'
      value: 'Access To Record'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_published_learning'
      language: '@language_en_gb'
      value: 'Published Learning'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_recovered_learning'
      language: '@language_en_gb'
      value: 'Recovered Learning'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_expired_learning'
      language: '@language_en_gb'
      value: 'Expired Learning'

  # Module Variables
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_title'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_details'
      language: '@language_en_gb'
      value: 'Details'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_key_lessons_and_outcomes'
      language: '@language_en_gb'
      value: 'Key Lessons and Outcomes'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_status'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_expiry_date'
      language: '@language_en_gb'
      value: 'Expiry Date'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_author'
      language: '@language_en_gb'
      value: 'Author'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_link'
      language: '@language_en_gb'
      value: 'Click Here'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_services'
      language: '@language_en_gb'
      value: 'Services'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_locations'
      language: '@language_en_gb'
      value: 'Locations'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_approver'
      language: '@language_en_gb'
      value: 'Approver'
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_safety_learnings_reviewer'
      language: '@language_en_gb'
      value: 'Reviewer'

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_rejected_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Learning Rejected Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_rejected_description'
      language: '@language_en_gb'
      value: '<p>These emails are sent to the author when a Safety Learning has been Rejected and will no longer require attention.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_update_to_draft_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Update to Draft Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_update_to_draft_description'
      language: '@language_en_gb'
      value: '<p>These emails are sent to an author when a Safety Learning they have created requires an update to the draft.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_access_to_record_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Access To Record Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_access_to_record_description'
      language: '@language_en_gb'
      value: '<p>These emails are sent to a user when they have been granted reviewer access to a Safety Learning so that they are aware that this Learning requires their attention.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_published_learning_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Published Learning Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_published_learning_description'
      language: '@language_en_gb'
      value: '<p>These emails are sent to both the author and the reviewer when a Safety Learning record has been published.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_recovered_learning_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Recovered Learning Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_recovered_learning_description'
      language: '@language_en_gb'
      value: '<p>These emails are sent to the author when a Safety Learning has been recovered from a Rejected or Expired status. It alerts the user that this Learning is active again.</p>'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_expired_learning_title'
      language: '@language_en_gb'
      value: 'Safety Learnings - Expired Learning Email'
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_safety_learnings_expired_learning_description'
      language: '@language_en_gb'
      value: "<p>These emails are sent to a Safety Learnings Reviewer when the relevent Safety Learnings' status has been manually changed by another Reviewer to 'Expired'</p>"
