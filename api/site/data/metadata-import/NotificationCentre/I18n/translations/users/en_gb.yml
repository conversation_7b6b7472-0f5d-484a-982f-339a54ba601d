entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@notification_centre_notification_centre_module_users'
      language: '@language_en_gb'
      value: Users

  # Template Types
  - fields:
      placeholder: '@notification_centre_template_type_users_new_admin_delegation'
      language: '@language_en_gb'
      value: New Admin Delegation

  # Template Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_users_from_user'
      language: '@language_en_gb'
      value: From User
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_users_to_user'
      language: '@language_en_gb'
      value: To User
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_users_admin_user'
      language: '@language_en_gb'
      value: Admin User
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_users_delegation_start_date'
      language: '@language_en_gb'
      value: Start Date
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_users_delegation_end_date'
      language: '@language_en_gb'
      value: End Date

  # Template Page
  - fields:
      placeholder: '@notification_centre_template_type_users_new_admin_delegation_title'
      language: '@language_en_gb'
      value: Users - New Admin Delegation Email
  - fields:
      placeholder: '@notification_centre_template_type_users_new_admin_delegation_description'
      language: '@language_en_gb'
      value: <p>When an access delegation is set up, both the delegating from and delegated to users are notified by email of the delegation. This ensures that all users involved in a delegation of access permissions are aware that this has occurred.</p>
