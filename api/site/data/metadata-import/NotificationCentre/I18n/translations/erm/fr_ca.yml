entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - { fields: { placeholder: '@placeholder_notification_centre_module_erm', language: '@language_fr_ca', value: 'Gestionnaire du risque de l''entreprise' } }
  # Template Type
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_acknowledgement', language: '@language_fr_ca', value: 'Accusé de réception' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_assignment', language: '@language_fr_ca', value: 'Attribution du risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_closed', language: '@language_fr_ca', value: 'Risque fermé' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_deescalated', language: '@language_fr_ca', value: 'Risque désescaladé' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_escalated', language: '@language_fr_ca', value: 'Risque escaladé' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_overdue', language: '@language_fr_ca', value: 'Risque en retard' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner_title', language: '@language_fr_ca', value: 'Propriétaire du risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_rejected', language: '@language_fr_ca', value: 'Risque rejeté' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_review_reminder', language: '@language_fr_ca', value: 'Rappel de révision de risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_submission_title', language: '@language_fr_ca', value: 'Soumission du risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner', language: '@language_fr_ca', value: 'Propriétaire du risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner_title', language: '@language_fr_ca', value: 'Propriétaire du risque' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_erm_risk_submission', language: '@language_fr_ca', value: 'Soumission du risque' } }
