entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_erm'
      language: '@language_en_gb'
      value: Enterprise Risk Manager

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner'
      language: '@language_en_gb'
      value: Risk Owner
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_closed'
      language: '@language_en_gb'
      value: Risk Closed
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_rejected'
      language: '@language_en_gb'
      value: Risk Rejected
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_assignment'
      language: '@language_en_gb'
      value: Risk Assignment
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_escalated'
      language: '@language_en_gb'
      value: Risk Escalated
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_deescalated'
      language: '@language_en_gb'
      value: Risk De-escalated
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_acknowledgement'
      language: '@language_en_gb'
      value: Acknowledgement
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_submission'
      language: '@language_en_gb'
      value: Risk Submission
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_review_reminder'
      language: '@language_en_gb'
      value: Risk Review Reminder
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_overdue'
      language: '@language_en_gb'
      value: Risk Overdue
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_acknowledgement_title'
      language: '@language_en_gb'
      value: Risk Acknowledgement
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_acknowledgement_description'
      language: '@language_en_gb'
      value: A Risk Acknowledgement email is sent to the reporter after submission. It is intended to be used to thank the reporter for submitting the record and to inform them of what they should expect in terms of future engagement or follow-up. In doing so, it keeps them engaged with the feedback loop and makes them more likely to contribute to any further dialogue and encourage future reporting.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_assignment_title'
      language: '@language_en_gb'
      value: Risk Reviewer Assignment
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_assignment_description'
      language: '@language_en_gb'
      value: A Risk Reviewer Assignment email is sent to a Risk Reviewer when a Register Owner assigns them to a risk record. It is intended to prompt the Risk Reviewer's engagement to review, edit, escalate and document any controls for the risk, where appropriate.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_closed_title'
      language: '@language_en_gb'
      value: Risk Closed
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_closed_description'
      language: '@language_en_gb'
      value: A Risk Closed email is sent to the Register Owner, Risk Reporter and/or Risk Reviewer when a risk has been marked as closed. It is not sent to the person who closed the risk record. At this point, a risk has been appropriately controlled and is no longer deemed a cause for concern.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_deescalated_title'
      language: '@language_en_gb'
      value: De-Escalated Risk
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_deescalated_description'
      language: '@language_en_gb'
      value: A De-Escalated Risk email is sent to the Register Owner of the risk register to which the risk has now been de-escalated. This email is intended to notify the Register Owner that there is a risk requiring their review for either accepting or rejecting onto their risk register.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_escalated_title'
      language: '@language_en_gb'
      value: Escalated Risk
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_escalated_description'
      language: '@language_en_gb'
      value: An Escalated Risk email is sent to the Register Owner of the risk register to which the risk has now been escalated. This email is intended to notify the Register Owner that there is a risk requiring their review for either accepting or rejecting onto their risk register.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_overdue_title'
      language: '@language_en_gb'
      value: Overdue Risk Review
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_overdue_description'
      language: '@language_en_gb'
      value: <p>The Overdue Risk email is sent to the Register Owner and Risk Reviewer {{overdueDays}} days after the overdue date set on a Risk Review record. The number of days can be configured in the Overdue Notifications section of the Notification Centre. This email only sends once. The intention of this email is to prompt the Register Owner and Risk Reviewer to conduct the review of any risks which are now outstanding.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_overdue_config_link'
      language: '@language_en_gb'
      value: <p><a href="{{configUrl}}">Click here to see the current configured overdue days</a></p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_rejected_title'
      language: '@language_en_gb'
      value: Rejected Risk
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_rejected_description'
      language: '@language_en_gb'
      value: The Rejected Risk email is sent to the Risk Reporter, Register Owner and/or Risk Reviewer when a risk is rejected. It is not sent to the person who performed the rejection of the risk record. This is intended to inform the reporter and reviewer of the current status of the risk, and allow any amendments to be made should the risk need more information for resubmission to the same or a more appropriate risk register.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_review_reminder_title'
      language: '@language_en_gb'
      value: Risk Review Reminder
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_review_reminder_description'
      language: '@language_en_gb'
      value: The Risk Review Reminder email is sent to the Register Owner and Risk Reviewer two days before the risk review is due. This is intended to provide them with the opportunity to review the risk ahead of its due date.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_submission_title'
      language: '@language_en_gb'
      value: Risk Submission
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_submission_description'
      language: '@language_en_gb'
      value: The Risk Submission email is sent to to the Register Owner once a new risk has been submitted. If there are any users who have been assigned ACL rules that match the submitted risk's location, location and service, or location and a specified risk level within the ACL rule, then the Risk Submission email is also sent to these users.<br/><br/>The purpose of this email is to inform the Register Owner of a newly submitted risk to their register so that they can responsibly manage the risk(s). It also provides awareness to the users assigned to the ACL rules of the risks raised within their location and/or service, and in some cases at a specific risk level, so they too can take appropriate action or be cognizant of trends in risks within their area. This helps with their engagement in risk management.
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner_title'
      language: '@language_en_gb'
      value: Risk Owner
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_erm_risk_owner_description'
      language: '@language_en_gb'
      value: A Risk Owner email is sent to the risk owner after submission. It is intended to be used to inform the user they have been assigned Risk Owner.

  # Module Variables
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_id'
      language: '@language_en_gb'
      value: Risk ID
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_link'
      language: '@language_en_gb'
      value: Link
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_title'
      language: '@language_en_gb'
      value: Title
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_description'
      language: '@language_en_gb'
      value: Description
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_type'
      language: '@language_en_gb'
      value: Risk Type
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_close_date'
      language: '@language_en_gb'
      value: Risk Close Date
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_open_date'
      language: '@language_en_gb'
      value: Risk Open Date
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_register'
      language: '@language_en_gb'
      value: Risk Register
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_risk_review_date'
      language: '@language_en_gb'
      value: Risk Review Date
  - fields:
      placeholder: '@placeholder_notification_centre_module_variable_erm_initial_grading'
      language: '@language_en_gb'
      value: Initial Grading

  # Template Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_erm_risk_owner'
      language: '@language_en_gb'
      value: Risk Owner
