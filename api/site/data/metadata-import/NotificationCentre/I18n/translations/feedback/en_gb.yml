entityClass: I18n\Entity\Translation
priority: 15
data:
  # Module
  - fields:
      placeholder: '@placeholder_notification_centre_module_feedback'
      language: '@language_en_gb'
      value: Feedback

  # Template Types
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_acknowledgement'
      language: '@language_en_gb'
      value: Acknowledgement
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_notification'
      language: '@language_en_gb'
      value: Notifications
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_update'
      language: '@language_en_gb'
      value: Updated
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_handler'
      language: '@language_en_gb'
      value: New Handler
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_communication_and_feedback'
      language: '@language_en_gb'
      value: Communication and Feedback
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_investigator'
      language: '@language_en_gb'
      value: New Investigator
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_overdue'
      language: '@language_en_gb'
      value: Overdue
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_reporter_progress'
      language: '@language_en_gb'
      value: Reporter progress notification

  # Template Variables
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_record_id'
      language: '@language_en_gb'
      value: Feedback ID
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_title'
      language: '@language_en_gb'
      value: Title
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_description'
      language: '@language_en_gb'
      value: Description
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_grade'
      language: '@language_en_gb'
      value: Grade
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_handler'
      language: '@language_en_gb'
      value: Handler
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_manager'
      language: '@language_en_gb'
      value: Manager
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_link'
      language: '@language_en_gb'
      value: Feedback Record Link
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_date_received'
      language: '@language_en_gb'
      value: Date Received
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_date_opened'
      language: '@language_en_gb'
      value: Opened
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_ourref'
      language: '@language_en_gb'
      value: Ref
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_method'
      language: '@language_en_gb'
      value: Method
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_type'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_type'
      language: '@language_en_gb'
      value: Sub-Type
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_subject'
      language: '@language_en_gb'
      value: Sub-Subject
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_summary'
      language: '@language_en_gb'
      value: Summary
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_feedback'
      language: '@language_en_gb'
      value: Feedback
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_full_name'
      language: '@language_en_gb'
      value: Full Name
  - fields:
      placeholder: '@placeholder_notification_centre_template_variable_feedback_cnf_module'
      language: '@language_en_gb'
      value: Module

  # Template Page
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_acknowledgement_title'
      language: '@language_en_gb'
      value: Feedback - Acknowledgement Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_acknowledgement_description'
      language: '@language_en_gb'
      value: <p>A feedback acknowledgement email is sent to the reporter of feedback after they have submitted it. It is intended to be used to thank the reporter for submitting the feedback report and to inform them of what they should expect in terms of future engagement or follow-up from the feedback team. In doing so, it keeps them engaged with the feedback loop and hopes to make them more likely to contribute to any further dialogue and more likely to report future feedback.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_communication_and_feedback_title'
      language: '@language_en_gb'
      value: Feedback - Communication And Feedback Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_communication_and_feedback_description'
      language: '@language_en_gb'
      value: <p>Communication and feedback emails are sent from a feedback record to provide ad-hoc updates, ask questions or request involvement. They can be sent to users attached to the record, users in the system or to manually-added email addresses, and can be edited by the user before sending. The template defined in this area will be the default text displayed when a user navigates to this section - the user will be able to edit this in the record to suit their purpose.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_handler_title'
      language: '@language_en_gb'
      value: Feedback - New Handler Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_handler_description'
      language: '@language_en_gb'
      value: <p>A New Handler email is sent to a user who has just been assigned as the handler of a feedback record. It is intended to ensure that this person is aware of their new role in relation to this record, preventing delays in them starting to process it. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new handler.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_investigator_title'
      language: '@language_en_gb'
      value: Feedback - New Investigator Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_new_investigator_description'
      language: '@language_en_gb'
      value: <p>A New Investigator email is sent to a user who has just been assigned as the investigator of a feedback record. It is intended to ensure that this person is aware of their new role in relation to this record to prevent delays in them starting their investigation. The email can be used simply to provide a link to the record, but could also include guidance on next steps to take for the new investigator.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_notification_title'
      language: '@language_en_gb'
      value: Feedback - Notification Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_notification_description'
      language: '@language_en_gb'
      value: <p>A Notification email is sent to inform one or more users about a newly created feedback record. The recipients will be calculated based on their membership of security groups with criteria that match the new feedback. This type of email could be used to make feedback managers aware of complaints being recorded in their location in the organisation, to alert senior leaders to high-severity issues, or to flag positive feedback to the right representative for a particular specialty or service.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_overdue_title'
      language: '@language_en_gb'
      value: Feedback - Overdue Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_overdue_description'
      language: '@language_en_gb'
      value: <p>An overdue email is sent to notify people involved in the management of a feedback record that the record has been in one status for too long, or that too much time has passed since it has been created without it being marked as closed. These emails will be sent to people who are marked with particular roles on the record and will be sent only for configured statuses. </p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_reporter_progress_title'
      language: '@language_en_gb'
      value: Feedback - Reporter Progress Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_reporter_progress_description'
      language: '@language_en_gb'
      value: <p>The reporter progress notification is an email sent to the reporter of a feedback record when the approval status is changed to "Finally Approved". The email is intended to give some information on the resolution and to thank the reporter again for submitting the report. This "closes the loop", ensuring that the user feels that their input has made an impact and encouraging them to continue to submit feedback in future.</p>
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_update_title'
      language: '@language_en_gb'
      value: Feedback - Update Email
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_update_description'
      language: '@language_en_gb'
      value: <p>A feedback Update email is sent when a change has been made to a record that would have triggered a notification email to users, had those changes been present at the point the feedback was created. This can be helpful when records that are logged with incorrect data but subsequently corrected so that the correct people are still notified. To prevent large numbers of identical emails from being sent, users will only receive an update email if they would not have received one the previous time the record was saved. Note that therefore this will therefore not notify users of all updates to the record.</p>

  # Template Page Links
  - fields:
      placeholder: '@placeholder_notification_centre_template_type_feedback_overdue_config_link'
      language: '@language_en_gb'
      value: <p>These emails are sent nightly until the feedback is no longer overdue.</p><p><a href="{{configUrl}}">Click here to see the currently configured combinations of roles and statuses that will trigger overdue feedback emails.</a></p>
