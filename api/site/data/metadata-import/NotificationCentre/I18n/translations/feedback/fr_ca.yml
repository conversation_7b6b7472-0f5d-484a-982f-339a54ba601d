entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_notification_centre_module_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_acknowledgement', language: '@language_fr_ca', value: 'Accusé de réception' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_notification', language: '@language_fr_ca', value: Notifications } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_update', language: '@language_fr_ca', value: 'Mis à jour' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_new_handler', language: '@language_fr_ca', value: 'Nouveau gestionnaire' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_communication_and_feedback', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_new_investigator', language: '@language_fr_ca', value: 'Nouvel enquêteur' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_overdue', language: '@language_fr_ca', value: Échu } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_type_feedback_reporter_progress', language: '@language_fr_ca', value: 'Avis de progrès au signaleur' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_record_id', language: '@language_fr_ca', value: 'ID de la rétroaction' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_grade', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_handler', language: '@language_fr_ca', value: Responsable } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_manager', language: '@language_fr_ca', value: Gestionnaire } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_link', language: '@language_fr_ca', value: 'Lien du dossier de rétroaction' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_date_received', language: '@language_fr_ca', value: 'Date de réception' } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_date_opened', language: '@language_fr_ca', value: Ouvert } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_ourref', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_method', language: '@language_fr_ca', value: Méthode } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_type', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_sub_subject', language: '@language_fr_ca', value: Sous-objet } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@placeholder_notification_centre_template_variable_feedback_full_name', language: '@language_fr_ca', value: 'Nom complet' } }
