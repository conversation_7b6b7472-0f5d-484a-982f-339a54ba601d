entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.ACTIONS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_module_actions

  # Template Types
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.NEW
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_new
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.UPDATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_update
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.REMINDER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_reminder
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNEE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_overdue_assignee
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_overdue_assigner
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.COMPLETE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_complete
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.PROGRESS_NOTE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_actions_progress_note

  # Template Variables
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.TITLE
      pointer: ACTIONS.FORM.FIELDS.TITLE.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.TYPE
      pointer: ACTIONS.FORM.FIELDS.ACTION_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.DUE_DATE
      pointer: ACTIONS.FORM.FIELDS.DUE_DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_due_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.PRIORITY
      pointer: ACTIONS.FORM.FIELDS.PRIORITY.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_priority
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.STATUS
      pointer: ACTIONS.FORM.FIELDS.STATUS.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_status
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.ACTION_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_action_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.RECORD_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_record_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.SUMMARY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_summary
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.PROGRESS_NOTE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_progress_note
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.ID
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.DESCRIPTION
      pointer: ACTIONS.FORM.FIELDS.DESCRIPTION.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.SOURCE_MODULE_LINE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_source_module_line
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.COMPLETED_BY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_completed_by
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.ASSIGNED_BY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_assigned_by
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ACTIONS.DUE_IN
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_actions_due_in

  # Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.COMPLETE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_complete_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.COMPLETE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_complete_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.NEW.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_new_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.NEW.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_new_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNEE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assignee_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNEE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assignee_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assigner_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assigner_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.PROGRESS_NOTE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_progress_note_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.PROGRESS_NOTE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_progress_note_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.REMINDER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_reminder_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.REMINDER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_reminder_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.UPDATE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_update_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.UPDATE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_update_description

  # Template Page Links
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNEE.CONFIG_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assignee_config_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNER.CONFIG_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_overdue_assigner_config_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.REMINDER.CONFIG_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_actions_reminder_config_link
