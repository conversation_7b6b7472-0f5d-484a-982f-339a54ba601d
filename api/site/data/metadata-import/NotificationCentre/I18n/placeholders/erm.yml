entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.ERM
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_erm

  # Template Types
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_CLOSED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_closed
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REJECTED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_rejected
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ASSIGNMENT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_assignment
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ESCALATED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_escalated
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_DEESCALATED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_deescalated
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ACKNOWLEDGEMENT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_acknowledgement
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_SUBMISSION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_submission
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REVIEW_REMINDER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_review_reminder
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OVERDUE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_overdue
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ACKNOWLEDGEMENT.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_acknowledgement_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ACKNOWLEDGEMENT.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_acknowledgement_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ASSIGNMENT.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_assignment_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ASSIGNMENT.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_assignment_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_CLOSED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_closed_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_CLOSED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_closed_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_DEESCALATED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_deescalated_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_DEESCALATED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_deescalated_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ESCALATED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_escalated_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_ESCALATED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_escalated_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OVERDUE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_overdue_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OVERDUE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_overdue_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REJECTED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_rejected_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REJECTED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_rejected_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REVIEW_REMINDER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_review_reminder_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_REVIEW_REMINDER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_review_reminder_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_SUBMISSION.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_submission_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_SUBMISSION.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_submission_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OWNER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_owner
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OWNER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_owner_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OWNER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_erm_risk_owner_description

  # Module Variables
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_ID
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_TYPE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_CLOSE_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_close_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_OPEN_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_open_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_REGISTER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_register
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.RISK_REVIEW_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_risk_review_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.ERM.INITIAL_GRADING
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_erm_initial_grading

  # Template Variables
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.ERM.RISK_OWNER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_erm_risk_owner
