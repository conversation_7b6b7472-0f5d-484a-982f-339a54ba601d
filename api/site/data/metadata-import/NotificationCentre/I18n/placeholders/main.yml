entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: notification_centre_module_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.PLURAL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: notification_centre_templates_plural
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: notification_centre_templates_description

  # Template Dashboard
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.COLUMNS.ID
      type: 0
      pointer: COMMON.ID
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_columns_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.COLUMNS.TYPE
      type: 0
      pointer: COMMON.TYPE
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_columns_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.COLUMNS.MODULE
      type: 0
      pointer: COMMON.MODULE
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_columns_module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.COLUMNS.NAME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_columns_name
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.COLUMNS.LOCATION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_columns_location

  # Template Form
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.NEW_TEMPLATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_new_template
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.EDIT_TEMPLATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_edit_template
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.FIELDS.NAME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_fields_name
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.FIELDS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_fields_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.FIELDS.LOCATION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_fields_location
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.FIELDS.MODULE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_fields_module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.FIELDS.TEMPLATE_TYPE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_fields_template_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.CONTENTS.SUBJECT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_contents_subject
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.CONTENTS.BODY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_contents_body
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.SAVE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_save
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.DELETE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_delete
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.CONTENTS.ONE_LANGUAGE_REQUIRED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_contents_one_language_required
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.TEMPLATE_LOCATION_EXISTS.POPUP_ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_template_location_exists_popup_error
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.TEMPLATE_LOCATION_EXISTS.ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_template_location_exists_error
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.TEMPLATE_LOCATION_EXISTS.LINK_TO_RECORD
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_templates_template_location_exists_link_to_record
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.DELETE_SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_delete_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.CANNOT_DELETE_DEFAULT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_cannot_delete_default
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATES.SAVE_SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_template_save_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.NOTIFICATIONS.OVERDUE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_notifications_overdue
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.NAV.VIEW_AND_CREATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_nav_view_and_create

  # Overdue
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.INCIDENTS.TITLE
      type: 0
      pointer: MODULE.TITLE.INCIDENTS
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_incidents_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.FEEDBACK.TITLE
      type: 0
      pointer: MODULE.TITLE.FEEDBACK
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_feedback_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.MORTALITY_REVIEW.TITLE
      type: 0
      pointer: MODULE.TITLE.MORTALITY_REVIEW
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_mortality_review_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ROLES.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_roles_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.STATUSES.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_statuses_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ACTIONS.TITLE
      type: 0
      pointer: MODULE.TITLE.ACTION
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_actions_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.OVERDUE_MESSAGES_HEADING
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_overdue_messages_heading
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ROLE_HEADING
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_role_heading
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ROLE_LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_role_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.STATUS_HEADING
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_status_heading
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.STATUS_LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_status_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.RULE_SAVE.SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_rule_save_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.RULE_REMOVAL.SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_rule_removal_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.INVALID_RULE.MISSING_VALUE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_invalid_rule_missing_value
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.INVALID_RULE.DUPLICATE_RULE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_invalid_rule_duplicate_rule
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ACTION_ASSIGNERS.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_action_assigners_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.NO_RULES
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_no_rules
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.FILTER.MODULES.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_filter_modules_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.FILTER.MODULES.DEFAULT_OPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_filter_modules_default_option
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.REMINDER_DAYS.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_reminder_days_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.OVERDUE_DAYS.LABEL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_overdue_days_label
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.REMINDER_DAYS.ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_reminder_days_error
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.OVERDUE_DAYS.ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_overdue_overdue_days_error

    #Domain whitelist
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.DOMAIN_WHITELIST
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_domain_whitelist

  - fields:
      placeholder: PLACEHOLDER.NAV.NOTIFICATION_CENTRE.DOMAIN_WHITELIST
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_nav_notification_centre_domain_whitelist

  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.DOMAIN_WHITELIST.DOMAIN
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_domain_whitelist_domain

  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ACTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_domain_whitelist_action

  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.DOMAIN_WHITELIST.HELP_MESSAGE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: placeholder_notification_centre_domain_whitelist_help_message

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.FILTER.TITLE
      type: 0
      domains:
          - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_filter_title

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.SAVING_NETWORK_ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_error_saving_network_error

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.NUM_DOMAIN_SAVED
      type: 0
      domains:
          - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_num_domain_saved

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.SAVE_MESSAGE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_error_save_message

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.DELETE.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_delete_success_message

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.DELETE_MESSAGE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_error_delete_message

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.INVALID_DOMAIN
      type: 0
      domains:
        - domain: '@domain_admin'
    ref: notification_centre_domain_whitelist_error_invalid_domain

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.DOMAIN_INVALID_CHARACTERS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_error_domain_invalid_characters

  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERROR.DOMAIN_INVALID_FORMAT
      type: 0
      domains:
        - domain: '@domain_admin'
    ref: notification_centre_domain_whitelist_error_domain_invalid_format

    #audit
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.NOTIFICATIONS.AUDIT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.NOTIFICATIONS.AUDIT.SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.NOTIFICATIONS.AUDIT.FAILURE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_failure
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.AUDIT.COLUMNS.RECIPIENT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_columns_recipient
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.AUDIT.COLUMNS.MODULE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_columns_module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.AUDIT.COLUMNS.TYPE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_columns_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.AUDIT.COLUMNS.DATE_OF_EMAIL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_columns_date_of_email
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.AUDIT.COLUMNS.ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_notification_centre_audit_columns_error
  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERRORS.CREATE_BATCH.DUPLICATE
      type: 0
      domains:
        - domain: '@domain_admin'
    ref: notification_centre_domain_whitelist_errors_create_batch_duplicate
  - fields:
      placeholder: NOTIFICATION_CENTRE.DOMAIN_WHITELIST.ERRORS.CREATE_BATCH.UNKNOWN_ERROR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
    ref: notification_centre_domain_whitelist_errors_create_batch_unknown_error
