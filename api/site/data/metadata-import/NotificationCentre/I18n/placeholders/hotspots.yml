entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.HOTSPOTS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_hotspots

  # Template Types
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.RESOLVE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_resolve
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ACTIVE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_active
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.WATCHERS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_watchers
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ARCHIVED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_archived
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.TRIGGER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_trigger

  # Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.RESOLVE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_resolve_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.RESOLVE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_resolve_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ACTIVE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_active_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ACTIVE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_active_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.WATCHERS.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_watchers_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.WATCHERS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_watchers_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ARCHIVED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_archived_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.ARCHIVED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_archived_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.TRIGGER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_trigger_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.HOTSPOTS.TRIGGER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_hotspots_trigger_description
