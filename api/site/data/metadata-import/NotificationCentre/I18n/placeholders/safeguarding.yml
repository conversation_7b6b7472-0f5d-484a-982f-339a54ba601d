entityClass: I18n\Entity\Placeholder
priority: 10
data:

  # Core
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.SAFEGUARDING
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_module_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.NOTIFICATION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_notification
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.UPDATE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_update
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.ACKNOWLEDGEMENT
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_acknowledgement
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.REPORTER_FEEDBACK
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_reporter_feedback
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.RECORD_ID
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_record_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.REFERENCE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_reference
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.LINK
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.NAME
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_name
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.DATE_REPORTED
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_date_reported
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.DATE_CLOSED
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_date_closed
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.LOCATION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_location
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.SERVICE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_service
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.IS_CHILD_CONNECTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_child_connection
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.HANDLER
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_handler
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.MANAGER
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_manager
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.APPROVAL_STATUS
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_approval_status
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.SAFEGUARDING.ALLEGED_ABUSE_TYPE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_variable_alleged_abuse_type
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.SAFEGUARDING.IS_THE_REPORT_IN_CONNECTION_WITH_A_CHILD
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_module_variable_is_the_report_in_connection_with_a_child

    # Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.NOTIFICATION.TITLE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_notification_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.NOTIFICATION.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_notification_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.UPDATE.TITLE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_update_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.UPDATE.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_update_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.ACKNOWLEDGEMENT.TITLE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_acknowledgement_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.ACKNOWLEDGEMENT.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_acknowledgement_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.REPORTER_FEEDBACK.TITLE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_reporter_feedback_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.REPORTER_FEEDBACK.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_reporter_feedback_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.COMMUNICATION_AND_FEEDBACK.TITLE
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_communication_and_feedback_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.COMMUNICATION_AND_FEEDBACK.DESCRIPTION
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_communication_and_feedback_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.SAFEGUARDING.COMMUNICATION_AND_FEEDBACK
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_safeguarding_template_type_communication_and_feedback
