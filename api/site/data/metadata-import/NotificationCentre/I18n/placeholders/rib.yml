entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.RIB
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_rib

  # Template Types
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.CREATED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_created
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.ASSIGNED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_assigned
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.REPORT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_report
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_A
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_a
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_AB
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_ab
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.SUCCESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_success
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.FAILURE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_failure

  # Template Variables
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.RECORD_ID
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_record_id
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.LOCATIONS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_locations
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.SERVICES
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_services
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.STATUS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_status
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.RECORD_SOURCE_REF
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_record_source_ref
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.SAC_SCORE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_sac_score
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.INCIDENT_SUMMARY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_incident_summary
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REASON_INCIDENT_IS_BEING_REPORTED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_reason_incident_is_being_reported
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REASON_FOR_REPORTING
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_reason_for_reporting
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REPORTED_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_reported_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.INCIDENT_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_incident_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.INCIDENT_TIME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_incident_time
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.CATEGORY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_category
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.ACTION_TAKEN_SUMMARY
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_action_taken_summary
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.DISCLOSURE_PROCESS_COMMENCED
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_disclosure_process_commenced
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.COMMENCEMENT_DATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_commencement_date
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REASON_FOR_COMMENCING_OPEN_DISCLOSURE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_reason_for_commencing_open_disclosure
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REFERRAL_TO_OTHER_AGENCIES
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_referral_to_other_agencies
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.OTHER_REFERRED_AGENCIES
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_other_referred_agencies
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.NAME_OF_RIB_CONTACT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_name_of_rib_contact
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.PHONE_NUMBER_OF_RIB_CONTACT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_phone_number_of_rib_contact
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.EMAIL_ADDRESS_OF_RIB_CONTACT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_email_address_of_rib_contact
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.REPORT_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_report_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.TEMPLATE_NAME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_template_name
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.RIB.OWNER_EMAIL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_rib_owner_email

  # Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.CREATED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_created_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.CREATED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_created_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.ASSIGNED.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_assigned_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.ASSIGNED.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_assigned_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.REPORT.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_report_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.REPORT.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_report_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_A.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_a_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_A.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_a_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_AB.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_ab_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.PART_AB.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_part_ab_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.SUCCESS.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_success_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.SUCCESS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_success_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.FAILURE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_failure_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.RIB.FAILURE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_rib_failure_description
