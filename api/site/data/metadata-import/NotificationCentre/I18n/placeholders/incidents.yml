entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Module
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE.INCIDENTS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_module_incidents

  # Template Types
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.ACKNOWLEDGEMENT
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_acknowledgement
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_HANDLER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_new_handler
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_INVESTIGATOR
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_new_investigator
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_overdue
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.REPORTER
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_reporter
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.UPDATE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_update
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NOTIFICATION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_notification
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.REPORTER_PROGRESS
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_reporter_progress

  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.COMMUNICATION_AND_FEEDBACK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_communication_and_feedback

  # Template Page
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.ACKNOWLEDGEMENT.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_acknowledgement_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.ACKNOWLEDGEMENT.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_acknowledgement_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_HANDLER.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_new_handler_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_HANDLER.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_new_handler_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_INVESTIGATOR.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_new_investigator_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NEW_INVESTIGATOR.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_new_investigator_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NOTIFICATION.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_notification_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.NOTIFICATION.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_notification_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.UPDATE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_update_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.UPDATE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_update_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.REPORTER_PROGRESS.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_reporter_progress_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.REPORTER_PROGRESS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_reporter_progress_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.COMMUNICATION_AND_FEEDBACK.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_communication_and_feedback_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.COMMUNICATION_AND_FEEDBACK.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_communication_and_feedback_description
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE.TITLE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_overdue_title
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_type_incidents_overdue_description

  # Template Page Links
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE.CONFIG_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_overdue_config_link
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE.DOCS_LINK
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: notification_centre_notification_centre_template_type_incidents_overdue_docs_link

  # Communication & Feedback
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.INCIDENTS.FULL_NAME
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_incidents_cnf_fullname
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_VARIABLE.INCIDENTS.MODULE
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_template_variable_incidents_cnf_module

  # Module variables
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.INCIDENTS.MAX_PHYSICAL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_incidents_max_physical
  - fields:
      placeholder: PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_VARIABLE.INCIDENTS.MAX_PSYCHOLOGICAL
      type: 0
      domains:
        - domain: '@domain_notification_centre'
        - domain: '@domain_admin'
    ref: placeholder_notification_centre_module_variable_incidents_max_psychological
