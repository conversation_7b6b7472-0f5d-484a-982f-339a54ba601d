entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@contacts_create', language: '@language_en_us', value: Create Contact } }
    - { fields: { placeholder: '@contacts_datasource_languages_akan', language: '@language_en_us', value: Akan } }
    - { fields: { placeholder: '@contacts_datasource_languages_amharic', language: '@language_en_us', value: Amharic } }
    - { fields: { placeholder: '@contacts_datasource_languages_arabic', language: '@language_en_us', value: Arabic } }
    - { fields: { placeholder: '@contacts_datasource_languages_assamese', language: '@language_en_us', value: Assamese } }
    - { fields: { placeholder: '@contacts_datasource_languages_awadhi', language: '@language_en_us', value: Awadhi } }
    - { fields: { placeholder: '@contacts_datasource_languages_azerbaijani', language: '@language_en_us', value: Azerbaijani } }
    - { fields: { placeholder: '@contacts_datasource_languages_balochi', language: '@language_en_us', value: Balochi } }
    - { fields: { placeholder: '@contacts_datasource_languages_belarusian', language: '@language_en_us', value: Belarusian } }
    - { fields: { placeholder: '@contacts_datasource_languages_bengali', language: '@language_en_us', value: Bengali (Bangla) } }
    - { fields: { placeholder: '@contacts_datasource_languages_bhojpuri', language: '@language_en_us', value: Bhojpuri } }
    - { fields: { placeholder: '@contacts_datasource_languages_burmese', language: '@language_en_us', value: Burmese } }
    - { fields: { placeholder: '@contacts_datasource_languages_cebuano', language: '@language_en_us', value: Cebuano (Visayan) } }
    - { fields: { placeholder: '@contacts_datasource_languages_chewa', language: '@language_en_us', value: Chewa } }
    - { fields: { placeholder: '@contacts_datasource_languages_chhattisgarhi', language: '@language_en_us', value: Chhattisgarhi } }
    - { fields: { placeholder: '@contacts_datasource_languages_chittagonian', language: '@language_en_us', value: Chittagonian } }
    - { fields: { placeholder: '@contacts_datasource_languages_czech', language: '@language_en_us', value: Czech } }
    - { fields: { placeholder: '@contacts_datasource_languages_deccan', language: '@language_en_us', value: Deccan } }
    - { fields: { placeholder: '@contacts_datasource_languages_dhundhari', language: '@language_en_us', value: Dhundhari } }
    - { fields: { placeholder: '@contacts_datasource_languages_dutch', language: '@language_en_us', value: Dutch } }
    - { fields: { placeholder: '@contacts_datasource_languages_english', language: '@language_en_us', value: English } }
    - { fields: { placeholder: '@contacts_datasource_languages_french', language: '@language_en_us', value: French } }
    - { fields: { placeholder: '@contacts_datasource_languages_fula', language: '@language_en_us', value: Fula } }
    - { fields: { placeholder: '@contacts_datasource_languages_fuzhounese', language: '@language_en_us', value: Eastern Min (inc. Fuzhounese) } }
    - { fields: { placeholder: '@contacts_datasource_languages_gan_chinese', language: '@language_en_us', value: Gan Chinese } }
    - { fields: { placeholder: '@contacts_datasource_languages_german', language: '@language_en_us', value: German } }
    - { fields: { placeholder: '@contacts_datasource_languages_greek', language: '@language_en_us', value: Greek } }
    - { fields: { placeholder: '@contacts_datasource_languages_gujarati', language: '@language_en_us', value: Gujarati } }
    - { fields: { placeholder: '@contacts_datasource_languages_haitian_creole', language: '@language_en_us', value: Haitian Creole } }
    - { fields: { placeholder: '@contacts_datasource_languages_hakka', language: '@language_en_us', value: Hakka } }
    - { fields: { placeholder: '@contacts_datasource_languages_haryanvi', language: '@language_en_us', value: Haryanvi } }
    - { fields: { placeholder: '@contacts_datasource_languages_hausa', language: '@language_en_us', value: Hausa } }
    - { fields: { placeholder: '@contacts_datasource_languages_hiligaynon_ilonggo', language: '@language_en_us', value: Hiligaynon/Ilonggo (Visayan) } }
    - { fields: { placeholder: '@contacts_datasource_languages_hindi', language: '@language_en_us', value: Hindi } }
    - { fields: { placeholder: '@contacts_datasource_languages_hmong', language: '@language_en_us', value: Hmong } }
    - { fields: { placeholder: '@contacts_datasource_languages_hungarian', language: '@language_en_us', value: Hungarian } }
    - { fields: { placeholder: '@contacts_datasource_languages_igbo', language: '@language_en_us', value: Igbo } }
    - { fields: { placeholder: '@contacts_datasource_languages_ilocano', language: '@language_en_us', value: Ilocano } }
    - { fields: { placeholder: '@contacts_datasource_languages_indian', language: '@language_en_us', value: Indian } }
    - { fields: { placeholder: '@contacts_datasource_languages_italian', language: '@language_en_us', value: Italian } }
    - { fields: { placeholder: '@contacts_datasource_languages_japanese', language: '@language_en_us', value: Japanese } }
    - { fields: { placeholder: '@contacts_datasource_languages_javanese', language: '@language_en_us', value: Javanese } }
    - { fields: { placeholder: '@contacts_datasource_languages_jin', language: '@language_en_us', value: Jin } }
    - { fields: { placeholder: '@contacts_datasource_languages_kannada', language: '@language_en_us', value: Kannada } }
    - { fields: { placeholder: '@contacts_datasource_languages_kazakh', language: '@language_en_us', value: Kazakh } }
    - { fields: { placeholder: '@contacts_datasource_languages_khmer', language: '@language_en_us', value: Khmer } }
    - { fields: { placeholder: '@contacts_datasource_languages_kinyarwanda', language: '@language_en_us', value: Kinyarwanda } }
    - { fields: { placeholder: '@contacts_datasource_languages_kirundi', language: '@language_en_us', value: Kirundi } }
    - { fields: { placeholder: '@contacts_datasource_languages_konkani', language: '@language_en_us', value: Konkani } }
    - { fields: { placeholder: '@contacts_datasource_languages_korean', language: '@language_en_us', value: Korean } }
    - { fields: { placeholder: '@contacts_datasource_languages_kurdish', language: '@language_en_us', value: Kurdish } }
    - { fields: { placeholder: '@contacts_datasource_languages_madurese', language: '@language_en_us', value: Madurese } }
    - { fields: { placeholder: '@contacts_datasource_languages_magahi', language: '@language_en_us', value: Magahi } }
    - { fields: { placeholder: '@contacts_datasource_languages_maithili', language: '@language_en_us', value: Maithili } }
    - { fields: { placeholder: '@contacts_datasource_languages_malagasy', language: '@language_en_us', value: Malagasy } }
    - { fields: { placeholder: '@contacts_datasource_languages_malay', language: '@language_en_us', value: Malay (inc. Malaysian and Indonesian) } }
    - { fields: { placeholder: '@contacts_datasource_languages_malayalam', language: '@language_en_us', value: Malayalam } }
    - { fields: { placeholder: '@contacts_datasource_languages_mandarin', language: '@language_en_us', value: Mandarin } }
    - { fields: { placeholder: '@contacts_datasource_languages_marathi', language: '@language_en_us', value: Marathi } }
    - { fields: { placeholder: '@contacts_datasource_languages_marwari', language: '@language_en_us', value: Marwari } }
    - { fields: { placeholder: '@contacts_datasource_languages_mossi', language: '@language_en_us', value: Mossi } }
    - { fields: { placeholder: '@contacts_datasource_languages_nepali', language: '@language_en_us', value: Nepali } }
    - { fields: { placeholder: '@contacts_datasource_languages_northern_min', language: '@language_en_us', value: Northern Min } }
    - { fields: { placeholder: '@contacts_datasource_languages_odia', language: '@language_en_us', value: Odia (Oriya) } }
    - { fields: { placeholder: '@contacts_datasource_languages_oromo', language: '@language_en_us', value: Oromo } }
    - { fields: { placeholder: '@contacts_datasource_languages_pashto', language: '@language_en_us', value: Pashto } }
    - { fields: { placeholder: '@contacts_datasource_languages_persian', language: '@language_en_us', value: Persian } }
    - { fields: { placeholder: '@contacts_datasource_languages_polish', language: '@language_en_us', value: Polish } }
    - { fields: { placeholder: '@contacts_datasource_languages_portuguese', language: '@language_en_us', value: Portuguese } }
    - { fields: { placeholder: '@contacts_datasource_languages_punjabi', language: '@language_en_us', value: Punjabi } }
    - { fields: { placeholder: '@contacts_datasource_languages_quechua', language: '@language_en_us', value: Quechua } }
    - { fields: { placeholder: '@contacts_datasource_languages_romanian', language: '@language_en_us', value: Romanian } }
    - { fields: { placeholder: '@contacts_datasource_languages_russian', language: '@language_en_us', value: Russian } }
    - { fields: { placeholder: '@contacts_datasource_languages_saraiki', language: '@language_en_us', value: Saraiki } }
    - { fields: { placeholder: '@contacts_datasource_languages_serbo_croatian', language: '@language_en_us', value: Serbo-Croatian } }
    - { fields: { placeholder: '@contacts_datasource_languages_shona', language: '@language_en_us', value: Shona } }
    - { fields: { placeholder: '@contacts_datasource_languages_sindhi', language: '@language_en_us', value: Sindhi } }
    - { fields: { placeholder: '@contacts_datasource_languages_sinhalese', language: '@language_en_us', value: Sinhalese } }
    - { fields: { placeholder: '@contacts_datasource_languages_somali', language: '@language_en_us', value: Somali } }
    - { fields: { placeholder: '@contacts_datasource_languages_southern_min', language: '@language_en_us', value: Southern Min (incl. Hokkien and Teochew) } }
    - { fields: { placeholder: '@contacts_datasource_languages_spanish', language: '@language_en_us', value: Spanish } }
    - { fields: { placeholder: '@contacts_datasource_languages_sundanese', language: '@language_en_us', value: Sundanese } }
    - { fields: { placeholder: '@contacts_datasource_languages_swedish', language: '@language_en_us', value: Swedish } }
    - { fields: { placeholder: '@contacts_datasource_languages_sylheti', language: '@language_en_us', value: Sylheti } }
    - { fields: { placeholder: '@contacts_datasource_languages_tagalog', language: '@language_en_us', value: Tagalog } }
    - { fields: { placeholder: '@contacts_datasource_languages_tamil', language: '@language_en_us', value: Tamil } }
    - { fields: { placeholder: '@contacts_datasource_languages_telugu', language: '@language_en_us', value: Telugu } }
    - { fields: { placeholder: '@contacts_datasource_languages_thai', language: '@language_en_us', value: Thai } }
    - { fields: { placeholder: '@contacts_datasource_languages_turkish', language: '@language_en_us', value: Turkish } }
    - { fields: { placeholder: '@contacts_datasource_languages_turkmen', language: '@language_en_us', value: Turkmen } }
    - { fields: { placeholder: '@contacts_datasource_languages_ukrainian', language: '@language_en_us', value: Ukrainian } }
    - { fields: { placeholder: '@contacts_datasource_languages_urdu', language: '@language_en_us', value: Urdu } }
    - { fields: { placeholder: '@contacts_datasource_languages_uyghur', language: '@language_en_us', value: Uyghur } }
    - { fields: { placeholder: '@contacts_datasource_languages_uzbek', language: '@language_en_us', value: Uzbek } }
    - { fields: { placeholder: '@contacts_datasource_languages_vietnamese', language: '@language_en_us', value: Vietnamese } }
    - { fields: { placeholder: '@contacts_datasource_languages_wu', language: '@language_en_us', value: Wu (inc. Shanghainese) } }
    - { fields: { placeholder: '@contacts_datasource_languages_xhosa', language: '@language_en_us', value: Xhosa } }
    - { fields: { placeholder: '@contacts_datasource_languages_xiang', language: '@language_en_us', value: Xiang (Hunnanese) } }
    - { fields: { placeholder: '@contacts_datasource_languages_yoruba', language: '@language_en_us', value: Yoruba } }
    - { fields: { placeholder: '@contacts_datasource_languages_yue', language: '@language_en_us', value: Yue (incl. Cantonese) } }
    - { fields: { placeholder: '@contacts_datasource_languages_zhuang', language: '@language_en_us', value: Zhuang } }
    - { fields: { placeholder: '@contacts_datasource_languages_zulu', language: '@language_en_us', value: Zulu } }
    - { fields: { placeholder: '@contacts_datasource_disabilities', language: '@language_en_us', value: Contact Disabilities } }
    - { fields: { placeholder: '@contacts_datasource_ethnicities', language: '@language_en_us', value: Contact Ethnicities } }
    - { fields: { placeholder: '@contacts_datasource_gender', language: '@language_en_us', value: Contact Gender } }
    - { fields: { placeholder: '@contacts_datasource_languages', language: '@language_en_us', value: Contact Languages } }
    - { fields: { placeholder: '@contacts_datasource_worker', language: '@language_en_us', value: Contact Lone Worker } }
    - { fields: { placeholder: '@contacts_datasource_religions', language: '@language_en_us', value: Contact Religions } }
    - { fields: { placeholder: '@contacts_datasource_orientations', language: '@language_en_us', value: Contact Sexual Orientations } }
    - { fields: { placeholder: '@contacts_datasource_subtype', language: '@language_en_us', value: Contact Subtype } }
    - { fields: { placeholder: '@contacts_datasource_title', language: '@language_en_us', value: Contact Title } }
    - { fields: { placeholder: '@contacts_datasource_type', language: '@language_en_us', value: Contact Type } }
    - { fields: { placeholder: '@contacts_edit', language: '@language_en_us', value: Edit Contact } }
    - { fields: { placeholder: '@contacts_error_not_synced_to_capture', language: '@language_en_us', value: Capture Contact not found. Please sync Contact by clicking save at the bottom of the screen. } }
    - { fields: { placeholder: '@contacts_error_get_form', language: '@language_en_us', value: An error occurred whilst retrieving the Contact form } }
    - { fields: { placeholder: '@contacts_form_title', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@contacts_form_section_title', language: '@language_en_us', value: New Contact } }
    - { fields: { placeholder: '@contacts_ethnicity_african', language: '@language_en_us', value: African } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_asian_background', language: '@language_en_us', value: Any other Asian background } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_black_background', language: '@language_en_us', value: Any other Black background } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_ethnic_group', language: '@language_en_us', value: Any other ethnic group } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_mixed_background', language: '@language_en_us', value: Any other mixed background } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_white_background', language: '@language_en_us', value: Any other White background } }
    - { fields: { placeholder: '@contacts_ethnicity_bangladeshi', language: '@language_en_us', value: Bangladeshi } }
    - { fields: { placeholder: '@contacts_ethnicity_british', language: '@language_en_us', value: British } }
    - { fields: { placeholder: '@contacts_ethnicity_caribbean', language: '@language_en_us', value: Caribbean } }
    - { fields: { placeholder: '@contacts_ethnicity_indian', language: '@language_en_us', value: Indian } }
    - { fields: { placeholder: '@contacts_ethnicity_irish', language: '@language_en_us', value: Irish } }
    - { fields: { placeholder: '@contacts_ethnicity_pakistani', language: '@language_en_us', value: Pakistani } }
    - { fields: { placeholder: '@contacts_ethnicity_and_black_caribbean', language: '@language_en_us', value: White and Black Caribbean } }
    - { fields: { placeholder: '@contacts_ethnicity_white_and_asian', language: '@language_en_us', value: White and Asian } }
    - { fields: { placeholder: '@contacts_ethnicity_white_and_black_african', language: '@language_en_us', value: White and Black African } }
    - { fields: { placeholder: '@contacts_form_type_contact', language: '@language_en_us', value: Contact Form } }
    - { fields: { placeholder: '@contacts_form_type_main', language: '@language_en_us', value: Contact Form } }
    - { fields: { placeholder: '@contacts_form_type_contact_filter', language: '@language_en_us', value: Contact Filter Form } }
    - { fields: { placeholder: '@contacts_form_addresses_business', language: '@language_en_us', value: Business } }
    - { fields: { placeholder: '@contacts_form_addresses_city', language: '@language_en_us', value: City } }
    - { fields: { placeholder: '@contacts_form_addresses_correspondence', language: '@language_en_us', value: Correspondence } }
    - { fields: { placeholder: '@contacts_form_addresses_country', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@contacts_form_addresses_county', language: '@language_en_us', value: County } }
    - { fields: { placeholder: '@contacts_form_addresses_line1', language: '@language_en_us', value: Line 1 } }
    - { fields: { placeholder: '@contacts_form_addresses_line2', language: '@language_en_us', value: Line 2 } }
    - { fields: { placeholder: '@contacts_form_addresses_line3', language: '@language_en_us', value: Line 3 } }
    - { fields: { placeholder: '@contacts_form_addresses_plural', language: '@language_en_us', value: Addresses } }
    - { fields: { placeholder: '@contacts_form_addresses_plural_label', language: '@language_en_us', value: Addresses } }
    - { fields: { placeholder: '@contacts_form_addresses_postal', language: '@language_en_us', value: Postal } }
    - { fields: { placeholder: '@contacts_form_addresses_postcode', language: '@language_en_us', value: Postcode } }
    - { fields: { placeholder: '@contacts_form_addresses_residential', language: '@language_en_us', value: Residential } }
    - { fields: { placeholder: '@contacts_form_addresses_singular', language: '@language_en_us', value: Address } }
    - { fields: { placeholder: '@contacts_form_addresses_trading', language: '@language_en_us', value: Trading } }
    - { fields: { placeholder: '@contacts_form_addresses_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_disability_plural', language: '@language_en_us', value: Disabilities } }
    - { fields: { placeholder: '@contacts_form_disability_plural_label', language: '@language_en_us', value: Disabilities } }
    - { fields: { placeholder: '@contacts_form_disability_singular', language: '@language_en_us', value: Disability } }
    - { fields: { placeholder: '@contacts_form_email_email_address', language: '@language_en_us', value: Email Address } }
    - { fields: { placeholder: '@contacts_form_email_personal', language: '@language_en_us', value: Personal } }
    - { fields: { placeholder: '@contacts_form_email_plural', language: '@language_en_us', value: Emails } }
    - { fields: { placeholder: '@contacts_form_email_plural_label', language: '@language_en_us', value: Emails } }
    - { fields: { placeholder: '@contacts_form_email_singular', language: '@language_en_us', value: Email } }
    - { fields: { placeholder: '@contacts_form_email_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_email_work', language: '@language_en_us', value: Work } }
    - { fields: { placeholder: '@contacts_form_field_assignment_number', language: '@language_en_us', value: Assignment Number } }
    - { fields: { placeholder: '@placeholder_contacts_form_field_contact_number_filter', language: '@language_en_us', value: Contact Number } }
    - { fields: { placeholder: '@contacts_form_field_date_of_birth', language: '@language_en_us', value: Date of birth } }
    - { fields: { placeholder: '@contacts_form_field_date_of_birth_label', language: '@language_en_us', value: Date of birth } }
    - { fields: { placeholder: '@contacts_form_field_date_of_death', language: '@language_en_us', value: Date of death } }
    - { fields: { placeholder: '@contacts_form_field_date_of_death_label', language: '@language_en_us', value: Date of death } }
    - { fields: { placeholder: '@contacts_form_field_employee_status', language: '@language_en_us', value: Employee Status } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_active', language: '@language_en_us', value: Active } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_not_started', language: '@language_en_us', value: Not Started } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_select_employee_status', language: '@language_en_us', value: Select Employee Status } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_terminated', language: '@language_en_us', value: Terminated } }
    - { fields: { placeholder: '@contacts_form_field_ethnicity', language: '@language_en_us', value: Ethnicity } }
    - { fields: { placeholder: '@contacts_form_field_ethnicity_label', language: '@language_en_us', value: Ethnicity } }
    - { fields: { placeholder: '@contacts_form_field_forename', language: '@language_en_us', value: Forename } }
    - { fields: { placeholder: '@contacts_form_field_forename_label', language: '@language_en_us', value: Forename } }
    - { fields: { placeholder: '@contacts_form_field_gender', language: '@language_en_us', value: Gender } }
    - { fields: { placeholder: '@contacts_form_field_gender_female', language: '@language_en_us', value: Female } }
    - { fields: { placeholder: '@contacts_form_field_gender_label', language: '@language_en_us', value: Gender } }
    - { fields: { placeholder: '@contacts_form_field_gender_male', language: '@language_en_us', value: Male } }
    - { fields: { placeholder: '@contacts_form_field_language', language: '@language_en_us', value: Language } }
    - { fields: { placeholder: '@contacts_form_field_language_label', language: '@language_en_us', value: Language } }
    - { fields: { placeholder: '@contacts_form_field_language_select_language', language: '@language_en_us', value: Select a Language } }
    - { fields: { placeholder: '@contacts_form_field_lone_worker', language: '@language_en_us', value: Lone worker } }
    - { fields: { placeholder: '@contacts_form_field_lone_worker_label', language: '@language_en_us', value: 'Assessed as lone worker?' } }
    - { fields: { placeholder: '@contacts_form_field_notes', language: '@language_en_us', value: Notes } }
    - { fields: { placeholder: '@contacts_form_field_notes_label', language: '@language_en_us', value: Notes } }
    - { fields: { placeholder: '@contacts_form_field_numbers_filter', language: '@language_en_us', value: Contact Numbers } }
    - { fields: { placeholder: '@contacts_form_field_numbers_filter_label', language: '@language_en_us', value: 'Contact Numbers' } }
    - { fields: { placeholder: '@contacts_form_field_positions_label', language: '@language_en_us', value: Positions } }
    - { fields: { placeholder: '@contacts_form_field_positions', language: '@language_en_us', value: Positions } }
    - { fields: { placeholder: '@contacts_form_field_relationship', language: '@language_en_us', value: 'Related data' } }
    - { fields: { placeholder: '@contacts_form_field_relationship_label', language: '@language_en_us', value: 'Related data' } }
    - { fields: { placeholder: '@contacts_form_field_religion', language: '@language_en_us', value: Religion } }
    - { fields: { placeholder: '@contacts_form_field_religion_label', language: '@language_en_us', value: Religion } }
    - { fields: { placeholder: '@contacts_form_field_select_ethnicity', language: '@language_en_us', value: Select Ethnicity } }
    - { fields: { placeholder: '@contacts_form_field_select_gender', language: '@language_en_us', value: Select Gender } }
    - { fields: { placeholder: '@contacts_form_field_select_religion', language: '@language_en_us', value: Select Religion } }
    - { fields: { placeholder: '@contacts_form_field_select_sexual_orientation', language: '@language_en_us', value: Select Sexual orientation } }
    - { fields: { placeholder: '@contacts_form_field_select_subtype', language: '@language_en_us', value: Select Sub Type } }
    - { fields: { placeholder: '@contacts_form_field_select_type', language: '@language_en_us', value: Select Type } }
    - { fields: { placeholder: '@contacts_form_field_sexual_orientation', language: '@language_en_us', value: Sexual orientation } }
    - { fields: { placeholder: '@contacts_form_field_sexual_orientation_label', language: '@language_en_us', value: Sexual orientation } }
    - { fields: { placeholder: '@contacts_form_field_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@contacts_form_field_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@contacts_form_field_status_select_status', language: '@language_en_us', value: Select status } }
    - { fields: { placeholder: '@contacts_form_field_status_value_approved', language: '@language_en_us', value: Approved } }
    - { fields: { placeholder: '@contacts_form_field_status_value_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@contacts_form_field_status_value_unapproved', language: '@language_en_us', value: Unapproved } }
    - { fields: { placeholder: '@contacts_form_field_subtype', language: '@language_en_us', value: Sub type } }
    - { fields: { placeholder: '@contacts_form_field_subtype_label', language: '@language_en_us', value: Sub type } }
    - { fields: { placeholder: '@contacts_form_field_surname', language: '@language_en_us', value: Last name } }
    - { fields: { placeholder: '@contacts_form_field_surname_label', language: '@language_en_us', value: Last name } }
    - { fields: { placeholder: '@contacts_form_field_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@contacts_form_field_title_label', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@contacts_form_field_title_select_title', language: '@language_en_us', value: Select Title } }
    - { fields: { placeholder: '@contacts_form_field_title_value_adv', language: '@language_en_us', value: Adv } }
    - { fields: { placeholder: '@contacts_form_field_title_value_br', language: '@language_en_us', value: Brother } }
    - { fields: { placeholder: '@contacts_form_field_title_value_dame', language: '@language_en_us', value: Dame } }
    - { fields: { placeholder: '@contacts_form_field_title_value_dr', language: '@language_en_us', value: Doctor } }
    - { fields: { placeholder: '@contacts_form_field_title_value_elder', language: '@language_en_us', value: Elder } }
    - { fields: { placeholder: '@contacts_form_field_title_value_esq', language: '@language_en_us', value: Esq } }
    - { fields: { placeholder: '@contacts_form_field_title_value_fr', language: '@language_en_us', value: Father } }
    - { fields: { placeholder: '@contacts_form_field_title_value_lady', language: '@language_en_us', value: Lady } }
    - { fields: { placeholder: '@contacts_form_field_title_value_lord', language: '@language_en_us', value: Lord } }
    - { fields: { placeholder: '@contacts_form_field_title_value_madam', language: '@language_en_us', value: Madam } }
    - { fields: { placeholder: '@contacts_form_field_title_value_master', language: '@language_en_us', value: Master } }
    - { fields: { placeholder: '@contacts_form_field_title_value_miss', language: '@language_en_us', value: Miss } }
    - { fields: { placeholder: '@contacts_form_field_title_value_mr', language: '@language_en_us', value: Mr } }
    - { fields: { placeholder: '@contacts_form_field_title_value_mrs', language: '@language_en_us', value: Mrs } }
    - { fields: { placeholder: '@contacts_form_field_title_value_ms', language: '@language_en_us', value: Ms } }
    - { fields: { placeholder: '@contacts_form_field_title_value_pr', language: '@language_en_us', value: Pastor } }
    - { fields: { placeholder: '@contacts_form_field_title_value_prof', language: '@language_en_us', value: Professor } }
    - { fields: { placeholder: '@contacts_form_field_title_value_rabbi', language: '@language_en_us', value: Rabbi } }
    - { fields: { placeholder: '@contacts_form_field_title_value_rev', language: '@language_en_us', value: Reverend } }
    - { fields: { placeholder: '@contacts_form_field_title_value_sir', language: '@language_en_us', value: Sir } }
    - { fields: { placeholder: '@contacts_form_field_title_value_sr', language: '@language_en_us', value: Sister } }
    - { fields: { placeholder: '@contacts_form_field_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_field_type_label', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_id_numbers_inpatient', language: '@language_en_us', value: In-patient } }
    - { fields: { placeholder: '@contacts_form_id_numbers_nhs_number', language: '@language_en_us', value: NHS Number } }
    - { fields: { placeholder: '@contacts_form_id_numbers_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@contacts_form_id_numbers_patient_number', language: '@language_en_us', value: Patient Number } }
    - { fields: { placeholder: '@contacts_form_id_numbers_plural', language: '@language_en_us', value: Individual Specific Number } }
    - { fields: { placeholder: '@contacts_form_id_numbers_police_id', language: '@language_en_us', value: Police ID } }
    - { fields: { placeholder: '@contacts_form_id_numbers_singular', language: '@language_en_us', value: Individual Specific Number } }
    - { fields: { placeholder: '@contacts_form_id_numbers_staff_number', language: '@language_en_us', value: Staff Number } }
    - { fields: { placeholder: '@contacts_form_id_numbers_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_numbers_home', language: '@language_en_us', value: Home } }
    - { fields: { placeholder: '@contacts_form_numbers_mobile', language: '@language_en_us', value: Mobile } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_number', language: '@language_en_us', value: Phone Number } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_number_label', language: '@language_en_us', value: Phone Numbers } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_numbers', language: '@language_en_us', value: Phone Number } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_numbers_label', language: '@language_en_us', value: Phone Numbers } }
    - { fields: { placeholder: '@contacts_form_numbers_plural', language: '@language_en_us', value: Numbers } }
    - { fields: { placeholder: '@contacts_form_numbers_plural_label', language: '@language_en_us', value: Numbers } }
    - { fields: { placeholder: '@contacts_form_numbers_singular', language: '@language_en_us', value: Number } }
    - { fields: { placeholder: '@contacts_form_numbers_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@contacts_form_numbers_work', language: '@language_en_us', value: Work } }
    - { fields: { placeholder: '@contacts_form_save_error', language: '@language_en_us', value: An error occurred whilst saving the Contact } }
    - { fields: { placeholder: '@contacts_forms_id_numbers_default', language: '@language_en_us', value: Select a number type } }
    - { fields: { placeholder: '@contacts_forms_nationality_default', language: '@language_en_us', value: Select Nationality } }
    - { fields: { placeholder: '@contacts_id_number_types_all_types', language: '@language_en_us', value: All types } }
    - { fields: { placeholder: '@contacts_id_number_types_label', language: '@language_en_us', value: Label } }
    - { fields: { placeholder: '@contacts_id_number_types_plural', language: '@language_en_us', value: ID Number Types } }
    - { fields: { placeholder: '@contacts_id_number_types_search', language: '@language_en_us', value: Search } }
    - { fields: { placeholder: '@contacts_id_number_types_singular', language: '@language_en_us', value: ID Number Type } }
    - { fields: { placeholder: '@contacts_loading_contact', language: '@language_en_us', value: Loading Contact } }
    - { fields: { placeholder: '@contacts_loading_contacts', language: '@language_en_us', value: Loading Contacts } }
    - { fields: { placeholder: '@contacts_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@contacts_nationality_afghanistan', language: '@language_en_us', value: Afghanistan } }
    - { fields: { placeholder: '@contacts_nationality_aland_islands', language: '@language_en_us', value: Aland Islands } }
    - { fields: { placeholder: '@contacts_nationality_albania', language: '@language_en_us', value: Albania } }
    - { fields: { placeholder: '@contacts_nationality_algeria', language: '@language_en_us', value: Algeria } }
    - { fields: { placeholder: '@contacts_nationality_american_samoa', language: '@language_en_us', value: American Samoa } }
    - { fields: { placeholder: '@contacts_nationality_andorra', language: '@language_en_us', value: Andorra } }
    - { fields: { placeholder: '@contacts_nationality_angola', language: '@language_en_us', value: Angola } }
    - { fields: { placeholder: '@contacts_nationality_anguilla', language: '@language_en_us', value: Anguilla } }
    - { fields: { placeholder: '@contacts_nationality_antarctica', language: '@language_en_us', value: Antarctica } }
    - { fields: { placeholder: '@contacts_nationality_antigua_and_barbuda', language: '@language_en_us', value: Antigua and Barbuda } }
    - { fields: { placeholder: '@contacts_nationality_argentina', language: '@language_en_us', value: Argentina } }
    - { fields: { placeholder: '@contacts_nationality_armenia', language: '@language_en_us', value: Armenia } }
    - { fields: { placeholder: '@contacts_nationality_aruba', language: '@language_en_us', value: Aruba } }
    - { fields: { placeholder: '@contacts_nationality_australia', language: '@language_en_us', value: Australia } }
    - { fields: { placeholder: '@contacts_nationality_austria', language: '@language_en_us', value: Austria } }
    - { fields: { placeholder: '@contacts_nationality_azerbaijan', language: '@language_en_us', value: Azerbaijan } }
    - { fields: { placeholder: '@contacts_nationality_bahamas', language: '@language_en_us', value: Bahamas } }
    - { fields: { placeholder: '@contacts_nationality_bahrain', language: '@language_en_us', value: Bahrain } }
    - { fields: { placeholder: '@contacts_nationality_bangladesh', language: '@language_en_us', value: Bangladesh } }
    - { fields: { placeholder: '@contacts_nationality_barbados', language: '@language_en_us', value: Barbados } }
    - { fields: { placeholder: '@contacts_nationality_belarus', language: '@language_en_us', value: Belarus } }
    - { fields: { placeholder: '@contacts_nationality_belgium', language: '@language_en_us', value: Belgium } }
    - { fields: { placeholder: '@contacts_nationality_belize', language: '@language_en_us', value: Belize } }
    - { fields: { placeholder: '@contacts_nationality_benin', language: '@language_en_us', value: Benin } }
    - { fields: { placeholder: '@contacts_nationality_bermuda', language: '@language_en_us', value: Bermuda } }
    - { fields: { placeholder: '@contacts_nationality_bhutan', language: '@language_en_us', value: Bhutan } }
    - { fields: { placeholder: '@contacts_nationality_bolivia', language: '@language_en_us', value: Bolivia } }
    - { fields: { placeholder: '@contacts_nationality_bonaire_sint_eustatius_and_saba', language: '@language_en_us', value: "Bonaire, Sint Eustatius and Saba" } }
    - { fields: { placeholder: '@contacts_nationality_bosnia_and_herzegovina', language: '@language_en_us', value: Bosnia and Herzegovina } }
    - { fields: { placeholder: '@contacts_nationality_botswana', language: '@language_en_us', value: Botswana } }
    - { fields: { placeholder: '@contacts_nationality_bouvet_island', language: '@language_en_us', value: Bouvet Island } }
    - { fields: { placeholder: '@contacts_nationality_brazil', language: '@language_en_us', value: Brazil } }
    - { fields: { placeholder: '@contacts_nationality_british_indian_ocean_territory', language: '@language_en_us', value: British Indian Ocean Territory } }
    - { fields: { placeholder: '@contacts_nationality_british_virgin_islands', language: '@language_en_us', value: British Virgin Islands } }
    - { fields: { placeholder: '@contacts_nationality_brunei_darussalam', language: '@language_en_us', value: Brunei Darussalam } }
    - { fields: { placeholder: '@contacts_nationality_bulgaria', language: '@language_en_us', value: Bulgaria } }
    - { fields: { placeholder: '@contacts_nationality_burkina_faso', language: '@language_en_us', value: Burkina Faso } }
    - { fields: { placeholder: '@contacts_nationality_burundi', language: '@language_en_us', value: Burundi } }
    - { fields: { placeholder: '@contacts_nationality_cambodia', language: '@language_en_us', value: Cambodia } }
    - { fields: { placeholder: '@contacts_nationality_cameroon', language: '@language_en_us', value: Cameroon } }
    - { fields: { placeholder: '@contacts_nationality_canada', language: '@language_en_us', value: Canada } }
    - { fields: { placeholder: '@contacts_nationality_cape_verde', language: '@language_en_us', value: Cape Verde } }
    - { fields: { placeholder: '@contacts_nationality_cayman_islands', language: '@language_en_us', value: Cayman Islands } }
    - { fields: { placeholder: '@contacts_nationality_central_african_republic', language: '@language_en_us', value: Central African Republic } }
    - { fields: { placeholder: '@contacts_nationality_chad', language: '@language_en_us', value: Chad } }
    - { fields: { placeholder: '@contacts_nationality_chile', language: '@language_en_us', value: Chile } }
    - { fields: { placeholder: '@contacts_nationality_china', language: '@language_en_us', value: China } }
    - { fields: { placeholder: '@contacts_nationality_christmas_island', language: '@language_en_us', value: Christmas Island } }
    - { fields: { placeholder: '@contacts_nationality_cocos_keeling_islands', language: '@language_en_us', value: Cocos (Keeling) Islands } }
    - { fields: { placeholder: '@contacts_nationality_colombia', language: '@language_en_us', value: Colombia } }
    - { fields: { placeholder: '@contacts_nationality_comoros', language: '@language_en_us', value: Comoros } }
    - { fields: { placeholder: '@contacts_nationality_congo_brazzaville', language: '@language_en_us', value: Congo (Brazzaville) } }
    - { fields: { placeholder: '@contacts_nationality_congo_democratic_republic_of_the', language: '@language_en_us', value: "Congo, Democratic Republic of the" } }
    - { fields: { placeholder: '@contacts_nationality_cook_islands', language: '@language_en_us', value: Cook Islands } }
    - { fields: { placeholder: '@contacts_nationality_costa_rica', language: '@language_en_us', value: Costa Rica } }
    - { fields: { placeholder: '@contacts_nationality_cote_divoire', language: '@language_en_us', value: "Côte d'Ivoire" } }
    - { fields: { placeholder: '@contacts_nationality_croatia', language: '@language_en_us', value: Croatia } }
    - { fields: { placeholder: '@contacts_nationality_cuba', language: '@language_en_us', value: Cuba } }
    - { fields: { placeholder: '@contacts_nationality_curacao', language: '@language_en_us', value: "Curaçao" } }
    - { fields: { placeholder: '@contacts_nationality_cyprus', language: '@language_en_us', value: Cyprus } }
    - { fields: { placeholder: '@contacts_nationality_czech_republic', language: '@language_en_us', value: Czech Republic } }
    - { fields: { placeholder: '@contacts_nationality_denmark', language: '@language_en_us', value: Denmark } }
    - { fields: { placeholder: '@contacts_nationality_djibouti', language: '@language_en_us', value: Djibouti } }
    - { fields: { placeholder: '@contacts_nationality_dominica', language: '@language_en_us', value: Dominica } }
    - { fields: { placeholder: '@contacts_nationality_dominican_republic', language: '@language_en_us', value: Dominican Republic } }
    - { fields: { placeholder: '@contacts_nationality_ecuador', language: '@language_en_us', value: Ecuador } }
    - { fields: { placeholder: '@contacts_nationality_egypt', language: '@language_en_us', value: Egypt } }
    - { fields: { placeholder: '@contacts_nationality_el_salvador', language: '@language_en_us', value: El Salvador } }
    - { fields: { placeholder: '@contacts_nationality_equatorial_guinea', language: '@language_en_us', value: Equatorial Guinea } }
    - { fields: { placeholder: '@contacts_nationality_eritrea', language: '@language_en_us', value: Eritrea } }
    - { fields: { placeholder: '@contacts_nationality_estonia', language: '@language_en_us', value: Estonia } }
    - { fields: { placeholder: '@contacts_nationality_ethiopia', language: '@language_en_us', value: Ethiopia } }
    - { fields: { placeholder: '@contacts_nationality_falkland_islands_malvinas', language: '@language_en_us', value: Falkland Islands (Malvinas) } }
    - { fields: { placeholder: '@contacts_nationality_faroe_islands', language: '@language_en_us', value: Faroe Islands } }
    - { fields: { placeholder: '@contacts_nationality_fiji', language: '@language_en_us', value: Fiji } }
    - { fields: { placeholder: '@contacts_nationality_finland', language: '@language_en_us', value: Finland } }
    - { fields: { placeholder: '@contacts_nationality_france', language: '@language_en_us', value: France } }
    - { fields: { placeholder: '@contacts_nationality_french_guiana', language: '@language_en_us', value: French Guiana } }
    - { fields: { placeholder: '@contacts_nationality_french_polynesia', language: '@language_en_us', value: French Polynesia } }
    - { fields: { placeholder: '@contacts_nationality_french_southern_territories', language: '@language_en_us', value: French Southern Territories } }
    - { fields: { placeholder: '@contacts_nationality_gabon', language: '@language_en_us', value: Gabon } }
    - { fields: { placeholder: '@contacts_nationality_gambia', language: '@language_en_us', value: Gambia } }
    - { fields: { placeholder: '@contacts_nationality_georgia', language: '@language_en_us', value: Georgia } }
    - { fields: { placeholder: '@contacts_nationality_germany', language: '@language_en_us', value: Germany } }
    - { fields: { placeholder: '@contacts_nationality_ghana', language: '@language_en_us', value: Ghana } }
    - { fields: { placeholder: '@contacts_nationality_gibraltar', language: '@language_en_us', value: Gibraltar } }
    - { fields: { placeholder: '@contacts_nationality_greece', language: '@language_en_us', value: Greece } }
    - { fields: { placeholder: '@contacts_nationality_greenland', language: '@language_en_us', value: Greenland } }
    - { fields: { placeholder: '@contacts_nationality_grenada', language: '@language_en_us', value: Grenada } }
    - { fields: { placeholder: '@contacts_nationality_guadeloupe', language: '@language_en_us', value: Guadeloupe } }
    - { fields: { placeholder: '@contacts_nationality_guam', language: '@language_en_us', value: Guam } }
    - { fields: { placeholder: '@contacts_nationality_guatemala', language: '@language_en_us', value: Guatemala } }
    - { fields: { placeholder: '@contacts_nationality_guernsey', language: '@language_en_us', value: Guernsey } }
    - { fields: { placeholder: '@contacts_nationality_guinea', language: '@language_en_us', value: Guinea } }
    - { fields: { placeholder: '@contacts_nationality_guinea_bissau', language: '@language_en_us', value: Guinea-Bissau } }
    - { fields: { placeholder: '@contacts_nationality_guyana', language: '@language_en_us', value: Guyana } }
    - { fields: { placeholder: '@contacts_nationality_haiti', language: '@language_en_us', value: Haiti } }
    - { fields: { placeholder: '@contacts_nationality_heard_island_and_mcdonald_islands', language: '@language_en_us', value: Heard Island and McDonald Islands } }
    - { fields: { placeholder: '@contacts_nationality_holy_see_vatican_city_state', language: '@language_en_us', value: Holy See (Vatican City State) } }
    - { fields: { placeholder: '@contacts_nationality_honduras', language: '@language_en_us', value: Honduras } }
    - { fields: { placeholder: '@contacts_nationality_hong_kong_special_administrative_region_of_china', language: '@language_en_us', value: "Hong Kong, Special Administrative Region of China" } }
    - { fields: { placeholder: '@contacts_nationality_hungary', language: '@language_en_us', value: Hungary } }
    - { fields: { placeholder: '@contacts_nationality_iceland', language: '@language_en_us', value: Iceland } }
    - { fields: { placeholder: '@contacts_nationality_india', language: '@language_en_us', value: India } }
    - { fields: { placeholder: '@contacts_nationality_indonesia', language: '@language_en_us', value: Indonesia } }
    - { fields: { placeholder: '@contacts_nationality_iran_islamic_republic_of', language: '@language_en_us', value: "Iran, Islamic Republic of" } }
    - { fields: { placeholder: '@contacts_nationality_iraq', language: '@language_en_us', value: Iraq } }
    - { fields: { placeholder: '@contacts_nationality_ireland', language: '@language_en_us', value: Ireland } }
    - { fields: { placeholder: '@contacts_nationality_isle_of_man', language: '@language_en_us', value: Isle of Man } }
    - { fields: { placeholder: '@contacts_nationality_israel', language: '@language_en_us', value: Israel } }
    - { fields: { placeholder: '@contacts_nationality_italy', language: '@language_en_us', value: Italy } }
    - { fields: { placeholder: '@contacts_nationality_jamaica', language: '@language_en_us', value: Jamaica } }
    - { fields: { placeholder: '@contacts_nationality_japan', language: '@language_en_us', value: Japan } }
    - { fields: { placeholder: '@contacts_nationality_jersey', language: '@language_en_us', value: Jersey } }
    - { fields: { placeholder: '@contacts_nationality_jordan', language: '@language_en_us', value: Jordan } }
    - { fields: { placeholder: '@contacts_nationality_kazakhstan', language: '@language_en_us', value: Kazakhstan } }
    - { fields: { placeholder: '@contacts_nationality_kenya', language: '@language_en_us', value: Kenya } }
    - { fields: { placeholder: '@contacts_nationality_kiribati', language: '@language_en_us', value: Kiribati } }
    - { fields: { placeholder: '@contacts_nationality_korea_democratic_peoples_republic_of', language: '@language_en_us', value: "Korea, Democratic People's Republic of" } }
    - { fields: { placeholder: '@contacts_nationality_korea_republic_of', language: '@language_en_us', value: "Korea, Republic of" } }
    - { fields: { placeholder: '@contacts_nationality_kuwait', language: '@language_en_us', value: Kuwait } }
    - { fields: { placeholder: '@contacts_nationality_kyrgyzstan', language: '@language_en_us', value: Kyrgyzstan } }
    - { fields: { placeholder: '@contacts_nationality_label', language: '@language_en_us', value: Nationality } }
    - { fields: { placeholder: '@contacts_nationality_lao_pdr', language: '@language_en_us', value: Lao PDR } }
    - { fields: { placeholder: '@contacts_nationality_latvia', language: '@language_en_us', value: Latvia } }
    - { fields: { placeholder: '@contacts_nationality_lebanon', language: '@language_en_us', value: Lebanon } }
    - { fields: { placeholder: '@contacts_nationality_lesotho', language: '@language_en_us', value: Lesotho } }
    - { fields: { placeholder: '@contacts_nationality_liberia', language: '@language_en_us', value: Liberia } }
    - { fields: { placeholder: '@contacts_nationality_libya', language: '@language_en_us', value: Libya } }
    - { fields: { placeholder: '@contacts_nationality_liechtenstein', language: '@language_en_us', value: Liechtenstein } }
    - { fields: { placeholder: '@contacts_nationality_lithuania', language: '@language_en_us', value: Lithuania } }
    - { fields: { placeholder: '@contacts_nationality_luxembourg', language: '@language_en_us', value: Luxembourg } }
    - { fields: { placeholder: '@contacts_nationality_macao_special_administrative_region_of_china', language: '@language_en_us', value: "Macao, Special Administrative Region of China" } }
    - { fields: { placeholder: '@contacts_nationality_macedonia_republic_of', language: '@language_en_us', value: "Macedonia, Republic of" } }
    - { fields: { placeholder: '@contacts_nationality_madagascar', language: '@language_en_us', value: Madagascar } }
    - { fields: { placeholder: '@contacts_nationality_malawi', language: '@language_en_us', value: Malawi } }
    - { fields: { placeholder: '@contacts_nationality_malaysia', language: '@language_en_us', value: Malaysia } }
    - { fields: { placeholder: '@contacts_nationality_maldives', language: '@language_en_us', value: Maldives } }
    - { fields: { placeholder: '@contacts_nationality_mali', language: '@language_en_us', value: Mali } }
    - { fields: { placeholder: '@contacts_nationality_malta', language: '@language_en_us', value: Malta } }
    - { fields: { placeholder: '@contacts_nationality_marshall_islands', language: '@language_en_us', value: Marshall Islands } }
    - { fields: { placeholder: '@contacts_nationality_martinique', language: '@language_en_us', value: Martinique } }
    - { fields: { placeholder: '@contacts_nationality_mauritania', language: '@language_en_us', value: Mauritania } }
    - { fields: { placeholder: '@contacts_nationality_mauritius', language: '@language_en_us', value: Mauritius } }
    - { fields: { placeholder: '@contacts_nationality_mayotte', language: '@language_en_us', value: Mayotte } }
    - { fields: { placeholder: '@contacts_nationality_mexico', language: '@language_en_us', value: Mexico } }
    - { fields: { placeholder: '@contacts_nationality_micronesia_federated_states_of', language: '@language_en_us', value: "Micronesia, Federated States of" } }
    - { fields: { placeholder: '@contacts_nationality_moldova', language: '@language_en_us', value: Moldova } }
    - { fields: { placeholder: '@contacts_nationality_monaco', language: '@language_en_us', value: Monaco } }
    - { fields: { placeholder: '@contacts_nationality_mongolia', language: '@language_en_us', value: Mongolia } }
    - { fields: { placeholder: '@contacts_nationality_montenegro', language: '@language_en_us', value: Montenegro } }
    - { fields: { placeholder: '@contacts_nationality_montserrat', language: '@language_en_us', value: Montserrat } }
    - { fields: { placeholder: '@contacts_nationality_morocco', language: '@language_en_us', value: Morocco } }
    - { fields: { placeholder: '@contacts_nationality_mozambique', language: '@language_en_us', value: Mozambique } }
    - { fields: { placeholder: '@contacts_nationality_myanmar', language: '@language_en_us', value: Myanmar } }
    - { fields: { placeholder: '@contacts_nationality_namibia', language: '@language_en_us', value: Namibia } }
    - { fields: { placeholder: '@contacts_nationality_nauru', language: '@language_en_us', value: Nauru } }
    - { fields: { placeholder: '@contacts_nationality_nepal', language: '@language_en_us', value: Nepal } }
    - { fields: { placeholder: '@contacts_nationality_netherlands', language: '@language_en_us', value: Netherlands } }
    - { fields: { placeholder: '@contacts_nationality_netherlands_antilles', language: '@language_en_us', value: Netherlands Antilles } }
    - { fields: { placeholder: '@contacts_nationality_new_caledonia', language: '@language_en_us', value: New Caledonia } }
    - { fields: { placeholder: '@contacts_nationality_new_zealand', language: '@language_en_us', value: New Zealand } }
    - { fields: { placeholder: '@contacts_nationality_nicaragua', language: '@language_en_us', value: Nicaragua } }
    - { fields: { placeholder: '@contacts_nationality_niger', language: '@language_en_us', value: Niger } }
    - { fields: { placeholder: '@contacts_nationality_nigeria', language: '@language_en_us', value: Nigeria } }
    - { fields: { placeholder: '@contacts_nationality_niue', language: '@language_en_us', value: Niue } }
    - { fields: { placeholder: '@contacts_nationality_norfolk_island', language: '@language_en_us', value: Norfolk Island } }
    - { fields: { placeholder: '@contacts_nationality_northern_mariana_islands', language: '@language_en_us', value: Northern Mariana Islands } }
    - { fields: { placeholder: '@contacts_nationality_norway', language: '@language_en_us', value: Norway } }
    - { fields: { placeholder: '@contacts_nationality_oman', language: '@language_en_us', value: Oman } }
    - { fields: { placeholder: '@contacts_nationality_pakistan', language: '@language_en_us', value: Pakistan } }
    - { fields: { placeholder: '@contacts_nationality_palau', language: '@language_en_us', value: Palau } }
    - { fields: { placeholder: '@contacts_nationality_palestinian_territory_occupied', language: '@language_en_us', value: "Palestinian Territory, Occupied" } }
    - { fields: { placeholder: '@contacts_nationality_panama', language: '@language_en_us', value: Panama } }
    - { fields: { placeholder: '@contacts_nationality_papua_new_guinea', language: '@language_en_us', value: Papua New Guinea } }
    - { fields: { placeholder: '@contacts_nationality_paraguay', language: '@language_en_us', value: Paraguay } }
    - { fields: { placeholder: '@contacts_nationality_peru', language: '@language_en_us', value: Peru } }
    - { fields: { placeholder: '@contacts_nationality_philippines', language: '@language_en_us', value: Philippines } }
    - { fields: { placeholder: '@contacts_nationality_pitcairn', language: '@language_en_us', value: Pitcairn } }
    - { fields: { placeholder: '@contacts_nationality_poland', language: '@language_en_us', value: Poland } }
    - { fields: { placeholder: '@contacts_nationality_portugal', language: '@language_en_us', value: Portugal } }
    - { fields: { placeholder: '@contacts_nationality_puerto_rico', language: '@language_en_us', value: Puerto Rico } }
    - { fields: { placeholder: '@contacts_nationality_qatar', language: '@language_en_us', value: Qatar } }
    - { fields: { placeholder: '@contacts_nationality_reunion', language: '@language_en_us', value: Réunion } }
    - { fields: { placeholder: '@contacts_nationality_romania', language: '@language_en_us', value: Romania } }
    - { fields: { placeholder: '@contacts_nationality_russian_federation', language: '@language_en_us', value: Russian Federation } }
    - { fields: { placeholder: '@contacts_nationality_rwanda', language: '@language_en_us', value: Rwanda } }
    - { fields: { placeholder: '@contacts_nationality_saint_barthelemy', language: '@language_en_us', value: Saint-Barthélemy } }
    - { fields: { placeholder: '@contacts_nationality_saint_helena', language: '@language_en_us', value: Saint Helena } }
    - { fields: { placeholder: '@contacts_nationality_saint_kitts_and_nevis', language: '@language_en_us', value: Saint Kitts and Nevis } }
    - { fields: { placeholder: '@contacts_nationality_saint_lucia', language: '@language_en_us', value: Saint Lucia } }
    - { fields: { placeholder: '@contacts_nationality_saint_martin_french_part', language: '@language_en_us', value: Saint-Martin (French part) } }
    - { fields: { placeholder: '@contacts_nationality_saint_pierre_and_miquelon', language: '@language_en_us', value: Saint Pierre and Miquelon } }
    - { fields: { placeholder: '@contacts_nationality_saint_vincent_and_grenadines', language: '@language_en_us', value: Saint Vincent and Grenadines } }
    - { fields: { placeholder: '@contacts_nationality_samoa', language: '@language_en_us', value: Samoa } }
    - { fields: { placeholder: '@contacts_nationality_san_marino', language: '@language_en_us', value: San Marino } }
    - { fields: { placeholder: '@contacts_nationality_sao_tome_and_principe', language: '@language_en_us', value: Sao Tome and Principe } }
    - { fields: { placeholder: '@contacts_nationality_saudi_arabia', language: '@language_en_us', value: Saudi Arabia } }
    - { fields: { placeholder: '@contacts_nationality_senegal', language: '@language_en_us', value: Senegal } }
    - { fields: { placeholder: '@contacts_nationality_serbia', language: '@language_en_us', value: Serbia } }
    - { fields: { placeholder: '@contacts_nationality_seychelles', language: '@language_en_us', value: Seychelles } }
    - { fields: { placeholder: '@contacts_nationality_sierra_leone', language: '@language_en_us', value: Sierra Leone } }
    - { fields: { placeholder: '@contacts_nationality_singapore', language: '@language_en_us', value: Singapore } }
    - { fields: { placeholder: '@contacts_nationality_sint_maarten_dutch_part', language: '@language_en_us', value: Sint Maarten (Dutch part) } }
    - { fields: { placeholder: '@contacts_nationality_slovakia', language: '@language_en_us', value: Slovakia } }
    - { fields: { placeholder: '@contacts_nationality_slovenia', language: '@language_en_us', value: Slovenia } }
    - { fields: { placeholder: '@contacts_nationality_solomon_islands', language: '@language_en_us', value: Solomon Islands } }
    - { fields: { placeholder: '@contacts_nationality_somalia', language: '@language_en_us', value: Somalia } }
    - { fields: { placeholder: '@contacts_nationality_south_africa', language: '@language_en_us', value: South Africa } }
    - { fields: { placeholder: '@contacts_nationality_south_georgia_and_the_south_sandwich_islands', language: '@language_en_us', value: South Georgia and the South Sandwich Islands } }
    - { fields: { placeholder: '@contacts_nationality_south_sudan', language: '@language_en_us', value: South Sudan } }
    - { fields: { placeholder: '@contacts_nationality_spain', language: '@language_en_us', value: Spain } }
    - { fields: { placeholder: '@contacts_nationality_sri_lanka', language: '@language_en_us', value: Sri Lanka } }
    - { fields: { placeholder: '@contacts_nationality_sudan', language: '@language_en_us', value: Sudan } }
    - { fields: { placeholder: '@contacts_nationality_suriname', language: '@language_en_us', value: Suriname * } }
    - { fields: { placeholder: '@contacts_nationality_svalbard_and_jan_mayen_islands', language: '@language_en_us', value: Svalbard and Jan Mayen Islands } }
    - { fields: { placeholder: '@contacts_nationality_swaziland', language: '@language_en_us', value: Swaziland } }
    - { fields: { placeholder: '@contacts_nationality_sweden', language: '@language_en_us', value: Sweden } }
    - { fields: { placeholder: '@contacts_nationality_switzerland', language: '@language_en_us', value: Switzerland } }
    - { fields: { placeholder: '@contacts_nationality_syrian_arab_republic_syria', language: '@language_en_us', value: Syrian Arab Republic (Syria) } }
    - { fields: { placeholder: '@contacts_nationality_taiwan', language: '@language_en_us', value: Taiwan } }
    - { fields: { placeholder: '@contacts_nationality_tajikistan', language: '@language_en_us', value: Tajikistan } }
    - { fields: { placeholder: '@contacts_nationality_tanzania_united_republic_of', language: '@language_en_us', value: "Tanzania *, United Republic of" } }
    - { fields: { placeholder: '@contacts_nationality_thailand', language: '@language_en_us', value: Thailand } }
    - { fields: { placeholder: '@contacts_nationality_timor_leste', language: '@language_en_us', value: Timor-Leste } }
    - { fields: { placeholder: '@contacts_nationality_title', language: '@language_en_us', value: Nationality } }
    - { fields: { placeholder: '@contacts_nationality_togo', language: '@language_en_us', value: Togo } }
    - { fields: { placeholder: '@contacts_nationality_tokelau', language: '@language_en_us', value: Tokelau } }
    - { fields: { placeholder: '@contacts_nationality_tonga', language: '@language_en_us', value: Tonga } }
    - { fields: { placeholder: '@contacts_nationality_trinidad_and_tobago', language: '@language_en_us', value: Trinidad and Tobago } }
    - { fields: { placeholder: '@contacts_nationality_tunisia', language: '@language_en_us', value: Tunisia } }
    - { fields: { placeholder: '@contacts_nationality_turkey', language: '@language_en_us', value: Turkey } }
    - { fields: { placeholder: '@contacts_nationality_turkmenistan', language: '@language_en_us', value: Turkmenistan } }
    - { fields: { placeholder: '@contacts_nationality_turks_and_caicos_islands', language: '@language_en_us', value: Turks and Caicos Islands } }
    - { fields: { placeholder: '@contacts_nationality_tuvalu', language: '@language_en_us', value: Tuvalu } }
    - { fields: { placeholder: '@contacts_nationality_uganda', language: '@language_en_us', value: Uganda } }
    - { fields: { placeholder: '@contacts_nationality_ukraine', language: '@language_en_us', value: Ukraine } }
    - { fields: { placeholder: '@contacts_nationality_united_arab_emirates', language: '@language_en_us', value: United Arab Emirates } }
    - { fields: { placeholder: '@contacts_nationality_united_kingdom', language: '@language_en_us', value: United Kingdom } }
    - { fields: { placeholder: '@contacts_nationality_united_states_minor_outlying_islands', language: '@language_en_us', value: United States Minor Outlying Islands } }
    - { fields: { placeholder: '@contacts_nationality_united_states_of_america', language: '@language_en_us', value: United States of America } }
    - { fields: { placeholder: '@contacts_nationality_uruguay', language: '@language_en_us', value: Uruguay } }
    - { fields: { placeholder: '@contacts_nationality_uzbekistan', language: '@language_en_us', value: Uzbekistan } }
    - { fields: { placeholder: '@contacts_nationality_vanuatu', language: '@language_en_us', value: Vanuatu } }
    - { fields: { placeholder: '@contacts_nationality_venezuela_bolivarian_republic_of', language: '@language_en_us', value: Venezuela (Bolivarian Republic of) } }
    - { fields: { placeholder: '@contacts_nationality_vietnam', language: '@language_en_us', value: Viet Nam } }
    - { fields: { placeholder: '@contacts_nationality_virgin_islands_us', language: '@language_en_us', value: "Virgin Islands, US" } }
    - { fields: { placeholder: '@contacts_nationality_wallis_and_futuna_islands', language: '@language_en_us', value: Wallis and Futuna Islands } }
    - { fields: { placeholder: '@contacts_nationality_western_sahara', language: '@language_en_us', value: Western Sahara } }
    - { fields: { placeholder: '@contacts_nationality_yemen', language: '@language_en_us', value: Yemen } }
    - { fields: { placeholder: '@contacts_nationality_zambia', language: '@language_en_us', value: Zambia } }
    - { fields: { placeholder: '@contacts_nationality_zimbabwe', language: '@language_en_us', value: Zimbabwe } }
    - { fields: { placeholder: '@contacts_nav_dashboard', language: '@language_en_us', value: Dashboard } }
    - { fields: { placeholder: '@contacts_nav_new_contact', language: '@language_en_us', value: New Contact } }
    - { fields: { placeholder: '@contacts_nav_permissions', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@contacts_numbers_type_label', language: '@language_en_us', value: Number Type } }
    - { fields: { placeholder: '@contacts_numbers_type_title', language: '@language_en_us', value: Number Type } }
    - { fields: { placeholder: '@contacts_plural', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@contacts_post_save_no_access', language: '@language_en_us', value: "Contact has been saved, but the access permissions assigned to your user do not permit you to see it" } }
    - { fields: { placeholder: '@contacts_religion_atheist', language: '@language_en_us', value: Atheist } }
    - { fields: { placeholder: '@contacts_religion_buddist', language: '@language_en_us', value: Buddhist } }
    - { fields: { placeholder: '@contacts_religion_christian', language: '@language_en_us', value: Christian } }
    - { fields: { placeholder: '@contacts_religion_hindu', language: '@language_en_us', value: Hindu } }
    - { fields: { placeholder: '@contacts_religion_jewish', language: '@language_en_us', value: Jewish } }
    - { fields: { placeholder: '@contacts_religion_muslim', language: '@language_en_us', value: Muslim } }
    - { fields: { placeholder: '@contacts_religion_sikh', language: '@language_en_us', value: Sikh } }
    - { fields: { placeholder: '@contacts_religion_taoist', language: '@language_en_us', value: Taoist } }
    - { fields: { placeholder: '@contacts_search', language: '@language_en_us', value: Search Contacts } }
    - { fields: { placeholder: '@contacts_sexual_orientation_bisexual', language: '@language_en_us', value: Bisexual } }
    - { fields: { placeholder: '@contacts_sexual_orientation_gay_man', language: '@language_en_us', value: Gay man } }
    - { fields: { placeholder: '@contacts_sexual_orientation_gay_woman_lesbian', language: '@language_en_us', value: Gay woman/lesbian } }
    - { fields: { placeholder: '@contacts_sexual_orientation_heterosexual', language: '@language_en_us', value: Heterosexual } }
    - { fields: { placeholder: '@contacts_sexual_orientation_information_refused', language: '@language_en_us', value: Information refused } }
    - { fields: { placeholder: '@contacts_sexual_orientation_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@contacts_singular', language: '@language_en_us', value: Contact } }
    - { fields: { placeholder: '@contacts_subtype_inpatient', language: '@language_en_us', value: In-patient } }
    - { fields: { placeholder: '@contacts_type_patient', language: '@language_en_us', value: Patient } }
    - { fields: { placeholder: '@contacts_form_field_id_label', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@contacts_events_saved', language: '@language_en_us', value: 'Contact saved successfully' } }
    - { fields: { placeholder: '@contacts_events_deleted', language: '@language_en_us', value: 'Contact deleted successfully' } }
    - { fields: { placeholder: '@contacts_related_primary_modules', language: '@language_en_us', value: 'Related Primary Modules' } }
    - { fields: { placeholder: '@contacts_edit_contact', language: '@language_en_us', value: 'Edit Contact' } }
    - { fields: { placeholder: '@contacts_view_contact', language: '@language_en_us', value: 'View Contact' } }
    - { fields: { placeholder: '@contacts_filter_form_title', language: '@language_en_us', value: 'Filter Contacts' } }
    - { fields: { placeholder: '@contacts_form_job_title_title', language: '@language_en_us', value: Job Title } }
    - { fields: { placeholder: '@contacts_form_job_title_label', language: '@language_en_us', value: Job Title } }
    - { fields: { placeholder: '@contacts_form_source_of_record_title', language: '@language_en_us', value: 'Source of record' } }
    - { fields: { placeholder: '@contacts_form_source_of_record_label', language: '@language_en_us', value: 'Source of record' } }
