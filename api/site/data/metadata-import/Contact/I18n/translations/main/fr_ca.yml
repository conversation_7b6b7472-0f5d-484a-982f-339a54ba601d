entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@contacts_loading_contacts', language: '@language_fr_ca', value: 'Chargement des contacts' } }
  - { fields: { placeholder: '@contacts_loading_contact', language: '@language_fr_ca', value: 'Chargement du contact' } }
  - { fields: { placeholder: '@contacts_singular', language: '@language_fr_ca', value: Contact } }
  - { fields: { placeholder: '@contacts_plural', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@contacts_search', language: '@language_fr_ca', value: 'Rechercher des contacts' } }
  - { fields: { placeholder: '@contacts_create', language: '@language_fr_ca', value: 'Créer un contact' } }
  - { fields: { placeholder: '@contacts_edit', language: '@language_fr_ca', value: 'Modifier le contact' } }
  - { fields: { placeholder: '@contacts_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@contacts_form_title', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@contacts_form_section_title', language: '@language_fr_ca', value: 'Nouveau contact' } }
  - { fields: { placeholder: '@contacts_form_addresses_singular', language: '@language_fr_ca', value: Adresse } }
  - { fields: { placeholder: '@contacts_form_addresses_plural', language: '@language_fr_ca', value: Adresses } }
  - { fields: { placeholder: '@contacts_form_addresses_plural_label', language: '@language_fr_ca', value: Adresses } }
  - { fields: { placeholder: '@contacts_form_addresses_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_addresses_line1', language: '@language_fr_ca', value: 'Ligne 1' } }
  - { fields: { placeholder: '@contacts_form_addresses_line2', language: '@language_fr_ca', value: 'Ligne 2' } }
  - { fields: { placeholder: '@contacts_form_addresses_line3', language: '@language_fr_ca', value: 'Ligne 3' } }
  - { fields: { placeholder: '@contacts_form_addresses_city', language: '@language_fr_ca', value: Ville } }
  - { fields: { placeholder: '@contacts_form_addresses_county', language: '@language_fr_ca', value: Comté } }
  - { fields: { placeholder: '@contacts_form_addresses_country', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@contacts_form_addresses_postcode', language: '@language_fr_ca', value: 'Code postal' } }
  - { fields: { placeholder: '@contacts_form_addresses_postal', language: '@language_fr_ca', value: Postal } }
  - { fields: { placeholder: '@contacts_form_addresses_correspondence', language: '@language_fr_ca', value: Correspondance } }
  - { fields: { placeholder: '@contacts_form_addresses_residential', language: '@language_fr_ca', value: Résidentiel } }
  - { fields: { placeholder: '@contacts_form_addresses_business', language: '@language_fr_ca', value: Professionnel } }
  - { fields: { placeholder: '@contacts_form_addresses_trading', language: '@language_fr_ca', value: Commercial } }
  - { fields: { placeholder: '@contacts_form_disability_singular', language: '@language_fr_ca', value: Handicap } }
  - { fields: { placeholder: '@contacts_form_disability_plural', language: '@language_fr_ca', value: Incapacités } }
  - { fields: { placeholder: '@contacts_form_disability_plural_label', language: '@language_fr_ca', value: Incapacités } }
  - { fields: { placeholder: '@contacts_form_email_email_address', language: '@language_fr_ca', value: 'Adresse de courriel' } }
  - { fields: { placeholder: '@contacts_form_email_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_email_personal', language: '@language_fr_ca', value: Personnel } }
  - { fields: { placeholder: '@contacts_form_email_work', language: '@language_fr_ca', value: Professionnel } }
  - { fields: { placeholder: '@contacts_form_email_plural', language: '@language_fr_ca', value: 'Messages courriel' } }
  - { fields: { placeholder: '@contacts_form_email_plural_label', language: '@language_fr_ca', value: 'Messages courriel' } }
  - { fields: { placeholder: '@contacts_form_email_singular', language: '@language_fr_ca', value: 'Adresse courriel' } }
  - { fields: { placeholder: '@contacts_error_get_form', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération du formulaire du contact' } }
  - { fields: { placeholder: '@contacts_form_field_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@contacts_form_field_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@contacts_form_field_id_label', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@contacts_form_field_forename', language: '@language_fr_ca', value: Prénom } }
  - { fields: { placeholder: '@contacts_form_field_forename_label', language: '@language_fr_ca', value: Prénom } }
  - { fields: { placeholder: '@contacts_form_field_surname', language: '@language_fr_ca', value: 'Nom de famille' } }
  - { fields: { placeholder: '@contacts_form_field_surname_label', language: '@language_fr_ca', value: 'Nom de famille' } }
  - { fields: { placeholder: '@contacts_form_field_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@contacts_form_field_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@contacts_form_field_gender', language: '@language_fr_ca', value: Sexe } }
  - { fields: { placeholder: '@contacts_form_field_gender_label', language: '@language_fr_ca', value: Sexe } }
  - { fields: { placeholder: '@contacts_form_field_select_gender', language: '@language_fr_ca', value: 'Sélectionnez le genre' } }
  - { fields: { placeholder: '@contacts_form_field_gender_male', language: '@language_fr_ca', value: Homme } }
  - { fields: { placeholder: '@contacts_form_field_gender_female', language: '@language_fr_ca', value: Femme } }
  - { fields: { placeholder: '@contacts_form_field_ethnicity', language: '@language_fr_ca', value: 'Origine ethnique' } }
  - { fields: { placeholder: '@contacts_form_field_ethnicity_label', language: '@language_fr_ca', value: 'Origine ethnique' } }
  - { fields: { placeholder: '@contacts_form_field_select_ethnicity', language: '@language_fr_ca', value: 'Sélectionnez l''ethnicité' } }
  - { fields: { placeholder: '@contacts_form_field_language', language: '@language_fr_ca', value: Langue } }
  - { fields: { placeholder: '@contacts_form_field_language_select_language', language: '@language_fr_ca', value: 'Sélectionnez la langue' } }
  - { fields: { placeholder: '@contacts_form_field_language_label', language: '@language_fr_ca', value: Langue } }
  - { fields: { placeholder: '@contacts_form_field_sexual_orientation', language: '@language_fr_ca', value: 'Orientation sexuelle' } }
  - { fields: { placeholder: '@contacts_form_field_sexual_orientation_label', language: '@language_fr_ca', value: 'Orientation sexuelle' } }
  - { fields: { placeholder: '@contacts_form_field_select_sexual_orientation', language: '@language_fr_ca', value: 'Sélectionnez l''orientation sexuelle' } }
  - { fields: { placeholder: '@contacts_form_field_religion', language: '@language_fr_ca', value: Religion } }
  - { fields: { placeholder: '@contacts_form_field_religion_label', language: '@language_fr_ca', value: Religion } }
  - { fields: { placeholder: '@contacts_form_field_relationship', language: '@language_fr_ca', value: 'Données associées' } }
  - { fields: { placeholder: '@contacts_form_field_relationship_label', language: '@language_fr_ca', value: 'Données associées' } }
  - { fields: { placeholder: '@contacts_form_field_numbers_filter', language: '@language_fr_ca', value: 'Numéros de contacts' } }
  - { fields: { placeholder: '@contacts_form_field_numbers_filter_label', language: '@language_fr_ca', value: 'Numéros de contacts' } }
  - { fields: { placeholder: '@contacts_form_field_select_religion', language: '@language_fr_ca', value: 'Sélectionnez la religion' } }
  - { fields: { placeholder: '@contacts_form_field_date_of_birth', language: '@language_fr_ca', value: 'Date de naissance' } }
  - { fields: { placeholder: '@contacts_form_field_date_of_birth_label', language: '@language_fr_ca', value: 'Date de naissance' } }
  - { fields: { placeholder: '@contacts_form_field_date_of_death', language: '@language_fr_ca', value: 'Date du décès' } }
  - { fields: { placeholder: '@contacts_form_field_date_of_death_label', language: '@language_fr_ca', value: 'Date du décès' } }
  - { fields: { placeholder: '@contacts_form_field_lone_worker', language: '@language_fr_ca', value: 'Travailleur seul' } }
  - { fields: { placeholder: '@contacts_form_field_lone_worker_label', language: '@language_fr_ca', value: 'Évalué en tant que travailleur seul?' } }
  - { fields: { placeholder: '@contacts_form_field_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_field_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_field_select_type', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@contacts_form_field_subtype', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@contacts_form_field_subtype_label', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@contacts_form_field_select_subtype', language: '@language_fr_ca', value: 'Sélectionnez le sous-type' } }
  - { fields: { placeholder: '@contacts_form_field_notes', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@contacts_form_field_notes_label', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@contacts_form_id_numbers_singular', language: '@language_fr_ca', value: 'Numéro spécifique individuel' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_plural', language: '@language_fr_ca', value: 'Numéro spécifique individuel' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_id_numbers_inpatient', language: '@language_fr_ca', value: 'Patient hospitalisé' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_nhs_number', language: '@language_fr_ca', value: 'Numéro NHS' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_patient_number', language: '@language_fr_ca', value: 'Numéro du patient' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_staff_number', language: '@language_fr_ca', value: 'Numéro du personnel' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_police_id', language: '@language_fr_ca', value: 'ID de police' } }
  - { fields: { placeholder: '@contacts_form_id_numbers_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@contacts_form_numbers_singular', language: '@language_fr_ca', value: Numéro } }
  - { fields: { placeholder: '@contacts_form_numbers_plural', language: '@language_fr_ca', value: Numéros } }
  - { fields: { placeholder: '@contacts_form_numbers_plural_label', language: '@language_fr_ca', value: Numéros } }
  - { fields: { placeholder: '@contacts_form_numbers_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@contacts_form_numbers_phone_number', language: '@language_fr_ca', value: 'Numéro de téléphone' } }
  - { fields: { placeholder: '@contacts_form_numbers_phone_number_label', language: '@language_fr_ca', value: 'Numéros de téléphone' } }
  - { fields: { placeholder: '@contacts_form_numbers_phone_numbers', language: '@language_fr_ca', value: 'Numéro de téléphone' } }
  - { fields: { placeholder: '@contacts_form_numbers_phone_numbers_label', language: '@language_fr_ca', value: 'Numéros de téléphone' } }
  - { fields: { placeholder: '@contacts_form_numbers_home', language: '@language_fr_ca', value: Domicile } }
  - { fields: { placeholder: '@contacts_form_numbers_work', language: '@language_fr_ca', value: Professionnel } }
  - { fields: { placeholder: '@contacts_form_numbers_mobile', language: '@language_fr_ca', value: Cellulaire } }
  - { fields: { placeholder: '@contacts_form_field_status_value_unapproved', language: '@language_fr_ca', value: 'Non approuvé' } }
  - { fields: { placeholder: '@contacts_form_field_status_value_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@contacts_form_field_status_value_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@contacts_form_field_status_select_status', language: '@language_fr_ca', value: 'Sélectionnez l''état' } }
  - { fields: { placeholder: '@contacts_form_field_title_value_mr', language: '@language_fr_ca', value: M. } }
  - { fields: { placeholder: '@contacts_form_field_title_value_mrs', language: '@language_fr_ca', value: Mme } }
  - { fields: { placeholder: '@contacts_form_field_title_value_master', language: '@language_fr_ca', value: Maître } }
  - { fields: { placeholder: '@contacts_form_field_title_value_miss', language: '@language_fr_ca', value: Mademoiselle } }
  - { fields: { placeholder: '@contacts_form_field_title_value_ms', language: '@language_fr_ca', value: Mme } }
  - { fields: { placeholder: '@contacts_form_field_title_value_dr', language: '@language_fr_ca', value: Docteur } }
  - { fields: { placeholder: '@contacts_form_field_title_value_prof', language: '@language_fr_ca', value: Professeur } }
  - { fields: { placeholder: '@contacts_form_field_title_value_rev', language: '@language_fr_ca', value: Révérend } }
  - { fields: { placeholder: '@contacts_form_field_title_value_br', language: '@language_fr_ca', value: Frère } }
  - { fields: { placeholder: '@contacts_form_field_title_value_sr', language: '@language_fr_ca', value: Sœur } }
  - { fields: { placeholder: '@contacts_form_field_title_value_fr', language: '@language_fr_ca', value: Père } }
  - { fields: { placeholder: '@contacts_form_field_title_value_pr', language: '@language_fr_ca', value: Pasteur } }
  - { fields: { placeholder: '@contacts_form_field_title_value_elder', language: '@language_fr_ca', value: Doyen } }
  - { fields: { placeholder: '@contacts_form_field_title_value_rabbi', language: '@language_fr_ca', value: Rabbin } }
  - { fields: { placeholder: '@contacts_form_field_title_value_sir', language: '@language_fr_ca', value: Sir } }
  - { fields: { placeholder: '@contacts_form_field_title_value_madam', language: '@language_fr_ca', value: Madame } }
  - { fields: { placeholder: '@contacts_form_field_title_value_dame', language: '@language_fr_ca', value: Dame } }
  - { fields: { placeholder: '@contacts_form_field_title_value_lord', language: '@language_fr_ca', value: Lord } }
  - { fields: { placeholder: '@contacts_form_field_title_value_lady', language: '@language_fr_ca', value: Lady } }
  - { fields: { placeholder: '@contacts_form_field_title_value_esq', language: '@language_fr_ca', value: M. } }
  - { fields: { placeholder: '@contacts_form_field_title_value_adv', language: '@language_fr_ca', value: Adv } }
  - { fields: { placeholder: '@contacts_form_field_title_select_title', language: '@language_fr_ca', value: 'Sélectionnez une civilité' } }
  - { fields: { placeholder: '@placeholder_contacts_form_field_contact_number_filter', language: '@language_fr_ca', value: 'Numéro de contact' } }
  - { fields: { placeholder: '@contacts_nav_dashboard', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@contacts_nav_new_contact', language: '@language_fr_ca', value: 'Nouveau contact' } }
  - { fields: { placeholder: '@contacts_nav_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@contacts_form_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement du contact' } }
  - { fields: { placeholder: '@contacts_form_type_contact', language: '@language_fr_ca', value: 'Formulaire de contact' } }
  - { fields: { placeholder: '@contacts_form_type_main', language: '@language_fr_ca', value: 'Formulaire de contact' } }
  - { fields: { placeholder: '@contacts_form_type_contact_filter', language: '@language_fr_ca', value: 'Formulaire de filtre de contact' } }
  - { fields: { placeholder: '@contacts_religion_christian', language: '@language_fr_ca', value: Chrétien } }
  - { fields: { placeholder: '@contacts_religion_muslim', language: '@language_fr_ca', value: Musulman } }
  - { fields: { placeholder: '@contacts_religion_atheist', language: '@language_fr_ca', value: Athée } }
  - { fields: { placeholder: '@contacts_religion_hindu', language: '@language_fr_ca', value: Hindou } }
  - { fields: { placeholder: '@contacts_religion_buddist', language: '@language_fr_ca', value: Bouddhiste } }
  - { fields: { placeholder: '@contacts_religion_taoist', language: '@language_fr_ca', value: Taoïste } }
  - { fields: { placeholder: '@contacts_religion_jewish', language: '@language_fr_ca', value: Juif } }
  - { fields: { placeholder: '@contacts_religion_sikh', language: '@language_fr_ca', value: Sikh } }
  - { fields: { placeholder: '@contacts_ethnicity_indian', language: '@language_fr_ca', value: Indien } }
  - { fields: { placeholder: '@contacts_ethnicity_pakistani', language: '@language_fr_ca', value: Pakistanais } }
  - { fields: { placeholder: '@contacts_ethnicity_bangladeshi', language: '@language_fr_ca', value: Bangladais } }
  - { fields: { placeholder: '@contacts_ethnicity_any_other_asian_background', language: '@language_fr_ca', value: 'Tout autre fond asiatique' } }
  - { fields: { placeholder: '@contacts_ethnicity_caribbean', language: '@language_fr_ca', value: Caraïbe } }
  - { fields: { placeholder: '@contacts_ethnicity_african', language: '@language_fr_ca', value: Africain } }
  - { fields: { placeholder: '@contacts_ethnicity_any_other_black_background', language: '@language_fr_ca', value: 'Tout autre fond noir' } }
  - { fields: { placeholder: '@contacts_ethnicity_and_black_caribbean', language: '@language_fr_ca', value: 'Caraïbe métissé blanc et noir' } }
  - { fields: { placeholder: '@contacts_ethnicity_white_and_black_african', language: '@language_fr_ca', value: 'Africain métissé blanc et noir' } }
  - { fields: { placeholder: '@contacts_ethnicity_white_and_asian', language: '@language_fr_ca', value: 'Blanc et asiatique métissé' } }
  - { fields: { placeholder: '@contacts_ethnicity_any_other_mixed_background', language: '@language_fr_ca', value: 'Tout autre fond de métissage' } }
  - { fields: { placeholder: '@contacts_ethnicity_any_other_ethnic_group', language: '@language_fr_ca', value: 'Tout autre groupe ethnique' } }
  - { fields: { placeholder: '@contacts_ethnicity_british', language: '@language_fr_ca', value: Britannique } }
  - { fields: { placeholder: '@contacts_ethnicity_irish', language: '@language_fr_ca', value: Irlandais } }
  - { fields: { placeholder: '@contacts_ethnicity_any_other_white_background', language: '@language_fr_ca', value: 'Tout autre fond blanc' } }
  - { fields: { placeholder: '@contacts_sexual_orientation_bisexual', language: '@language_fr_ca', value: Bisexuel } }
  - { fields: { placeholder: '@contacts_sexual_orientation_gay_man', language: '@language_fr_ca', value: 'Homosexuel masculin' } }
  - { fields: { placeholder: '@contacts_sexual_orientation_gay_woman_lesbian', language: '@language_fr_ca', value: 'Homosexuel féminin/lesbienne' } }
  - { fields: { placeholder: '@contacts_sexual_orientation_heterosexual', language: '@language_fr_ca', value: Hétérosexuel } }
  - { fields: { placeholder: '@contacts_sexual_orientation_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@contacts_sexual_orientation_information_refused', language: '@language_fr_ca', value: 'Information refusée' } }
  - { fields: { placeholder: '@contacts_type_patient', language: '@language_fr_ca', value: Patient } }
  - { fields: { placeholder: '@contacts_subtype_inpatient', language: '@language_fr_ca', value: 'Patient hospitalisé' } }
  - { fields: { placeholder: '@contacts_datasource_gender', language: '@language_fr_ca', value: 'Genre du contact' } }
  - { fields: { placeholder: '@contacts_datasource_title', language: '@language_fr_ca', value: 'Titre du contact' } }
  - { fields: { placeholder: '@contacts_datasource_type', language: '@language_fr_ca', value: 'Type de contact' } }
  - { fields: { placeholder: '@contacts_datasource_subtype', language: '@language_fr_ca', value: 'Sous-type du contact' } }
  - { fields: { placeholder: '@contacts_datasource_worker', language: '@language_fr_ca', value: 'Contact travailleur seul' } }
  - { fields: { placeholder: '@contacts_datasource_ethnicities', language: '@language_fr_ca', value: 'Ethnicités de contact' } }
  - { fields: { placeholder: '@contacts_datasource_orientations', language: '@language_fr_ca', value: 'Orientations sexuelles de contact' } }
  - { fields: { placeholder: '@contacts_datasource_languages', language: '@language_fr_ca', value: 'Langues de contact' } }
  - { fields: { placeholder: '@contacts_datasource_religions', language: '@language_fr_ca', value: 'Religions de contact' } }
  - { fields: { placeholder: '@contacts_datasource_disabilities', language: '@language_fr_ca', value: 'Handicaps de contact' } }
  - { fields: { placeholder: '@contacts_datasource_languages_indian', language: '@language_fr_ca', value: Indien } }
  - { fields: { placeholder: '@contacts_datasource_languages_akan', language: '@language_fr_ca', value: Akan } }
  - { fields: { placeholder: '@contacts_datasource_languages_amharic', language: '@language_fr_ca', value: Amharique } }
  - { fields: { placeholder: '@contacts_datasource_languages_arabic', language: '@language_fr_ca', value: Arabe } }
  - { fields: { placeholder: '@contacts_datasource_languages_assamese', language: '@language_fr_ca', value: Assamais } }
  - { fields: { placeholder: '@contacts_datasource_languages_awadhi', language: '@language_fr_ca', value: Awadhi } }
  - { fields: { placeholder: '@contacts_datasource_languages_azerbaijani', language: '@language_fr_ca', value: Azerbaïdjanais } }
  - { fields: { placeholder: '@contacts_datasource_languages_balochi', language: '@language_fr_ca', value: Balochi } }
  - { fields: { placeholder: '@contacts_datasource_languages_belarusian', language: '@language_fr_ca', value: Biélorusse } }
  - { fields: { placeholder: '@contacts_datasource_languages_bengali', language: '@language_fr_ca', value: 'Bengali (bangla)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_bhojpuri', language: '@language_fr_ca', value: Bhojpuri } }
  - { fields: { placeholder: '@contacts_datasource_languages_burmese', language: '@language_fr_ca', value: Birman } }
  - { fields: { placeholder: '@contacts_datasource_languages_cebuano', language: '@language_fr_ca', value: 'Cebuano (visayan)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_chewa', language: '@language_fr_ca', value: Chewa } }
  - { fields: { placeholder: '@contacts_datasource_languages_chhattisgarhi', language: '@language_fr_ca', value: Chattisgarhi } }
  - { fields: { placeholder: '@contacts_datasource_languages_chittagonian', language: '@language_fr_ca', value: Chittagonien } }
  - { fields: { placeholder: '@contacts_datasource_languages_czech', language: '@language_fr_ca', value: Tchèque } }
  - { fields: { placeholder: '@contacts_datasource_languages_deccan', language: '@language_fr_ca', value: Deccan } }
  - { fields: { placeholder: '@contacts_datasource_languages_dhundhari', language: '@language_fr_ca', value: Dhudhari } }
  - { fields: { placeholder: '@contacts_datasource_languages_dutch', language: '@language_fr_ca', value: Néerlandais } }
  - { fields: { placeholder: '@contacts_datasource_languages_fuzhounese', language: '@language_fr_ca', value: 'Min oriental (y compris dialecte de Fuzhou)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_english', language: '@language_fr_ca', value: Anglais } }
  - { fields: { placeholder: '@contacts_datasource_languages_french', language: '@language_fr_ca', value: Français } }
  - { fields: { placeholder: '@contacts_datasource_languages_fula', language: '@language_fr_ca', value: Peul } }
  - { fields: { placeholder: '@contacts_datasource_languages_gan_chinese', language: '@language_fr_ca', value: 'Gan chinois' } }
  - { fields: { placeholder: '@contacts_datasource_languages_german', language: '@language_fr_ca', value: Allemand } }
  - { fields: { placeholder: '@contacts_datasource_languages_greek', language: '@language_fr_ca', value: Grec } }
  - { fields: { placeholder: '@contacts_datasource_languages_gujarati', language: '@language_fr_ca', value: Gujarati } }
  - { fields: { placeholder: '@contacts_datasource_languages_haitian_creole', language: '@language_fr_ca', value: 'Créole haïtien' } }
  - { fields: { placeholder: '@contacts_datasource_languages_hakka', language: '@language_fr_ca', value: Hakka } }
  - { fields: { placeholder: '@contacts_datasource_languages_haryanvi', language: '@language_fr_ca', value: Haryanvi } }
  - { fields: { placeholder: '@contacts_datasource_languages_hausa', language: '@language_fr_ca', value: Haoussa } }
  - { fields: { placeholder: '@contacts_datasource_languages_hiligaynon_ilonggo', language: '@language_fr_ca', value: 'Hiligaïnon / ilonggo (visayan)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_hindi', language: '@language_fr_ca', value: Hindi } }
  - { fields: { placeholder: '@contacts_datasource_languages_hmong', language: '@language_fr_ca', value: 'Hmong (miao chuanqiandian)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_hungarian', language: '@language_fr_ca', value: Hongrois } }
  - { fields: { placeholder: '@contacts_datasource_languages_igbo', language: '@language_fr_ca', value: Igbo } }
  - { fields: { placeholder: '@contacts_datasource_languages_ilocano', language: '@language_fr_ca', value: Ilocano } }
  - { fields: { placeholder: '@contacts_datasource_languages_italian', language: '@language_fr_ca', value: Italien } }
  - { fields: { placeholder: '@contacts_datasource_languages_japanese', language: '@language_fr_ca', value: Japonais } }
  - { fields: { placeholder: '@contacts_datasource_languages_javanese', language: '@language_fr_ca', value: Javanais } }
  - { fields: { placeholder: '@contacts_datasource_languages_jin', language: '@language_fr_ca', value: Jin } }
  - { fields: { placeholder: '@contacts_datasource_languages_kannada', language: '@language_fr_ca', value: Kannada } }
  - { fields: { placeholder: '@contacts_datasource_languages_kazakh', language: '@language_fr_ca', value: Kazakh } }
  - { fields: { placeholder: '@contacts_datasource_languages_khmer', language: '@language_fr_ca', value: Khmer } }
  - { fields: { placeholder: '@contacts_datasource_languages_kinyarwanda', language: '@language_fr_ca', value: Kinyarwanda } }
  - { fields: { placeholder: '@contacts_datasource_languages_kirundi', language: '@language_fr_ca', value: Kirundi } }
  - { fields: { placeholder: '@contacts_datasource_languages_konkani', language: '@language_fr_ca', value: Konkani } }
  - { fields: { placeholder: '@contacts_datasource_languages_korean', language: '@language_fr_ca', value: Coréen } }
  - { fields: { placeholder: '@contacts_datasource_languages_kurdish', language: '@language_fr_ca', value: Kurde } }
  - { fields: { placeholder: '@contacts_datasource_languages_madurese', language: '@language_fr_ca', value: Madurèse } }
  - { fields: { placeholder: '@contacts_datasource_languages_magahi', language: '@language_fr_ca', value: Magahi } }
  - { fields: { placeholder: '@contacts_datasource_languages_maithili', language: '@language_fr_ca', value: Maïthili } }
  - { fields: { placeholder: '@contacts_datasource_languages_malagasy', language: '@language_fr_ca', value: Malgache } }
  - { fields: { placeholder: '@contacts_datasource_languages_malay', language: '@language_fr_ca', value: 'Malais (y compris malais et indonésien)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_malayalam', language: '@language_fr_ca', value: Malayalam } }
  - { fields: { placeholder: '@contacts_datasource_languages_mandarin', language: '@language_fr_ca', value: Mandarin } }
  - { fields: { placeholder: '@contacts_datasource_languages_marathi', language: '@language_fr_ca', value: Marathi } }
  - { fields: { placeholder: '@contacts_datasource_languages_marwari', language: '@language_fr_ca', value: Marwari } }
  - { fields: { placeholder: '@contacts_datasource_languages_mossi', language: '@language_fr_ca', value: Mossi } }
  - { fields: { placeholder: '@contacts_datasource_languages_nepali', language: '@language_fr_ca', value: Népalais } }
  - { fields: { placeholder: '@contacts_datasource_languages_northern_min', language: '@language_fr_ca', value: 'Min septentrional (minbei)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_odia', language: '@language_fr_ca', value: 'Odia (oriya)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_oromo', language: '@language_fr_ca', value: Oromo } }
  - { fields: { placeholder: '@contacts_datasource_languages_pashto', language: '@language_fr_ca', value: Pashto } }
  - { fields: { placeholder: '@contacts_datasource_languages_persian', language: '@language_fr_ca', value: Perse } }
  - { fields: { placeholder: '@contacts_datasource_languages_polish', language: '@language_fr_ca', value: Polonais } }
  - { fields: { placeholder: '@contacts_datasource_languages_portuguese', language: '@language_fr_ca', value: Portugais } }
  - { fields: { placeholder: '@contacts_datasource_languages_punjabi', language: '@language_fr_ca', value: Punjabi } }
  - { fields: { placeholder: '@contacts_datasource_languages_quechua', language: '@language_fr_ca', value: Quechua } }
  - { fields: { placeholder: '@contacts_datasource_languages_romanian', language: '@language_fr_ca', value: Roumain } }
  - { fields: { placeholder: '@contacts_datasource_languages_russian', language: '@language_fr_ca', value: Russe } }
  - { fields: { placeholder: '@contacts_datasource_languages_saraiki', language: '@language_fr_ca', value: Saraiki } }
  - { fields: { placeholder: '@contacts_datasource_languages_serbo_croatian', language: '@language_fr_ca', value: Serbo-croate } }
  - { fields: { placeholder: '@contacts_datasource_languages_shona', language: '@language_fr_ca', value: Shona } }
  - { fields: { placeholder: '@contacts_datasource_languages_sindhi', language: '@language_fr_ca', value: Sindhi } }
  - { fields: { placeholder: '@contacts_datasource_languages_sinhalese', language: '@language_fr_ca', value: Cinghalais } }
  - { fields: { placeholder: '@contacts_datasource_languages_somali', language: '@language_fr_ca', value: Somali } }
  - { fields: { placeholder: '@contacts_datasource_languages_southern_min', language: '@language_fr_ca', value: 'Min méridional / minnan (y compris hokkien et teochew)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_spanish', language: '@language_fr_ca', value: Espagnol } }
  - { fields: { placeholder: '@contacts_datasource_languages_sundanese', language: '@language_fr_ca', value: Soundanais } }
  - { fields: { placeholder: '@contacts_datasource_languages_swedish', language: '@language_fr_ca', value: Suédois } }
  - { fields: { placeholder: '@contacts_datasource_languages_sylheti', language: '@language_fr_ca', value: Sylheti } }
  - { fields: { placeholder: '@contacts_datasource_languages_tagalog', language: '@language_fr_ca', value: Tagalog } }
  - { fields: { placeholder: '@contacts_datasource_languages_tamil', language: '@language_fr_ca', value: Tamil } }
  - { fields: { placeholder: '@contacts_datasource_languages_telugu', language: '@language_fr_ca', value: Télougou } }
  - { fields: { placeholder: '@contacts_datasource_languages_thai', language: '@language_fr_ca', value: Thaï } }
  - { fields: { placeholder: '@contacts_datasource_languages_turkish', language: '@language_fr_ca', value: Turc } }
  - { fields: { placeholder: '@contacts_datasource_languages_turkmen', language: '@language_fr_ca', value: Turkmène } }
  - { fields: { placeholder: '@contacts_datasource_languages_ukrainian', language: '@language_fr_ca', value: Ukrainien } }
  - { fields: { placeholder: '@contacts_datasource_languages_urdu', language: '@language_fr_ca', value: Ourdou } }
  - { fields: { placeholder: '@contacts_datasource_languages_uyghur', language: '@language_fr_ca', value: Ouïghour } }
  - { fields: { placeholder: '@contacts_datasource_languages_uzbek', language: '@language_fr_ca', value: Ouzbek } }
  - { fields: { placeholder: '@contacts_datasource_languages_vietnamese', language: '@language_fr_ca', value: Vietnamien } }
  - { fields: { placeholder: '@contacts_datasource_languages_wu', language: '@language_fr_ca', value: 'Wu (y compris shanghaïen)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_xhosa', language: '@language_fr_ca', value: Xhosa } }
  - { fields: { placeholder: '@contacts_datasource_languages_xiang', language: '@language_fr_ca', value: 'Xiang (Hunnanais)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_yoruba', language: '@language_fr_ca', value: Yoruba } }
  - { fields: { placeholder: '@contacts_datasource_languages_yue', language: '@language_fr_ca', value: 'Yue (y compris cantonais)' } }
  - { fields: { placeholder: '@contacts_datasource_languages_zhuang', language: '@language_fr_ca', value: Zhuang } }
  - { fields: { placeholder: '@contacts_datasource_languages_zulu', language: '@language_fr_ca', value: Zoulou } }
  - { fields: { placeholder: '@contacts_post_save_no_access', language: '@language_fr_ca', value: 'Le contact a été enregistré, mais les autorisations d''accès attribuées à votre utilisateur ne vous permettent pas de le voir' } }
  - { fields: { placeholder: '@contacts_forms_id_numbers_default', language: '@language_fr_ca', value: 'Sélectionnez un type de nombre' } }
  - { fields: { placeholder: '@contacts_numbers_type_title', language: '@language_fr_ca', value: 'Type de nombre' } }
  - { fields: { placeholder: '@contacts_numbers_type_label', language: '@language_fr_ca', value: 'Type de nombre' } }
  - { fields: { placeholder: '@contacts_form_field_positions_label', language: '@language_fr_ca', value: Positions } }
  - { fields: { placeholder: '@contacts_form_field_positions', language: '@language_fr_ca', value: Positions } }
  - { fields: { placeholder: '@contacts_form_field_assignment_number', language: '@language_fr_ca', value: 'Numéro d''attribution' } }
  - { fields: { placeholder: '@contacts_form_field_employee_status', language: '@language_fr_ca', value: 'État de l''employé' } }
  - { fields: { placeholder: '@contacts_form_field_employee_status_select_employee_status', language: '@language_fr_ca', value: 'Sélectionnez un état de l''employé' } }
  - { fields: { placeholder: '@contacts_form_field_employee_status_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@contacts_form_field_employee_status_terminated', language: '@language_fr_ca', value: 'A cessé ses fonctions' } }
  - { fields: { placeholder: '@contacts_form_field_employee_status_not_started', language: '@language_fr_ca', value: 'Non démarré' } }
  - { fields: { placeholder: '@contacts_forms_nationality_default', language: '@language_fr_ca', value: 'Sélectionnez la nationalité' } }
  - { fields: { placeholder: '@contacts_nationality_title', language: '@language_fr_ca', value: Nationalité } }
  - { fields: { placeholder: '@contacts_nationality_label', language: '@language_fr_ca', value: Nationalité } }
  - { fields: { placeholder: '@contacts_nationality_afghanistan', language: '@language_fr_ca', value: Afghanistan } }
  - { fields: { placeholder: '@contacts_nationality_aland_islands', language: '@language_fr_ca', value: 'Iles Aland' } }
  - { fields: { placeholder: '@contacts_nationality_albania', language: '@language_fr_ca', value: Albanie } }
  - { fields: { placeholder: '@contacts_nationality_algeria', language: '@language_fr_ca', value: Algérie } }
  - { fields: { placeholder: '@contacts_nationality_american_samoa', language: '@language_fr_ca', value: 'Samoa américaines' } }
  - { fields: { placeholder: '@contacts_nationality_andorra', language: '@language_fr_ca', value: Andorre } }
  - { fields: { placeholder: '@contacts_nationality_angola', language: '@language_fr_ca', value: Angola } }
  - { fields: { placeholder: '@contacts_nationality_anguilla', language: '@language_fr_ca', value: Anguilla } }
  - { fields: { placeholder: '@contacts_nationality_antarctica', language: '@language_fr_ca', value: Antarctique } }
  - { fields: { placeholder: '@contacts_nationality_antigua_and_barbuda', language: '@language_fr_ca', value: Antigua-et-Barbuda } }
  - { fields: { placeholder: '@contacts_nationality_argentina', language: '@language_fr_ca', value: Argentine } }
  - { fields: { placeholder: '@contacts_nationality_armenia', language: '@language_fr_ca', value: Arménie } }
  - { fields: { placeholder: '@contacts_nationality_aruba', language: '@language_fr_ca', value: Aruba } }
  - { fields: { placeholder: '@contacts_nationality_australia', language: '@language_fr_ca', value: Australie } }
  - { fields: { placeholder: '@contacts_nationality_austria', language: '@language_fr_ca', value: Autriche } }
  - { fields: { placeholder: '@contacts_nationality_azerbaijan', language: '@language_fr_ca', value: Azerbaijan } }
  - { fields: { placeholder: '@contacts_nationality_bahamas', language: '@language_fr_ca', value: Bahamas } }
  - { fields: { placeholder: '@contacts_nationality_bahrain', language: '@language_fr_ca', value: Bahreïn } }
  - { fields: { placeholder: '@contacts_nationality_bangladesh', language: '@language_fr_ca', value: Bangladesh } }
  - { fields: { placeholder: '@contacts_nationality_barbados', language: '@language_fr_ca', value: Barbade } }
  - { fields: { placeholder: '@contacts_nationality_belarus', language: '@language_fr_ca', value: Biélorussie } }
  - { fields: { placeholder: '@contacts_nationality_belgium', language: '@language_fr_ca', value: Belgique } }
  - { fields: { placeholder: '@contacts_nationality_belize', language: '@language_fr_ca', value: Belize } }
  - { fields: { placeholder: '@contacts_nationality_benin', language: '@language_fr_ca', value: Bénin } }
  - { fields: { placeholder: '@contacts_nationality_bermuda', language: '@language_fr_ca', value: Bermudes } }
  - { fields: { placeholder: '@contacts_nationality_bhutan', language: '@language_fr_ca', value: Bhoutan } }
  - { fields: { placeholder: '@contacts_nationality_bolivia', language: '@language_fr_ca', value: Bolivie } }
  - { fields: { placeholder: '@contacts_nationality_bonaire_sint_eustatius_and_saba', language: '@language_fr_ca', value: 'Bonaire, Sint Eustatius et Saba' } }
  - { fields: { placeholder: '@contacts_nationality_bosnia_and_herzegovina', language: '@language_fr_ca', value: Bosnie-Herzégovine } }
  - { fields: { placeholder: '@contacts_nationality_botswana', language: '@language_fr_ca', value: Botswana } }
  - { fields: { placeholder: '@contacts_nationality_bouvet_island', language: '@language_fr_ca', value: 'île Bouvet' } }
  - { fields: { placeholder: '@contacts_nationality_brazil', language: '@language_fr_ca', value: Brésil } }
  - { fields: { placeholder: '@contacts_nationality_british_virgin_islands', language: '@language_fr_ca', value: 'Îles Vierges Britanniques' } }
  - { fields: { placeholder: '@contacts_nationality_british_indian_ocean_territory', language: '@language_fr_ca', value: 'Territoire britannique de l’océan Indien' } }
  - { fields: { placeholder: '@contacts_nationality_brunei_darussalam', language: '@language_fr_ca', value: 'Brunéi Darussalam' } }
  - { fields: { placeholder: '@contacts_nationality_bulgaria', language: '@language_fr_ca', value: Bulgarie } }
  - { fields: { placeholder: '@contacts_nationality_burkina_faso', language: '@language_fr_ca', value: 'Burkina Faso' } }
  - { fields: { placeholder: '@contacts_nationality_burundi', language: '@language_fr_ca', value: Burundi } }
  - { fields: { placeholder: '@contacts_nationality_cambodia', language: '@language_fr_ca', value: Cambodge } }
  - { fields: { placeholder: '@contacts_nationality_cameroon', language: '@language_fr_ca', value: Cameroun } }
  - { fields: { placeholder: '@contacts_nationality_canada', language: '@language_fr_ca', value: Canada } }
  - { fields: { placeholder: '@contacts_nationality_cape_verde', language: '@language_fr_ca', value: 'Cap Vert' } }
  - { fields: { placeholder: '@contacts_nationality_cayman_islands', language: '@language_fr_ca', value: 'Îles Caïmans' } }
  - { fields: { placeholder: '@contacts_nationality_central_african_republic', language: '@language_fr_ca', value: 'République centrafricaine' } }
  - { fields: { placeholder: '@contacts_nationality_chad', language: '@language_fr_ca', value: Tchad } }
  - { fields: { placeholder: '@contacts_nationality_chile', language: '@language_fr_ca', value: Chili } }
  - { fields: { placeholder: '@contacts_nationality_china', language: '@language_fr_ca', value: Chine } }
  - { fields: { placeholder: '@contacts_nationality_hong_kong_special_administrative_region_of_china', language: '@language_fr_ca', value: 'Hong Kong, Région administrative spéciale de la Chine' } }
  - { fields: { placeholder: '@contacts_nationality_macao_special_administrative_region_of_china', language: '@language_fr_ca', value: 'Macao, Région administrative spéciale de la Chine' } }
  - { fields: { placeholder: '@contacts_nationality_christmas_island', language: '@language_fr_ca', value: 'île Christmas' } }
  - { fields: { placeholder: '@contacts_nationality_cocos_keeling_islands', language: '@language_fr_ca', value: 'Îles Cocos (Keeling)' } }
  - { fields: { placeholder: '@contacts_nationality_colombia', language: '@language_fr_ca', value: Colombie } }
  - { fields: { placeholder: '@contacts_nationality_comoros', language: '@language_fr_ca', value: Comores } }
  - { fields: { placeholder: '@contacts_nationality_congo_brazzaville', language: '@language_fr_ca', value: 'Congo (Brazzaville)' } }
  - { fields: { placeholder: '@contacts_nationality_congo_democratic_republic_of_the', language: '@language_fr_ca', value: 'Congo, République démocratique du' } }
  - { fields: { placeholder: '@contacts_nationality_cook_islands', language: '@language_fr_ca', value: 'Îles Cook' } }
  - { fields: { placeholder: '@contacts_nationality_costa_rica', language: '@language_fr_ca', value: 'Costa Rica' } }
  - { fields: { placeholder: '@contacts_nationality_cote_divoire', language: '@language_fr_ca', value: "Côte d'Ivoire" } }
  - { fields: { placeholder: '@contacts_nationality_croatia', language: '@language_fr_ca', value: Croatie } }
  - { fields: { placeholder: '@contacts_nationality_cuba', language: '@language_fr_ca', value: Cuba } }
  - { fields: { placeholder: '@contacts_nationality_curacao', language: '@language_fr_ca', value: 'Curaçao' } }
  - { fields: { placeholder: '@contacts_nationality_cyprus', language: '@language_fr_ca', value: Chypre } }
  - { fields: { placeholder: '@contacts_nationality_czech_republic', language: '@language_fr_ca', value: Tchéquie } }
  - { fields: { placeholder: '@contacts_nationality_denmark', language: '@language_fr_ca', value: Danemark } }
  - { fields: { placeholder: '@contacts_nationality_djibouti', language: '@language_fr_ca', value: Djibouti } }
  - { fields: { placeholder: '@contacts_nationality_dominica', language: '@language_fr_ca', value: 'La Dominique' } }
  - { fields: { placeholder: '@contacts_nationality_dominican_republic', language: '@language_fr_ca', value: 'République dominicaine' } }
  - { fields: { placeholder: '@contacts_nationality_ecuador', language: '@language_fr_ca', value: Équateur } }
  - { fields: { placeholder: '@contacts_nationality_egypt', language: '@language_fr_ca', value: Égypte } }
  - { fields: { placeholder: '@contacts_nationality_el_salvador', language: '@language_fr_ca', value: Salvador } }
  - { fields: { placeholder: '@contacts_nationality_equatorial_guinea', language: '@language_fr_ca', value: 'Guinée Équatoriale' } }
  - { fields: { placeholder: '@contacts_nationality_eritrea', language: '@language_fr_ca', value: Érythrée } }
  - { fields: { placeholder: '@contacts_nationality_estonia', language: '@language_fr_ca', value: Estonie } }
  - { fields: { placeholder: '@contacts_nationality_ethiopia', language: '@language_fr_ca', value: Éthiopie } }
  - { fields: { placeholder: '@contacts_nationality_falkland_islands_malvinas', language: '@language_fr_ca', value: 'Îles Falkland (Malouines)' } }
  - { fields: { placeholder: '@contacts_nationality_faroe_islands', language: '@language_fr_ca', value: 'Îles Féroé' } }
  - { fields: { placeholder: '@contacts_nationality_fiji', language: '@language_fr_ca', value: Fidji } }
  - { fields: { placeholder: '@contacts_nationality_finland', language: '@language_fr_ca', value: Finlande } }
  - { fields: { placeholder: '@contacts_nationality_france', language: '@language_fr_ca', value: France } }
  - { fields: { placeholder: '@contacts_nationality_french_guiana', language: '@language_fr_ca', value: 'Guyane française' } }
  - { fields: { placeholder: '@contacts_nationality_french_polynesia', language: '@language_fr_ca', value: 'Polynésie française' } }
  - { fields: { placeholder: '@contacts_nationality_french_southern_territories', language: '@language_fr_ca', value: 'Terres australes françaises' } }
  - { fields: { placeholder: '@contacts_nationality_gabon', language: '@language_fr_ca', value: Gabon } }
  - { fields: { placeholder: '@contacts_nationality_gambia', language: '@language_fr_ca', value: Gambie } }
  - { fields: { placeholder: '@contacts_nationality_georgia', language: '@language_fr_ca', value: Géorgie } }
  - { fields: { placeholder: '@contacts_nationality_germany', language: '@language_fr_ca', value: Allemagne } }
  - { fields: { placeholder: '@contacts_nationality_ghana', language: '@language_fr_ca', value: Ghana } }
  - { fields: { placeholder: '@contacts_nationality_gibraltar', language: '@language_fr_ca', value: Gibraltar } }
  - { fields: { placeholder: '@contacts_nationality_greece', language: '@language_fr_ca', value: Grèce } }
  - { fields: { placeholder: '@contacts_nationality_greenland', language: '@language_fr_ca', value: Groenland } }
  - { fields: { placeholder: '@contacts_nationality_grenada', language: '@language_fr_ca', value: Grenade } }
  - { fields: { placeholder: '@contacts_nationality_guadeloupe', language: '@language_fr_ca', value: Guadeloupe } }
  - { fields: { placeholder: '@contacts_nationality_guam', language: '@language_fr_ca', value: Guam } }
  - { fields: { placeholder: '@contacts_nationality_guatemala', language: '@language_fr_ca', value: Guatemala } }
  - { fields: { placeholder: '@contacts_nationality_guernsey', language: '@language_fr_ca', value: Guernesey } }
  - { fields: { placeholder: '@contacts_nationality_guinea', language: '@language_fr_ca', value: Guinée } }
  - { fields: { placeholder: '@contacts_nationality_guinea_bissau', language: '@language_fr_ca', value: Guinée-Bissau } }
  - { fields: { placeholder: '@contacts_nationality_guyana', language: '@language_fr_ca', value: Guyane } }
  - { fields: { placeholder: '@contacts_nationality_haiti', language: '@language_fr_ca', value: Haïti } }
  - { fields: { placeholder: '@contacts_nationality_heard_island_and_mcdonald_islands', language: '@language_fr_ca', value: 'Île Heard et Îles McDonald' } }
  - { fields: { placeholder: '@contacts_nationality_holy_see_vatican_city_state', language: '@language_fr_ca', value: 'Saint-Siège (État de la Cité du Vatican )' } }
  - { fields: { placeholder: '@contacts_nationality_honduras', language: '@language_fr_ca', value: Honduras } }
  - { fields: { placeholder: '@contacts_nationality_hungary', language: '@language_fr_ca', value: Hongrie } }
  - { fields: { placeholder: '@contacts_nationality_iceland', language: '@language_fr_ca', value: Islande } }
  - { fields: { placeholder: '@contacts_nationality_india', language: '@language_fr_ca', value: Inde } }
  - { fields: { placeholder: '@contacts_nationality_indonesia', language: '@language_fr_ca', value: Indonésie } }
  - { fields: { placeholder: '@contacts_nationality_iran_islamic_republic_of', language: '@language_fr_ca', value: 'Iran, République islamique d''' } }
  - { fields: { placeholder: '@contacts_nationality_iraq', language: '@language_fr_ca', value: Irak } }
  - { fields: { placeholder: '@contacts_nationality_ireland', language: '@language_fr_ca', value: Irlande } }
  - { fields: { placeholder: '@contacts_nationality_isle_of_man', language: '@language_fr_ca', value: 'Île de Man' } }
  - { fields: { placeholder: '@contacts_nationality_israel', language: '@language_fr_ca', value: Israël } }
  - { fields: { placeholder: '@contacts_nationality_italy', language: '@language_fr_ca', value: Italie } }
  - { fields: { placeholder: '@contacts_nationality_jamaica', language: '@language_fr_ca', value: Jamaïque } }
  - { fields: { placeholder: '@contacts_nationality_japan', language: '@language_fr_ca', value: Japon } }
  - { fields: { placeholder: '@contacts_nationality_jersey', language: '@language_fr_ca', value: Jersey } }
  - { fields: { placeholder: '@contacts_nationality_jordan', language: '@language_fr_ca', value: Jordanie } }
  - { fields: { placeholder: '@contacts_nationality_kazakhstan', language: '@language_fr_ca', value: Kazakhstan } }
  - { fields: { placeholder: '@contacts_nationality_kenya', language: '@language_fr_ca', value: Kenya } }
  - { fields: { placeholder: '@contacts_nationality_kiribati', language: '@language_fr_ca', value: Kiribati } }
  - { fields: { placeholder: '@contacts_nationality_korea_democratic_peoples_republic_of', language: '@language_fr_ca', value: 'Corée, République populaire démocratique de' } }
  - { fields: { placeholder: '@contacts_nationality_korea_republic_of', language: '@language_fr_ca', value: 'Corée, République de' } }
  - { fields: { placeholder: '@contacts_nationality_kuwait', language: '@language_fr_ca', value: Kowait } }
  - { fields: { placeholder: '@contacts_nationality_kyrgyzstan', language: '@language_fr_ca', value: Kirghizistan } }
  - { fields: { placeholder: '@contacts_nationality_lao_pdr', language: '@language_fr_ca', value: 'Laos, RPD' } }
  - { fields: { placeholder: '@contacts_nationality_latvia', language: '@language_fr_ca', value: Lettonie } }
  - { fields: { placeholder: '@contacts_nationality_lebanon', language: '@language_fr_ca', value: Liban } }
  - { fields: { placeholder: '@contacts_nationality_lesotho', language: '@language_fr_ca', value: Lesotho } }
  - { fields: { placeholder: '@contacts_nationality_liberia', language: '@language_fr_ca', value: Liberia } }
  - { fields: { placeholder: '@contacts_nationality_libya', language: '@language_fr_ca', value: Libye } }
  - { fields: { placeholder: '@contacts_nationality_liechtenstein', language: '@language_fr_ca', value: Liechtenstein } }
  - { fields: { placeholder: '@contacts_nationality_lithuania', language: '@language_fr_ca', value: Lituanie } }
  - { fields: { placeholder: '@contacts_nationality_luxembourg', language: '@language_fr_ca', value: Luxembourg } }
  - { fields: { placeholder: '@contacts_nationality_macedonia_republic_of', language: '@language_fr_ca', value: 'Macédonie, République de' } }
  - { fields: { placeholder: '@contacts_nationality_madagascar', language: '@language_fr_ca', value: Madagascar } }
  - { fields: { placeholder: '@contacts_nationality_malawi', language: '@language_fr_ca', value: Malawi } }
  - { fields: { placeholder: '@contacts_nationality_malaysia', language: '@language_fr_ca', value: Malaisie } }
  - { fields: { placeholder: '@contacts_nationality_maldives', language: '@language_fr_ca', value: Maldives } }
  - { fields: { placeholder: '@contacts_nationality_mali', language: '@language_fr_ca', value: Mali } }
  - { fields: { placeholder: '@contacts_nationality_malta', language: '@language_fr_ca', value: Malte } }
  - { fields: { placeholder: '@contacts_nationality_marshall_islands', language: '@language_fr_ca', value: 'Iles Marshall' } }
  - { fields: { placeholder: '@contacts_nationality_martinique', language: '@language_fr_ca', value: Martinique } }
  - { fields: { placeholder: '@contacts_nationality_mauritania', language: '@language_fr_ca', value: Mauritanie } }
  - { fields: { placeholder: '@contacts_nationality_mauritius', language: '@language_fr_ca', value: Maurice } }
  - { fields: { placeholder: '@contacts_nationality_mayotte', language: '@language_fr_ca', value: Mayotte } }
  - { fields: { placeholder: '@contacts_nationality_mexico', language: '@language_fr_ca', value: Mexique } }
  - { fields: { placeholder: '@contacts_nationality_micronesia_federated_states_of', language: '@language_fr_ca', value: 'Micronésie, États fédérés de' } }
  - { fields: { placeholder: '@contacts_nationality_moldova', language: '@language_fr_ca', value: Moldavie } }
  - { fields: { placeholder: '@contacts_nationality_monaco', language: '@language_fr_ca', value: Monaco } }
  - { fields: { placeholder: '@contacts_nationality_mongolia', language: '@language_fr_ca', value: Mongolie } }
  - { fields: { placeholder: '@contacts_nationality_montenegro', language: '@language_fr_ca', value: Monténégro } }
  - { fields: { placeholder: '@contacts_nationality_montserrat', language: '@language_fr_ca', value: Montserrat } }
  - { fields: { placeholder: '@contacts_nationality_morocco', language: '@language_fr_ca', value: Maroc } }
  - { fields: { placeholder: '@contacts_nationality_mozambique', language: '@language_fr_ca', value: Mozambique } }
  - { fields: { placeholder: '@contacts_nationality_myanmar', language: '@language_fr_ca', value: Myanmar } }
  - { fields: { placeholder: '@contacts_nationality_namibia', language: '@language_fr_ca', value: Namibie } }
  - { fields: { placeholder: '@contacts_nationality_nauru', language: '@language_fr_ca', value: Nauru } }
  - { fields: { placeholder: '@contacts_nationality_nepal', language: '@language_fr_ca', value: Népal } }
  - { fields: { placeholder: '@contacts_nationality_netherlands', language: '@language_fr_ca', value: Pays-Bas } }
  - { fields: { placeholder: '@contacts_nationality_netherlands_antilles', language: '@language_fr_ca', value: 'Antilles néerlandaises' } }
  - { fields: { placeholder: '@contacts_nationality_new_caledonia', language: '@language_fr_ca', value: 'Nouvelle Calédonie' } }
  - { fields: { placeholder: '@contacts_nationality_new_zealand', language: '@language_fr_ca', value: Nouvelle-Zélande } }
  - { fields: { placeholder: '@contacts_nationality_nicaragua', language: '@language_fr_ca', value: Nicaragua } }
  - { fields: { placeholder: '@contacts_nationality_niger', language: '@language_fr_ca', value: Niger } }
  - { fields: { placeholder: '@contacts_nationality_nigeria', language: '@language_fr_ca', value: Nigeria } }
  - { fields: { placeholder: '@contacts_nationality_niue', language: '@language_fr_ca', value: Niue } }
  - { fields: { placeholder: '@contacts_nationality_norfolk_island', language: '@language_fr_ca', value: 'Île de Norfolk' } }
  - { fields: { placeholder: '@contacts_nationality_northern_mariana_islands', language: '@language_fr_ca', value: 'Îles Mariannes du Nord' } }
  - { fields: { placeholder: '@contacts_nationality_norway', language: '@language_fr_ca', value: Norvège } }
  - { fields: { placeholder: '@contacts_nationality_oman', language: '@language_fr_ca', value: Oman } }
  - { fields: { placeholder: '@contacts_nationality_pakistan', language: '@language_fr_ca', value: Pakistan } }
  - { fields: { placeholder: '@contacts_nationality_palau', language: '@language_fr_ca', value: Palau } }
  - { fields: { placeholder: '@contacts_nationality_palestinian_territory_occupied', language: '@language_fr_ca', value: 'Territoire palestinien, occupé' } }
  - { fields: { placeholder: '@contacts_nationality_panama', language: '@language_fr_ca', value: Panama } }
  - { fields: { placeholder: '@contacts_nationality_papua_new_guinea', language: '@language_fr_ca', value: 'Papouasie Nouvelle-Guinée' } }
  - { fields: { placeholder: '@contacts_nationality_paraguay', language: '@language_fr_ca', value: Paraguay } }
  - { fields: { placeholder: '@contacts_nationality_peru', language: '@language_fr_ca', value: Pérou } }
  - { fields: { placeholder: '@contacts_nationality_philippines', language: '@language_fr_ca', value: Philippines } }
  - { fields: { placeholder: '@contacts_nationality_pitcairn', language: '@language_fr_ca', value: Pitcairn } }
  - { fields: { placeholder: '@contacts_nationality_poland', language: '@language_fr_ca', value: Pologne } }
  - { fields: { placeholder: '@contacts_nationality_portugal', language: '@language_fr_ca', value: Portugal } }
  - { fields: { placeholder: '@contacts_nationality_puerto_rico', language: '@language_fr_ca', value: 'Porto Rico' } }
  - { fields: { placeholder: '@contacts_nationality_qatar', language: '@language_fr_ca', value: Qatar } }
  - { fields: { placeholder: '@contacts_nationality_reunion', language: '@language_fr_ca', value: 'La Réunion' } }
  - { fields: { placeholder: '@contacts_nationality_romania', language: '@language_fr_ca', value: Roumanie } }
  - { fields: { placeholder: '@contacts_nationality_russian_federation', language: '@language_fr_ca', value: 'Russie, Fédération de' } }
  - { fields: { placeholder: '@contacts_nationality_rwanda', language: '@language_fr_ca', value: Rwanda } }
  - { fields: { placeholder: '@contacts_nationality_saint_barthelemy', language: '@language_fr_ca', value: Saint-Barthélemy } }
  - { fields: { placeholder: '@contacts_nationality_saint_helena', language: '@language_fr_ca', value: Sainte-Hélène } }
  - { fields: { placeholder: '@contacts_nationality_saint_kitts_and_nevis', language: '@language_fr_ca', value: Saint-Christophe-et-Niévès } }
  - { fields: { placeholder: '@contacts_nationality_saint_lucia', language: '@language_fr_ca', value: Sainte-Lucie } }
  - { fields: { placeholder: '@contacts_nationality_saint_martin_french_part', language: '@language_fr_ca', value: 'Saint-Martin (partie française)' } }
  - { fields: { placeholder: '@contacts_nationality_saint_pierre_and_miquelon', language: '@language_fr_ca', value: 'Saint-Pierre et Miquelon' } }
  - { fields: { placeholder: '@contacts_nationality_saint_vincent_and_grenadines', language: '@language_fr_ca', value: Saint-Vincent-et-les-Grenadines } }
  - { fields: { placeholder: '@contacts_nationality_samoa', language: '@language_fr_ca', value: Samoa } }
  - { fields: { placeholder: '@contacts_nationality_san_marino', language: '@language_fr_ca', value: 'San Marin' } }
  - { fields: { placeholder: '@contacts_nationality_sao_tome_and_principe', language: '@language_fr_ca', value: 'Sao Tomé et Principe' } }
  - { fields: { placeholder: '@contacts_nationality_saudi_arabia', language: '@language_fr_ca', value: 'Arabie Saoudite' } }
  - { fields: { placeholder: '@contacts_nationality_senegal', language: '@language_fr_ca', value: Sénégal } }
  - { fields: { placeholder: '@contacts_nationality_serbia', language: '@language_fr_ca', value: Serbie } }
  - { fields: { placeholder: '@contacts_nationality_seychelles', language: '@language_fr_ca', value: Seychelles } }
  - { fields: { placeholder: '@contacts_nationality_sierra_leone', language: '@language_fr_ca', value: 'Sierra Leone' } }
  - { fields: { placeholder: '@contacts_nationality_singapore', language: '@language_fr_ca', value: Singapour } }
  - { fields: { placeholder: '@contacts_nationality_sint_maarten_dutch_part', language: '@language_fr_ca', value: 'Sint Maarten (partie néerlandaise)' } }
  - { fields: { placeholder: '@contacts_nationality_slovakia', language: '@language_fr_ca', value: Slovaquie } }
  - { fields: { placeholder: '@contacts_nationality_slovenia', language: '@language_fr_ca', value: Slovénie } }
  - { fields: { placeholder: '@contacts_nationality_solomon_islands', language: '@language_fr_ca', value: 'îles Salomon' } }
  - { fields: { placeholder: '@contacts_nationality_somalia', language: '@language_fr_ca', value: Somalie } }
  - { fields: { placeholder: '@contacts_nationality_south_africa', language: '@language_fr_ca', value: 'Afrique du Sud' } }
  - { fields: { placeholder: '@contacts_nationality_south_georgia_and_the_south_sandwich_islands', language: '@language_fr_ca', value: 'Géorgie du Sud et les îles Sandwich du Sud' } }
  - { fields: { placeholder: '@contacts_nationality_south_sudan', language: '@language_fr_ca', value: 'Soudan du Sud' } }
  - { fields: { placeholder: '@contacts_nationality_spain', language: '@language_fr_ca', value: Espagne } }
  - { fields: { placeholder: '@contacts_nationality_sri_lanka', language: '@language_fr_ca', value: 'Sri Lanka' } }
  - { fields: { placeholder: '@contacts_nationality_sudan', language: '@language_fr_ca', value: Soudan } }
  - { fields: { placeholder: '@contacts_nationality_suriname', language: '@language_fr_ca', value: 'Suriname *' } }
  - { fields: { placeholder: '@contacts_nationality_svalbard_and_jan_mayen_islands', language: '@language_fr_ca', value: 'Îles Svalbard et Jan Mayen' } }
  - { fields: { placeholder: '@contacts_nationality_swaziland', language: '@language_fr_ca', value: Swaziland } }
  - { fields: { placeholder: '@contacts_nationality_sweden', language: '@language_fr_ca', value: Suède } }
  - { fields: { placeholder: '@contacts_nationality_switzerland', language: '@language_fr_ca', value: Suisse } }
  - { fields: { placeholder: '@contacts_nationality_syrian_arab_republic_syria', language: '@language_fr_ca', value: 'Syrie, République arabe syrienne' } }
  - { fields: { placeholder: '@contacts_nationality_taiwan', language: '@language_fr_ca', value: Taïwan } }
  - { fields: { placeholder: '@contacts_nationality_tajikistan', language: '@language_fr_ca', value: Tadjikistan } }
  - { fields: { placeholder: '@contacts_nationality_tanzania_united_republic_of', language: '@language_fr_ca', value: 'Tanzanie *, République unie de' } }
  - { fields: { placeholder: '@contacts_nationality_thailand', language: '@language_fr_ca', value: Thaïlande } }
  - { fields: { placeholder: '@contacts_nationality_timor_leste', language: '@language_fr_ca', value: Timor-Leste } }
  - { fields: { placeholder: '@contacts_nationality_togo', language: '@language_fr_ca', value: Togo } }
  - { fields: { placeholder: '@contacts_nationality_tokelau', language: '@language_fr_ca', value: Tokélaou } }
  - { fields: { placeholder: '@contacts_nationality_tonga', language: '@language_fr_ca', value: Tonga } }
  - { fields: { placeholder: '@contacts_nationality_trinidad_and_tobago', language: '@language_fr_ca', value: Trinité-et-Tobago } }
  - { fields: { placeholder: '@contacts_nationality_tunisia', language: '@language_fr_ca', value: Tunisie } }
  - { fields: { placeholder: '@contacts_nationality_turkey', language: '@language_fr_ca', value: Turquie } }
  - { fields: { placeholder: '@contacts_nationality_turkmenistan', language: '@language_fr_ca', value: Turkménistan } }
  - { fields: { placeholder: '@contacts_nationality_turks_and_caicos_islands', language: '@language_fr_ca', value: 'îles Turques-et-Caïques' } }
  - { fields: { placeholder: '@contacts_nationality_tuvalu', language: '@language_fr_ca', value: Tuvalu } }
  - { fields: { placeholder: '@contacts_nationality_uganda', language: '@language_fr_ca', value: Ouganda } }
  - { fields: { placeholder: '@contacts_nationality_ukraine', language: '@language_fr_ca', value: Ukraine } }
  - { fields: { placeholder: '@contacts_nationality_united_arab_emirates', language: '@language_fr_ca', value: 'Émirats arabes unis' } }
  - { fields: { placeholder: '@contacts_nationality_united_kingdom', language: '@language_fr_ca', value: Royaume-Uni } }
  - { fields: { placeholder: '@contacts_nationality_united_states_of_america', language: '@language_fr_ca', value: 'États-Unis d''Amérique' } }
  - { fields: { placeholder: '@contacts_nationality_united_states_minor_outlying_islands', language: '@language_fr_ca', value: 'Îles mineures éloignées des États-Unis' } }
  - { fields: { placeholder: '@contacts_nationality_uruguay', language: '@language_fr_ca', value: Uruguay } }
  - { fields: { placeholder: '@contacts_nationality_uzbekistan', language: '@language_fr_ca', value: Ouzbékistan } }
  - { fields: { placeholder: '@contacts_nationality_vanuatu', language: '@language_fr_ca', value: Vanuatu } }
  - { fields: { placeholder: '@contacts_nationality_venezuela_bolivarian_republic_of', language: '@language_fr_ca', value: 'Venezuela (République bolivarienne du)' } }
  - { fields: { placeholder: '@contacts_nationality_vietnam', language: '@language_fr_ca', value: Vietnam } }
  - { fields: { placeholder: '@contacts_nationality_virgin_islands_us', language: '@language_fr_ca', value: 'Îles vierges américaines' } }
  - { fields: { placeholder: '@contacts_nationality_wallis_and_futuna_islands', language: '@language_fr_ca', value: 'Îles de Wallis-et-Futuna' } }
  - { fields: { placeholder: '@contacts_nationality_western_sahara', language: '@language_fr_ca', value: 'Sahara occidental' } }
  - { fields: { placeholder: '@contacts_nationality_yemen', language: '@language_fr_ca', value: Yémen } }
  - { fields: { placeholder: '@contacts_nationality_zambia', language: '@language_fr_ca', value: Zambie } }
  - { fields: { placeholder: '@contacts_nationality_zimbabwe', language: '@language_fr_ca', value: Zimbabwe } }
  - { fields: { placeholder: '@contacts_id_number_types_singular', language: '@language_fr_ca', value: 'Type de numéro d''ID' } }
  - { fields: { placeholder: '@contacts_id_number_types_plural', language: '@language_fr_ca', value: 'Types de numéro d''identifiant' } }
  - { fields: { placeholder: '@contacts_id_number_types_all_types', language: '@language_fr_ca', value: 'Tous les types' } }
  - { fields: { placeholder: '@contacts_id_number_types_search', language: '@language_fr_ca', value: Rechercher } }
  - { fields: { placeholder: '@contacts_id_number_types_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@contacts_events_saved', language: '@language_fr_ca', value: 'Contact enregistré avec succès' } }
  - { fields: { placeholder: '@contacts_events_deleted', language: '@language_fr_ca', value: 'Contact supprimé avec succès' } }
  - { fields: { placeholder: '@contacts_filter_form_title', language: '@language_fr_ca', value: 'Filtrer les contacts' } }
  - { fields: { placeholder: '@contacts_form_job_title_title', language: '@language_fr_ca', value: 'Titre du poste' } }
  - { fields: { placeholder: '@contacts_form_job_title_label', language: '@language_fr_ca', value: 'Titre du poste' } }
  - { fields: { placeholder: '@contacts_form_source_of_record_title', language: '@language_fr_ca', value: 'Source du dossier' } }
  - { fields: { placeholder: '@contacts_form_source_of_record_label', language: '@language_fr_ca', value: 'Source du dossier' } }
