entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@contacts_create', language: '@language_ar', value: 'إنشاء جهة اتصال' } }
    - { fields: { placeholder: '@contacts_edit', language: '@language_ar', value: 'تحرير الاتصال' } }
    - { fields: { placeholder: '@contacts_edit_contact', language: '@language_ar', value: 'تحرير الاتصال' } }
    - { fields: { placeholder: '@contacts_form_title', language: '@language_ar', value: 'جهات الاتصال' } }
    - { fields: { placeholder: '@contacts_form_section_title', language: '@language_ar', value: 'جهة اتصال جديدة' } }
    - { fields: { placeholder: '@contacts_error_get_form', language: '@language_ar', value: 'حدث خطأ أثناء استرداد نموذج جهة الاتصال' } }
    - { fields: { placeholder: '@contacts_form_addresses_business', language: '@language_ar', value: اعمال } }
    - { fields: { placeholder: '@contacts_form_addresses_city', language: '@language_ar', value: مدينة } }
    - { fields: { placeholder: '@contacts_form_addresses_correspondence', language: '@language_ar', value: مراسلة } }
    - { fields: { placeholder: '@contacts_form_addresses_country', language: '@language_ar', value: بلد } }
    - { fields: { placeholder: '@contacts_form_addresses_county', language: '@language_ar', value: بلد } }
    - { fields: { placeholder: '@contacts_form_addresses_line1', language: '@language_ar', value: 'خط 1' } }
    - { fields: { placeholder: '@contacts_form_addresses_line2', language: '@language_ar', value: 'خط 2' } }
    - { fields: { placeholder: '@contacts_form_addresses_line3', language: '@language_ar', value: 'خط 3' } }
    - { fields: { placeholder: '@contacts_form_addresses_plural', language: '@language_ar', value: عناوين } }
    - { fields: { placeholder: '@contacts_form_addresses_plural_label', language: '@language_ar', value: عناوين } }
    - { fields: { placeholder: '@contacts_form_addresses_postal', language: '@language_ar', value: بريدي } }
    - { fields: { placeholder: '@contacts_form_addresses_postcode', language: '@language_ar', value: 'الرمز البريدي' } }
    - { fields: { placeholder: '@contacts_form_addresses_residential', language: '@language_ar', value: سكني } }
    - { fields: { placeholder: '@contacts_form_addresses_singular', language: '@language_ar', value: العنوان } }
    - { fields: { placeholder: '@contacts_form_addresses_trading', language: '@language_ar', value: تجارة } }
    - { fields: { placeholder: '@contacts_form_addresses_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_disability_plural', language: '@language_ar', value: الإعاقة } }
    - { fields: { placeholder: '@contacts_form_disability_plural_label', language: '@language_ar', value: الإعاقة } }
    - { fields: { placeholder: '@contacts_form_disability_singular', language: '@language_ar', value: عجز } }
    - { fields: { placeholder: '@contacts_form_email_email_address', language: '@language_ar', value: 'عنوان البريد الإلكتروني' } }
    - { fields: { placeholder: '@contacts_form_email_personal', language: '@language_ar', value: 'رسائل البريد الإلكتروني' } }
    - { fields: { placeholder: '@contacts_form_email_plural', language: '@language_ar', value: 'رسائل البريد الإلكتروني' } }
    - { fields: { placeholder: '@contacts_form_email_plural_label', language: '@language_ar', value: 'رسائل البريد الإلكتروني' } }
    - { fields: { placeholder: '@contacts_form_email_singular', language: '@language_ar', value: 'البريد الإلكتروني' } }
    - { fields: { placeholder: '@contacts_form_email_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_email_work', language: '@language_ar', value: عمل } }
    - { fields: { placeholder: '@contacts_form_field_date_of_birth', language: '@language_ar', value: 'تاريخ الولادة' } }
    - { fields: { placeholder: '@contacts_form_field_date_of_death', language: '@language_ar', value: 'تاريخ الوفاة' } }
    - { fields: { placeholder: '@contacts_form_field_ethnicity', language: '@language_ar', value: 'الأصل العرقي' } }
    - { fields: { placeholder: '@contacts_form_field_forename', language: '@language_ar', value: 'الاسم الاول' } }
    - { fields: { placeholder: '@contacts_form_field_gender', language: '@language_ar', value: جنس } }
    - { fields: { placeholder: '@contacts_form_field_gender_female', language: '@language_ar', value: أنثى } }
    - { fields: { placeholder: '@contacts_form_field_gender_male', language: '@language_ar', value: ذكر } }
    - { fields: { placeholder: '@contacts_form_field_language', language: '@language_ar', value: لغة } }
    - { fields: { placeholder: '@contacts_form_field_lone_worker', language: '@language_ar', value: 'تقييم كعامل وحيد؟' } }
    - { fields: { placeholder: '@contacts_form_field_notes', language: '@language_ar', value: ملاحظات } }
    - { fields: { placeholder: '@contacts_form_field_religion', language: '@language_ar', value: دين } }
    - { fields: { placeholder: '@contacts_form_field_select_ethnicity', language: '@language_ar', value: 'اختر العرق' } }
    - { fields: { placeholder: '@contacts_form_field_select_gender', language: '@language_ar', value: 'حدد نوع الجنس' } }
    - { fields: { placeholder: '@contacts_form_field_select_religion', language: '@language_ar', value: 'اختر الدين' } }
    - { fields: { placeholder: '@contacts_form_field_select_sexual_orientation', language: '@language_ar', value: 'اختر التوجه الجنسي' } }
    - { fields: { placeholder: '@contacts_form_field_select_subtype', language: '@language_ar', value: 'حدد النوع الفرعي' } }
    - { fields: { placeholder: '@contacts_form_field_select_type', language: '@language_ar', value: 'اختر صنف' } }
    - { fields: { placeholder: '@contacts_form_field_sexual_orientation', language: '@language_ar', value: 'التوجه الجنسي' } }
    - { fields: { placeholder: '@contacts_form_field_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@contacts_form_field_status_select_status', language: '@language_ar', value: 'اختر الحالة' } }
    - { fields: { placeholder: '@contacts_form_field_status_value_approved', language: '@language_ar', value: 'موافق عليه' } }
    - { fields: { placeholder: '@contacts_form_field_status_value_rejected', language: '@language_ar', value: مرفوض } }
    - { fields: { placeholder: '@contacts_form_field_status_value_unapproved', language: '@language_ar', value: 'غير مموافق عليه عليها' } }
    - { fields: { placeholder: '@contacts_form_field_subtype', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@contacts_form_field_surname', language: '@language_ar', value: لقب } }
    - { fields: { placeholder: '@contacts_form_field_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@contacts_form_field_title_select_title', language: '@language_ar', value: 'اختر العنوان' } }
    - { fields: { placeholder: '@contacts_form_field_title_value_adv', language: '@language_ar', value: حال } }
    - { fields: { placeholder: '@contacts_form_field_title_value_br', language: '@language_ar', value: شقيق } }
    - { fields: { placeholder: '@contacts_form_field_title_value_dame', language: '@language_ar', value: سيدة } }
    - { fields: { placeholder: '@contacts_form_field_title_value_dr', language: '@language_ar', value: طبيب } }
    - { fields: { placeholder: '@contacts_form_field_title_value_elder', language: '@language_ar', value: المسنين } }
    - { fields: { placeholder: '@contacts_form_field_title_value_esq', language: '@language_ar', value: إسق } }
    - { fields: { placeholder: '@contacts_form_field_title_value_fr', language: '@language_ar', value: الآب } }
    - { fields: { placeholder: '@contacts_form_field_title_value_lady', language: '@language_ar', value: سيدة } }
    - { fields: { placeholder: '@contacts_form_field_title_value_lord', language: '@language_ar', value: رب } }
    - { fields: { placeholder: '@contacts_form_field_title_value_madam', language: '@language_ar', value: سيدتي } }
    - { fields: { placeholder: '@contacts_form_field_title_value_master', language: '@language_ar', value: رئيس } }
    - { fields: { placeholder: '@contacts_form_field_title_value_miss', language: '@language_ar', value: يغيب } }
    - { fields: { placeholder: '@contacts_form_field_title_value_mr', language: '@language_ar', value: السيد } }
    - { fields: { placeholder: '@contacts_form_field_title_value_mrs', language: '@language_ar', value: السيدة } }
    - { fields: { placeholder: '@contacts_form_field_title_value_ms', language: '@language_ar', value: الآنسة } }
    - { fields: { placeholder: '@contacts_form_field_title_value_pr', language: '@language_ar', value: القس } }
    - { fields: { placeholder: '@contacts_form_field_title_value_prof', language: '@language_ar', value: 'دكتور جامعى' } }
    - { fields: { placeholder: '@contacts_form_field_title_value_rabbi', language: '@language_ar', value: الحبر } }
    - { fields: { placeholder: '@contacts_form_field_title_value_rev', language: '@language_ar', value: موقر } }
    - { fields: { placeholder: '@contacts_form_field_title_value_sir', language: '@language_ar', value: 'سيدي المحترم' } }
    - { fields: { placeholder: '@contacts_form_field_title_value_sr', language: '@language_ar', value: أخت } }
    - { fields: { placeholder: '@contacts_form_field_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_id_numbers_inpatient', language: '@language_ar', value: 'في المريض' } }
    - { fields: { placeholder: '@contacts_form_id_numbers_nhs_number', language: '@language_ar', value: 'رقم NHS' } }
    - { fields: { placeholder: '@contacts_form_id_numbers_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@contacts_form_id_numbers_patient_number', language: '@language_ar', value: 'رقم المريض' } }
    - { fields: { placeholder: '@contacts_form_id_numbers_plural', language: '@language_ar', value: أعداد } }
    - { fields: { placeholder: '@contacts_form_id_numbers_police_id', language: '@language_ar', value: 'معرف الشرطة' } }
    - { fields: { placeholder: '@contacts_form_id_numbers_singular', language: '@language_ar', value: رقم } }
    - { fields: { placeholder: '@contacts_form_id_numbers_staff_number', language: '@language_ar', value: 'رقم الموظف' } }
    - { fields: { placeholder: '@contacts_form_id_numbers_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_numbers_home', language: '@language_ar', value: 'الصفحة الرئيسية' } }
    - { fields: { placeholder: '@contacts_form_numbers_mobile', language: '@language_ar', value: 'التليفون المحمول' } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_numbers', language: '@language_ar', value: 'أرقام الهواتف' } }
    - { fields: { placeholder: '@contacts_form_numbers_plural', language: '@language_ar', value: أعداد } }
    - { fields: { placeholder: '@contacts_form_numbers_plural_label', language: '@language_ar', value: أعداد } }
    - { fields: { placeholder: '@contacts_form_numbers_singular', language: '@language_ar', value: رقم } }
    - { fields: { placeholder: '@contacts_form_numbers_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_numbers_work', language: '@language_ar', value: عمل } }
    - { fields: { placeholder: '@contacts_form_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ جهة الاتصال' } }
    - { fields: { placeholder: '@contacts_loading_contact', language: '@language_ar', value: 'تحميل جهة الاتصال' } }
    - { fields: { placeholder: '@contacts_loading_contacts', language: '@language_ar', value: 'تحميل جهات الاتصال' } }
    - { fields: { placeholder: '@contacts_name', language: '@language_ar', value: اسم } }
    - { fields: { placeholder: '@contacts_nav_dashboard', language: '@language_ar', value: 'لوحة القيادة' } }
    - { fields: { placeholder: '@contacts_nav_new_contact', language: '@language_ar', value: 'جهة اتصال جديدة' } }
    - { fields: { placeholder: '@contacts_nav_permissions', language: '@language_ar', value: صلاحيات } }
    - { fields: { placeholder: '@contacts_plural', language: '@language_ar', value: 'جهات الاتصال' } }
    - { fields: { placeholder: '@contacts_related_primary_modules', language: '@language_ar', value: 'الوحدات الأساسية ذات الصلة' } }
    - { fields: { placeholder: '@contacts_search', language: '@language_ar', value: 'اتصالات بحث' } }
    - { fields: { placeholder: '@contacts_singular', language: '@language_ar', value: اتصل } }
    - { fields: { placeholder: '@contacts_view_contact', language: '@language_ar', value: 'عرض الاتصال' } }
    - { fields: { placeholder: '@contacts_datasource_languages_akan', language: '@language_ar', value: اكان } }
    - { fields: { placeholder: '@contacts_datasource_languages_amharic', language: '@language_ar', value: الأمهرية } }
    - { fields: { placeholder: '@contacts_datasource_languages_arabic', language: '@language_ar', value: عربى } }
    - { fields: { placeholder: '@contacts_datasource_languages_assamese', language: '@language_ar', value: الأسامية } }
    - { fields: { placeholder: '@contacts_datasource_languages_awadhi', language: '@language_ar', value: العوضي } }
    - { fields: { placeholder: '@contacts_datasource_languages_azerbaijani', language: '@language_ar', value: أذربيجان } }
    - { fields: { placeholder: '@contacts_datasource_languages_balochi', language: '@language_ar', value: البلوشية } }
    - { fields: { placeholder: '@contacts_datasource_languages_belarusian', language: '@language_ar', value: البيلاروسية } }
    - { fields: { placeholder: '@contacts_datasource_languages_bengali', language: '@language_ar', value: 'البنغالية (البنغالية)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_bhojpuri', language: '@language_ar', value: البهوجبرية } }
    - { fields: { placeholder: '@contacts_datasource_languages_burmese', language: '@language_ar', value: البورمية } }
    - { fields: { placeholder: '@contacts_datasource_languages_cebuano', language: '@language_ar', value: 'سيبوانو (فيسايان)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_chewa', language: '@language_ar', value: الشيوا } }
    - { fields: { placeholder: '@contacts_datasource_languages_chhattisgarhi', language: '@language_ar', value: شاتيسغاري } }
    - { fields: { placeholder: '@contacts_datasource_languages_chittagonian', language: '@language_ar', value: شاتيسغاري } }
    - { fields: { placeholder: '@contacts_datasource_languages_czech', language: '@language_ar', value: تشيكي } }
    - { fields: { placeholder: '@contacts_datasource_languages_deccan', language: '@language_ar', value: ديكان } }
    - { fields: { placeholder: '@contacts_datasource_languages_dhundhari', language: '@language_ar', value: دوندهاري } }
    - { fields: { placeholder: '@contacts_datasource_languages_dutch', language: '@language_ar', value: هولندي } }
    - { fields: { placeholder: '@contacts_datasource_languages_english', language: '@language_ar', value: الإنجليزية } }
    - { fields: { placeholder: '@contacts_datasource_languages_french', language: '@language_ar', value: الفرنسية } }
    - { fields: { placeholder: '@contacts_datasource_languages_fula', language: '@language_ar', value: فولا } }
    - { fields: { placeholder: '@contacts_datasource_languages_fuzhounese', language: '@language_ar', value: 'مين الشرقية (معفاة فوزونيز)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_gan_chinese', language: '@language_ar', value: 'قان الصينية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_german', language: '@language_ar', value: ألمانية } }
    - { fields: { placeholder: '@contacts_datasource_languages_greek', language: '@language_ar', value: الإغريقي } }
    - { fields: { placeholder: '@contacts_datasource_languages_gujarati', language: '@language_ar', value: الغوجاراتية } }
    - { fields: { placeholder: '@contacts_datasource_languages_haitian_creole', language: '@language_ar', value: 'الكريولية الهايتية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_hakka', language: '@language_ar', value: هاكا } }
    - { fields: { placeholder: '@contacts_datasource_languages_haryanvi', language: '@language_ar', value: هويانفي } }
    - { fields: { placeholder: '@contacts_datasource_languages_hausa', language: '@language_ar', value: الهوسا } }
    - { fields: { placeholder: '@contacts_datasource_languages_hiligaynon_ilonggo', language: '@language_ar', value: 'الهيليجينون/ اللونغو' } }
    - { fields: { placeholder: '@contacts_datasource_languages_hindi', language: '@language_ar', value: الهندية } }
    - { fields: { placeholder: '@contacts_datasource_languages_hmong', language: '@language_ar', value: همونغ } }
    - { fields: { placeholder: '@contacts_datasource_languages_hungarian', language: '@language_ar', value: الهنغارية } }
    - { fields: { placeholder: '@contacts_datasource_languages_igbo', language: '@language_ar', value: الإيبو } }
    - { fields: { placeholder: '@contacts_datasource_languages_ilocano', language: '@language_ar', value: إيلوكانو } }
    - { fields: { placeholder: '@contacts_datasource_languages_indian', language: '@language_ar', value: هندي } }
    - { fields: { placeholder: '@contacts_datasource_languages_italian', language: '@language_ar', value: الإيطالي } }
    - { fields: { placeholder: '@contacts_datasource_languages_japanese', language: '@language_ar', value: اليابانية } }
    - { fields: { placeholder: '@contacts_datasource_languages_javanese', language: '@language_ar', value: جاوي } }
    - { fields: { placeholder: '@contacts_datasource_languages_jin', language: '@language_ar', value: جين } }
    - { fields: { placeholder: '@contacts_datasource_languages_kannada', language: '@language_ar', value: الكانادا } }
    - { fields: { placeholder: '@contacts_datasource_languages_kazakh', language: '@language_ar', value: الكازاخية } }
    - { fields: { placeholder: '@contacts_datasource_languages_khmer', language: '@language_ar', value: الخمير } }
    - { fields: { placeholder: '@contacts_datasource_languages_kinyarwanda', language: '@language_ar', value: الكينيارواندية } }
    - { fields: { placeholder: '@contacts_datasource_languages_kirundi', language: '@language_ar', value: كيروندي } }
    - { fields: { placeholder: '@contacts_datasource_languages_konkani', language: '@language_ar', value: الكونكانية } }
    - { fields: { placeholder: '@contacts_datasource_languages_korean', language: '@language_ar', value: الكورية } }
    - { fields: { placeholder: '@contacts_datasource_languages_kurdish', language: '@language_ar', value: كردي } }
    - { fields: { placeholder: '@contacts_datasource_languages_madurese', language: '@language_ar', value: المادريز } }
    - { fields: { placeholder: '@contacts_datasource_languages_magahi', language: '@language_ar', value: ماغاي } }
    - { fields: { placeholder: '@contacts_datasource_languages_maithili', language: '@language_ar', value: ميثيلي } }
    - { fields: { placeholder: '@contacts_datasource_languages_malagasy', language: '@language_ar', value: مدغشقر } }
    - { fields: { placeholder: '@contacts_datasource_languages_malay', language: '@language_ar', value: 'الملايو (inc. الماليزية والإندونيسية)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_malayalam', language: '@language_ar', value: المالايالامية } }
    - { fields: { placeholder: '@contacts_datasource_languages_mandarin', language: '@language_ar', value: اليوسفي } }
    - { fields: { placeholder: '@contacts_datasource_languages_marathi', language: '@language_ar', value: المهاراتية } }
    - { fields: { placeholder: '@contacts_datasource_languages_marwari', language: '@language_ar', value: الماروارى } }
    - { fields: { placeholder: '@contacts_datasource_languages_mossi', language: '@language_ar', value: موسي } }
    - { fields: { placeholder: '@contacts_datasource_languages_nepali', language: '@language_ar', value: النيبالية } }
    - { fields: { placeholder: '@contacts_datasource_languages_northern_min', language: '@language_ar', value: 'شمال مين' } }
    - { fields: { placeholder: '@contacts_datasource_languages_odia', language: '@language_ar', value: 'أوديا (الأوريا)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_oromo', language: '@language_ar', value: أورومو } }
    - { fields: { placeholder: '@contacts_datasource_languages_pashto', language: '@language_ar', value: الباشتو } }
    - { fields: { placeholder: '@contacts_datasource_languages_persian', language: '@language_ar', value: 'اللغة الفارسية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_polish', language: '@language_ar', value: البولندي } }
    - { fields: { placeholder: '@contacts_datasource_languages_portuguese', language: '@language_ar', value: البرتغالية } }
    - { fields: { placeholder: '@contacts_datasource_languages_punjabi', language: '@language_ar', value: البنجابية } }
    - { fields: { placeholder: '@contacts_datasource_languages_quechua', language: '@language_ar', value: الكيشوا } }
    - { fields: { placeholder: '@contacts_datasource_languages_romanian', language: '@language_ar', value: روماني } }
    - { fields: { placeholder: '@contacts_datasource_languages_russian', language: '@language_ar', value: الروسية } }
    - { fields: { placeholder: '@contacts_datasource_languages_saraiki', language: '@language_ar', value: سارائيكي } }
    - { fields: { placeholder: '@contacts_datasource_languages_serbo_croatian', language: '@language_ar', value: 'الصربية الكرواتية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_shona', language: '@language_ar', value: شونا } }
    - { fields: { placeholder: '@contacts_datasource_languages_sindhi', language: '@language_ar', value: السندية } }
    - { fields: { placeholder: '@contacts_datasource_languages_sinhalese', language: '@language_ar', value: السنهالية } }
    - { fields: { placeholder: '@contacts_datasource_languages_somali', language: '@language_ar', value: الصومالية } }
    - { fields: { placeholder: '@contacts_datasource_languages_southern_min', language: '@language_ar', value: 'لهجة جنوب الصين  (بما في ذلك هوكين و تيوشو)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_spanish', language: '@language_ar', value: الأسبانية } }
    - { fields: { placeholder: '@contacts_datasource_languages_sundanese', language: '@language_ar', value: السودانية } }
    - { fields: { placeholder: '@contacts_datasource_languages_swedish', language: '@language_ar', value: 'اللغة السويدية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_sylheti', language: '@language_ar', value: سيلهيتي } }
    - { fields: { placeholder: '@contacts_datasource_languages_tagalog', language: '@language_ar', value: التغالوغ } }
    - { fields: { placeholder: '@contacts_datasource_languages_tamil', language: '@language_ar', value: التاميل } }
    - { fields: { placeholder: '@contacts_datasource_languages_telugu', language: '@language_ar', value: التيلجو } }
    - { fields: { placeholder: '@contacts_datasource_languages_thai', language: '@language_ar', value: التايلاندية } }
    - { fields: { placeholder: '@contacts_datasource_languages_turkish', language: '@language_ar', value: 'اللغة التركية' } }
    - { fields: { placeholder: '@contacts_datasource_languages_turkmen', language: '@language_ar', value: التركمان } }
    - { fields: { placeholder: '@contacts_datasource_languages_ukrainian', language: '@language_ar', value: الأوكراني } }
    - { fields: { placeholder: '@contacts_datasource_languages_urdu', language: '@language_ar', value: الأردية } }
    - { fields: { placeholder: '@contacts_datasource_languages_uyghur', language: '@language_ar', value: الأويغور } }
    - { fields: { placeholder: '@contacts_datasource_languages_uzbek', language: '@language_ar', value: الأوزبكي } }
    - { fields: { placeholder: '@contacts_datasource_languages_vietnamese', language: '@language_ar', value: الفيتنامية } }
    - { fields: { placeholder: '@contacts_datasource_languages_wu', language: '@language_ar', value: 'وو (بما في ذلك شنغهاي)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_xhosa', language: '@language_ar', value: زوسا } }
    - { fields: { placeholder: '@contacts_datasource_languages_xiang', language: '@language_ar', value: 'شيانغ (هونانيز)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_yoruba', language: '@language_ar', value: اليوروبا } }
    - { fields: { placeholder: '@contacts_datasource_languages_yue', language: '@language_ar', value: 'يوي (بما في ذلك الكانتونية)' } }
    - { fields: { placeholder: '@contacts_datasource_languages_zhuang', language: '@language_ar', value: 'الحكم لقومية تشوانغ' } }
    - { fields: { placeholder: '@contacts_datasource_languages_zulu', language: '@language_ar', value: الزولو } }
    - { fields: { placeholder: '@contacts_datasource_disabilities', language: '@language_ar', value: الإعاقة } }
    - { fields: { placeholder: '@contacts_datasource_ethnicities', language: '@language_ar', value: العرقية } }
    - { fields: { placeholder: '@contacts_datasource_gender', language: '@language_ar', value: 'نوع الجنس' } }
    - { fields: { placeholder: '@contacts_datasource_languages', language: '@language_ar', value: 'لغات التواصل' } }
    - { fields: { placeholder: '@contacts_datasource_worker', language: '@language_ar', value: 'عامل منفرد (عمل فردي)' } }
    - { fields: { placeholder: '@contacts_datasource_religions', language: '@language_ar', value: الديانة } }
    - { fields: { placeholder: '@contacts_datasource_orientations', language: '@language_ar', value: 'التوجيه الجنسي' } }
    - { fields: { placeholder: '@contacts_datasource_subtype', language: '@language_ar', value: 'التصنيف الفرعي ' } }
    - { fields: { placeholder: '@contacts_datasource_title', language: '@language_ar', value: العنوان } }
    - { fields: { placeholder: '@contacts_datasource_type', language: '@language_ar', value: النوع } }
    - { fields: { placeholder: '@contacts_ethnicity_african', language: '@language_ar', value: الأفريقي } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_asian_background', language: '@language_ar', value: 'أي خلفية آسيوية أخرى' } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_black_background', language: '@language_ar', value: 'أي خلفية سوداء أخرى' } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_ethnic_group', language: '@language_ar', value: 'أي مجموعة عرقية أخرى' } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_mixed_background', language: '@language_ar', value: 'أي خلفية مختلطة أخرى' } }
    - { fields: { placeholder: '@contacts_ethnicity_any_other_white_background', language: '@language_ar', value: 'أي خلفية بيضاء أخرى' } }
    - { fields: { placeholder: '@contacts_ethnicity_bangladeshi', language: '@language_ar', value: بنجلاديش } }
    - { fields: { placeholder: '@contacts_ethnicity_british', language: '@language_ar', value: بريطاني } }
    - { fields: { placeholder: '@contacts_ethnicity_caribbean', language: '@language_ar', value: 'منطقة البحر الكاريبي' } }
    - { fields: { placeholder: '@contacts_ethnicity_indian', language: '@language_ar', value: هندي } }
    - { fields: { placeholder: '@contacts_ethnicity_irish', language: '@language_ar', value: الأيرلندية } }
    - { fields: { placeholder: '@contacts_ethnicity_pakistani', language: '@language_ar', value: باكستاني } }
    - { fields: { placeholder: '@contacts_ethnicity_and_black_caribbean', language: '@language_ar', value: 'البحر الأبيض والأسود الكاريبي' } }
    - { fields: { placeholder: '@contacts_ethnicity_white_and_asian', language: '@language_ar', value: 'أبيض وآسيوي' } }
    - { fields: { placeholder: '@contacts_ethnicity_white_and_black_african', language: '@language_ar', value: 'أبيض وأسود أفريقي' } }
    - { fields: { placeholder: '@contacts_form_type_contact', language: '@language_ar', value: 'نموذج الاتصال' } }
    - { fields: { placeholder: '@contacts_form_type_main', language: '@language_ar', value: 'نموذج الاتصال' } }
    - { fields: { placeholder: '@contacts_form_field_date_of_birth_label', language: '@language_ar', value: 'تاريخ الولادة' } }
    - { fields: { placeholder: '@contacts_form_field_date_of_death_label', language: '@language_ar', value: 'تاريخ الوفاة' } }
    - { fields: { placeholder: '@contacts_form_field_ethnicity_label', language: '@language_ar', value: 'الأصل العرقي' } }
    - { fields: { placeholder: '@contacts_form_field_forename_label', language: '@language_ar', value: 'الاسم الاول' } }
    - { fields: { placeholder: '@contacts_form_field_gender_label', language: '@language_ar', value: جنس } }
    - { fields: { placeholder: '@contacts_form_field_language_label', language: '@language_ar', value: لغة } }
    - { fields: { placeholder: '@contacts_form_field_lone_worker_label', language: '@language_ar', value: 'تم تقييمه كعامل وحيد؟' } }
    - { fields: { placeholder: '@contacts_form_field_notes_label', language: '@language_ar', value: ملاحظات } }
    - { fields: { placeholder: '@contacts_form_field_relationship', language: '@language_ar', value: 'البيانات ذات الصلة' } }
    - { fields: { placeholder: '@contacts_form_field_relationship_label', language: '@language_ar', value: 'البيانات ذات الصلة' } }
    - { fields: { placeholder: '@contacts_form_field_religion_label', language: '@language_ar', value: دين } }
    - { fields: { placeholder: '@contacts_form_field_sexual_orientation_label', language: '@language_ar', value: 'التوجه الجنسي' } }
    - { fields: { placeholder: '@contacts_form_field_status_label', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@contacts_form_field_subtype_label', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@contacts_form_field_surname_label', language: '@language_ar', value: لقب } }
    - { fields: { placeholder: '@contacts_form_field_title_label', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@contacts_form_field_type_label', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_number_label', language: '@language_ar', value: 'أرقام الهواتف' } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_numbers_label', language: '@language_ar', value: 'أرقام الهواتف' } }
    - { fields: { placeholder: '@contacts_religion_atheist', language: '@language_ar', value: ملحد } }
    - { fields: { placeholder: '@contacts_religion_buddist', language: '@language_ar', value: بوذي } }
    - { fields: { placeholder: '@contacts_religion_christian', language: '@language_ar', value: مسيحي } }
    - { fields: { placeholder: '@contacts_religion_hindu', language: '@language_ar', value: الهندوسي } }
    - { fields: { placeholder: '@contacts_religion_jewish', language: '@language_ar', value: يهودي } }
    - { fields: { placeholder: '@contacts_religion_muslim', language: '@language_ar', value: مسلم } }
    - { fields: { placeholder: '@contacts_religion_sikh', language: '@language_ar', value: السيخ } }
    - { fields: { placeholder: '@contacts_religion_taoist', language: '@language_ar', value: الطاوية } }
    - { fields: { placeholder: '@contacts_sexual_orientation_bisexual', language: '@language_ar', value: 'ثنائي الجنس' } }
    - { fields: { placeholder: '@contacts_sexual_orientation_gay_man', language: '@language_ar', value: 'رجل مثلي الجنس' } }
    - { fields: { placeholder: '@contacts_sexual_orientation_gay_woman_lesbian', language: '@language_ar', value: 'مثلي الجنس امرأة / مثليه' } }
    - { fields: { placeholder: '@contacts_sexual_orientation_heterosexual', language: '@language_ar', value: 'متباين الجنس' } }
    - { fields: { placeholder: '@contacts_sexual_orientation_information_refused', language: '@language_ar', value: 'المعلومات رفضت' } }
    - { fields: { placeholder: '@contacts_sexual_orientation_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@contacts_subtype_inpatient', language: '@language_ar', value: 'في المريض' } }
    - { fields: { placeholder: '@contacts_type_patient', language: '@language_ar', value: المريض } }
    - { fields: { placeholder: '@controls_control_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@controls_datasource_contributory_factor_classifications', language: '@language_ar', value: 'تصنيفات العوامل المساهمة' } }
    - { fields: { placeholder: '@controls_datasource_control_statuses', language: '@language_ar', value: 'حالات التحكم' } }
    - { fields: { placeholder: '@controls_datasource_recommendation_priorities', language: '@language_ar', value: 'أولويات التوصية' } }
    - { fields: { placeholder: '@control_form_type_control_form', language: '@language_ar', value: 'نموذج التحكم' } }
    - { fields: { placeholder: '@controls_list_columns_aggregate_score_label', language: '@language_ar', value: 'النتيجة الإجمالية' } }
    - { fields: { placeholder: '@controls_list_columns_contributory_factor_label', language: '@language_ar', value: 'عامل المساهمة' } }
    - { fields: { placeholder: '@controls_list_columns_id_label', language: '@language_ar', value: 'هوية شخصية' } }
    - { fields: { placeholder: '@controls_list_columns_title_label', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@contacts_form_type_contact_filter', language: '@language_ar', value: 'مرشحات نموذج الإتصال' } }
    - { fields: { placeholder: '@contacts_form_field_assignment_number', language: '@language_ar', value: 'رقم المهمة' } }
    - { fields: { placeholder: '@placeholder_contacts_form_field_contact_number_filter', language: '@language_ar', value: 'رقم الإتصال' } }
    - { fields: { placeholder: '@contacts_form_field_employee_status', language: '@language_ar', value: 'حالة الموظف' } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_active', language: '@language_ar', value: فعال } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_not_started', language: '@language_ar', value: 'لم يبدأ' } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_select_employee_status', language: '@language_ar', value: 'حدد حالة الموظف' } }
    - { fields: { placeholder: '@contacts_form_field_employee_status_terminated', language: '@language_ar', value: إنهاء } }
    - { fields: { placeholder: '@contacts_form_field_language_select_language', language: '@language_ar', value: 'إختر لغة' } }
    - { fields: { placeholder: '@contacts_form_field_numbers_filter', language: '@language_ar', value: 'أرقام التواصل' } }
    - { fields: { placeholder: '@contacts_form_field_numbers_filter_label', language: '@language_ar', value: '' } }
    - { fields: { placeholder: '@contacts_form_field_positions_label', language: '@language_ar', value: مناصب } }
    - { fields: { placeholder: '@contacts_form_field_positions', language: '@language_ar', value: مناصب } }
    - { fields: { placeholder: '@contacts_form_numbers_phone_number', language: '@language_ar', value: 'رقم الهاتف ' } }
    - { fields: { placeholder: '@contacts_forms_id_numbers_default', language: '@language_ar', value: 'اختر نوع الرقم' } }
    - { fields: { placeholder: '@contacts_forms_nationality_default', language: '@language_ar', value: 'اختر الجنسية' } }
    - { fields: { placeholder: '@contacts_nationality_afghanistan', language: '@language_ar', value: أفغانستان } }
    - { fields: { placeholder: '@contacts_nationality_aland_islands', language: '@language_ar', value: 'جزر آلاند' } }
    - { fields: { placeholder: '@contacts_nationality_albania', language: '@language_ar', value: ألبانيا } }
    - { fields: { placeholder: '@contacts_nationality_algeria', language: '@language_ar', value: الجزائر } }
    - { fields: { placeholder: '@contacts_nationality_american_samoa', language: '@language_ar', value: 'ساموا الأمريكية' } }
    - { fields: { placeholder: '@contacts_nationality_andorra', language: '@language_ar', value: أندورا } }
    - { fields: { placeholder: '@contacts_nationality_angola', language: '@language_ar', value: أنغولا } }
    - { fields: { placeholder: '@contacts_nationality_anguilla', language: '@language_ar', value: أنغيلا } }
    - { fields: { placeholder: '@contacts_nationality_antarctica', language: '@language_ar', value: 'القارة القطبية الجنوبية' } }
    - { fields: { placeholder: '@contacts_nationality_antigua_and_barbuda', language: '@language_ar', value: 'أنتيغوا وبربودا' } }
    - { fields: { placeholder: '@contacts_nationality_argentina', language: '@language_ar', value: الأرجنتين } }
    - { fields: { placeholder: '@contacts_nationality_armenia', language: '@language_ar', value: أرمينيا } }
    - { fields: { placeholder: '@contacts_nationality_aruba', language: '@language_ar', value: أروبا } }
    - { fields: { placeholder: '@contacts_nationality_australia', language: '@language_ar', value: أستراليا } }
    - { fields: { placeholder: '@contacts_nationality_austria', language: '@language_ar', value: النمسا } }
    - { fields: { placeholder: '@contacts_nationality_azerbaijan', language: '@language_ar', value: أذربيجان } }
    - { fields: { placeholder: '@contacts_nationality_bahamas', language: '@language_ar', value: الباهاما } }
    - { fields: { placeholder: '@contacts_nationality_bahrain', language: '@language_ar', value: البحرين } }
    - { fields: { placeholder: '@contacts_nationality_bangladesh', language: '@language_ar', value: بنغلاديش } }
    - { fields: { placeholder: '@contacts_nationality_barbados', language: '@language_ar', value: بربادوس } }
    - { fields: { placeholder: '@contacts_nationality_belarus', language: '@language_ar', value: 'روسيا البيضاء' } }
    - { fields: { placeholder: '@contacts_nationality_belgium', language: '@language_ar', value: بلجيكا } }
    - { fields: { placeholder: '@contacts_nationality_belize', language: '@language_ar', value: بليز } }
    - { fields: { placeholder: '@contacts_nationality_benin', language: '@language_ar', value: بنين } }
    - { fields: { placeholder: '@contacts_nationality_bermuda', language: '@language_ar', value: برمودا } }
    - { fields: { placeholder: '@contacts_nationality_bhutan', language: '@language_ar', value: بوتان } }
    - { fields: { placeholder: '@contacts_nationality_bolivia', language: '@language_ar', value: بوليفيا } }
    - { fields: { placeholder: '@contacts_nationality_bonaire_sint_eustatius_and_saba', language: '@language_ar', value: 'بونير ، سينت أوستاتيوس وسابا' } }
    - { fields: { placeholder: '@contacts_nationality_bosnia_and_herzegovina', language: '@language_ar', value: 'البوسنة والهرسك' } }
    - { fields: { placeholder: '@contacts_nationality_botswana', language: '@language_ar', value: بوتسوانا } }
    - { fields: { placeholder: '@contacts_nationality_bouvet_island', language: '@language_ar', value: 'جزيرة بوفيت' } }
    - { fields: { placeholder: '@contacts_nationality_brazil', language: '@language_ar', value: البرازيل } }
    - { fields: { placeholder: '@contacts_nationality_british_indian_ocean_territory', language: '@language_ar', value: 'إقليم المحيط البريطاني الهندي' } }
    - { fields: { placeholder: '@contacts_nationality_british_virgin_islands', language: '@language_ar', value: 'جزر فيرجن البريطانية' } }
    - { fields: { placeholder: '@contacts_nationality_brunei_darussalam', language: '@language_ar', value: 'بروناي دار السلام' } }
    - { fields: { placeholder: '@contacts_nationality_bulgaria', language: '@language_ar', value: بلغاريا } }
    - { fields: { placeholder: '@contacts_nationality_burkina_faso', language: '@language_ar', value: 'بوركينا فاسو' } }
    - { fields: { placeholder: '@contacts_nationality_burundi', language: '@language_ar', value: بوروندي } }
    - { fields: { placeholder: '@contacts_nationality_cambodia', language: '@language_ar', value: كمبوديا } }
    - { fields: { placeholder: '@contacts_nationality_cameroon', language: '@language_ar', value: الكاميرون } }
    - { fields: { placeholder: '@contacts_nationality_canada', language: '@language_ar', value: كندا } }
    - { fields: { placeholder: '@contacts_nationality_cape_verde', language: '@language_ar', value: 'الرأس الأخضر' } }
    - { fields: { placeholder: '@contacts_nationality_cayman_islands', language: '@language_ar', value: 'جزر كايمان' } }
    - { fields: { placeholder: '@contacts_nationality_central_african_republic', language: '@language_ar', value: 'جمهورية افريقيا الوسطى' } }
    - { fields: { placeholder: '@contacts_nationality_chad', language: '@language_ar', value: تشاد } }
    - { fields: { placeholder: '@contacts_nationality_chile', language: '@language_ar', value: تشيلي } }
    - { fields: { placeholder: '@contacts_nationality_china', language: '@language_ar', value: الصين } }
    - { fields: { placeholder: '@contacts_nationality_christmas_island', language: '@language_ar', value: 'جزيرة الكريسماس' } }
    - { fields: { placeholder: '@contacts_nationality_cocos_keeling_islands', language: '@language_ar', value: 'جزر كوكوس (كيلينغ)' } }
    - { fields: { placeholder: '@contacts_nationality_colombia', language: '@language_ar', value: كولومبيا } }
    - { fields: { placeholder: '@contacts_nationality_comoros', language: '@language_ar', value: 'جزر القمر' } }
    - { fields: { placeholder: '@contacts_nationality_congo_brazzaville', language: '@language_ar', value: 'الكونغو (برازافيل)' } }
    - { fields: { placeholder: '@contacts_nationality_congo_democratic_republic_of_the', language: '@language_ar', value: 'جمهورية الكونغو الديمقراطية' } }
    - { fields: { placeholder: '@contacts_nationality_cook_islands', language: '@language_ar', value: 'جزر كوك' } }
    - { fields: { placeholder: '@contacts_nationality_costa_rica', language: '@language_ar', value: 'كوستا ريكا' } }
    - { fields: { placeholder: '@contacts_nationality_cote_divoire', language: '@language_ar', value: 'ساحل العاج' } }
    - { fields: { placeholder: '@contacts_nationality_croatia', language: '@language_ar', value: كرواتيا } }
    - { fields: { placeholder: '@contacts_nationality_cuba', language: '@language_ar', value: كوبا } }
    - { fields: { placeholder: '@contacts_nationality_curacao', language: '@language_ar', value: كوراساو } }
    - { fields: { placeholder: '@contacts_nationality_cyprus', language: '@language_ar', value: قبرص } }
    - { fields: { placeholder: '@contacts_nationality_czech_republic', language: '@language_ar', value: 'جمهورية التشيك' } }
    - { fields: { placeholder: '@contacts_nationality_denmark', language: '@language_ar', value: الدنمارك } }
    - { fields: { placeholder: '@contacts_nationality_djibouti', language: '@language_ar', value: جيبوتي } }
    - { fields: { placeholder: '@contacts_nationality_dominica', language: '@language_ar', value: دومينيكا } }
    - { fields: { placeholder: '@contacts_nationality_dominican_republic', language: '@language_ar', value: 'جمهورية الدومينيكان' } }
    - { fields: { placeholder: '@contacts_nationality_ecuador', language: '@language_ar', value: الأكوادور } }
    - { fields: { placeholder: '@contacts_nationality_egypt', language: '@language_ar', value: مصر } }
    - { fields: { placeholder: '@contacts_nationality_el_salvador', language: '@language_ar', value: السلفادور } }
    - { fields: { placeholder: '@contacts_nationality_equatorial_guinea', language: '@language_ar', value: 'غينيا الإستوائية' } }
    - { fields: { placeholder: '@contacts_nationality_eritrea', language: '@language_ar', value: إريتريا } }
    - { fields: { placeholder: '@contacts_nationality_estonia', language: '@language_ar', value: أستونيا } }
    - { fields: { placeholder: '@contacts_nationality_ethiopia', language: '@language_ar', value: أثيوبيا } }
    - { fields: { placeholder: '@contacts_nationality_falkland_islands_malvinas', language: '@language_ar', value: 'جزر فوكلاند (مالفيناس)' } }
    - { fields: { placeholder: '@contacts_nationality_faroe_islands', language: '@language_ar', value: 'جزر فارو' } }
    - { fields: { placeholder: '@contacts_nationality_fiji', language: '@language_ar', value: فيجي } }
    - { fields: { placeholder: '@contacts_nationality_finland', language: '@language_ar', value: فنلندا } }
    - { fields: { placeholder: '@contacts_nationality_france', language: '@language_ar', value: فرنسا } }
    - { fields: { placeholder: '@contacts_nationality_french_guiana', language: '@language_ar', value: 'غيانا الفرنسية' } }
    - { fields: { placeholder: '@contacts_nationality_french_polynesia', language: '@language_ar', value: 'بولينيزيا الفرنسية' } }
    - { fields: { placeholder: '@contacts_nationality_french_southern_territories', language: '@language_ar', value: 'الأقاليم الجنوبية الفرنسية' } }
    - { fields: { placeholder: '@contacts_nationality_gabon', language: '@language_ar', value: الغابون } }
    - { fields: { placeholder: '@contacts_nationality_gambia', language: '@language_ar', value: غامبيا } }
    - { fields: { placeholder: '@contacts_nationality_georgia', language: '@language_ar', value: جورجيا } }
    - { fields: { placeholder: '@contacts_nationality_germany', language: '@language_ar', value: ألمانيا } }
    - { fields: { placeholder: '@contacts_nationality_ghana', language: '@language_ar', value: غانا } }
    - { fields: { placeholder: '@contacts_nationality_gibraltar', language: '@language_ar', value: 'جبل طارق' } }
    - { fields: { placeholder: '@contacts_nationality_greece', language: '@language_ar', value: يونان } }
    - { fields: { placeholder: '@contacts_nationality_greenland', language: '@language_ar', value: غرينلاند } }
    - { fields: { placeholder: '@contacts_nationality_grenada', language: '@language_ar', value: غرينادا } }
    - { fields: { placeholder: '@contacts_nationality_guadeloupe', language: '@language_ar', value: جوادلوب } }
    - { fields: { placeholder: '@contacts_nationality_guam', language: '@language_ar', value: غوام } }
    - { fields: { placeholder: '@contacts_nationality_guatemala', language: '@language_ar', value: غواتيمالا } }
    - { fields: { placeholder: '@contacts_nationality_guernsey', language: '@language_ar', value: غيرنسي } }
    - { fields: { placeholder: '@contacts_nationality_guinea', language: '@language_ar', value: غينيا } }
    - { fields: { placeholder: '@contacts_nationality_guinea_bissau', language: '@language_ar', value: 'غينيا بيساو' } }
    - { fields: { placeholder: '@contacts_nationality_guyana', language: '@language_ar', value: غيانا } }
    - { fields: { placeholder: '@contacts_nationality_haiti', language: '@language_ar', value: هايتي } }
    - { fields: { placeholder: '@contacts_nationality_heard_island_and_mcdonald_islands', language: '@language_ar', value: 'جزيرة هيرد وجزر ماكدونالد' } }
    - { fields: { placeholder: '@contacts_nationality_holy_see_vatican_city_state', language: '@language_ar', value: 'الكرسي الرسولي (دولة الفاتيكان)' } }
    - { fields: { placeholder: '@contacts_nationality_honduras', language: '@language_ar', value: هندوراس } }
    - { fields: { placeholder: '@contacts_nationality_hong_kong_special_administrative_region_of_china', language: '@language_ar', value: 'منطقة هونج كونج الإدارية الخاصة للصين' } }
    - { fields: { placeholder: '@contacts_nationality_hungary', language: '@language_ar', value: هنغاريا } }
    - { fields: { placeholder: '@contacts_nationality_iceland', language: '@language_ar', value: أيسلندا } }
    - { fields: { placeholder: '@contacts_nationality_india', language: '@language_ar', value: الهند } }
    - { fields: { placeholder: '@contacts_nationality_indonesia', language: '@language_ar', value: أندونيسيا } }
    - { fields: { placeholder: '@contacts_nationality_iran_islamic_republic_of', language: '@language_ar', value: 'جمهورية إيران الإسلامية' } }
    - { fields: { placeholder: '@contacts_nationality_iraq', language: '@language_ar', value: العراق } }
    - { fields: { placeholder: '@contacts_nationality_ireland', language: '@language_ar', value: أيرلندا } }
    - { fields: { placeholder: '@contacts_nationality_isle_of_man', language: '@language_ar', value: 'جزيرة مان' } }
    - { fields: { placeholder: '@contacts_nationality_israel', language: '@language_ar', value: 'الكيان الصهيوني' } }
    - { fields: { placeholder: '@contacts_nationality_italy', language: '@language_ar', value: إيطاليا } }
    - { fields: { placeholder: '@contacts_nationality_jamaica', language: '@language_ar', value: جامايكا } }
    - { fields: { placeholder: '@contacts_nationality_japan', language: '@language_ar', value: اليابان } }
    - { fields: { placeholder: '@contacts_nationality_jersey', language: '@language_ar', value: جيرسي } }
    - { fields: { placeholder: '@contacts_nationality_jordan', language: '@language_ar', value: الأردن } }
    - { fields: { placeholder: '@contacts_nationality_kazakhstan', language: '@language_ar', value: كازاخستان } }
    - { fields: { placeholder: '@contacts_nationality_kenya', language: '@language_ar', value: كينيا } }
    - { fields: { placeholder: '@contacts_nationality_kiribati', language: '@language_ar', value: كيريباس } }
    - { fields: { placeholder: '@contacts_nationality_korea_democratic_peoples_republic_of', language: '@language_ar', value: 'جمهورية كوريا الديمقراطية الشعبية' } }
    - { fields: { placeholder: '@contacts_nationality_korea_republic_of', language: '@language_ar', value: 'جمهورية كوريا' } }
    - { fields: { placeholder: '@contacts_nationality_kuwait', language: '@language_ar', value: الكويت } }
    - { fields: { placeholder: '@contacts_nationality_kyrgyzstan', language: '@language_ar', value: قيرغيزستان } }
    - { fields: { placeholder: '@contacts_nationality_label', language: '@language_ar', value: جنسية } }
    - { fields: { placeholder: '@contacts_nationality_lao_pdr', language: '@language_ar', value: 'جمهورية لاو الديمقراطية الشعبية' } }
    - { fields: { placeholder: '@contacts_nationality_latvia', language: '@language_ar', value: لاتفيا } }
    - { fields: { placeholder: '@contacts_nationality_lebanon', language: '@language_ar', value: لبنان } }
    - { fields: { placeholder: '@contacts_nationality_lesotho', language: '@language_ar', value: ليسوتو } }
    - { fields: { placeholder: '@contacts_nationality_liberia', language: '@language_ar', value: ليبيريا } }
    - { fields: { placeholder: '@contacts_nationality_libya', language: '@language_ar', value: ليبيا } }
    - { fields: { placeholder: '@contacts_nationality_liechtenstein', language: '@language_ar', value: ليختنشتاين } }
    - { fields: { placeholder: '@contacts_nationality_lithuania', language: '@language_ar', value: ليتوانيا } }
    - { fields: { placeholder: '@contacts_nationality_luxembourg', language: '@language_ar', value: لوكسمبورغ } }
    - { fields: { placeholder: '@contacts_nationality_macao_special_administrative_region_of_china', language: '@language_ar', value: 'ماكاو ، المنطقة الإدارية الخاصة للصين' } }
    - { fields: { placeholder: '@contacts_nationality_macedonia_republic_of', language: '@language_ar', value: 'جمهورية مقدونيا' } }
    - { fields: { placeholder: '@contacts_nationality_madagascar', language: '@language_ar', value: مدغشقر } }
    - { fields: { placeholder: '@contacts_nationality_malawi', language: '@language_ar', value: مالاوي } }
    - { fields: { placeholder: '@contacts_nationality_malaysia', language: '@language_ar', value: ماليزيا } }
    - { fields: { placeholder: '@contacts_nationality_maldives', language: '@language_ar', value: 'جزر المالديف' } }
    - { fields: { placeholder: '@contacts_nationality_mali', language: '@language_ar', value: مالي } }
    - { fields: { placeholder: '@contacts_nationality_malta', language: '@language_ar', value: مالطا } }
    - { fields: { placeholder: '@contacts_nationality_marshall_islands', language: '@language_ar', value: 'جزر مارشال' } }
    - { fields: { placeholder: '@contacts_nationality_martinique', language: '@language_ar', value: مارتينيك } }
    - { fields: { placeholder: '@contacts_nationality_mauritania', language: '@language_ar', value: موريتانيا } }
    - { fields: { placeholder: '@contacts_nationality_mauritius', language: '@language_ar', value: موريشيوس } }
    - { fields: { placeholder: '@contacts_nationality_mayotte', language: '@language_ar', value: مايوت } }
    - { fields: { placeholder: '@contacts_nationality_mexico', language: '@language_ar', value: المكسيك } }
    - { fields: { placeholder: '@contacts_nationality_micronesia_federated_states_of', language: '@language_ar', value: 'ولايات ميكرونيزيا الموحدة' } }
    - { fields: { placeholder: '@contacts_nationality_moldova', language: '@language_ar', value: مولدوفا } }
    - { fields: { placeholder: '@contacts_nationality_monaco', language: '@language_ar', value: موناكو } }
    - { fields: { placeholder: '@contacts_nationality_mongolia', language: '@language_ar', value: منغوليا } }
    - { fields: { placeholder: '@contacts_nationality_montenegro', language: '@language_ar', value: 'الجبل الأسود' } }
    - { fields: { placeholder: '@contacts_nationality_montserrat', language: '@language_ar', value: مونتسيرات } }
    - { fields: { placeholder: '@contacts_nationality_morocco', language: '@language_ar', value: المغرب } }
    - { fields: { placeholder: '@contacts_nationality_mozambique', language: '@language_ar', value: موزمبيق } }
    - { fields: { placeholder: '@contacts_nationality_myanmar', language: '@language_ar', value: ميانمار } }
    - { fields: { placeholder: '@contacts_nationality_namibia', language: '@language_ar', value: ناميبيا } }
    - { fields: { placeholder: '@contacts_nationality_nauru', language: '@language_ar', value: ناورو } }
    - { fields: { placeholder: '@contacts_nationality_nepal', language: '@language_ar', value: نيبال } }
    - { fields: { placeholder: '@contacts_nationality_netherlands', language: '@language_ar', value: هولندا } }
    - { fields: { placeholder: '@contacts_nationality_netherlands_antilles', language: '@language_ar', value: 'جزر الأنتيل الهولندية' } }
    - { fields: { placeholder: '@contacts_nationality_new_caledonia', language: '@language_ar', value: 'كاليدونيا الجديدة' } }
    - { fields: { placeholder: '@contacts_nationality_new_zealand', language: '@language_ar', value: نيوزيلندا } }
    - { fields: { placeholder: '@contacts_nationality_nicaragua', language: '@language_ar', value: نيكاراغوا } }
    - { fields: { placeholder: '@contacts_nationality_niger', language: '@language_ar', value: النيجر } }
    - { fields: { placeholder: '@contacts_nationality_nigeria', language: '@language_ar', value: نيجيريا } }
    - { fields: { placeholder: '@contacts_nationality_niue', language: '@language_ar', value: نيوي } }
    - { fields: { placeholder: '@contacts_nationality_norfolk_island', language: '@language_ar', value: 'جزيرة نورفولك' } }
    - { fields: { placeholder: '@contacts_nationality_northern_mariana_islands', language: '@language_ar', value: 'جزر ماريانا الشمالية' } }
    - { fields: { placeholder: '@contacts_nationality_norway', language: '@language_ar', value: النرويج } }
    - { fields: { placeholder: '@contacts_nationality_oman', language: '@language_ar', value: عمان } }
    - { fields: { placeholder: '@contacts_nationality_pakistan', language: '@language_ar', value: باكستان } }
    - { fields: { placeholder: '@contacts_nationality_palau', language: '@language_ar', value: بالاو } }
    - { fields: { placeholder: '@contacts_nationality_palestinian_territory_occupied', language: '@language_ar', value: 'الأراضي الفلسطينية المحتلة' } }
    - { fields: { placeholder: '@contacts_nationality_panama', language: '@language_ar', value: بناما } }
    - { fields: { placeholder: '@contacts_nationality_papua_new_guinea', language: '@language_ar', value: 'بابوا غينيا الجديدة' } }
    - { fields: { placeholder: '@contacts_nationality_paraguay', language: '@language_ar', value: باراغواي } }
    - { fields: { placeholder: '@contacts_nationality_peru', language: '@language_ar', value: بيرو } }
    - { fields: { placeholder: '@contacts_nationality_philippines', language: '@language_ar', value: الفلبين } }
    - { fields: { placeholder: '@contacts_nationality_pitcairn', language: '@language_ar', value: بيتكيرن } }
    - { fields: { placeholder: '@contacts_nationality_poland', language: '@language_ar', value: بولندا } }
    - { fields: { placeholder: '@contacts_nationality_portugal', language: '@language_ar', value: البرتغال } }
    - { fields: { placeholder: '@contacts_nationality_puerto_rico', language: '@language_ar', value: بورتوريكو } }
    - { fields: { placeholder: '@contacts_nationality_qatar', language: '@language_ar', value: قطر } }
    - { fields: { placeholder: '@contacts_nationality_reunion', language: '@language_ar', value: ريونيون } }
    - { fields: { placeholder: '@contacts_nationality_romania', language: '@language_ar', value: رومانيا } }
    - { fields: { placeholder: '@contacts_nationality_russian_federation', language: '@language_ar', value: 'الاتحاد الروسي' } }
    - { fields: { placeholder: '@contacts_nationality_rwanda', language: '@language_ar', value: رواندا } }
    - { fields: { placeholder: '@contacts_nationality_saint_barthelemy', language: '@language_ar', value: 'سانت بارتيليمي' } }
    - { fields: { placeholder: '@contacts_nationality_saint_helena', language: '@language_ar', value: 'سانت هيلانة' } }
    - { fields: { placeholder: '@contacts_nationality_saint_kitts_and_nevis', language: '@language_ar', value: 'سانت كيتس ونيفيس' } }
    - { fields: { placeholder: '@contacts_nationality_saint_lucia', language: '@language_ar', value: 'القديسة لوسيا' } }
    - { fields: { placeholder: '@contacts_nationality_saint_martin_french_part', language: '@language_ar', value: 'سان مارتن (الجزء الفرنسي)' } }
    - { fields: { placeholder: '@contacts_nationality_saint_pierre_and_miquelon', language: '@language_ar', value: 'سانت بيير وميكلون' } }
    - { fields: { placeholder: '@contacts_nationality_saint_vincent_and_grenadines', language: '@language_ar', value: 'سانت فنسنت وجزر غرينادين' } }
    - { fields: { placeholder: '@contacts_nationality_samoa', language: '@language_ar', value: ساموا } }
    - { fields: { placeholder: '@contacts_nationality_san_marino', language: '@language_ar', value: 'سان مارينو' } }
    - { fields: { placeholder: '@contacts_nationality_sao_tome_and_principe', language: '@language_ar', value: 'ساو تومي وبرينسيبي' } }
    - { fields: { placeholder: '@contacts_nationality_saudi_arabia', language: '@language_ar', value: 'المملكة العربية السعودية' } }
    - { fields: { placeholder: '@contacts_nationality_senegal', language: '@language_ar', value: السنغال } }
    - { fields: { placeholder: '@contacts_nationality_serbia', language: '@language_ar', value: صربيا } }
    - { fields: { placeholder: '@contacts_nationality_seychelles', language: '@language_ar', value: سيشيل } }
    - { fields: { placeholder: '@contacts_nationality_sierra_leone', language: '@language_ar', value: 'سيرا ليون' } }
    - { fields: { placeholder: '@contacts_nationality_singapore', language: '@language_ar', value: سنغافورة } }
    - { fields: { placeholder: '@contacts_nationality_sint_maarten_dutch_part', language: '@language_ar', value: 'سانت مارتن (الجزء الهولندي)' } }
    - { fields: { placeholder: '@contacts_nationality_slovakia', language: '@language_ar', value: سلوفاكيا } }
    - { fields: { placeholder: '@contacts_nationality_slovenia', language: '@language_ar', value: سلوفينيا } }
    - { fields: { placeholder: '@contacts_nationality_solomon_islands', language: '@language_ar', value: 'جزر سليمان' } }
    - { fields: { placeholder: '@contacts_nationality_somalia', language: '@language_ar', value: الصومال } }
    - { fields: { placeholder: '@contacts_nationality_south_africa', language: '@language_ar', value: 'جنوب أفريقيا' } }
    - { fields: { placeholder: '@contacts_nationality_south_georgia_and_the_south_sandwich_islands', language: '@language_ar', value: 'جورجيا الجنوبية وجزر ساندويتش الجنوبية' } }
    - { fields: { placeholder: '@contacts_nationality_south_sudan', language: '@language_ar', value: 'جنوب السودان' } }
    - { fields: { placeholder: '@contacts_nationality_spain', language: '@language_ar', value: إسبانيا } }
    - { fields: { placeholder: '@contacts_nationality_sri_lanka', language: '@language_ar', value: سيريلانكا } }
    - { fields: { placeholder: '@contacts_nationality_sudan', language: '@language_ar', value: سودان } }
    - { fields: { placeholder: '@contacts_nationality_suriname', language: '@language_ar', value: 'سورينام *' } }
    - { fields: { placeholder: '@contacts_nationality_svalbard_and_jan_mayen_islands', language: '@language_ar', value: 'جزر سفالبارد وجان ماين' } }
    - { fields: { placeholder: '@contacts_nationality_swaziland', language: '@language_ar', value: سوازيلاند } }
    - { fields: { placeholder: '@contacts_nationality_sweden', language: '@language_ar', value: السويد } }
    - { fields: { placeholder: '@contacts_nationality_switzerland', language: '@language_ar', value: سويسرا } }
    - { fields: { placeholder: '@contacts_nationality_syrian_arab_republic_syria', language: '@language_ar', value: 'الجمهورية العربية السورية (سوريا)' } }
    - { fields: { placeholder: '@contacts_nationality_taiwan', language: '@language_ar', value: تايوان } }
    - { fields: { placeholder: '@contacts_nationality_tajikistan', language: '@language_ar', value: طاجيكستان } }
    - { fields: { placeholder: '@contacts_nationality_tanzania_united_republic_of', language: '@language_ar', value: 'جمهورية تنزانيا المتحدة' } }
    - { fields: { placeholder: '@contacts_nationality_thailand', language: '@language_ar', value: تايلاند } }
    - { fields: { placeholder: '@contacts_nationality_timor_leste', language: '@language_ar', value: 'تيمور الشرقية' } }
    - { fields: { placeholder: '@contacts_nationality_title', language: '@language_ar', value: الجنسية } }
    - { fields: { placeholder: '@contacts_nationality_togo', language: '@language_ar', value: توغو } }
    - { fields: { placeholder: '@contacts_nationality_tokelau', language: '@language_ar', value: توكيلاو } }
    - { fields: { placeholder: '@contacts_nationality_tonga', language: '@language_ar', value: تونغا } }
    - { fields: { placeholder: '@contacts_nationality_trinidad_and_tobago', language: '@language_ar', value: 'ترينداد وتوباغو' } }
    - { fields: { placeholder: '@contacts_nationality_tunisia', language: '@language_ar', value: تونس } }
    - { fields: { placeholder: '@contacts_nationality_turkey', language: '@language_ar', value: تركيا } }
    - { fields: { placeholder: '@contacts_nationality_turkmenistan', language: '@language_ar', value: تركمانستان } }
    - { fields: { placeholder: '@contacts_nationality_turks_and_caicos_islands', language: '@language_ar', value: 'جزر تركس وكايكوس' } }
    - { fields: { placeholder: '@contacts_nationality_tuvalu', language: '@language_ar', value: توفالو } }
    - { fields: { placeholder: '@contacts_nationality_uganda', language: '@language_ar', value: أوغندا } }
    - { fields: { placeholder: '@contacts_nationality_ukraine', language: '@language_ar', value: أوكرانيا } }
    - { fields: { placeholder: '@contacts_nationality_united_arab_emirates', language: '@language_ar', value: 'الإمارات العربية المتحدة' } }
    - { fields: { placeholder: '@contacts_nationality_united_kingdom', language: '@language_ar', value: 'المملكة المتحدة' } }
    - { fields: { placeholder: '@contacts_nationality_united_states_minor_outlying_islands', language: '@language_ar', value: 'جزر الولايات المتحدة البعيدة الصغرى' } }
    - { fields: { placeholder: '@contacts_nationality_united_states_of_america', language: '@language_ar', value: 'الولايات المتحدة الامريكية' } }
    - { fields: { placeholder: '@contacts_nationality_uruguay', language: '@language_ar', value: أوروغواي } }
    - { fields: { placeholder: '@contacts_nationality_uzbekistan', language: '@language_ar', value: أوزبكستان } }
    - { fields: { placeholder: '@contacts_nationality_vanuatu', language: '@language_ar', value: فانواتو } }
    - { fields: { placeholder: '@contacts_nationality_venezuela_bolivarian_republic_of', language: '@language_ar', value: 'فنزويلا (جمهورية - البوليفارية)' } }
    - { fields: { placeholder: '@contacts_nationality_vietnam', language: '@language_ar', value: فييتنام } }
    - { fields: { placeholder: '@contacts_nationality_virgin_islands_us', language: '@language_ar', value: 'جزر فيرجن الأمريكية' } }
    - { fields: { placeholder: '@contacts_nationality_wallis_and_futuna_islands', language: '@language_ar', value: 'جزر واليس وفوتونا' } }
    - { fields: { placeholder: '@contacts_nationality_western_sahara', language: '@language_ar', value: 'الصحراء الغربية' } }
    - { fields: { placeholder: '@contacts_nationality_yemen', language: '@language_ar', value: اليمن } }
    - { fields: { placeholder: '@contacts_nationality_zambia', language: '@language_ar', value: زامبيا } }
    - { fields: { placeholder: '@contacts_nationality_zimbabwe', language: '@language_ar', value: زيمبابوي } }
    - { fields: { placeholder: '@contacts_numbers_type_label', language: '@language_ar', value: 'نوع الرقم' } }
    - { fields: { placeholder: '@contacts_numbers_type_title', language: '@language_ar', value: 'نوع الرقم' } }
    - { fields: { placeholder: '@contacts_post_save_no_access', language: '@language_ar', value: 'تم حفظ جهة الاتصال ، لكن الصلاحيات الوصول الممنوحة للمستخدم لا تسمح لك برؤيته' } }
    - { fields: { placeholder: '@contacts_form_source_of_record_title', language: '@language_ar', value: 'مصدر إدخال السجل' } }
    - { fields: { placeholder: '@contacts_form_source_of_record_label', language: '@language_ar', value: 'مصدر إدخال السجل' } }
