entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@contacts_loading_contacts'
      language: '@language_en_gb'
      value: 'Loading Contacts'
  -
    fields:
      placeholder: '@contacts_loading_contact'
      language: '@language_en_gb'
      value: 'Loading Contact'
  -
    fields:
      placeholder: '@contacts_singular'
      language: '@language_en_gb'
      value: Contact
  -
    fields:
      placeholder: '@contacts_plural'
      language: '@language_en_gb'
      value: Contacts
  -
    fields:
      placeholder: '@contacts_search'
      language: '@language_en_gb'
      value: 'Search Contacts'
  -
    fields:
      placeholder: '@contacts_create'
      language: '@language_en_gb'
      value: 'Create Contact'
  -
    fields:
      placeholder: '@contacts_edit'
      language: '@language_en_gb'
      value: 'Edit Contact'
  -
    fields:
      placeholder: '@contacts_name'
      language: '@language_en_gb'
      value: Name
  -
    fields:
      placeholder: '@contacts_form_title'
      language: '@language_en_gb'
      value: Contacts
  -
    fields:
      placeholder: '@contacts_form_section_title'
      language: '@language_en_gb'
      value: 'New Contact'
  -
    fields:
      placeholder: '@contacts_form_addresses_singular'
      language: '@language_en_gb'
      value: Address
  -
    fields:
      placeholder: '@contacts_form_addresses_plural'
      language: '@language_en_gb'
      value: Addresses
  -
    fields:
      placeholder: '@contacts_form_addresses_plural_label'
      language: '@language_en_gb'
      value: Addresses
  -
    fields:
      placeholder: '@contacts_form_addresses_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_addresses_line1'
      language: '@language_en_gb'
      value: 'Line 1'
  -
    fields:
      placeholder: '@contacts_form_addresses_line2'
      language: '@language_en_gb'
      value: 'Line 2'
  -
    fields:
      placeholder: '@contacts_form_addresses_line3'
      language: '@language_en_gb'
      value: 'Line 3'
  -
    fields:
      placeholder: '@contacts_form_addresses_city'
      language: '@language_en_gb'
      value: City
  -
    fields:
      placeholder: '@contacts_form_addresses_county'
      language: '@language_en_gb'
      value: County
  -
    fields:
      placeholder: '@contacts_form_addresses_country'
      language: '@language_en_gb'
      value: Country
  -
    fields:
      placeholder: '@contacts_form_addresses_postcode'
      language: '@language_en_gb'
      value: Postcode
  -
    fields:
      placeholder: '@contacts_form_addresses_postal'
      language: '@language_en_gb'
      value: Postal
  -
    fields:
      placeholder: '@contacts_form_addresses_correspondence'
      language: '@language_en_gb'
      value: Correspondence
  -
    fields:
      placeholder: '@contacts_form_addresses_residential'
      language: '@language_en_gb'
      value: Residential
  -
    fields:
      placeholder: '@contacts_form_addresses_business'
      language: '@language_en_gb'
      value: Business
  -
    fields:
      placeholder: '@contacts_form_addresses_trading'
      language: '@language_en_gb'
      value: Trading
  -
    fields:
      placeholder: '@contacts_form_disability_singular'
      language: '@language_en_gb'
      value: Disability
  -
    fields:
      placeholder: '@contacts_form_disability_plural'
      language: '@language_en_gb'
      value: Disabilities
  -
    fields:
      placeholder: '@contacts_form_disability_plural_label'
      language: '@language_en_gb'
      value: Disabilities
  -
    fields:
      placeholder: '@contacts_form_email_email_address'
      language: '@language_en_gb'
      value: 'Email Address'
  -
    fields:
      placeholder: '@contacts_form_email_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_email_personal'
      language: '@language_en_gb'
      value: Personal
  -
    fields:
      placeholder: '@contacts_form_email_work'
      language: '@language_en_gb'
      value: Work
  -
    fields:
      placeholder: '@contacts_form_email_plural'
      language: '@language_en_gb'
      value: Emails
  -
    fields:
      placeholder: '@contacts_form_email_plural_label'
      language: '@language_en_gb'
      value: Emails
  -
    fields:
      placeholder: '@contacts_form_email_singular'
      language: '@language_en_gb'
      value: Email
  -
    fields:
      placeholder: '@contacts_error_not_synced_to_capture'
      language: '@language_en_gb'
      value: 'Capture Contact not found. Please sync Contact by clicking save at the bottom of the screen.'
  -
    fields:
      placeholder: '@contacts_error_get_form'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Contact form'
  -
    fields:
      placeholder: '@contacts_form_field_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@contacts_form_field_title_label'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@contacts_form_field_id_label'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@contacts_form_field_forename'
      language: '@language_en_gb'
      value: Forename
  -
    fields:
      placeholder: '@contacts_form_field_forename_label'
      language: '@language_en_gb'
      value: Forename
  -
    fields:
      placeholder: '@contacts_form_field_surname'
      language: '@language_en_gb'
      value: Surname
  -
    fields:
      placeholder: '@contacts_form_field_surname_label'
      language: '@language_en_gb'
      value: Surname
  -
    fields:
      placeholder: '@contacts_form_field_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@contacts_form_field_status_label'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@contacts_form_field_gender'
      language: '@language_en_gb'
      value: Gender
  -
    fields:
      placeholder: '@contacts_form_field_gender_label'
      language: '@language_en_gb'
      value: Gender
  -
    fields:
      placeholder: '@contacts_form_field_select_gender'
      language: '@language_en_gb'
      value: 'Select Gender'
  -
    fields:
      placeholder: '@contacts_form_field_gender_male'
      language: '@language_en_gb'
      value: Male
  -
    fields:
      placeholder: '@contacts_form_field_gender_female'
      language: '@language_en_gb'
      value: Female
  -
    fields:
      placeholder: '@contacts_form_field_ethnicity'
      language: '@language_en_gb'
      value: Ethnicity
  -
    fields:
      placeholder: '@contacts_form_field_ethnicity_label'
      language: '@language_en_gb'
      value: Ethnicity
  -
    fields:
      placeholder: '@contacts_form_field_select_ethnicity'
      language: '@language_en_gb'
      value: 'Select Ethnicity'
  -
    fields:
      placeholder: '@contacts_form_field_language'
      language: '@language_en_gb'
      value: Language
  -
    fields:
      placeholder: '@contacts_form_field_language_label'
      language: '@language_en_gb'
      value: Language
  -
    fields:
      placeholder: '@contacts_form_field_select_language'
      language: '@language_en_gb'
      value: Select a Language
  -
    fields:
      placeholder: '@contacts_form_field_sexual_orientation'
      language: '@language_en_gb'
      value: 'Sexual orientation'
  -
    fields:
      placeholder: '@contacts_form_field_sexual_orientation_label'
      language: '@language_en_gb'
      value: 'Sexual orientation'
  -
    fields:
      placeholder: '@contacts_form_field_select_sexual_orientation'
      language: '@language_en_gb'
      value: 'Select Sexual orientation'
  -
    fields:
      placeholder: '@contacts_form_field_religion'
      language: '@language_en_gb'
      value: Religion
  -
    fields:
      placeholder: '@contacts_form_field_religion_label'
      language: '@language_en_gb'
      value: Religion
  -
    fields:
      placeholder: '@contacts_form_field_select_religion'
      language: '@language_en_gb'
      value: 'Select Religion'
  -
    fields:
      placeholder: '@contacts_form_field_relationship'
      language: '@language_en_gb'
      value: 'Related data'
  -
    fields:
      placeholder: '@contacts_form_field_relationship_label'
      language: '@language_en_gb'
      value: 'Related data'
  -
    fields:
      placeholder: '@contacts_form_field_numbers_filter'
      language: '@language_en_gb'
      value: Contact Numbers
  -
    fields:
      placeholder: '@contacts_form_field_numbers_filter_label'
      language: '@language_en_gb'
      value: 'Contact Numbers'
  -
    fields:
      placeholder: '@contacts_form_field_date_of_birth'
      language: '@language_en_gb'
      value: 'Date of birth'
  -
    fields:
      placeholder: '@contacts_form_field_date_of_birth_label'
      language: '@language_en_gb'
      value: 'Date of birth'
  -
    fields:
      placeholder: '@contacts_form_field_date_of_death'
      language: '@language_en_gb'
      value: 'Date of death'
  -
    fields:
      placeholder: '@contacts_form_field_date_of_death_label'
      language: '@language_en_gb'
      value: 'Date of death'
  -
    fields:
      placeholder: '@contacts_form_field_lone_worker'
      language: '@language_en_gb'
      value: 'Lone worker'
  -
    fields:
      placeholder: '@contacts_form_field_lone_worker_label'
      language: '@language_en_gb'
      value: 'Assessed as lone worker?'
  -
    fields:
      placeholder: '@contacts_form_field_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_field_type_label'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_field_select_type'
      language: '@language_en_gb'
      value: 'Select Type'
  -
    fields:
      placeholder: '@contacts_form_field_subtype'
      language: '@language_en_gb'
      value: 'Sub type'
  -
    fields:
      placeholder: '@contacts_form_field_subtype_label'
      language: '@language_en_gb'
      value: 'Sub type'
  -
    fields:
      placeholder: '@contacts_form_field_select_subtype'
      language: '@language_en_gb'
      value: 'Select Sub Type'
  -
    fields:
      placeholder: '@contacts_form_field_notes'
      language: '@language_en_gb'
      value: Notes
  -
    fields:
      placeholder: '@contacts_form_field_notes_label'
      language: '@language_en_gb'
      value: Notes
  -
    fields:
      placeholder: '@contacts_form_id_numbers_singular'
      language: '@language_en_gb'
      value: 'Individual Specific Number'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_plural'
      language: '@language_en_gb'
      value: 'Individual Specific Number'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_id_numbers_inpatient'
      language: '@language_en_gb'
      value: 'In-patient'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_nhs_number'
      language: '@language_en_gb'
      value: 'NHS Number'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_patient_number'
      language: '@language_en_gb'
      value: 'Patient Number'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_staff_number'
      language: '@language_en_gb'
      value: 'Staff Number'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_police_id'
      language: '@language_en_gb'
      value: 'Police ID'
  -
    fields:
      placeholder: '@contacts_form_id_numbers_other'
      language: '@language_en_gb'
      value: Other
  -
    fields:
      placeholder: '@contacts_form_numbers_singular'
      language: '@language_en_gb'
      value: Number
  -
    fields:
      placeholder: '@contacts_form_numbers_plural'
      language: '@language_en_gb'
      value: Numbers
  -
    fields:
      placeholder: '@contacts_form_numbers_plural_label'
      language: '@language_en_gb'
      value: Numbers
  -
    fields:
      placeholder: '@contacts_form_numbers_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@contacts_form_numbers_phone_number'
      language: '@language_en_gb'
      value: 'Phone Number'
  -
    fields:
      placeholder: '@contacts_form_numbers_phone_number_label'
      language: '@language_en_gb'
      value: 'Phone Numbers'
  -
    fields:
      placeholder: '@contacts_form_numbers_phone_numbers'
      language: '@language_en_gb'
      value: 'Phone Number'
  -
    fields:
      placeholder: '@contacts_form_numbers_phone_numbers_label'
      language: '@language_en_gb'
      value: 'Phone Numbers'
  -
    fields:
      placeholder: '@contacts_form_numbers_home'
      language: '@language_en_gb'
      value: Home
  -
    fields:
      placeholder: '@contacts_form_numbers_work'
      language: '@language_en_gb'
      value: Work
  -
    fields:
      placeholder: '@contacts_form_numbers_mobile'
      language: '@language_en_gb'
      value: Mobile
  -
    fields:
      placeholder: '@contacts_form_field_status_value_unapproved'
      language: '@language_en_gb'
      value: Unapproved
  -
    fields:
      placeholder: '@contacts_form_field_status_value_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@contacts_form_field_status_value_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@contacts_form_field_status_select_status'
      language: '@language_en_gb'
      value: 'Select status'
  -
    fields:
      placeholder: '@contacts_form_field_title_value_mr'
      language: '@language_en_gb'
      value: Mr
  -
    fields:
      placeholder: '@contacts_form_field_title_value_mrs'
      language: '@language_en_gb'
      value: Mrs
  -
    fields:
      placeholder: '@contacts_form_field_title_value_master'
      language: '@language_en_gb'
      value: Master
  -
    fields:
      placeholder: '@contacts_form_field_title_value_miss'
      language: '@language_en_gb'
      value: Miss
  -
    fields:
      placeholder: '@contacts_form_field_title_value_ms'
      language: '@language_en_gb'
      value: Ms
  -
    fields:
      placeholder: '@contacts_form_field_title_value_dr'
      language: '@language_en_gb'
      value: Doctor
  -
    fields:
      placeholder: '@contacts_form_field_title_value_prof'
      language: '@language_en_gb'
      value: Professor
  -
    fields:
      placeholder: '@contacts_form_field_title_value_rev'
      language: '@language_en_gb'
      value: Reverend
  -
    fields:
      placeholder: '@contacts_form_field_title_value_br'
      language: '@language_en_gb'
      value: Brother
  -
    fields:
      placeholder: '@contacts_form_field_title_value_sr'
      language: '@language_en_gb'
      value: Sister
  -
    fields:
      placeholder: '@contacts_form_field_title_value_fr'
      language: '@language_en_gb'
      value: Father
  -
    fields:
      placeholder: '@contacts_form_field_title_value_pr'
      language: '@language_en_gb'
      value: Pastor
  -
    fields:
      placeholder: '@contacts_form_field_title_value_elder'
      language: '@language_en_gb'
      value: Elder
  -
    fields:
      placeholder: '@contacts_form_field_title_value_rabbi'
      language: '@language_en_gb'
      value: Rabbi
  -
    fields:
      placeholder: '@contacts_form_field_title_value_sir'
      language: '@language_en_gb'
      value: Sir
  -
    fields:
      placeholder: '@contacts_form_field_title_value_madam'
      language: '@language_en_gb'
      value: Madam
  -
    fields:
      placeholder: '@contacts_form_field_title_value_dame'
      language: '@language_en_gb'
      value: Dame
  -
    fields:
      placeholder: '@contacts_form_field_title_value_lord'
      language: '@language_en_gb'
      value: Lord
  -
    fields:
      placeholder: '@contacts_form_field_title_value_lady'
      language: '@language_en_gb'
      value: Lady
  -
    fields:
      placeholder: '@contacts_form_field_title_value_esq'
      language: '@language_en_gb'
      value: Esq
  -
    fields:
      placeholder: '@contacts_form_field_title_value_adv'
      language: '@language_en_gb'
      value: Adv
  -
    fields:
      placeholder: '@contacts_form_field_title_select_title'
      language: '@language_en_gb'
      value: 'Select Title'
  -
    fields:
      placeholder: '@placeholder_contacts_form_field_contact_number_filter'
      language: '@language_en_gb'
      value: 'Contact Number'
  -
    fields:
      placeholder: '@contacts_nav_dashboard'
      language: '@language_en_gb'
      value: Dashboard
  -
    fields:
      placeholder: '@contacts_nav_new_contact'
      language: '@language_en_gb'
      value: 'New Contact'
  -
    fields:
      placeholder: '@contacts_nav_permissions'
      language: '@language_en_gb'
      value: Permissions
  -
    fields:
      placeholder: '@contacts_nav_contact_merging'
      language: '@language_en_gb'
      value: 'Contact Merging'
  -
    fields:
      placeholder: '@contacts_form_save_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Contact'
  -
    fields:
      placeholder: '@contacts_form_field_language_select_language'
      language: '@language_en_gb'
      value: Select Language
  -
    fields:
      placeholder: '@contacts_form_type_contact'
      language: '@language_en_gb'
      value: 'Contact Form'
  -
    fields:
      placeholder: '@contacts_form_type_main'
      language: '@language_en_gb'
      value: 'Contact Form'
  -
    fields:
      placeholder: '@contacts_form_type_contact_filter'
      language: '@language_en_gb'
      value: 'Contact Filter Form'
  -
    fields:
      placeholder: '@contacts_religion_christian'
      language: '@language_en_gb'
      value: 'Christian'
  -
    fields:
      placeholder: '@contacts_religion_muslim'
      language: '@language_en_gb'
      value: 'Muslim'
  -
    fields:
      placeholder: '@contacts_religion_atheist'
      language: '@language_en_gb'
      value: 'Atheist'
  -
    fields:
      placeholder: '@contacts_religion_hindu'
      language: '@language_en_gb'
      value: 'Hindu'
  -
    fields:
      placeholder: '@contacts_religion_buddist'
      language: '@language_en_gb'
      value: 'Buddhist'
  -
    fields:
      placeholder: '@contacts_religion_taoist'
      language: '@language_en_gb'
      value: 'Taoist'
  -
    fields:
      placeholder: '@contacts_religion_jewish'
      language: '@language_en_gb'
      value: 'Jewish'
  -
    fields:
      placeholder: '@contacts_religion_sikh'
      language: '@language_en_gb'
      value: 'Sikh'
  -
    fields:
      placeholder: '@contacts_ethnicity_indian'
      language: '@language_en_gb'
      value: 'Indian'
  -
    fields:
      placeholder: '@contacts_ethnicity_pakistani'
      language: '@language_en_gb'
      value: 'Pakistani'
  -
    fields:
      placeholder: '@contacts_ethnicity_bangladeshi'
      language: '@language_en_gb'
      value: 'Bangladeshi'
  -
    fields:
      placeholder: '@contacts_ethnicity_any_other_asian_background'
      language: '@language_en_gb'
      value: 'Any other Asian background'
  -
    fields:
      placeholder: '@contacts_ethnicity_caribbean'
      language: '@language_en_gb'
      value: 'Caribbean'
  -
    fields:
      placeholder: '@contacts_ethnicity_african'
      language: '@language_en_gb'
      value: 'African'
  -
    fields:
      placeholder: '@contacts_ethnicity_any_other_black_background'
      language: '@language_en_gb'
      value: 'Any other Black background'
  -
    fields:
      placeholder: '@contacts_ethnicity_and_black_caribbean'
      language: '@language_en_gb'
      value: 'White and Black Caribbean'
  -
    fields:
      placeholder: '@contacts_ethnicity_white_and_black_african'
      language: '@language_en_gb'
      value: 'White and Black African'
  -
    fields:
      placeholder: '@contacts_ethnicity_white_and_asian'
      language: '@language_en_gb'
      value: 'White and Asian'
  -
    fields:
      placeholder: '@contacts_ethnicity_any_other_mixed_background'
      language: '@language_en_gb'
      value: 'Any other mixed background'
  -
    fields:
      placeholder: '@contacts_ethnicity_any_other_ethnic_group'
      language: '@language_en_gb'
      value: 'Any other ethnic group'
  -
    fields:
      placeholder: '@contacts_ethnicity_british'
      language: '@language_en_gb'
      value: 'British'
  -
    fields:
      placeholder: '@contacts_ethnicity_irish'
      language: '@language_en_gb'
      value: 'Irish'
  -
    fields:
      placeholder: '@contacts_ethnicity_any_other_white_background'
      language: '@language_en_gb'
      value: 'Any other White background'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_bisexual'
      language: '@language_en_gb'
      value: 'Bisexual'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_gay_man'
      language: '@language_en_gb'
      value: 'Gay man'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_gay_woman_lesbian'
      language: '@language_en_gb'
      value: 'Gay woman/lesbian'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_heterosexual'
      language: '@language_en_gb'
      value: 'Heterosexual'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@contacts_sexual_orientation_information_refused'
      language: '@language_en_gb'
      value: 'Information refused'
  -
    fields:
      placeholder: '@contacts_type_patient'
      language: '@language_en_gb'
      value: 'Patient'
  -
    fields:
      placeholder: '@contacts_subtype_inpatient'
      language: '@language_en_gb'
      value: 'In-patient'
  -
    fields:
      placeholder: '@contacts_datasource_gender'
      language: '@language_en_gb'
      value: 'Contact Gender'
  -
    fields:
      placeholder: '@contacts_datasource_title'
      language: '@language_en_gb'
      value: 'Contact Title'
  -
    fields:
      placeholder: '@contacts_datasource_type'
      language: '@language_en_gb'
      value: 'Contact Type'
  -
    fields:
      placeholder: '@contacts_datasource_subtype'
      language: '@language_en_gb'
      value: 'Contact Subtype'
  -
    fields:
      placeholder: '@contacts_datasource_worker'
      language: '@language_en_gb'
      value: 'Contact Lone Worker'
  -
    fields:
      placeholder: '@contacts_datasource_ethnicities'
      language: '@language_en_gb'
      value: 'Contact Ethnicities'
  -
    fields:
      placeholder: '@contacts_datasource_orientations'
      language: '@language_en_gb'
      value: 'Contact Sexual Orientations'
  -
    fields:
      placeholder: '@contacts_datasource_languages'
      language: '@language_en_gb'
      value: 'Contact Languages'
  -
    fields:
      placeholder: '@contacts_datasource_religions'
      language: '@language_en_gb'
      value: 'Contact Religions'
  -
    fields:
      placeholder: '@contacts_datasource_disabilities'
      language: '@language_en_gb'
      value: 'Contact Disabilities'
  -
    fields:
      placeholder: '@contacts_datasource_languages_indian'
      language: '@language_en_gb'
      value: 'Indian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_akan'
      language: '@language_en_gb'
      value: 'Akan'
  -
    fields:
      placeholder: '@contacts_datasource_languages_amharic'
      language: '@language_en_gb'
      value: 'Amharic'
  -
    fields:
      placeholder: '@contacts_datasource_languages_arabic'
      language: '@language_en_gb'
      value: 'Arabic'
  -
    fields:
      placeholder: '@contacts_datasource_languages_assamese'
      language: '@language_en_gb'
      value: 'Assamese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_awadhi'
      language: '@language_en_gb'
      value: 'Awadhi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_azerbaijani'
      language: '@language_en_gb'
      value: 'Azerbaijani'
  -
    fields:
      placeholder: '@contacts_datasource_languages_balochi'
      language: '@language_en_gb'
      value: 'Balochi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_belarusian'
      language: '@language_en_gb'
      value: 'Belarusian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_bengali'
      language: '@language_en_gb'
      value: 'Bengali (Bangla)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_bhojpuri'
      language: '@language_en_gb'
      value: 'Bhojpuri'
  -
    fields:
      placeholder: '@contacts_datasource_languages_burmese'
      language: '@language_en_gb'
      value: 'Burmese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_cebuano'
      language: '@language_en_gb'
      value: 'Cebuano (Visayan)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_chewa'
      language: '@language_en_gb'
      value: 'Chewa'
  -
    fields:
      placeholder: '@contacts_datasource_languages_chhattisgarhi'
      language: '@language_en_gb'
      value: 'Chhattisgarhi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_chittagonian'
      language: '@language_en_gb'
      value: 'Chittagonian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_czech'
      language: '@language_en_gb'
      value: 'Czech'
  -
    fields:
      placeholder: '@contacts_datasource_languages_deccan'
      language: '@language_en_gb'
      value: 'Deccan'
  -
    fields:
      placeholder: '@contacts_datasource_languages_dhundhari'
      language: '@language_en_gb'
      value: 'Dhundhari'
  -
    fields:
      placeholder: '@contacts_datasource_languages_dutch'
      language: '@language_en_gb'
      value: 'Dutch'
  -
    fields:
      placeholder: '@contacts_datasource_languages_fuzhounese'
      language: '@language_en_gb'
      value: 'Eastern Min (inc. Fuzhounese)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_english'
      language: '@language_en_gb'
      value: 'English'
  -
    fields:
      placeholder: '@contacts_datasource_languages_french'
      language: '@language_en_gb'
      value: 'French'
  -
    fields:
      placeholder: '@contacts_datasource_languages_fula'
      language: '@language_en_gb'
      value: 'Fula'
  -
    fields:
      placeholder: '@contacts_datasource_languages_gan_chinese'
      language: '@language_en_gb'
      value: 'Gan Chinese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_german'
      language: '@language_en_gb'
      value: 'German'
  -
    fields:
      placeholder: '@contacts_datasource_languages_greek'
      language: '@language_en_gb'
      value: 'Greek'
  -
    fields:
      placeholder: '@contacts_datasource_languages_gujarati'
      language: '@language_en_gb'
      value: 'Gujarati'
  -
    fields:
      placeholder: '@contacts_datasource_languages_haitian_creole'
      language: '@language_en_gb'
      value: 'Haitian Creole'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hakka'
      language: '@language_en_gb'
      value: 'Hakka'
  -
    fields:
      placeholder: '@contacts_datasource_languages_haryanvi'
      language: '@language_en_gb'
      value: 'Haryanvi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hausa'
      language: '@language_en_gb'
      value: 'Hausa'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hiligaynon_ilonggo'
      language: '@language_en_gb'
      value: 'Hiligaynon/Ilonggo (Visayan)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hindi'
      language: '@language_en_gb'
      value: 'Hindi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hmong'
      language: '@language_en_gb'
      value: 'Hmong'
  -
    fields:
      placeholder: '@contacts_datasource_languages_hungarian'
      language: '@language_en_gb'
      value: 'Hungarian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_igbo'
      language: '@language_en_gb'
      value: 'Igbo'
  -
    fields:
      placeholder: '@contacts_datasource_languages_ilocano'
      language: '@language_en_gb'
      value: 'Ilocano'
  -
    fields:
      placeholder: '@contacts_datasource_languages_italian'
      language: '@language_en_gb'
      value: 'Italian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_japanese'
      language: '@language_en_gb'
      value: 'Japanese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_javanese'
      language: '@language_en_gb'
      value: 'Javanese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_jin'
      language: '@language_en_gb'
      value: 'Jin'
  -
    fields:
      placeholder: '@contacts_datasource_languages_kannada'
      language: '@language_en_gb'
      value: 'Kannada'
  -
    fields:
      placeholder: '@contacts_datasource_languages_kazakh'
      language: '@language_en_gb'
      value: 'Kazakh'
  -
    fields:
      placeholder: '@contacts_datasource_languages_khmer'
      language: '@language_en_gb'
      value: 'Khmer'
  -
    fields:
      placeholder: '@contacts_datasource_languages_kinyarwanda'
      language: '@language_en_gb'
      value: 'Kinyarwanda'
  -
    fields:
      placeholder: '@contacts_datasource_languages_kirundi'
      language: '@language_en_gb'
      value: 'Kirundi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_konkani'
      language: '@language_en_gb'
      value: 'Konkani'
  -
    fields:
      placeholder: '@contacts_datasource_languages_korean'
      language: '@language_en_gb'
      value: 'Korean'
  -
    fields:
      placeholder: '@contacts_datasource_languages_kurdish'
      language: '@language_en_gb'
      value: 'Kurdish'
  -
    fields:
      placeholder: '@contacts_datasource_languages_madurese'
      language: '@language_en_gb'
      value: 'Madurese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_magahi'
      language: '@language_en_gb'
      value: 'Magahi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_maithili'
      language: '@language_en_gb'
      value: 'Maithili'
  -
    fields:
      placeholder: '@contacts_datasource_languages_malagasy'
      language: '@language_en_gb'
      value: 'Malagasy'
  -
    fields:
      placeholder: '@contacts_datasource_languages_malay'
      language: '@language_en_gb'
      value: 'Malay (inc. Malaysian and Indonesian)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_malayalam'
      language: '@language_en_gb'
      value: 'Malayalam'
  -
    fields:
      placeholder: '@contacts_datasource_languages_mandarin'
      language: '@language_en_gb'
      value: 'Mandarin'
  -
    fields:
      placeholder: '@contacts_datasource_languages_marathi'
      language: '@language_en_gb'
      value: 'Marathi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_marwari'
      language: '@language_en_gb'
      value: 'Marwari'
  -
    fields:
      placeholder: '@contacts_datasource_languages_mossi'
      language: '@language_en_gb'
      value: 'Mossi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_nepali'
      language: '@language_en_gb'
      value: 'Nepali'
  -
    fields:
      placeholder: '@contacts_datasource_languages_northern_min'
      language: '@language_en_gb'
      value: 'Northern Min'
  -
    fields:
      placeholder: '@contacts_datasource_languages_odia'
      language: '@language_en_gb'
      value: 'Odia (Oriya)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_oromo'
      language: '@language_en_gb'
      value: 'Oromo'
  -
    fields:
      placeholder: '@contacts_datasource_languages_pashto'
      language: '@language_en_gb'
      value: 'Pashto'
  -
    fields:
      placeholder: '@contacts_datasource_languages_persian'
      language: '@language_en_gb'
      value: 'Persian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_polish'
      language: '@language_en_gb'
      value: 'Polish'
  -
    fields:
      placeholder: '@contacts_datasource_languages_portuguese'
      language: '@language_en_gb'
      value: 'Portuguese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_punjabi'
      language: '@language_en_gb'
      value: 'Punjabi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_quechua'
      language: '@language_en_gb'
      value: 'Quechua'
  -
    fields:
      placeholder: '@contacts_datasource_languages_romanian'
      language: '@language_en_gb'
      value: 'Romanian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_russian'
      language: '@language_en_gb'
      value: 'Russian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_saraiki'
      language: '@language_en_gb'
      value: 'Saraiki'
  -
    fields:
      placeholder: '@contacts_datasource_languages_serbo_croatian'
      language: '@language_en_gb'
      value: 'Serbo-Croatian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_shona'
      language: '@language_en_gb'
      value: 'Shona'
  -
    fields:
      placeholder: '@contacts_datasource_languages_sindhi'
      language: '@language_en_gb'
      value: 'Sindhi'
  -
    fields:
      placeholder: '@contacts_datasource_languages_sinhalese'
      language: '@language_en_gb'
      value: 'Sinhalese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_somali'
      language: '@language_en_gb'
      value: 'Somali'
  -
    fields:
      placeholder: '@contacts_datasource_languages_southern_min'
      language: '@language_en_gb'
      value: 'Southern Min (incl. Hokkien and Teochew)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_spanish'
      language: '@language_en_gb'
      value: 'Spanish'
  -
    fields:
      placeholder: '@contacts_datasource_languages_sundanese'
      language: '@language_en_gb'
      value: 'Sundanese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_swedish'
      language: '@language_en_gb'
      value: 'Swedish'
  -
    fields:
      placeholder: '@contacts_datasource_languages_sylheti'
      language: '@language_en_gb'
      value: 'Sylheti'
  -
    fields:
      placeholder: '@contacts_datasource_languages_tagalog'
      language: '@language_en_gb'
      value: 'Tagalog'
  -
    fields:
      placeholder: '@contacts_datasource_languages_tamil'
      language: '@language_en_gb'
      value: 'Tamil'
  -
    fields:
      placeholder: '@contacts_datasource_languages_telugu'
      language: '@language_en_gb'
      value: 'Telugu'
  -
    fields:
      placeholder: '@contacts_datasource_languages_thai'
      language: '@language_en_gb'
      value: 'Thai'
  -
    fields:
      placeholder: '@contacts_datasource_languages_turkish'
      language: '@language_en_gb'
      value: 'Turkish'
  -
    fields:
      placeholder: '@contacts_datasource_languages_turkmen'
      language: '@language_en_gb'
      value: 'Turkmen'
  -
    fields:
      placeholder: '@contacts_datasource_languages_ukrainian'
      language: '@language_en_gb'
      value: 'Ukrainian'
  -
    fields:
      placeholder: '@contacts_datasource_languages_urdu'
      language: '@language_en_gb'
      value: 'Urdu'
  -
    fields:
      placeholder: '@contacts_datasource_languages_uyghur'
      language: '@language_en_gb'
      value: 'Uyghur'
  -
    fields:
      placeholder: '@contacts_datasource_languages_uzbek'
      language: '@language_en_gb'
      value: 'Uzbek'
  -
    fields:
      placeholder: '@contacts_datasource_languages_vietnamese'
      language: '@language_en_gb'
      value: 'Vietnamese'
  -
    fields:
      placeholder: '@contacts_datasource_languages_wu'
      language: '@language_en_gb'
      value: 'Wu (inc. Shanghainese)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_xhosa'
      language: '@language_en_gb'
      value: 'Xhosa'
  -
    fields:
      placeholder: '@contacts_datasource_languages_xiang'
      language: '@language_en_gb'
      value: 'Xiang (Hunnanese)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_yoruba'
      language: '@language_en_gb'
      value: 'Yoruba'
  -
    fields:
      placeholder: '@contacts_datasource_languages_yue'
      language: '@language_en_gb'
      value: 'Yue (incl. Cantonese)'
  -
    fields:
      placeholder: '@contacts_datasource_languages_zhuang'
      language: '@language_en_gb'
      value: 'Zhuang'
  -
    fields:
      placeholder: '@contacts_datasource_languages_zulu'
      language: '@language_en_gb'
      value: 'Zulu'
  -
    fields:
      placeholder: '@contacts_form_field_language_select_language'
      language: '@language_en_gb'
      value: 'Select Language'
  -
    fields:
      placeholder: '@contacts_post_save_no_access'
      language: '@language_en_gb'
      value: 'Contact has been saved, but the access permissions assigned to your user do not permit you to see it'
  -
    fields:
      placeholder: '@contacts_forms_id_numbers_default'
      language: '@language_en_gb'
      value: 'Select a number type'
  -
    fields:
      placeholder: '@contacts_numbers_type_title'
      language: '@language_en_gb'
      value: 'Number Type'
  -
    fields:
      placeholder: '@contacts_numbers_type_label'
      language: '@language_en_gb'
      value: 'Number Type'
  -
    fields:
      placeholder: '@contacts_form_field_positions_label'
      language: '@language_en_gb'
      value: 'Positions'
  -
    fields:
      placeholder: '@contacts_form_field_positions'
      language: '@language_en_gb'
      value: 'Positions'
  -
    fields:
      placeholder: '@contacts_form_field_assignment_number'
      language: '@language_en_gb'
      value: Assignment Number
  -
    fields:
      placeholder: '@contacts_form_field_employee_status'
      language: '@language_en_gb'
      value: Employee Status
  -
    fields:
      placeholder: '@contacts_form_field_employee_status_select_employee_status'
      language: '@language_en_gb'
      value: Select Employee Status
  -
    fields:
      placeholder: '@contacts_form_field_employee_status_active'
      language: '@language_en_gb'
      value: Active
  -
    fields:
      placeholder: '@contacts_form_field_employee_status_terminated'
      language: '@language_en_gb'
      value: Terminated
  -
    fields:
      placeholder: '@contacts_form_field_employee_status_not_started'
      language: '@language_en_gb'
      value: Not Started
  -
    fields:
      placeholder: '@contacts_forms_nationality_default'
      language: '@language_en_gb'
      value: Select Nationality
  -
    fields:
      placeholder: '@contacts_nationality_title'
      language: '@language_en_gb'
      value: Nationality
  -
    fields:
      placeholder: '@contacts_nationality_label'
      language: '@language_en_gb'
      value: Nationality
  -
    fields:
      placeholder: '@contacts_nationality_afghanistan'
      language: '@language_en_gb'
      value: Afghanistan
  -
    fields:
      placeholder: '@contacts_nationality_aland_islands'
      language: '@language_en_gb'
      value: 'Aland Islands'
  -
    fields:
      placeholder: '@contacts_nationality_albania'
      language: '@language_en_gb'
      value: 'Albania'
  -
    fields:
      placeholder: '@contacts_nationality_algeria'
      language: '@language_en_gb'
      value: 'Algeria'
  -
    fields:
      placeholder: '@contacts_nationality_american_samoa'
      language: '@language_en_gb'
      value: 'American Samoa'
  -
    fields:
      placeholder: '@contacts_nationality_andorra'
      language: '@language_en_gb'
      value: 'Andorra'
  -
    fields:
      placeholder: '@contacts_nationality_angola'
      language: '@language_en_gb'
      value: 'Angola'
  -
    fields:
      placeholder: '@contacts_nationality_anguilla'
      language: '@language_en_gb'
      value: 'Anguilla'
  -
    fields:
      placeholder: '@contacts_nationality_antarctica'
      language: '@language_en_gb'
      value: 'Antarctica'
  -
    fields:
      placeholder: '@contacts_nationality_antigua_and_barbuda'
      language: '@language_en_gb'
      value: 'Antigua and Barbuda'
  -
    fields:
      placeholder: '@contacts_nationality_argentina'
      language: '@language_en_gb'
      value: 'Argentina'
  -
    fields:
      placeholder: '@contacts_nationality_armenia'
      language: '@language_en_gb'
      value: 'Armenia'
  -
    fields:
      placeholder: '@contacts_nationality_aruba'
      language: '@language_en_gb'
      value: 'Aruba'
  -
    fields:
      placeholder: '@contacts_nationality_australia'
      language: '@language_en_gb'
      value: 'Australia'
  -
    fields:
      placeholder: '@contacts_nationality_austria'
      language: '@language_en_gb'
      value: 'Austria'
  -
    fields:
      placeholder: '@contacts_nationality_azerbaijan'
      language: '@language_en_gb'
      value: 'Azerbaijan'
  -
    fields:
      placeholder: '@contacts_nationality_bahamas'
      language: '@language_en_gb'
      value: 'Bahamas'
  -
    fields:
      placeholder: '@contacts_nationality_bahrain'
      language: '@language_en_gb'
      value: 'Bahrain'
  -
    fields:
      placeholder: '@contacts_nationality_bangladesh'
      language: '@language_en_gb'
      value: 'Bangladesh'
  -
    fields:
      placeholder: '@contacts_nationality_barbados'
      language: '@language_en_gb'
      value: 'Barbados'
  -
    fields:
      placeholder: '@contacts_nationality_belarus'
      language: '@language_en_gb'
      value: 'Belarus'
  -
    fields:
      placeholder: '@contacts_nationality_belgium'
      language: '@language_en_gb'
      value: 'Belgium'
  -
    fields:
      placeholder: '@contacts_nationality_belize'
      language: '@language_en_gb'
      value: 'Belize'
  -
    fields:
      placeholder: '@contacts_nationality_benin'
      language: '@language_en_gb'
      value: 'Benin'
  -
    fields:
      placeholder: '@contacts_nationality_bermuda'
      language: '@language_en_gb'
      value: 'Bermuda'
  -
    fields:
      placeholder: '@contacts_nationality_bhutan'
      language: '@language_en_gb'
      value: 'Bhutan'
  -
    fields:
      placeholder: '@contacts_nationality_bolivia'
      language: '@language_en_gb'
      value: 'Bolivia'
  -
    fields:
      placeholder: '@contacts_nationality_bonaire_sint_eustatius_and_saba'
      language: '@language_en_gb'
      value: 'Bonaire, Sint Eustatius and Saba'
  -
    fields:
      placeholder: '@contacts_nationality_bonaire_sint_eustatius_and_saba'
      language: '@language_en_gb'
      value: 'Bonaire, Sint Eustatius and Saba'
  -
    fields:
      placeholder: '@contacts_nationality_bosnia_and_herzegovina'
      value: 'Bosnia and Herzegovina'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_botswana'
      value: 'Botswana'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_bouvet_island'
      value: 'Bouvet Island'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_brazil'
      value: 'Brazil'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_british_virgin_islands'
      value: 'British Virgin Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_british_indian_ocean_territory'
      value: 'British Indian Ocean Territory'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_brunei_darussalam'
      value: 'Brunei Darussalam'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_bulgaria'
      value: 'Bulgaria'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_burkina_faso'
      value: 'Burkina Faso'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_burundi'
      value: 'Burundi'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cambodia'
      value: 'Cambodia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cameroon'
      value: 'Cameroon'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_canada'
      value: 'Canada'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cape_verde'
      value: 'Cape Verde'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cayman_islands'
      value: 'Cayman Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_central_african_republic'
      value: 'Central African Republic'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_chad'
      value: 'Chad'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_chile'
      value: 'Chile'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_china'
      value: 'China'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_hong_kong_special_administrative_region_of_china'
      value: 'Hong Kong, Special Administrative Region of China'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_macao_special_administrative_region_of_china'
      value: 'Macao, Special Administrative Region of China'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_christmas_island'
      value: 'Christmas Island'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cocos_keeling_islands'
      value: 'Cocos (Keeling) Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_colombia'
      value: 'Colombia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_comoros'
      value: 'Comoros'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_congo_brazzaville'
      value: 'Congo (Brazzaville)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_congo_democratic_republic_of_the'
      value: 'Congo, Democratic Republic of the'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cook_islands'
      value: 'Cook Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_costa_rica'
      value: 'Costa Rica'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cote_divoire'
      value: "Côte d'Ivoire"
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_croatia'
      value: 'Croatia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cuba'
      value: 'Cuba'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_curacao'
      value: 'Curaçao'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_cyprus'
      value: 'Cyprus'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_czech_republic'
      value: 'Czech Republic'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_denmark'
      value: 'Denmark'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_djibouti'
      value: 'Djibouti'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_dominica'
      value: 'Dominica'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_dominican_republic'
      value: 'Dominican Republic'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_ecuador'
      value: 'Ecuador'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_egypt'
      value: 'Egypt'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_el_salvador'
      value: 'El Salvador'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_equatorial_guinea'
      value: 'Equatorial Guinea'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_eritrea'
      value: 'Eritrea'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_estonia'
      value: 'Estonia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_ethiopia'
      value: 'Ethiopia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_falkland_islands_malvinas'
      value: 'Falkland Islands (Malvinas)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_faroe_islands'
      value: 'Faroe Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_fiji'
      value: 'Fiji'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_finland'
      value: 'Finland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_france'
      value: 'France'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_french_guiana'
      value: 'French Guiana'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_french_polynesia'
      value: 'French Polynesia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_french_southern_territories'
      value: 'French Southern Territories'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_gabon'
      value: 'Gabon'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_gambia'
      value: 'Gambia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_georgia'
      value: 'Georgia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_germany'
      value: 'Germany'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_ghana'
      value: 'Ghana'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_gibraltar'
      value: 'Gibraltar'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_greece'
      value: 'Greece'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_greenland'
      value: 'Greenland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_grenada'
      value: 'Grenada'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guadeloupe'
      value: 'Guadeloupe'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guam'
      value: 'Guam'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guatemala'
      value: 'Guatemala'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guernsey'
      value: 'Guernsey'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guinea'
      value: 'Guinea'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guinea_bissau'
      value: 'Guinea-Bissau'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_guyana'
      value: 'Guyana'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_haiti'
      value: 'Haiti'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_heard_island_and_mcdonald_islands'
      value: 'Heard Island and Mcdonald Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_holy_see_vatican_city_state'
      value: 'Holy See (Vatican City State)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_honduras'
      value: 'Honduras'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_hungary'
      value: 'Hungary'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_iceland'
      value: 'Iceland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_india'
      value: 'India'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_indonesia'
      value: 'Indonesia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_iran_islamic_republic_of'
      value: 'Iran, Islamic Republic of'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_iraq'
      value: 'Iraq'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_ireland'
      value: 'Ireland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_isle_of_man'
      value: 'Isle of Man'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_israel'
      value: 'Israel'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_italy'
      value: 'Italy'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_jamaica'
      value: 'Jamaica'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_japan'
      value: 'Japan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_jersey'
      value: 'Jersey'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_jordan'
      value: 'Jordan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_kazakhstan'
      value: 'Kazakhstan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_kenya'
      value: 'Kenya'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_kiribati'
      value: 'Kiribati'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_korea_democratic_peoples_republic_of'
      value: "Korea, Democratic People's Republic of"
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_korea_republic_of'
      value: 'Korea, Republic of'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_kuwait'
      value: 'Kuwait'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_kyrgyzstan'
      value: 'Kyrgyzstan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_lao_pdr'
      value: 'Lao PDR'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_latvia'
      value: 'Latvia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_lebanon'
      value: 'Lebanon'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_lesotho'
      value: 'Lesotho'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_liberia'
      value: 'Liberia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_libya'
      value: 'Libya'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_liechtenstein'
      value: 'Liechtenstein'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_lithuania'
      value: 'Lithuania'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_luxembourg'
      value: 'Luxembourg'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_macedonia_republic_of'
      value: 'Macedonia, Republic of'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_madagascar'
      value: 'Madagascar'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_malawi'
      value: 'Malawi'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_malaysia'
      value: 'Malaysia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_maldives'
      value: 'Maldives'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mali'
      value: 'Mali'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_malta'
      value: 'Malta'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_marshall_islands'
      value: 'Marshall Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_martinique'
      value: 'Martinique'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mauritania'
      value: 'Mauritania'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mauritius'
      value: 'Mauritius'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mayotte'
      value: 'Mayotte'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mexico'
      value: 'Mexico'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_micronesia_federated_states_of'
      value: 'Micronesia, Federated States of'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_moldova'
      value: 'Moldova'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_monaco'
      value: 'Monaco'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mongolia'
      value: 'Mongolia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_montenegro'
      value: 'Montenegro'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_montserrat'
      value: 'Montserrat'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_morocco'
      value: 'Morocco'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_mozambique'
      value: 'Mozambique'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_myanmar'
      value: 'Myanmar'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_namibia'
      value: 'Namibia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_nauru'
      value: 'Nauru'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_nepal'
      value: 'Nepal'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_netherlands'
      value: 'Netherlands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_netherlands_antilles'
      value: 'Netherlands Antilles'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_new_caledonia'
      value: 'New Caledonia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_new_zealand'
      value: 'New Zealand'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_nicaragua'
      value: 'Nicaragua'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_niger'
      value: 'Niger'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_nigeria'
      value: 'Nigeria'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_niue'
      value: 'Niue'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_norfolk_island'
      value: 'Norfolk Island'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_northern_mariana_islands'
      value: 'Northern Mariana Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_norway'
      value: 'Norway'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_oman'
      value: 'Oman'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_pakistan'
      value: 'Pakistan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_palau'
      value: 'Palau'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_palestinian_territory_occupied'
      value: 'Palestinian Territory, Occupied'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_panama'
      value: 'Panama'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_papua_new_guinea'
      value: 'Papua New Guinea'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_paraguay'
      value: 'Paraguay'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_peru'
      value: 'Peru'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_philippines'
      value: 'Philippines'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_pitcairn'
      value: 'Pitcairn'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_poland'
      value: 'Poland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_portugal'
      value: 'Portugal'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_puerto_rico'
      value: 'Puerto Rico'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_qatar'
      value: 'Qatar'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_reunion'
      value: 'Réunion'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_romania'
      value: 'Romania'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_russian_federation'
      value: 'Russian Federation'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_rwanda'
      value: 'Rwanda'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_barthelemy'
      value: 'Saint-Barthélemy'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_helena'
      value: 'Saint Helena'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_kitts_and_nevis'
      value: 'Saint Kitts and Nevis'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_lucia'
      value: 'Saint Lucia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_martin_french_part'
      value: 'Saint-Martin (French part)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_pierre_and_miquelon'
      value: 'Saint Pierre and Miquelon'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saint_vincent_and_grenadines'
      value: 'Saint Vincent and Grenadines'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_samoa'
      value: 'Samoa'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_san_marino'
      value: 'San Marino'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sao_tome_and_principe'
      value: 'Sao Tome and Principe'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_saudi_arabia'
      value: 'Saudi Arabia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_senegal'
      value: 'Senegal'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_serbia'
      value: 'Serbia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_seychelles'
      value: 'Seychelles'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sierra_leone'
      value: 'Sierra Leone'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_singapore'
      value: 'Singapore'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sint_maarten_dutch_part'
      value: 'Sint Maarten (Dutch part)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_slovakia'
      value: 'Slovakia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_slovenia'
      value: 'Slovenia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_solomon_islands'
      value: 'Solomon Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_somalia'
      value: 'Somalia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_south_africa'
      value: 'South Africa'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_south_georgia_and_the_south_sandwich_islands'
      value: 'South Georgia and the South Sandwich Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_south_sudan'
      value: 'South Sudan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_spain'
      value: 'Spain'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sri_lanka'
      value: 'Sri Lanka'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sudan'
      value: 'Sudan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_suriname'
      value: 'Suriname *'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_svalbard_and_jan_mayen_islands'
      value: 'Svalbard and Jan Mayen Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_swaziland'
      value: 'Swaziland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_sweden'
      value: 'Sweden'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_switzerland'
      value: 'Switzerland'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_syrian_arab_republic_syria'
      value: 'Syrian Arab Republic (Syria)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_taiwan'
      value: 'Taiwan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tajikistan'
      value: 'Tajikistan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tanzania_united_republic_of'
      value: 'Tanzania *, United Republic of'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_thailand'
      value: 'Thailand'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_timor_leste'
      value: 'Timor-Leste'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_togo'
      value: 'Togo'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tokelau'
      value: 'Tokelau'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tonga'
      value: 'Tonga'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_trinidad_and_tobago'
      value: 'Trinidad and Tobago'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tunisia'
      value: 'Tunisia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_turkey'
      value: 'Turkey'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_turkmenistan'
      value: 'Turkmenistan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_turks_and_caicos_islands'
      value: 'Turks and Caicos Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_tuvalu'
      value: 'Tuvalu'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_uganda'
      value: 'Uganda'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_ukraine'
      value: 'Ukraine'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_united_arab_emirates'
      value: 'United Arab Emirates'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_united_kingdom'
      value: 'United Kingdom'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_united_states_of_america'
      value: 'United States of America'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_united_states_minor_outlying_islands'
      value: 'United States Minor Outlying Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_uruguay'
      value: 'Uruguay'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_uzbekistan'
      value: 'Uzbekistan'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_vanuatu'
      value: 'Vanuatu'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_venezuela_bolivarian_republic_of'
      value: 'Venezuela (Bolivarian Republic of)'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_vietnam'
      value: 'Viet Nam'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_virgin_islands_us'
      value: 'Virgin Islands, US'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_wallis_and_futuna_islands'
      value: 'Wallis and Futuna Islands'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_western_sahara'
      value: 'Western Sahara'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_yemen'
      value: 'Yemen'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_zambia'
      value: 'Zambia'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_nationality_zimbabwe'
      value: 'Zimbabwe'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_id_number_types_singular'
      value: 'ID Number Type'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_id_number_types_plural'
      value: 'ID Number Types'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_id_number_types_all_types'
      value: 'All types'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_id_number_types_search'
      value: 'Search'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_id_number_types_label'
      value: 'Label'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_events_saved'
      value: 'Contact saved successfully'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_events_deleted'
      value: 'Contact deleted successfully'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_filter_form_title'
      value: 'Filter Contacts'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_submit'
      value: 'Submit'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_select_fields'
      value: 'Select Contact Fields'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_id_number_types'
      language: '@language_en_gb'
      value: 'Select ID Number Types'
  -
    fields:
      placeholder: '@contacts_contact_merging_continue'
      value: 'Continue'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_successfully_created_job'
      value: 'Successfully created job.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_validation_at_least_one_field'
      value: 'Please select at least one field.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_select_type'
      value: 'Select Contact Type'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_validation_at_least_one_contact_type'
      value: 'Please select at least one Contact Type.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_tooltip'
      value: 'If you select multiple contact types, the merge will be executed for all types, but contacts of different types will not be merged together.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_help_text'
      value: 'Select the criteria you wish to use to merge contacts. Any contacts that match all of the selected criteria with a non-blank value will be merged together. Any records that were linked to the previous contacts will be linked to the merged contact.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_merge_contacts'
      value: 'Merge Contacts'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_info_line1'
      value: 'There are currently {{value}} contacts in the contacts module.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_info_line2'
      value: 'Clicking {{button}} will merge {{duplicates}} contacts that match the selected criteria into {{primary}} merged contacts.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_info_line3'
      value: 'After the merge is completed, there will now be {{value}} contacts in the contacts module.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_download_pre_csv'
      value: 'Download the list of {{value}} contacts that will be merged.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_download_post_csv'
      value: 'Download the list of {{value}} contacts that were merged.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_completed_line1'
      value: '{{duplicates}} contacts have successfully been combined into {{merged}} merged contacts.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_completed_line2'
      value: 'There are now {{contacts}} contacts in the Contacts Module.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_perform_another_merge'
      value: 'Perform another merge'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_selected_criteria'
      value: 'Selected criteria'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_job_completed_successfully'
      value: 'Contact merge has completed successfully'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_job_completed_unsuccessfully'
      value: 'Contact merge has failed to complete successfully'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_job_unsuccessful_message'
      value: 'There was an issue which stopped the contact merging job from completing successfully'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_job_cancelled_message'
      value: 'The job has been cancelled'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_number_of_duplicates'
      value: 'Number of duplicates:'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_summary'
      value: 'Summary'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_warning'
      value: 'This action cannot be undone, please ensure you have reviewed all details below before proceeding.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_number_of_primary_contacts'
      value: 'Number of primary contacts:'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_number_of_contacts_before'
      value: 'Number of contacts before:'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_number_of_contacts_after'
      value: 'Number of contacts after:'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_close_warn'
      value: 'Merging in progress, do not navigate away from the page or close the browser'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_close_prompt'
      value: 'Merge is in progress, are you sure you want to leave the page?'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_merging'
      value: 'Merging...'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_contact_merging_duplicate_overlap'
      value: 'Some contacts were not merged because the system could not determine a primary contact, this is because of overlap with another contact.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_job_title_label'
      value: 'Job Title'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_job_title_title'
      value: 'Job Title'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_source_of_record_title'
      value: 'Source of record'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_source_of_record_label'
      value: 'Source of record'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_source_of_record_manual'
      value: 'Manual'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_source_of_record_api'
      value: 'API'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contacts_form_source_of_record_merged'
      value: 'Merged Contact'
      language: '@language_en_gb'
  - fields:
      placeholder: '@contacts_contact_merging_protected_contact_found_error'
      value: 'The merging of some contacts is not permitted as the contact data is from external service.'
      language: '@language_en_gb'
  - fields:
      placeholder: '@contacts_contact_merging_protected_contacts_affected'
      value: 'Contacts affected'
      language: '@language_en_gb'
  - fields:
      placeholder: '@contacts_contact_merging_protected_contacts_filters'
      value: 'Filters'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contact_merging_conflict_pending'
      value: 'Cannot proceed with contact merging as there is a pending contact merge job, please wait at least 15 minutes for this to expire.'
      language: '@language_en_gb'
  -
    fields:
      placeholder: '@contact_merging_conflict_failed'
      value: 'Cannot proceed to create a job.'
      language: '@language_en_gb'
