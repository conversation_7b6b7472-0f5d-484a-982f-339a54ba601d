entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: SERVICES.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
        -
          domain: '@domain_field_maintenance'
        -
          domain: '@domain_safety_learnings'
        -
          domain: '@domain_safety_alerts'
        -
          domain: '@domain_actions'
    ref: services_singular
  -
    fields:
      placeholder: SERVICES.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_users'
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
        -
          domain: '@domain_safety_learnings'
        -
          domain: '@domain_safety_alerts'
    ref: services_plural
  -
    fields:
      placeholder: SERVICES.PLURAL.DRAFT
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: placeholder_services_plural_draft
  -
    fields:
      placeholder: SERVICES.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_users'
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
        -
          domain: '@domain_safety_learnings'
        -
          domain: '@domain_safety_alerts'
    ref: services_search
  -
    fields:
      placeholder: SERVICES.CREATE
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
    ref: services_create
  -
    fields:
      placeholder: SERVICES.EDIT
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
    ref: services_edit
  -
    fields:
      placeholder: SERVICES.POINT_OF
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
    ref: services_point_of
  -
    fields:
      placeholder: SERVICES.POINT_OF.ORIGINATION
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
    ref: services_point_of_origination
  -
    fields:
      placeholder: SERVICES.POINT_OF.DISCOVERY
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
    ref: services_point_of_discovery
  -
    fields:
      placeholder: SERVICES.SERVICE
      type: 0
      domains:
        -
          domain: '@domain_services'
        -
          domain: '@domain_locations'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_service_list'
        -
          domain: '@domain_contacts'
        -
          domain: '@domain_users'
    ref: services_service
  -
    fields:
      placeholder: SERVICES.ASSOCIATE_MEDICINE
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_associate_medicine
  -
    fields:
      placeholder: SERVICES.SURGERY_AND_CANCER
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_surgery_and_cancer
  -
    fields:
      placeholder: SERVICES.EAR_NOSE_AND_THROAT
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_ear_nose_and_throat
  -
    fields:
      placeholder: SERVICES.GENERAL_SURGERY
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_general_surgery
  -
    fields:
      placeholder: SERVICES.OPTHALMOLOGY
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_opthalmology
  -
    fields:
      placeholder: SERVICES.MEDICINE_AND_EMERGENCY_CARE
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_medicine_and_emergency_care
  -
    fields:
      placeholder: SERVICES.CARDIOLOGY
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_cardiology
  -
    fields:
      placeholder: SERVICES.CARE_OF_THE_ELDERLY
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_care_of_the_elderly
  -
    fields:
      placeholder: SERVICES.MINOR_INJURIES
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_minor_injuries
  -
    fields:
      placeholder: SERVICES.ESTATES
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_estates
  -
    fields:
      placeholder: SERVICES.GENERAL_MAINTENANCE
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_general_maintenance
  -
    fields:
      placeholder: SERVICES.LINEN_SERVICES
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_linen_services
  -
    fields:
      placeholder: SERVICES.SITE_SERVICES
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_site_services
  -
    fields:
      placeholder: SERVICES.CATERING
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_catering
  -
    fields:
      placeholder: SERVICES.CORPORATE
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_corporate
  -
    fields:
      placeholder: SERVICES.HR_AND_PAYROLL
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_hr_and_payroll
  -
    fields:
      placeholder: SERVICES.COMMUNICATIONS
      type: 0
      domains:
      -
        domain: '@domain_service_list'
    ref: services_communications
  -
    fields:
      placeholder: SERVICES.SERVICE_TAG_ONE
      type: 0
      domains:
      -
        domain: '@domain_service_tag'
    ref: services_service_tag_one
  -
    fields:
      placeholder: SERVICES.SERVICE_TAG_TWO
      type: 0
      domains:
      -
        domain: '@domain_service_tag'
    ref: services_service_tag_two
  -
    fields:
      placeholder: SERVICES.SERVICE_TAG_THREE
      type: 0
      domains:
      -
        domain: '@domain_service_tag'
    ref: services_service_tag_three
  -
    fields:
      placeholder: SERVICES.SERVICE_SAVED_SUCCESSFULLY
      type: 0
      domains:
      -
        domain: '@domain_services'
    ref: services_service_saved_successfully
  -
    fields:
      placeholder: SERVICES.SERVICE_NOT_FOUND
      type: 0
      domains:
      -
        domain: '@domain_services'
      -
        domain: '@domain_locations'
    ref: services_service_not_found
  -
    fields:
      placeholder: SERVICES.SUCCESS.SERVICE_DELETED
      type: 0
      domains:
      -
        domain: '@domain_services'
    ref: services_service_deleted_successfully
  -
    fields:
      placeholder: SERVICES.ERRORS.DELETE_HAS_CHILDREN
      type: 0
      domains:
      -
        domain: '@domain_services'
      -
        domain: '@domain_locations'
    ref: services_errors_delete_has_children
  -
    fields:
      placeholder: SERVICES.ERRORS.ERROR_DELETING_RECORD
      type: 0
      domains:
      -
        domain: '@domain_services'
      -
        domain: '@domain_locations'
    ref: services_errors_error_deleting_record
  -
    fields:
      placeholder: SERVICES.ERRORS.ERROR_RETRIEVING_SERVICE_HIERARCHY
      type: 0
      domains:
      -
        domain: '@domain_services'
    ref: services_errors_error_retrieving_service_hierarchy
  -
    fields:
      placeholder: SERVICES.ERRORS.SERVICE_NAME_REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_errors_service_name_required
  -
    fields:
      placeholder: SERVICES.ERRORS.SERVICE_NAME_MAX_LENGTH
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_errors_service_name_max_length
  -
    fields:
      placeholder: SERVICES.FORM.TITLE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_form_title_label
  -
    fields:
      placeholder: SERVICES.FORM.STATUS.LABEL
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_form_status_label
  -
    fields:
      placeholder: SERVICES.FORM.STATUS.DATASOURCE.VALUES.ENABLED
      pointer: COMMON.COMPONENTS.TREE.STATUS.ENABLED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_form_status_datasource_values_enabled
  -
    fields:
      placeholder: SERVICES.FORM.STATUS.DATASOURCE.VALUES.DEACTIVATED
      pointer: COMMON.COMPONENTS.TREE.STATUS.DEACTIVATED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_form_status_datasource_values_deactivated
  -
    fields:
      placeholder: SERVICES.FORM.STATUS.DATASOURCE.VALUES.DISABLED
      pointer: COMMON.COMPONENTS.TREE.STATUS.DISABLED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_form_status_datasource_values_disabled
  -
    fields:
      placeholder: SERVICES.ERROR_MESSAGES.INVALID_STATUS_CHANGE
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_error_messages_invalid_status_change
  -
    fields:
      placeholder: SERVICES.ERROR_MESSAGES.INVALID_STATUS_CHANGE_DISABLED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_error_messages_invalid_status_change_disabled
  -
    fields:
      placeholder: SERVICE.DELETE.WARNING_CHILD_NODES
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_delete_warning_child_nodes
  -
    fields:
      placeholder: SERVICE.DELETE.WARNING_LINKED_DATA
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_delete_warning_linked_data
  -
    fields:
      placeholder: SERVICE.DELETE.WARNING_CONFIRM_TEXT
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_delete_warning_confirm_text
  -
   fields:
     placeholder: SERVICE_DRAFTS.SERVICE_DRAFT_ROOT
     type: 0
     domains:
       - domain: '@domain_service_list'
   ref: service_drafts_service_draft_root
  -
    fields:
      placeholder: SERVICE_DRAFTS.SUCCESS_MESSAGES.PUBLISHED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_success_messages_published
  -
    fields:
      placeholder: SERVICE_DRAFTS.SUCCESS_MESSAGES.DISCARDED
      type: 0
      domains:
        -
          domain: '@domain_services'
    ref: services_success_messages_discarded
