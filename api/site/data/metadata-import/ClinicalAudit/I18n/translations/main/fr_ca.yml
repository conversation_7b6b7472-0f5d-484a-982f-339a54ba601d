entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@clinical_audit_nav_new', language: '@language_fr_ca', value: 'Proposer une nouvelle vérification' } }
  - { fields: { placeholder: '@clinical_audit_nav_permissions', language: '@language_fr_ca', value: 'Admin - Permissions' } }
  - { fields: { placeholder: '@clinical_audit_form_1_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@clinical_audit_form_1_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@clinical_audit_form_1_summary', language: '@language_fr_ca', value: 'Sommaire de la vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_1_summary_label', language: '@language_fr_ca', value: 'Sommaire de la vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_1_directorate', language: '@language_fr_ca', value: Direction } }
  - { fields: { placeholder: '@clinical_audit_form_1_directorate_label', language: '@language_fr_ca', value: Direction } }
  - { fields: { placeholder: '@clinical_audit_form_1_department', language: '@language_fr_ca', value: Direction } }
  - { fields: { placeholder: '@clinical_audit_form_1_department_label', language: '@language_fr_ca', value: Direction } }
  - { fields: { placeholder: '@clinical_audit_form_1_grade', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@clinical_audit_form_1_grade_label', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@clinical_audit_form_1_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@clinical_audit_form_1_name_label', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@clinical_audit_form_1_aim', language: '@language_fr_ca', value: Objectif } }
  - { fields: { placeholder: '@clinical_audit_form_1_aim_label', language: '@language_fr_ca', value: Objectif } }
  - { fields: { placeholder: '@clinical_audit_form_1_status', language: '@language_fr_ca', value: 'État de la vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_1_status_label', language: '@language_fr_ca', value: 'État de la vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_1_priority', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@clinical_audit_form_1_priority_label', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@clinical_audit_form_2_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@clinical_audit_form_2_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@clinical_audit_form_3_discussion', language: '@language_fr_ca', value: Discussion } }
  - { fields: { placeholder: '@clinical_audit_form_3_discussion_label', language: '@language_fr_ca', value: Discussion } }
  - { fields: { placeholder: '@clinical_audit_form_3_learning_points', language: '@language_fr_ca', value: 'Points d''apprentissage' } }
  - { fields: { placeholder: '@clinical_audit_form_3_learning_points_label', language: '@language_fr_ca', value: 'Points d''apprentissage' } }
  - { fields: { placeholder: '@clinical_audit_form_3_acknowledgements_and_references', language: '@language_fr_ca', value: 'Accusés de réception et références' } }
  - { fields: { placeholder: '@clinical_audit_form_3_acknowledgements_and_references_label', language: '@language_fr_ca', value: 'Accusés de réception et références' } }
  - { fields: { placeholder: '@clinical_audit_form_3_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@clinical_audit_form_3_attachments_label', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@clinical_audit_form_3_control_comments', language: '@language_fr_ca', value: 'Commentaires de contrôle' } }
  - { fields: { placeholder: '@clinical_audit_form_3_control_comments_label', language: '@language_fr_ca', value: 'Commentaires de contrôle' } }
  - { fields: { placeholder: '@clinical_audit_form_3_cost_of_pilot', language: '@language_fr_ca', value: 'Coûts du pilote de contrôle' } }
  - { fields: { placeholder: '@clinical_audit_form_3_cost_of_pilot_label', language: '@language_fr_ca', value: 'Coûts du pilote de contrôle' } }
  - { fields: { placeholder: '@clinical_audit_data_source_clinical_audit_statuses', language: '@language_fr_ca', value: 'États de vérification clinique' } }
  - { fields: { placeholder: '@clinical_audit_data_source_select_options', language: '@language_fr_ca', value: 'Sélectionnez les options' } }
  - { fields: { placeholder: '@clinical_audit_form_type_audit_form', language: '@language_fr_ca', value: 'Formulaire de vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_type_audit_cycle_form', language: '@language_fr_ca', value: 'Formulaire de cycle de vérification' } }
  - { fields: { placeholder: '@clinical_audit_form_type_audit_review_form', language: '@language_fr_ca', value: 'Formulaire de révision de vérification' } }
  - { fields: { placeholder: '@clinical_audit_datasource_item_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@clinical_audit_datasource_item_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@clinical_audit_datasource_item_high', language: '@language_fr_ca', value: Élevée } }
