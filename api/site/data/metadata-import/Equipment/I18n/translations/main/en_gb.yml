entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@equipment_permissions_subscription_controller_title', language: '@language_en_gb', value: 'Subscription controller' } }
  - { fields: { placeholder: '@equipment_permissions_local_controller_title', language: '@language_en_gb', value: 'Local controller' } }
  - { fields: { placeholder: '@equipment_v2_form_message_equipment_search_invalid_location', language: '@language_en_gb', value: 'No equipment found. The user must be linked to only one location.' } }
  - { fields: { placeholder: '@equipment_v2_form_message_invalid_search_length', language: '@language_en_gb', value: 'Please enter a minimum of two characters to proceed with your search' } }
  - { fields: { placeholder: '@equipment_form_message_not_found', language: '@language_en_gb', value: 'Equipment not found' } }
