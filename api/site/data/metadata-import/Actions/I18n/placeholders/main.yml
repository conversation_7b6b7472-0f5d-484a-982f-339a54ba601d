entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: ACTIONS.ACTIONS_CONFIGURATION
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_actions_configuration
  - fields:
      placeholder: ACTIONS.NAV.MY_ACTIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_nav_my_actions
  - fields:
      placeholder: ACTIONS.NAV.MY_ASSIGNED_ACTIONS
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_nav_my_assigned_actions
  - fields:
      placeholder: ACTIONS.NAV.ALL_ACTIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_nav_all_actions
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.ID
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_id
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_title
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.MODULE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_module
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.PRIORITY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_priority
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.ACTION_TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_action_type
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.START_DATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_start_date
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.DUE_DATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_due_date
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.STATUS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_status
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.COMPLETED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_completed
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.ASSIGNED_TO
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_assigned_to
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.ASSIGNED_BY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_assigned_by
  - fields:
      placeholder: ACTIONS.LIST.MY_ACTIONS.SUMMARY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_list_my_actions_summary
  - fields:
      placeholder: ACTIONS.LIST.MY_ASSIGNED_ACTIONS.SUMMARY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_list_my_assigned_actions_summary
  - fields:
      placeholder: ACTIONS.LIST.ALL_ACTIONS.SUMMARY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_list_all_actions_summary
  - fields:
      placeholder: ACTIONS.FORM.TITLE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_title
  - fields:
      placeholder: ACTIONS.FORM.TITLE.NEW_ACTION
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_title_new_action
  - fields:
      placeholder: ACTIONS.FORM.TITLE.EDIT_ACTION
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_title_edit_action
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.TITLE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_title
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.TITLE.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_title_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DESCRIPTION
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_description
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DESCRIPTION.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_description_label
  - fields:
      placeholder: ACTIONS.ACTION_PLAN.MANAGER
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_action_plan_manager
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.MANAGER.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_manager_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.TYPE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_type
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.TYPE.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_type_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PRIORITY
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_priority
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PRIORITY.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_priority_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PRIORITY.LOW
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: actions_form_fields_priority_low
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PRIORITY.MEDIUM
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: actions_form_fields_priority_medium
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PRIORITY.HIGH
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: actions_form_fields_priority_high
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_start_date
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_start_date_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_DATE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_due_date
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_DATE.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_due_date_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_INTERVAL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_due_interval
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_INTERVAL.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_due_interval_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PROGRESS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_progress
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.PROGRESS.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_progress_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_BASIS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_due_basis
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_BASIS.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_due_basis_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.STATUS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_status
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_status_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.STATUS.OPTIONS.INACTIVE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_status_options_inactive
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.STATUS.OPTIONS.ACTIVE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_status_options_active
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.STATUS.OPTIONS.COMPLETED
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_status_options_completed
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ASSIGNED_TO
      type: 1
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_assigned_to
  - fields:
      placeholder: ACTIONS.FORM.ADD_ACTION_TO_RECORD
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_add_action_to_record
  - fields:
      placeholder: ACTION.FORM.UPDATE_ACTION_ON_RECORD
      type: 0
      domains:
        - domain: '@domain_common'
    ref: action_form_update_action_on_record
  - fields:
      placeholder: ACTIONS.FORM.SAVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_save
  - fields:
      placeholder: ACTIONS.FORM.DELETE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_delete
  - fields:
      placeholder: ACTIONS.FORM.DELETE.WARNING
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_delete_warning
  - fields:
      placeholder: ACTIONS.FORM.RECORD_DETAILS.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_form_record_details_title
  - fields:
      placeholder: ACTIONS.FORM.RECORD_DETAILS.MODULE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_form_record_details_module
  - fields:
      placeholder: ACTIONS.FORM.RECORD_DETAILS.RECORD_ID
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_form_record_details_record_id
  - fields:
      placeholder: ACTIONS.FORM.RECORD_DETAILS.RECORD_TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_form_record_details_record_title
  - fields:
      placeholder: ACTIONS.FORM.RECORD_DETAILS.MODULE_ID
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_record_details_module_id
  - fields:
      placeholder: ACTIONS.FORM.SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_saved_successfully
  - fields:
      placeholder: ACTIONS.FORM.COMPLETED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_completed_successfully
  - fields:
      placeholder: ACTIONS.FORM.DELETE.SUCCESS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_delete_success
  - fields:
      placeholder: ACTIONS.FORM.SAVE_ERROR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_save_error
  - fields:
      placeholder: ACTIONS.ADMIN.NAV.NEW_ACTION_PLAN
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_nav_new_action_plan
  - fields:
      placeholder: ACTIONS.ADMIN.NAV.PERMISSIONS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_nav_permissions
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.FORM.TITLE
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_form_title
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.FORM.FIELDS.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_form_fields_title
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.FORM.FIELDS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_form_fields_description
  - fields:
      placeholder: ACTIONS.ACTION_PLAN.MANAGER_LABEL
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_action_plan_manager_label
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ADD_ACTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_template_add_action
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ACTIONS.LIST.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_template_actions_list_title
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ACTIONS.LIST.SUMMARY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_actions_list_summary
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ACTIONS.LIST.COLUMNS.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_actions_list_columns_title
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ACTIONS.LIST.COLUMNS.DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_actions_list_columns_description
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.ADD_ACTION.SAVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_add_action_save
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_TEMPLATE.TITLE
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_template_title
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.ERRORS.NOT_ENOUGH_ACTIONS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_errors_not_enough_actions
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.TEMPLATE.SAVE_ERROR
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_template_save_error
  - fields:
      placeholder: ACTIONS.RECORD.NAV.MY_ACTIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_record_nav_my_actions
  - fields:
      placeholder: ACTIONS.RECORD.NAV.ALL_ACTIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_record_nav_all_actions
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.SAVE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_admin_action_plan_save
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.ACTION_REMOVED.SUCCESS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_action_removed_success
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.ACTION_REMOVED.ERROR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_action_removed_error
  - fields:
      placeholder: ACTIONS.ADMIN.ACTION_PLAN.ACTIONS.REORDER.SUCCESS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_admin_action_plan_actions_reorder_success
  - fields:
      placeholder: ACTIONS.RECORD.ADD_NEW_ACTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_record_add_new_action
  - fields:
      placeholder: ACTIONS.RECORD.ADD_ACTION_PLAN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_record_add_action_plan
  - fields:
      placeholder: ACTIONS.RECORD.EDIT_ACTION_PLAN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_record_edit_action_plan
  - fields:
      placeholder: ACTIONS.ACTION.MARK_AS_COMPLETE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_mark_as_complete
  - fields:
      placeholder: ACTIONS.ACTION.PROGRESS_NOTES.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_progress_notes_title
  - fields:
      placeholder: ACTIONS.ACTION.PROGRESS_NOTES.NEW
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_progress_notes_new
  - fields:
      placeholder: ACTIONS.ACTION.PROGRESS_NOTES.NOTES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_progress_notes_notes
  - fields:
      placeholder: ACTIONS.ACTION.PROGRESS_NOTES.SAVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_progress_notes_save
  - fields:
      placeholder: ACTIONS.ACTION.BANNERS.COMPLETED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_banners_completed
  - fields:
      placeholder: ACTIONS.NAV.BACK_TO_ACTIONS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_back_to_actions
  - fields:
      placeholder: ACTIONS.NAV.BACK_TO_ACTION
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_back_to_action
  - fields:
      placeholder: ACTIONS.NAV.DETAILS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_details
  - fields:
      placeholder: ACTIONS.NAV.LINKED_RECORDS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_linked_records
  - fields:
      placeholder: ACTIONS.NAV.TO_LINKED_RECORD
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_to_linked_record
  - fields:
      placeholder: ACTIONS.NAV.TO_LINKED_RECORD.ERROR
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_nav_to_linked_record_error
  - fields:
      placeholder: ACTIONS.ERRORS.LOAD
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_errors_load
  - fields:
      placeholder: ACTIONS.ACTION_USERS.TITLE
      domains:
        - domain: '@domain_common'
    ref: actions_action_users_title
  - fields:
      placeholder: ACTIONS.ACTION_USERS.ASSIGNED_BY
      domains:
        - domain: '@domain_common'
    ref: actions_action_users_assigned_by
  - fields:
      placeholder: ACTIONS.ACTION_USERS.ASSIGNED_TO
      domains:
        - domain: '@domain_common'
    ref: actions_action_users_assigned_to
  - fields:
      placeholder: ACTIONS.ACTION_USERS.DATE_ASSIGNED
      domains:
        - domain: '@domain_common'
    ref: actions_action_users_date_assigned
  # Forms
  - fields:
      placeholder: ACTION.FORM_PLAN.NAME
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_form_plan_name
  # From Types
  - fields:
      placeholder: ACTION.TYPE.MAIN.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_main_label
  - fields:
      placeholder: ACTION.TYPE.PLAN.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_plan_label
  - fields:
      placeholder: ACTION.TYPE.TEMPLATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_template_label
  - fields:
      placeholder: ACTION.TYPE.PLAN_TEMPLATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_plan_template_label
  # Data Sources
  - fields:
      placeholder: ACTION.DATA_SOURCE.DATA_PROVIDER
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_provided
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress
  - fields:
      placeholder: ACTION.DATA_SOURCE.DUE_BASIS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_due_basis
  - fields:
      placeholder: ACTION.DATA_SOURCE.STATUS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_status
  # Data Source Item
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.ZERO.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_zero_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.TEN.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_ten_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.TWENTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_twenty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.THIRTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_thirty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.FORTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_forty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.FIFTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_fifty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.SIXTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_sixty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.SEVENTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_seventy_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.EIGHTY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_eighty_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.NINETY.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_ninety_label
  - fields:
      placeholder: ACTION.DATA_SOURCE.PROGRESS.ITEMS.ONE_HUNDRED.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_data_source_progress_items_one_hundred_label
  - fields:
      placeholder: ACTION.DATASOURCE.TYPE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_type
  - fields:
      placeholder: ACTION.DATASOURCE.PROGRESS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_progress
  - fields:
      placeholder: ACTION.DATASOURCE.DUE_BASIS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_due_basis
  - fields:
      placeholder: ACTION.DATASOURCE.STATUS
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_status
  - fields:
      placeholder: ACTION.FORM_TYPE.ACTION_FORM
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
        - domain: '@domain_form_types'
    ref: actions_form_type_action_form
  - fields:
      placeholder: ACTION.FORM_TYPE.ACTION_PLAN_FORM
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
        - domain: '@domain_form_types'
    ref: actions_form_type_action_plan_form
  - fields:
      placeholder: ACTION.FORM_TYPE.ACTION_TEMPLATE_FORM
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
        - domain: '@domain_form_types'
    ref: actions_form_type_action_template_form
  - fields:
      placeholder: ACTION.FORM_TYPE.ACTION_PLAN_TEMPLATE_FORM
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
        - domain: '@domain_form_types'
    ref: actions_form_type_action_plan_template_form
  - fields:
      placeholder: ACTION.TYPE.A
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_a
  - fields:
      placeholder: ACTION.TYPE.B
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_b
  - fields:
      placeholder: ACTION.TYPE.C
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_type_c
  - fields:
      placeholder: ACTION.PROGRESS.0
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_0
  - fields:
      placeholder: ACTION.PROGRESS.10
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_10
  - fields:
      placeholder: ACTION.PROGRESS.20
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_20
  - fields:
      placeholder: ACTION.PROGRESS.30
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_30
  - fields:
      placeholder: ACTION.PROGRESS.40
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_40
  - fields:
      placeholder: ACTION.PROGRESS.50
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_50
  - fields:
      placeholder: ACTION.PROGRESS.60
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_60
  - fields:
      placeholder: ACTION.PROGRESS.70
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_70
  - fields:
      placeholder: ACTION.PROGRESS.80
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_80
  - fields:
      placeholder: ACTION.PROGRESS.90
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_90
  - fields:
      placeholder: ACTION.PROGRESS.100
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_progress_100
  - fields:
      placeholder: ACTION.PROGRESS.COMPLETION_DATE_OF_PREVIOUS_ACTION
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_due_basis_previous_action
  - fields:
      placeholder: ACTION.PROGRESS.CHAIN_ATTACHMENT_DATE
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_due_basis_chain_attachment
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_PRIORITIES
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_action_priorities
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPES
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_datasource_action_types
  - fields:
      placeholder: ACTION.FORMS.NAMES.ACTION_PLAN
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: action_forms_names_action_plan
  - fields:
      placeholder: ACTION.FILTER.FORM.TITLE.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_title_title
  - fields:
      placeholder: ACTION.FILTER.FORM.TITLE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_title_label
  - fields:
      placeholder: ACTION.FORM_TYPE.ACTION_FILTER_FORM
      type: 0
      domains:
        - domain: '@domain_form_types'
    ref: action_filter_form_type_name
  - fields:
      placeholder: ACTION.FILTER.FORM.DESCRIPTION.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_description_title
  - fields:
      placeholder: ACTION.FILTER.FORM.DESCRIPTION.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_description_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.DUE_DATE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_due_date_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.DUE_DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_due_date_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.START_DATE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_start_date_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.START_DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_start_date_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.STATUS
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_status_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.STATUS.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_status_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.ID
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_id_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.ID.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_id_label
  - fields:
      placeholder: ACTION.DATASOURCE.MODULE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_datasource_modules
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.MODULE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_module_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.MODULE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_module_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.PRIORITY
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_priority_title
  - fields:
      placeholder: ACTIONS.FILTER.FORM.FIELDS.PRIORITY.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_filter_form_priority_label
  - fields:
      placeholder: ACTIONS.FILTER.FORM.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: action_filter_form_title
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.AUDIT
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_audit
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.COMMITTEE_STEERING_GROUP
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_committee_steering_group
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.DOCUMENTATION
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_documentation
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.ENVIRONMENT_RELATED
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_environment_related
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.INVESTIGATION
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_investigation
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.POLICY_PROCEDURE_GUIDELINE_RELATED
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_policy_procedure_guideline_related
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.RISK_ASSESSMENT
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_risk_assessment
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.ROOT_CAUSE_ANALYSIS
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_root_cause_analysis
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.TRAINING_RELATED
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_training_related
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.OTHER_TYPE_OF_ACTION
      type: 1
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_action_type_other_type_of_action
  - fields:
      placeholder: ACTION.DATASOURCE.ACTION_TYPE.CE_MANAGEMENT_APPROVAL
      type: 1
      domains:
      - domain: '@domain_actions'
    ref: action_datasource_action_type_ce_management_approval
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ACTION_TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_action_type
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ACTION_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_notification_centre'
    ref: actions_form_fields_action_type_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.SERVICES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_services
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.SERVICES.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_services_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.LOCATIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_locations
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.LOCATIONS.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_form_fields_locations_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE_INTERVAL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_start_date_interval
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE_INTERVAL.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_start_date_interval_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_DATE_INTERVAL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_due_date_interval
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE_DATE_INTERVAL.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_due_date_interval_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.REMINDER_INTERVAL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_reminder_interval
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.REMINDER_INTERVAL.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_reminder_interval_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_start_date
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.START_DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_start_date_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE.DATE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_due_date
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.DUE.DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_due_date_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.REMINDER.DATE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_reminder_date
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.REMINDER.DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: actions_admin_action_plan_template_form_reminder_date_label

  - fields:
      placeholder: ACTION.DATASOURCE.START_DATE
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: action_datasource_start_date
  - fields:
      placeholder: ACTION.START.DATE.PLAN.COMMENCEMENT
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: action_start_date_plan_commencement
  - fields:
      placeholder: ACTION.START.DATE.COMPLETION.OF.STEP
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: action_start_date_completion_of_step
  - fields:
      placeholder: ACTION.DATASOURCE.PLAN_MODULE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_datasource_plan_module
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.MODULE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_form_fields_module_title
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.MODULE.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: action_form_fields_module_label
  - fields:
      placeholder: ACTIONS.PLAN.WORK_DAY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_plan_work_day
  - fields:
      placeholder: ACTIONS.PLAN.CALENDAR_DAY
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_plan_calendar_day
  - fields:
      placeholder: ACTIONS.PLAN.PREVIOUS_STEP
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_plan_previous_step
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.LINKED_RECORD
      type: 0
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_admin'
    ref: actions_linked_records_linked_record

  - fields:
      placeholder: ACTIONS.FORMS.LOCATIONS_FILTER.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_locations_filter_title
  - fields:
      placeholder: ACTIONS.FORMS.LOCATIONS_FILTER.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_locations_filter_label
  - fields:
      placeholder: ACTIONS.FORMS.SERVICES_FILTER.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_services_filter_title
  - fields:
      placeholder: ACTIONS.FORMS.SERVICES_FILTER.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_services_filter_label
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types
  - fields:
      placeholder: ACTIONS.FORMS.USER_ASSIGNER_FILTER.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_user_assigner_filter_title
  - fields:
      placeholder: ACTIONS.FORMS.USER_ASSIGNER_FILTER.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_user_assigner_filter_label
  - fields:
      placeholder: ACTIONS.FORMS.USER_ASSIGNEE_FILTER.TITLE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_user_assignee_filter_title
  - fields:
      placeholder: ACTIONS.FORMS.USER_ASSIGNEE_FILTER.LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_forms_user_assignee_filter_label
  - fields:
      placeholder: ACTIONS.ACTION.BACK_TO_RECORD
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_action_back_to_record
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.CLAIM
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_claim
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.CLINICAL_AUDIT
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_clinical_audit
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.CONTROL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_control
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.FEEDBACK
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_feedback
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.INCIDENT
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_incident
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.INVESTIGATION
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_investigation
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.LEARNING
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_learning
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.MORTALITY
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_mortality
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.PROGRAMME
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_programme
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.RECOMMENDATION
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_recommendation
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.RISK
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_risk
  - fields:
      placeholder: ACTIONS.LINKED_RECORDS.RECORD_TYPES.SAFETY_ROUND
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_linked_records_record_types_safety_round
  - fields:
      placeholder: ACTIONS.AUDIT.ENTITIES.ACTION
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_audit_entities_action
  - fields:
      placeholder: ACTIONS.AUDIT.ENTITIES.ACTION_USER
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_audit_entities_action_user
  - fields:
      placeholder: ACTIONS.EMAIL_NOTIFICATION.DAY
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_email_notification_day
  - fields:
      placeholder: ACTIONS.EMAIL_NOTIFICATION.DAYS
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_email_notification_days
  - fields:
      placeholder: ACTIONS.EMAIL_NOTIFICATION.NO_DUE_DATE
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_email_notification_no_due_date
  - fields:
      placeholder: ACTIONS.COPY.ACTION_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_copy_action_not_found
  - fields:
      placeholder: ACTIONS.COPY.MODULE_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_copy_module_not_found
  - fields:
      placeholder: ACTIONS.ACTION.RECORD_LINK_LABEL
      type: 0
      domains:
        - domain: '@domain_actions'
    ref: actions_action_record_link_label
  - fields:
      placeholder: ACTIONS.ACTION_NOTES.NOTE_LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_action_notes_note_label
  - fields:
      placeholder: ACTIONS.FIELD.PROGRESS.NOTE.SUCCESS
      type: 0
      domains:
      - domain: '@domain_actions'
    ref: actions_field_progress_note_success
  - fields:
      placeholder: ACTIONS.FIELD.PROGRESS.NOTE.ERROR
      type: 0
      domains:
      - domain: '@domain_actions'
    ref: actions_field_progress_note_error
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ATTACHMENTS.TITLE
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: action_form_fields_attachments
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ATTACHMENTS.LABEL
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: action_form_fields_attachments_label
  - fields:
      placeholder: ACTIONS.FORM.FIELDS.ATTACHMENTS.SUCCESS_MESSAGE
      type: 1
      domains:
        - domain: '@domain_actions'
        - domain: '@domain_field_maintenance'
    ref: action_form_fields_attachments_success_message
  - fields:
      placeholder: ACTIONS.LIST.COLUMNS.HAS_ATTACHMENTS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_list_columns_has_attachments
  - fields:
      placeholder: ACTIONS.ERRORS.DUE_DATE_AFTER_START_DATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_errors_due_date_after_start_date

  - fields:
      placeholder: ACTIONS.ERRORS.NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_errors_not_found
  - fields:
      placeholder: ACTIONS.COMPLETE_ACTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: actions_complete_action
