entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@actions_actions_configuration'
      language: '@language_en_gb'
      value: 'Actions Configuration'
  -
    fields:
      placeholder: '@actions_nav_my_actions'
      language: '@language_en_gb'
      value: 'My Actions'
  -
    fields:
      placeholder: '@actions_nav_my_assigned_actions'
      language: '@language_en_gb'
      value: 'My Assigned Actions'
  -
    fields:
      placeholder: '@actions_nav_all_actions'
      language: '@language_en_gb'
      value: 'All Actions'
  -
    fields:
      placeholder: '@actions_list_columns_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@actions_list_columns_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@actions_list_columns_module'
      language: '@language_en_gb'
      value: Module
  -
    fields:
      placeholder: '@actions_list_columns_priority'
      language: '@language_en_gb'
      value: Priority
  -
    fields:
      placeholder: '@actions_list_columns_action_type'
      language: '@language_en_gb'
      value: 'Action Type'
  -
    fields:
      placeholder: '@actions_list_columns_start_date'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@actions_list_columns_due_date'
      language: '@language_en_gb'
      value: 'Due Date'
  -
    fields:
      placeholder: '@actions_list_columns_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@actions_list_columns_completed'
      language: '@language_en_gb'
      value: Completed
  -
    fields:
      placeholder: '@actions_list_columns_assigned_to'
      language: '@language_en_gb'
      value: 'Assigned To'
  -
    fields:
      placeholder: '@actions_list_columns_assigned_by'
      language: '@language_en_gb'
      value: 'Assigned By'
  -
    fields:
      placeholder: '@actions_list_my_actions_summary'
      language: '@language_en_gb'
      value: 'This area displays all actions that have been assigned to you'
  -
    fields:
      placeholder: '@actions_list_my_assigned_actions_summary'
      language: '@language_en_gb'
      value: 'This area displays all actions that you have assigned to another user'
  -
    fields:
      placeholder: '@actions_list_all_actions_summary'
      language: '@language_en_gb'
      value: 'This area displays all actions that exist across the entire system'
  -
    fields:
      placeholder: '@actions_form_title'
      language: '@language_en_gb'
      value: Action
  -
    fields:
      placeholder: '@actions_form_title_new_action'
      language: '@language_en_gb'
      value: 'New Action'
  -
    fields:
      placeholder: '@actions_form_title_edit_action'
      language: '@language_en_gb'
      value: 'Edit Action'
  -
    fields:
      placeholder: '@actions_form_fields_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@actions_form_fields_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@actions_action_plan_manager'
      language: '@language_en_gb'
      value: Manager
  -
    fields:
      placeholder: '@actions_form_fields_priority_label'
      language: '@language_en_gb'
      value: Priority
  -
    fields:
      placeholder: '@actions_form_fields_priority_low'
      language: '@language_en_gb'
      value: Low
  -
    fields:
      placeholder: '@actions_form_fields_priority_medium'
      language: '@language_en_gb'
      value: Medium
  -
    fields:
      placeholder: '@actions_form_fields_priority_high'
      language: '@language_en_gb'
      value: High
  -
    fields:
      placeholder: '@actions_form_fields_start_date'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@actions_form_fields_due_date'
      language: '@language_en_gb'
      value: 'Due Date'
  -
    fields:
      placeholder: '@actions_form_fields_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@actions_form_fields_status_label'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@actions_form_fields_status_options_inactive'
      language: '@language_en_gb'
      value: Inactive
  -
    fields:
      placeholder: '@actions_form_fields_status_options_active'
      language: '@language_en_gb'
      value: Active
  -
    fields:
      placeholder: '@actions_form_fields_status_options_completed'
      language: '@language_en_gb'
      value: Completed
  -
    fields:
      placeholder: '@actions_form_fields_assigned_to'
      language: '@language_en_gb'
      value: 'Assigned To'
  -
    fields:
      placeholder: '@actions_form_add_action_to_record'
      language: '@language_en_gb'
      value: 'Add Action to Record'
  -
    fields:
      placeholder: '@action_form_update_action_on_record'
      language: '@language_en_gb'
      value: 'Update Action on Record'
  -
    fields:
      placeholder: '@actions_form_save'
      language: '@language_en_gb'
      value: 'Save Action'
  -
    fields:
      placeholder: '@actions_form_delete'
      language: '@language_en_gb'
      value: 'Delete Action'
  -
    fields:
      placeholder: '@actions_form_delete_warning'
      language: '@language_en_gb'
      value: 'Deleting an action record is irreversible. Please ensure that no linked data will be impacted by this deletion. If you are happy to proceed, please confirm.'
  -
    fields:
      placeholder: '@actions_form_record_details_title'
      language: '@language_en_gb'
      value: 'Record Details'
  -
    fields:
      placeholder: '@actions_form_record_details_module'
      language: '@language_en_gb'
      value: Module
  -
    fields:
      placeholder: '@actions_form_record_details_record_id'
      language: '@language_en_gb'
      value: 'Record ID'
  -
    fields:
      placeholder: '@actions_form_record_details_record_title'
      language: '@language_en_gb'
      value: 'Record Title'
  -
    fields:
      placeholder: '@actions_form_record_details_module_id'
      language: '@language_en_gb'
      value: 'Module ID'
  -
    fields:
      placeholder: '@actions_form_saved_successfully'
      language: '@language_en_gb'
      value: 'Action saved successfully'
  -
    fields:
      placeholder: '@actions_form_completed_successfully'
      language: '@language_en_gb'
      value: 'Action completed successfully'
  -
    fields:
      placeholder: '@actions_form_delete_success'
      language: '@language_en_gb'
      value: 'Action deleted successfully'
  -
    fields:
      placeholder: '@actions_form_save_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Action'
  -
    fields:
      placeholder: '@actions_admin_nav_new_action_plan'
      language: '@language_en_gb'
      value: 'New Action Plan'
  -
    fields:
      placeholder: '@actions_admin_nav_permissions'
      language: '@language_en_gb'
      value: Permissions
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_form_title'
      language: '@language_en_gb'
      value: 'Action Template'
  -
    fields:
      placeholder: '@actions_admin_action_plan_form_fields_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@actions_admin_action_plan_form_fields_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@actions_action_plan_manager_label'
      language: '@language_en_gb'
      value: Manager
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_add_action'
      language: '@language_en_gb'
      value: 'Add action(s)'
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_actions_list_title'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_actions_list_summary'
      language: '@language_en_gb'
      value: 'A minimum of one Action is required for an Action Plan'
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_actions_list_columns_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_actions_list_columns_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@actions_admin_action_plan_add_action_save'
      language: '@language_en_gb'
      value: 'Add Action'
  -
    fields:
      placeholder: '@actions_admin_action_template_title'
      language: '@language_en_gb'
      value: 'Action Template'
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_errors_not_enough_actions'
      language: '@language_en_gb'
      value: 'A minimum of one Action are required for an Action Plan'
  -
    fields:
      placeholder: '@actions_admin_action_plan_template_save_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Action Plan'
  -
    fields:
      placeholder: '@actions_record_nav_my_actions'
      language: '@language_en_gb'
      value: 'My Actions'
  -
    fields:
      placeholder: '@actions_record_nav_all_actions'
      language: '@language_en_gb'
      value: 'All Actions'
  -
    fields:
      placeholder: '@actions_admin_action_plan_save'
      language: '@language_en_gb'
      value: 'Save Action Plan'
  -
    fields:
      placeholder: '@actions_admin_action_plan_action_removed_success'
      language: '@language_en_gb'
      value: 'Action removed successfully'
  -
    fields:
      placeholder: '@actions_admin_action_plan_action_removed_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst removing this Action'
  -
    fields:
      placeholder: '@actions_admin_action_plan_actions_reorder_success'
      language: '@language_en_gb'
      value: 'Actions reordered successfully'
  -
    fields:
      placeholder: '@actions_record_add_new_action'
      language: '@language_en_gb'
      value: 'Add New Action'
  -
    fields:
      placeholder: '@actions_record_add_action_plan'
      language: '@language_en_gb'
      value: 'Add Action Plan'
  -
    fields:
      placeholder: '@actions_record_edit_action_plan'
      language: '@language_en_gb'
      value: 'Edit Action Plan'
  -
    fields:
      placeholder: '@actions_action_mark_as_complete'
      language: '@language_en_gb'
      value: 'Mark As Complete'
  -
    fields:
      placeholder: '@actions_action_progress_notes_title'
      language: '@language_en_gb'
      value: 'Progress Notes'
  -
    fields:
      placeholder: '@actions_action_progress_notes_new'
      language: '@language_en_gb'
      value: 'New Progress Note'
  -
    fields:
      placeholder: '@actions_action_progress_notes_notes'
      language: '@language_en_gb'
      value: Notes
  -
    fields:
      placeholder: '@actions_action_progress_notes_save'
      language: '@language_en_gb'
      value: 'Save Progress Note'
  -
    fields:
      placeholder: '@actions_action_banners_completed'
      language: '@language_en_gb'
      value: 'This Action was completed at {{ date }} by {{ name }}'
  -
    fields:
      placeholder: '@actions_nav_back_to_actions'
      language: '@language_en_gb'
      value: 'Back to Actions'
  -
    fields:
      placeholder: '@actions_nav_back_to_action'
      language: '@language_en_gb'
      value: 'Back to Action'
  -
    fields:
      placeholder: '@actions_nav_details'
      language: '@language_en_gb'
      value: 'Details'
  -
    fields:
      placeholder: '@actions_nav_linked_records'
      language: '@language_en_gb'
      value: 'Linked Record'
  -
    fields:
      placeholder: '@actions_nav_to_linked_record'
      language: '@language_en_gb'
      value: 'To Linked Record'
  -
    fields:
      placeholder: '@actions_nav_to_linked_record_error'
      language: '@language_en_gb'
      value: 'You do not have access to the linked record'
  -
    fields:
      placeholder: '@actions_errors_load'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Action'
  -
    fields:
      placeholder: '@actions_action_users_title'
      language: '@language_en_gb'
      value: 'Assigned Users'
  -
    fields:
      placeholder: '@actions_action_users_assigned_by'
      language: '@language_en_gb'
      value: 'Assigned By'
  -
    fields:
      placeholder: '@actions_action_users_assigned_to'
      language: '@language_en_gb'
      value: 'Assigned To'
  -
    fields:
      placeholder: '@actions_action_users_date_assigned'
      language: '@language_en_gb'
      value: 'Date Assigned'
# Form
  - fields:
      placeholder: '@actions_form_fields_title'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@actions_form_fields_title_label'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@actions_form_fields_description'
      language: '@language_en_gb'
      value: 'Description'
  - fields:
      placeholder: '@actions_form_fields_description_label'
      language: '@language_en_gb'
      value: 'Description'
  - fields:
      placeholder: '@actions_action_plan_manager'
      language: '@language_en_gb'
      value: 'Manager'
  - fields:
      placeholder: '@actions_form_fields_manager_label'
      language: '@language_en_gb'
      value: 'Manager'
  - fields:
      placeholder: '@actions_form_fields_type'
      language: '@language_en_gb'
      value: 'Type'
  - fields:
      placeholder: '@actions_form_fields_type_label'
      language: '@language_en_gb'
      value: 'Type'
  - fields:
      placeholder: '@actions_form_fields_priority'
      language: '@language_en_gb'
      value: 'Priority'
  - fields:
      placeholder: '@actions_form_fields_priority_label'
      language: '@language_en_gb'
      value: 'Priority'
  - fields:
      placeholder: '@actions_form_fields_start_date'
      language: '@language_en_gb'
      value: 'Start Date'
  - fields:
      placeholder: '@actions_form_fields_start_date_label'
      language: '@language_en_gb'
      value: 'Start Date'
  - fields:
      placeholder: '@actions_form_fields_due_date'
      language: '@language_en_gb'
      value: 'Due Date'
  - fields:
      placeholder: '@actions_form_fields_due_date_label'
      language: '@language_en_gb'
      value: 'Due Date'
  - fields:
      placeholder: '@actions_form_fields_progress'
      language: '@language_en_gb'
      value: 'Progress'
  - fields:
      placeholder: '@actions_form_fields_progress_label'
      language: '@language_en_gb'
      value: 'Progress'
  - fields:
      placeholder: '@actions_form_fields_due_basis'
      language: '@language_en_gb'
      value: 'Due Basis'
  - fields:
      placeholder: '@actions_form_fields_due_basis_label'
      language: '@language_en_gb'
      value: 'Due Basis'
  - fields:
      placeholder: '@actions_form_fields_due_interval'
      language: '@language_en_gb'
      value: 'Due Interval'
  - fields:
      placeholder: '@actions_form_fields_due_interval_label'
      language: '@language_en_gb'
      value: 'Due Interval'
  - fields:
      placeholder: '@actions_form_fields_status'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@actions_form_fields_status_label'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@actions_datasource_type'
      language: '@language_en_gb'
      value: 'Type'
  - fields:
      placeholder: '@actions_datasource_progress'
      language: '@language_en_gb'
      value: 'Progress'
  - fields:
      placeholder: '@actions_datasource_due_basis'
      language: '@language_en_gb'
      value: 'Due Basis'
  - fields:
      placeholder: '@actions_datasource_status'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@actions_form_type_action_form'
      language: '@language_en_gb'
      value: 'Action Form'
  - fields:
      placeholder: '@actions_form_type_action_plan_form'
      language: '@language_en_gb'
      value: 'Action Plan Form'
  - fields:
      placeholder: '@actions_form_type_action_template_form'
      language: '@language_en_gb'
      value: 'Action Template Form'
  - fields:
      placeholder: '@actions_form_type_action_plan_template_form'
      language: '@language_en_gb'
      value: 'Action Plan Template Form'
  - fields:
      placeholder: '@actions_type_a'
      language: '@language_en_gb'
      value: 'Type A'
  - fields:
      placeholder: '@actions_type_b'
      language: '@language_en_gb'
      value: 'Type B'
  - fields:
      placeholder: '@actions_type_c'
      language: '@language_en_gb'
      value: 'Type C'
  - fields:
      placeholder: '@actions_progress_0'
      language: '@language_en_gb'
      value: '0%'
  - fields:
      placeholder: '@actions_progress_10'
      language: '@language_en_gb'
      value: '10%'
  - fields:
      placeholder: '@actions_progress_20'
      language: '@language_en_gb'
      value: '20%'
  - fields:
      placeholder: '@actions_progress_30'
      language: '@language_en_gb'
      value: '30%'
  - fields:
      placeholder: '@actions_progress_40'
      language: '@language_en_gb'
      value: '40%'
  - fields:
      placeholder: '@actions_progress_50'
      language: '@language_en_gb'
      value: '50%'
  - fields:
      placeholder: '@actions_progress_60'
      language: '@language_en_gb'
      value: '60%'
  - fields:
      placeholder: '@actions_progress_70'
      language: '@language_en_gb'
      value: '70%'
  - fields:
      placeholder: '@actions_progress_80'
      language: '@language_en_gb'
      value: '80%'
  - fields:
      placeholder: '@actions_progress_90'
      language: '@language_en_gb'
      value: '90%'
  - fields:
      placeholder: '@actions_progress_100'
      language: '@language_en_gb'
      value: '100%'
  - fields:
      placeholder: '@actions_due_basis_previous_action'
      language: '@language_en_gb'
      value: 'Completion Date of Previous Action'
  - fields:
      placeholder: '@actions_due_basis_chain_attachment'
      language: '@language_en_gb'
      value: 'Chain Attachment Date'
  - fields:
      placeholder: '@actions_datasource_action_priorities'
      language: '@language_en_gb'
      value: 'Action priorities'
  - fields:
      placeholder: '@actions_datasource_action_types'
      language: '@language_en_gb'
      value: 'Action Types'
  - fields:
      placeholder: '@action_forms_names_action_plan'
      language: '@language_en_gb'
      value: 'Action Plan'
  - fields:
      placeholder: '@action_filter_form_title_title'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@action_filter_form_title_label'
      language: '@language_en_gb'
      value: 'Action Title'
  - fields:
      placeholder: '@action_filter_form_type_name'
      language: '@language_en_gb'
      value: 'Action Filter Form'
  - fields:
      placeholder: '@action_filter_form_description_title'
      language: '@language_en_gb'
      value: 'Description'
  - fields:
      placeholder: '@action_filter_form_description_label'
      language: '@language_en_gb'
      value: 'Action Description'
  - fields:
      placeholder: '@action_filter_form_due_date_title'
      language: '@language_en_gb'
      value: 'Due Date'
  - fields:
      placeholder: '@action_filter_form_due_date_label'
      language: '@language_en_gb'
      value: 'Action Due Date'
  -
    fields:
      placeholder: '@action_filter_form_start_date_title'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@action_filter_form_start_date_label'
      language: '@language_en_gb'
      value: 'Action Start Date'
  - fields:
      placeholder: '@action_filter_form_status_title'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@action_filter_form_status_label'
      language: '@language_en_gb'
      value: 'Action Status'
  - fields:
      placeholder: '@action_filter_form_id_title'
      language: '@language_en_gb'
      value: 'ID'
  - fields:
      placeholder: '@action_filter_form_id_label'
      language: '@language_en_gb'
      value: 'Action ID'
  - fields:
      placeholder: '@actions_datasource_modules'
      language: '@language_en_gb'
      value: 'Modules'
  - fields:
      placeholder: '@action_filter_form_module_title'
      language: '@language_en_gb'
      value: 'Module'
  - fields:
      placeholder: '@action_filter_form_module_label'
      language: '@language_en_gb'
      value: 'Action Module'
  - fields:
      placeholder: '@action_filter_form_priority_title'
      language: '@language_en_gb'
      value: 'Priority'
  - fields:
      placeholder: '@action_filter_form_priority_label'
      language: '@language_en_gb'
      value: 'Action Priority'
  - fields:
      placeholder: '@action_filter_form_title'
      language: '@language_en_gb'
      value: 'Action Filters'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_start_date_interval'
      language: '@language_en_gb'
      value: 'Start Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_start_date_interval_label'
      language: '@language_en_gb'
      value: 'Start Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_due_date_interval'
      language: '@language_en_gb'
      value: 'Due Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_due_date_interval_label'
      language: '@language_en_gb'
      value: 'Due Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_reminder_interval'
      language: '@language_en_gb'
      value: 'Reminder Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_reminder_interval_label'
      language: '@language_en_gb'
      value: 'Reminder Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_start_date'
      language: '@language_en_gb'
      value: 'Start Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_start_date_label'
      language: '@language_en_gb'
      value: 'Start Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_due_date'
      language: '@language_en_gb'
      value: 'Due Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_due_date_label'
      language: '@language_en_gb'
      value: 'Due Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_reminder_date'
      language: '@language_en_gb'
      value: 'Reminder Date Interval'
  - fields:
      placeholder: '@actions_admin_action_plan_template_form_reminder_date_label'
      language: '@language_en_gb'
      value: 'Reminder Date Interval'
  - fields:
      placeholder: '@action_datasource_start_date'
      language: '@language_en_gb'
      value: 'Start Date'

  - fields:
      placeholder: '@action_datasource_action_type'
      language: '@language_en_gb'
      value: 'Action type'
  - fields:
      placeholder: '@action_datasource_action_type_audit'
      language: '@language_en_gb'
      value: 'Audit'
  - fields:
      placeholder: '@action_datasource_action_type_committee_steering_group'
      language: '@language_en_gb'
      value: 'Committee/Steering Group'
  - fields:
      placeholder: '@action_datasource_action_type_documentation'
      language: '@language_en_gb'
      value: 'Documentation'
  - fields:
      placeholder: '@action_datasource_action_type_environment_related'
      language: '@language_en_gb'
      value: 'Environment Related'
  - fields:
      placeholder: '@action_datasource_action_type_investigation'
      language: '@language_en_gb'
      value: 'Investigation'
  - fields:
      placeholder: '@action_datasource_action_type_policy_procedure_guideline_related'
      language: '@language_en_gb'
      value: 'Policy/Procedure/Guideline Related'
  - fields:
      placeholder: '@action_datasource_action_type_risk_assessment'
      language: '@language_en_gb'
      value: 'Risk Assessment'
  - fields:
      placeholder: '@action_datasource_action_type_root_cause_analysis'
      language: '@language_en_gb'
      value: 'Root Cause Analysis'
  - fields:
      placeholder: '@action_datasource_action_type_training_related'
      language: '@language_en_gb'
      value: 'Training Related'
  - fields:
      placeholder: '@action_datasource_action_type_other_type_of_action'
      language: '@language_en_gb'
      value: 'Other Type of Action'
  - fields:
      placeholder: '@action_datasource_action_type_ce_management_approval'
      language: '@language_en_gb'
      value: 'CE/Management approval'
  - fields:
      placeholder: '@actions_form_fields_action_type'
      language: '@language_en_gb'
      value: 'Action type'
  - fields:
      placeholder: '@actions_form_fields_action_type_label'
      language: '@language_en_gb'
      value: 'Action type'
  - fields:
      placeholder: '@actions_form_fields_services'
      language: '@language_en_gb'
      value: 'Services'
  - fields:
      placeholder: '@actions_form_fields_services_label'
      language: '@language_en_gb'
      value: 'Services'
  - fields:
      placeholder: '@actions_form_fields_locations'
      language: '@language_en_gb'
      value: 'Locations'
  - fields:
      placeholder: '@actions_form_fields_locations_label'
      language: '@language_en_gb'
      value: 'Locations'
  - fields:
      placeholder: '@action_start_date_plan_commencement'
      language: '@language_en_gb'
      value: 'Plan Commencement'
  - fields:
      placeholder: '@action_start_date_completion_of_step'
      language: '@language_en_gb'
      value: 'Completion of Step'

  - fields:
      placeholder: '@action_form_fields_module_title'
      language: '@language_en_gb'
      value: 'Available Module'
  - fields:
      placeholder: '@action_form_fields_module_label'
      language: '@language_en_gb'
      value: 'Available Module'
  - fields:
      placeholder: '@action_datasource_plan_module'
      language: '@language_en_gb'
      value: 'Action Plan Module'
  - fields:
      placeholder: '@actions_plan_work_day'
      language: '@language_en_gb'
      value: 'Working Day'
  - fields:
      placeholder: '@actions_plan_calendar_day'
      language: '@language_en_gb'
      value: 'Calendar Day'
  - fields:
      placeholder: '@actions_plan_previous_step'
      language: '@language_en_gb'
      value: 'Previous Step'
  - fields:
      placeholder: '@actions_linked_records_linked_record'
      language: '@language_en_gb'
      value: 'Linked Records'
  - fields:
      placeholder: '@actions_forms_locations_filter_title'
      language: '@language_en_gb'
      value: 'Locations'
  - fields:
      placeholder: '@actions_forms_locations_filter_label'
      language: '@language_en_gb'
      value: 'Locations'
  - fields:
      placeholder: '@actions_forms_services_filter_title'
      language: '@language_en_gb'
      value: 'Services'
  - fields:
      placeholder: '@actions_forms_services_filter_label'
      language: '@language_en_gb'
      value: 'Services'
  - fields:
      placeholder: '@actions_linked_records_record_types'
      language: '@language_en_gb'
      value: '{{module}} {{id}}'
  - fields:
      placeholder: '@actions_forms_user_assigner_filter_title'
      language: '@language_en_gb'
      value: 'Assigned By'
  - fields:
      placeholder: '@actions_forms_user_assigner_filter_label'
      language: '@language_en_gb'
      value: 'Assigned By'
  - fields:
      placeholder: '@actions_forms_user_assignee_filter_title'
      language: '@language_en_gb'
      value: 'Assigned To'
  - fields:
      placeholder: '@actions_forms_user_assignee_filter_label'
      language: '@language_en_gb'
      value: 'Assigned To'
  - fields:
      placeholder: '@actions_action_back_to_record'
      language: '@language_en_gb'
      value: 'Back to Record'
  - fields:
      placeholder: '@actions_linked_records_record_types_claim'
      language: '@language_en_gb'
      value: 'Claim {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_control'
      language: '@language_en_gb'
      value: 'Control {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_feedback'
      language: '@language_en_gb'
      value: 'Feedback {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_incident'
      language: '@language_en_gb'
      value: 'Incident {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_investigation'
      language: '@language_en_gb'
      value: 'Investigation {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_learning'
      language: '@language_en_gb'
      value: 'Learning {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_mortality'
      language: '@language_en_gb'
      value: 'Mortality {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_programme'
      language: '@language_en_gb'
      value: 'Compliance Assessment Programme {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_recommendation'
      language: '@language_en_gb'
      value: 'Recommendation {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_risk'
      language: '@language_en_gb'
      value: 'Risk {{id}}'
  - fields:
      placeholder: '@actions_linked_records_record_types_safety_round'
      language: '@language_en_gb'
      value: 'Safety Round {{id}}'
  - fields:
      placeholder: '@actions_audit_entities_action'
      language: '@language_en_gb'
      value: 'Action'
  - fields:
      placeholder: '@actions_audit_entities_action_user'
      language: '@language_en_gb'
      value: 'Action User'
  - fields:
      placeholder: '@actions_email_notification_day'
      language: '@language_en_gb'
      value: '{{days}} Day'
  - fields:
      placeholder: '@actions_email_notification_days'
      language: '@language_en_gb'
      value: '{{days}} Days'
  - fields:
      placeholder: '@actions_email_notification_no_due_date'
      language: '@language_en_gb'
      value: 'No due date set'
  - fields:
      placeholder: '@actions_copy_action_not_found'
      language: '@language_en_gb'
      value: 'Unable to find action'
  - fields:
      placeholder: '@actions_copy_module_not_found'
      language: '@language_en_gb'
      value: 'Unable to find module'
  - fields:
      placeholder: '@actions_action_record_link_label'
      language: '@language_en_gb'
      value: 'This action is linked to {{module}} ID {{recordId}}'
  - fields:
      placeholder: '@action_form_fields_attachments'
      language: '@language_en_gb'
      value: 'Attachments'
  - fields:
      placeholder: '@action_form_fields_attachments_label'
      language: '@language_en_gb'
      value: 'Attachments'
  - fields:
      placeholder: '@actions_action_notes_note_label'
      language: '@language_en_gb'
      value: '{{time}} on {{date}} by {{name}}'
  - fields:
      placeholder: '@actions_field_progress_note_success'
      language: '@language_en_gb'
      value: 'Progress note saved successfully'
  - fields:
      placeholder: '@actions_field_progress_note_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Progress Note'
  - fields:
      placeholder: '@actions_errors_due_date_after_start_date'
      language: '@language_en_gb'
      value: 'Due date must be after start date'
  - fields:
      placeholder: '@actions_list_columns_has_attachments'
      language: '@language_en_gb'
      value: 'Has Attachments'
  - fields:
      placeholder: '@action_form_fields_attachments_success_message'
      language: '@language_en_gb'
      value: 'Attachment saved successfully'
  - fields:
      placeholder: '@actions_errors_not_found'
      language: '@language_en_gb'
      value: 'This action may not exist or you may not have permission to access it'
  - fields:
      placeholder: '@actions_complete_action'
      language: '@language_en_gb'
      value: 'Complete Action'
