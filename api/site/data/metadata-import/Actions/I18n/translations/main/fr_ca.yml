entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@actions_actions_configuration', language: '@language_fr_ca', value: 'Configuration d''actions' } }
  - { fields: { placeholder: '@actions_nav_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@actions_nav_my_assigned_actions', language: '@language_fr_ca', value: 'Mes actions attribuées' } }
  - { fields: { placeholder: '@actions_nav_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@actions_list_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@actions_list_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@actions_list_columns_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@actions_list_columns_priority', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@actions_list_columns_action_type', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@actions_list_columns_start_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@actions_list_columns_due_date', language: '@language_fr_ca', value: 'Date d''échéance' } }
  - { fields: { placeholder: '@actions_list_columns_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@actions_list_columns_completed', language: '@language_fr_ca', value: Terminé } }
  - { fields: { placeholder: '@actions_list_columns_assigned_to', language: '@language_fr_ca', value: 'Attribué à' } }
  - { fields: { placeholder: '@actions_list_columns_assigned_by', language: '@language_fr_ca', value: 'Attribué par' } }
  - { fields: { placeholder: '@actions_list_my_actions_summary', language: '@language_fr_ca', value: 'Cette zone affiche toutes les actions qui vous ont été attribuées' } }
  - { fields: { placeholder: '@actions_list_my_assigned_actions_summary', language: '@language_fr_ca', value: 'Cette zone affiche toutes les actions que vous avez attribuées à un autre utilisateur' } }
  - { fields: { placeholder: '@actions_list_all_actions_summary', language: '@language_fr_ca', value: 'Cette zone affiche toutes les actions qui existent dans tout le système' } }
  - { fields: { placeholder: '@actions_form_title', language: '@language_fr_ca', value: Action } }
  - { fields: { placeholder: '@actions_form_title_new_action', language: '@language_fr_ca', value: 'Nouvelle action' } }
  - { fields: { placeholder: '@actions_form_title_edit_action', language: '@language_fr_ca', value: 'Modifier l''action' } }
  - { fields: { placeholder: '@actions_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@actions_form_fields_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@actions_form_fields_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@actions_form_fields_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@actions_action_plan_manager', language: '@language_fr_ca', value: Gestionnaire } }
  - { fields: { placeholder: '@actions_form_fields_manager_label', language: '@language_fr_ca', value: Gestionnaire } }
  - { fields: { placeholder: '@actions_form_fields_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@actions_form_fields_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@actions_form_fields_priority', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@actions_form_fields_priority_label', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@actions_form_fields_priority_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@actions_form_fields_priority_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@actions_form_fields_priority_high', language: '@language_fr_ca', value: Élevée } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_label', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@actions_form_fields_due_date', language: '@language_fr_ca', value: 'Date d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_due_date_label', language: '@language_fr_ca', value: 'Date d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_due_interval', language: '@language_fr_ca', value: 'Intervale d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_due_interval_label', language: '@language_fr_ca', value: 'Intervale d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_progress', language: '@language_fr_ca', value: Progression } }
  - { fields: { placeholder: '@actions_form_fields_progress_label', language: '@language_fr_ca', value: Progression } }
  - { fields: { placeholder: '@actions_form_fields_due_basis', language: '@language_fr_ca', value: 'Base d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_due_basis_label', language: '@language_fr_ca', value: 'Base d''échéance' } }
  - { fields: { placeholder: '@actions_form_fields_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@actions_form_fields_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@actions_form_fields_status_options_inactive', language: '@language_fr_ca', value: Inactif } }
  - { fields: { placeholder: '@actions_form_fields_status_options_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@actions_form_fields_status_options_completed', language: '@language_fr_ca', value: Terminé } }
  - { fields: { placeholder: '@actions_form_fields_assigned_to', language: '@language_fr_ca', value: 'Attribué à' } }
  - { fields: { placeholder: '@actions_form_add_action_to_record', language: '@language_fr_ca', value: 'Ajouter l''action au dossier' } }
  - { fields: { placeholder: '@action_form_update_action_on_record', language: '@language_fr_ca', value: 'Modifier l''action au dossier' } }
  - { fields: { placeholder: '@actions_form_save', language: '@language_fr_ca', value: 'Enregistrer l''action' } }
  - { fields: { placeholder: '@actions_form_record_details_title', language: '@language_fr_ca', value: 'Détails du dossier' } }
  - { fields: { placeholder: '@actions_form_record_details_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@actions_form_record_details_record_id', language: '@language_fr_ca', value: 'ID d''enregistrement' } }
  - { fields: { placeholder: '@actions_form_record_details_record_title', language: '@language_fr_ca', value: 'Titre du dossier' } }
  - { fields: { placeholder: '@actions_form_record_details_module_id', language: '@language_fr_ca', value: 'ID de module' } }
  - { fields: { placeholder: '@actions_form_saved_successfully', language: '@language_fr_ca', value: 'Action enregistrée avec succès' } }
  - { fields: { placeholder: '@actions_form_completed_successfully', language: '@language_fr_ca', value: 'Action achevée avec succès' } }
  - { fields: { placeholder: '@actions_form_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de l''action' } }
  - { fields: { placeholder: '@actions_admin_nav_new_action_plan', language: '@language_fr_ca', value: 'Nouveau plan d''action' } }
  - { fields: { placeholder: '@actions_admin_nav_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_title', language: '@language_fr_ca', value: 'Modèle d''action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@actions_admin_action_plan_form_fields_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@actions_action_plan_manager_label', language: '@language_fr_ca', value: Gestionnaire } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_add_action', language: '@language_fr_ca', value: 'Ajouter action(s)' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_title', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_summary', language: '@language_fr_ca', value: 'Un minimum d''une action est requis pour un plan d''action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_columns_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@actions_admin_action_plan_add_action_save', language: '@language_fr_ca', value: 'Ajouter action' } }
  - { fields: { placeholder: '@actions_admin_action_template_title', language: '@language_fr_ca', value: 'Modèle d''action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_errors_not_enough_actions', language: '@language_fr_ca', value: 'Un minimum d''une action est requis pour un plan d''action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement du plan d''action' } }
  - { fields: { placeholder: '@actions_record_nav_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@actions_record_nav_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@actions_admin_action_plan_save', language: '@language_fr_ca', value: 'Enregistrer le plan d''action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_action_removed_success', language: '@language_fr_ca', value: 'Action supprimée avec succès' } }
  - { fields: { placeholder: '@actions_admin_action_plan_action_removed_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression de cette action' } }
  - { fields: { placeholder: '@actions_admin_action_plan_actions_reorder_success', language: '@language_fr_ca', value: 'Actions réorganisées avec succès' } }
  - { fields: { placeholder: '@actions_record_add_new_action', language: '@language_fr_ca', value: 'Ajouter une nouvelle action' } }
  - { fields: { placeholder: '@actions_record_add_action_plan', language: '@language_fr_ca', value: 'Ajouter un plan d''action' } }
  - { fields: { placeholder: '@actions_record_edit_action_plan', language: '@language_fr_ca', value: 'Modifier un plan d''action' } }
  - { fields: { placeholder: '@actions_action_mark_as_complete', language: '@language_fr_ca', value: 'Marquer comme terminé' } }
  - { fields: { placeholder: '@actions_action_progress_notes_title', language: '@language_fr_ca', value: 'Remarques sur la progression' } }
  - { fields: { placeholder: '@actions_action_progress_notes_new', language: '@language_fr_ca', value: 'Nouvelle remarque de progression' } }
  - { fields: { placeholder: '@actions_action_progress_notes_notes', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@actions_action_progress_notes_save', language: '@language_fr_ca', value: 'Enregistrer une remarque de progression' } }
  - { fields: { placeholder: '@actions_action_banners_completed', language: '@language_fr_ca', value: 'L''action a été conclue le {{ date }} par {{ name }}' } }
  - { fields: { placeholder: '@actions_nav_back_to_actions', language: '@language_fr_ca', value: 'Retour aux actions' } }
  - { fields: { placeholder: '@actions_nav_back_to_action', language: '@language_fr_ca', value: 'Retour à l''action' } }
  - { fields: { placeholder: '@actions_nav_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@actions_nav_linked_records', language: '@language_fr_ca', value: 'Dossier lié' } }
  - { fields: { placeholder: '@actions_nav_to_linked_record', language: '@language_fr_ca', value: 'Vers le dossier lié' } }
  - { fields: { placeholder: '@actions_nav_to_linked_record_error', language: '@language_fr_ca', value: 'Vous n''avez pas accès au dossier lié' } }
  - { fields: { placeholder: '@actions_errors_load', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération de l''action' } }
  - { fields: { placeholder: '@actions_action_users_title', language: '@language_fr_ca', value: 'Utilisateurs attribués' } }
  - { fields: { placeholder: '@actions_action_users_assigned_by', language: '@language_fr_ca', value: 'Attribué par' } }
  - { fields: { placeholder: '@actions_action_users_assigned_to', language: '@language_fr_ca', value: 'Attribué à' } }
  - { fields: { placeholder: '@actions_action_users_date_assigned', language: '@language_fr_ca', value: 'Date d''attribution' } }
  - { fields: { placeholder: '@actions_datasource_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@actions_datasource_progress', language: '@language_fr_ca', value: Progression } }
  - { fields: { placeholder: '@actions_datasource_due_basis', language: '@language_fr_ca', value: 'Base d''échéance' } }
  - { fields: { placeholder: '@actions_datasource_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@actions_form_type_action_form', language: '@language_fr_ca', value: 'Formulaire d''action' } }
  - { fields: { placeholder: '@actions_form_type_action_plan_form', language: '@language_fr_ca', value: 'Formulaire de plan d''action' } }
  - { fields: { placeholder: '@actions_form_type_action_template_form', language: '@language_fr_ca', value: 'Formulaire de modèle d''action' } }
  - { fields: { placeholder: '@actions_form_type_action_plan_template_form', language: '@language_fr_ca', value: 'Formulaire de modèle de plan d''action' } }
  - { fields: { placeholder: '@actions_type_a', language: '@language_fr_ca', value: 'Type A' } }
  - { fields: { placeholder: '@actions_type_b', language: '@language_fr_ca', value: 'Type B' } }
  - { fields: { placeholder: '@actions_type_c', language: '@language_fr_ca', value: 'Type C' } }
  - { fields: { placeholder: '@actions_progress_0', language: '@language_fr_ca', value: "0\_%" } }
  - { fields: { placeholder: '@actions_progress_10', language: '@language_fr_ca', value: "10\_%" } }
  - { fields: { placeholder: '@actions_progress_20', language: '@language_fr_ca', value: "20\_%" } }
  - { fields: { placeholder: '@actions_progress_30', language: '@language_fr_ca', value: "30\_%" } }
  - { fields: { placeholder: '@actions_progress_40', language: '@language_fr_ca', value: "40\_%" } }
  - { fields: { placeholder: '@actions_progress_50', language: '@language_fr_ca', value: "50\_%" } }
  - { fields: { placeholder: '@actions_progress_60', language: '@language_fr_ca', value: "60\_%" } }
  - { fields: { placeholder: '@actions_progress_70', language: '@language_fr_ca', value: "70\_%" } }
  - { fields: { placeholder: '@actions_progress_80', language: '@language_fr_ca', value: "80\_%" } }
  - { fields: { placeholder: '@actions_progress_90', language: '@language_fr_ca', value: "90\_%" } }
  - { fields: { placeholder: '@actions_progress_100', language: '@language_fr_ca', value: "100\_%" } }
  - { fields: { placeholder: '@actions_due_basis_previous_action', language: '@language_fr_ca', value: 'Date d''achèvement de l''action précédente' } }
  - { fields: { placeholder: '@actions_due_basis_chain_attachment', language: '@language_fr_ca', value: 'Date de pièce jointe de la chaîne' } }
  - { fields: { placeholder: '@actions_datasource_action_priorities', language: '@language_fr_ca', value: 'Priorités de l''action' } }
  - { fields: { placeholder: '@actions_datasource_action_types', language: '@language_fr_ca', value: 'Types d''action' } }
  - { fields: { placeholder: '@action_forms_names_action_plan', language: '@language_fr_ca', value: 'Plan d''action' } }
  - { fields: { placeholder: '@action_filter_form_title_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@action_filter_form_title_label', language: '@language_fr_ca', value: 'Titre de l''action' } }
  - { fields: { placeholder: '@action_filter_form_type_name', language: '@language_fr_ca', value: 'Formulaire de filtre d''action' } }
  - { fields: { placeholder: '@action_filter_form_description_title', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@action_filter_form_description_label', language: '@language_fr_ca', value: 'Description de l''action' } }
  - { fields: { placeholder: '@action_filter_form_due_date_title', language: '@language_fr_ca', value: 'Date d''échéance' } }
  - { fields: { placeholder: '@action_filter_form_due_date_label', language: '@language_fr_ca', value: 'Date d''échéance de l''action' } }
  - { fields: { placeholder: '@action_filter_form_start_date_title', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@action_filter_form_start_date_label', language: '@language_fr_ca', value: 'Date de début de l''action' } }
  - { fields: { placeholder: '@action_filter_form_status_title', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@action_filter_form_status_label', language: '@language_fr_ca', value: 'État de l''action' } }
  - { fields: { placeholder: '@action_filter_form_id_title', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@action_filter_form_id_label', language: '@language_fr_ca', value: 'ID de l''action' } }
  - { fields: { placeholder: '@actions_datasource_modules', language: '@language_fr_ca', value: Modules } }
  - { fields: { placeholder: '@action_filter_form_module_title', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@action_filter_form_module_label', language: '@language_fr_ca', value: 'Module de l''action' } }
  - { fields: { placeholder: '@action_filter_form_priority_title', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@action_filter_form_priority_label', language: '@language_fr_ca', value: 'Priorité de l''action' } }
  - { fields: { placeholder: '@action_filter_form_title', language: '@language_fr_ca', value: 'Filtres d''actions' } }
  - { fields: { placeholder: '@action_datasource_action_type', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@action_datasource_action_type_audit', language: '@language_fr_ca', value: Vérification } }
  - { fields: { placeholder: '@action_datasource_action_type_committee_steering_group', language: '@language_fr_ca', value: 'Comité/groupe de pilotage' } }
  - { fields: { placeholder: '@action_datasource_action_type_documentation', language: '@language_fr_ca', value: Documentation } }
  - { fields: { placeholder: '@action_datasource_action_type_environment_related', language: '@language_fr_ca', value: 'Environnement concerné' } }
  - { fields: { placeholder: '@action_datasource_action_type_investigation', language: '@language_fr_ca', value: Enquête } }
  - { fields: { placeholder: '@action_datasource_action_type_policy_procedure_guideline_related', language: '@language_fr_ca', value: 'Politique/procédure/directive concernée' } }
  - { fields: { placeholder: '@action_datasource_action_type_risk_assessment', language: '@language_fr_ca', value: 'Évaluation du risque' } }
  - { fields: { placeholder: '@action_datasource_action_type_root_cause_analysis', language: '@language_fr_ca', value: 'Analyse de la cause racine' } }
  - { fields: { placeholder: '@action_datasource_action_type_training_related', language: '@language_fr_ca', value: 'Formation associée' } }
  - { fields: { placeholder: '@action_datasource_action_type_other_type_of_action', language: '@language_fr_ca', value: 'Autre type d''action' } }
  - { fields: { placeholder: '@action_datasource_action_type_ce_management_approval', language: '@language_fr_ca', value: 'Approbation CE/direction' } }
  - { fields: { placeholder: '@actions_form_fields_action_type', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@actions_form_fields_action_type_label', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@actions_form_fields_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@actions_form_fields_services_label', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@actions_form_fields_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@actions_form_fields_locations_label', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_interval', language: '@language_fr_ca', value: 'Intervale de date de début' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_interval_label', language: '@language_fr_ca', value: 'Intervale de date de début' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_interval', language: '@language_fr_ca', value: 'Intervale de date d''échéance' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_interval_label', language: '@language_fr_ca', value: 'Intervale de date d''échéance' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_interval', language: '@language_fr_ca', value: 'Intervale de rappel' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_interval_label', language: '@language_fr_ca', value: 'Intervale de rappel' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date', language: '@language_fr_ca', value: 'Intervale de date d''échéance' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_label', language: '@language_fr_ca', value: 'Intervale de date d''échéance' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_date', language: '@language_fr_ca', value: 'Intervale de date de rappel' } }
  - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_date_label', language: '@language_fr_ca', value: 'Intervale de date de rappel' } }
  - { fields: { placeholder: '@action_datasource_start_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@action_start_date_plan_commencement', language: '@language_fr_ca', value: 'Commencement du plan' } }
  - { fields: { placeholder: '@action_start_date_completion_of_step', language: '@language_fr_ca', value: 'Achèvement de l''étape' } }
  - { fields: { placeholder: '@action_datasource_plan_module', language: '@language_fr_ca', value: 'Module du plan d''action' } }
  - { fields: { placeholder: '@action_form_fields_module_title', language: '@language_fr_ca', value: 'Module disponible' } }
  - { fields: { placeholder: '@action_form_fields_module_label', language: '@language_fr_ca', value: 'Module disponible' } }
  - { fields: { placeholder: '@actions_plan_work_day', language: '@language_fr_ca', value: 'Jour ouvré' } }
  - { fields: { placeholder: '@actions_plan_calendar_day', language: '@language_fr_ca', value: 'Jour civil' } }
  - { fields: { placeholder: '@actions_plan_previous_step', language: '@language_fr_ca', value: 'Étape précédente' } }
  - { fields: { placeholder: '@actions_linked_records_linked_record', language: '@language_fr_ca', value: 'Dossiers liés' } }
  - { fields: { placeholder: '@actions_forms_locations_filter_title', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@actions_forms_locations_filter_label', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@actions_forms_services_filter_title', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@actions_forms_services_filter_label', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@actions_linked_records_record_types', language: '@language_fr_ca', value: '{{module}} {{id}}' } }
  - { fields: { placeholder: '@actions_forms_user_assigner_filter_title', language: '@language_fr_ca', value: 'Attribué par' } }
  - { fields: { placeholder: '@actions_forms_user_assigner_filter_label', language: '@language_fr_ca', value: 'Attribué par' } }
  - { fields: { placeholder: '@actions_forms_user_assignee_filter_title', language: '@language_fr_ca', value: 'Attribué à' } }
  - { fields: { placeholder: '@actions_forms_user_assignee_filter_label', language: '@language_fr_ca', value: 'Attribué à' } }
  - { fields: { placeholder: '@actions_action_back_to_record', language: '@language_fr_ca', value: 'Retour au dossier' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_claim', language: '@language_fr_ca', value: 'Plainte {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_control', language: '@language_fr_ca', value: 'Contrôle {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_feedback', language: '@language_fr_ca', value: 'Rétroaction {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_incident', language: '@language_fr_ca', value: 'Incident {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_investigation', language: '@language_fr_ca', value: 'Enquête {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_learning', language: '@language_fr_ca', value: 'Apprentissage {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_mortality', language: '@language_fr_ca', value: 'Mortalité {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_programme', language: '@language_fr_ca', value: 'Programme d''évaluation de conformité {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_recommendation', language: '@language_fr_ca', value: 'Recommandation {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_risk', language: '@language_fr_ca', value: 'Risque {{id}}' } }
  - { fields: { placeholder: '@actions_linked_records_record_types_safety_round', language: '@language_fr_ca', value: 'Contexte de sécurité {{id}}' } }
  - { fields: { placeholder: '@actions_audit_entities_action', language: '@language_fr_ca', value: Action } }
  - { fields: { placeholder: '@actions_audit_entities_action_user', language: '@language_fr_ca', value: 'Utilisateur de l''action' } }
  - { fields: { placeholder: '@actions_email_notification_day', language: '@language_fr_ca', value: '{{days}} jour(s)' } }
  - { fields: { placeholder: '@actions_email_notification_days', language: '@language_fr_ca', value: '{{days}} jours' } }
  - { fields: { placeholder: '@actions_email_notification_no_due_date', language: '@language_fr_ca', value: 'Aucune date d''échéance définie' } }
  - { fields: { placeholder: '@actions_copy_action_not_found', language: '@language_fr_ca', value: 'Impossible de trouver l''action' } }
  - { fields: { placeholder: '@actions_copy_module_not_found', language: '@language_fr_ca', value: 'Impossible de trouver le module' } }
  - { fields: { placeholder: '@actions_action_record_link_label', language: '@language_fr_ca', value: 'Cette action est liée à l''ID de {{module}}, {{recordId}}' } }
  - { fields: { placeholder: '@actions_action_notes_note_label', language: '@language_fr_ca', value: '{{time}} du {{date}} par {{name}}' } }
  - { fields: { placeholder: '@actions_field_progress_note_success', language: '@language_fr_ca', value: 'Remarque de progression enregistrée avec succès' } }
  - { fields: { placeholder: '@actions_field_progress_note_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la remarque de progression' } }
  - { fields: { placeholder: '@action_form_fields_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@action_form_fields_attachments_label', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@action_form_fields_attachments_success_message', language: '@language_fr_ca', value: 'Pièce jointe enregistrée avec succès' } }
  - { fields: { placeholder: '@actions_list_columns_has_attachments', language: '@language_fr_ca', value: 'Possède des pièces jointes' } }
  - { fields: { placeholder: '@actions_errors_due_date_after_start_date', language: '@language_fr_ca', value: 'La date d''échéance doit être ultérieure à la date de début' } }
  - { fields: { placeholder: '@actions_errors_not_found', language: '@language_fr_ca', value: 'Cette action n''existe peut-être pas ou vous n''avez peut-être pas l''autorisation d''y accéder' } }
  - { fields: { placeholder: '@actions_complete_action', language: '@language_fr_ca', value: 'Terminer l’action' } }
