entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@action_form_update_action_on_record', language: '@language_ar', value: 'تحديث الإجراء على السجّل' } }
    - { fields: { placeholder: '@actions_action_banners_completed', language: '@language_ar', value: 'تم إكمال هذا الإجراء في {{date}} بواسطة {{name}}' } }
    - { fields: { placeholder: '@actions_action_mark_as_complete', language: '@language_ar', value: 'وضع علامة اكتمال' } }
    - { fields: { placeholder: '@actions_action_progress_notes_new', language: '@language_ar', value: 'ملاحظة التقدم الجديده' } }
    - { fields: { placeholder: '@actions_action_progress_notes_notes', language: '@language_ar', value: ملاحظات } }
    - { fields: { placeholder: '@actions_action_progress_notes_save', language: '@language_ar', value: 'حفظ ملاحظة التقدم' } }
    - { fields: { placeholder: '@actions_action_progress_notes_title', language: '@language_ar', value: 'ملاحظات التقدم' } }
    - { fields: { placeholder: '@actions_actions_configuration', language: '@language_ar', value: 'تكوين الإجراءات' } }
    - { fields: { placeholder: '@actions_action_record_link_label', language: '@language_ar', value: 'هذا الإجراء مرتبط مع رقم التعريف {{module}} ID {{recordId}}' } }
    - { fields: { placeholder: '@actions_admin_action_plan_action_removed_error', language: '@language_ar', value: 'حدث خطأ أثناء إزالة هذا الإجراء' } }
    - { fields: { placeholder: '@actions_admin_action_plan_action_removed_success', language: '@language_ar', value: 'تمت إزالة الإجراء بنجاح' } }
    - { fields: { placeholder: '@actions_admin_action_plan_actions_reorder_success', language: '@language_ar', value: 'تمت إعادة ترتيب الإجراءات بنجاح' } }
    - { fields: { placeholder: '@actions_admin_action_plan_add_action_save', language: '@language_ar', value: 'أضف إجراء' } }
    - { fields: { placeholder: '@actions_admin_action_plan_form_fields_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@actions_admin_action_plan_form_fields_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@actions_admin_action_plan_save', language: '@language_ar', value: 'حفظ خطة الإجراء' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_columns_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_columns_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_summary', language: '@language_ar', value: 'ما لا يقل عن إجراء واحد مطلوب لخطة العمل' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_actions_list_title', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_add_action', language: '@language_ar', value: 'إضافة إجراء (إجراءات)' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_errors_not_enough_actions', language: '@language_ar', value: 'ما لا يقل عن إجراء واحد مطلوب لخطة العمل' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_title', language: '@language_ar', value: 'قالب إلاجراء' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ خطة الإجراء' } }
    - { fields: { placeholder: '@actions_admin_action_template_title', language: '@language_ar', value: 'قالب إلاجراء' } }
    - { fields: { placeholder: '@actions_admin_add_action_update', language: '@language_ar', value: 'تحديث العمل على الخطة' } }
    - { fields: { placeholder: '@actions_admin_nav_new_action_plan', language: '@language_ar', value: 'إجراء جديد' } }
    - { fields: { placeholder: '@actions_admin_nav_permissions', language: '@language_ar', value: 'ضوابط ' } }
    - { fields: { placeholder: '@actions_errors_load', language: '@language_ar', value: 'حدث خطأ أثناء استرداد الإجراء' } }
    - { fields: { placeholder: '@actions_form_add_action_to_record', language: '@language_ar', value: 'إضافة  الإجراء على السجّل' } }
    - { fields: { placeholder: '@actions_form_fields_assigned_to', language: '@language_ar', value: 'مخصص الى' } }
    - { fields: { placeholder: '@actions_form_fields_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@actions_form_fields_due_date', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@actions_form_fields_priority_high', language: '@language_ar', value: مرتفع } }
    - { fields: { placeholder: '@actions_form_fields_priority_label', language: '@language_ar', value: 'الأولوية' } }
    - { fields: { placeholder: '@actions_form_fields_priority_low', language: '@language_ar', value: منخفض } }
    - { fields: { placeholder: '@actions_form_fields_priority_medium', language: '@language_ar', value: متوسط } }
    - { fields: { placeholder: '@actions_form_fields_start_date', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@actions_form_fields_status_label', language: '@language_ar', value: حالة الإجراء } }
    - { fields: { placeholder: '@actions_form_fields_status_options_active', language: '@language_ar', value: نشيط } }
    - { fields: { placeholder: '@actions_form_fields_status_options_completed', language: '@language_ar', value: منجز } }
    - { fields: { placeholder: '@actions_form_fields_status_options_inactive', language: '@language_ar', value: 'غير نشط' } }
    - { fields: { placeholder: '@actions_form_fields_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@actions_form_record_details_module', language: '@language_ar', value: وحدة } }
    - { fields: { placeholder: '@actions_form_record_details_record_id', language: '@language_ar', value: 'معرف السجل' } }
    - { fields: { placeholder: '@actions_form_record_details_record_title', language: '@language_ar', value: 'عنوان السجل' } }
    - { fields: { placeholder: '@actions_form_record_details_title', language: '@language_ar', value: 'تفاصيل السجل' } }
    - { fields: { placeholder: '@actions_form_save', language: '@language_ar', value: 'حفظ العمل' } }
    - { fields: { placeholder: '@actions_form_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ الإجراء' } }
    - { fields: { placeholder: '@actions_form_saved_successfully', language: '@language_ar', value: 'تم حفظ الإجراء بنجاح' } }
    - { fields: { placeholder: '@actions_form_completed_successfully', language: '@language_ar', value: 'اكتمل الإجراء بنجاح' } }
    - { fields: { placeholder: '@actions_form_title', language: '@language_ar', value: إجراء } }
    - { fields: { placeholder: '@actions_form_title_edit_action', language: '@language_ar', value: 'تحرير الإجراء' } }
    - { fields: { placeholder: '@actions_form_title_new_action', language: '@language_ar', value: 'إجراء جديد' } }
    - { fields: { placeholder: '@actions_list_all_actions_summary', language: '@language_ar', value: 'تعرض هذه المنطقة جميع الإجراءات الموجودة عبر النظام بأكمله' } }
    - { fields: { placeholder: '@actions_list_columns_assigned_by', language: '@language_ar', value: 'عين من' } }
    - { fields: { placeholder: '@actions_list_columns_assigned_to', language: '@language_ar', value: 'مخصص الى' } }
    - { fields: { placeholder: '@actions_list_columns_completed', language: '@language_ar', value: منجز } }
    - { fields: { placeholder: '@actions_list_columns_due_date', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@actions_list_columns_id', language: '@language_ar', value: 'هوية شخصية' } }
    - { fields: { placeholder: '@actions_list_columns_module', language: '@language_ar', value: وحدة } }
    - { fields: { placeholder: '@actions_list_columns_priority', language: '@language_ar', value: الأولوية } }
    - { fields: { placeholder: '@actions_list_columns_start_date', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@actions_list_columns_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@actions_list_columns_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@actions_list_my_actions_summary', language: '@language_ar', value: 'تعرض هذه المنطقة كل الإجراءات التي تم تخصيصها لك' } }
    - { fields: { placeholder: '@actions_list_my_assigned_actions_summary', language: '@language_ar', value: 'تعرض هذه المنطقة كل الإجراءات التي قمت بتعيينها لمستخدم آخر' } }
    - { fields: { placeholder: '@actions_nav_all_actions', language: '@language_ar', value: 'جميع الإجراءات' } }
    - { fields: { placeholder: '@actions_nav_back_to_action', language: '@language_ar', value: 'العودة إلى الإجراء' } }
    - { fields: { placeholder: '@actions_nav_back_to_actions', language: '@language_ar', value: 'العودة إلى الإجراءات' } }
    - { fields: { placeholder: '@actions_nav_my_actions', language: '@language_ar', value: أفعالي } }
    - { fields: { placeholder: '@actions_nav_my_assigned_actions', language: '@language_ar', value: 'أعمالي المخصصة' } }
    - { fields: { placeholder: '@actions_nav_to_linked_record', language: '@language_ar', value: 'إلى السجل المرتبط معه' } }
    - { fields: { placeholder: '@actions_record_add_action_plan', language: '@language_ar', value: 'أضف خطة إلاجراء' } }
    - { fields: { placeholder: '@actions_record_add_action_plan_to_record', language: '@language_ar', value: 'أضف خطة إلاجراء للتسجيل' } }
    - { fields: { placeholder: '@actions_record_add_new_action', language: '@language_ar', value: 'إضافة إجراء جديد' } }
    - { fields: { placeholder: '@actions_record_edit_action_plan', language: '@language_ar', value: 'تحرير خطة الإجراء' } }
    - { fields: { placeholder: '@actions_record_nav_all_actions', language: '@language_ar', value: 'جميع الإجراءات' } }
    - { fields: { placeholder: '@actions_record_nav_my_actions', language: '@language_ar', value: أفعالي } }
    - { fields: { placeholder: '@actions_datasource_action_priorities', language: '@language_ar', value: 'أولويات الإجراء' } }
    - { fields: { placeholder: '@actions_datasource_action_types', language: '@language_ar', value: 'أنواع الإجراء' } }
    - { fields: { placeholder: '@actions_datasource_due_basis', language: '@language_ar', value: 'أساس الاستحقاق' } }
    - { fields: { placeholder: '@actions_datasource_progress', language: '@language_ar', value: تقدم } }
    - { fields: { placeholder: '@actions_datasource_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@actions_datasource_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@actions_form_type_action_form', language: '@language_ar', value: 'نموذج الإجراء' } }
    - { fields: { placeholder: '@actions_form_type_action_plan_form', language: '@language_ar', value: 'نموذج خطة الإجراء' } }
    - { fields: { placeholder: '@actions_form_type_action_plan_template_form', language: '@language_ar', value: 'نموذج قالب خطة الإجراء' } }
    - { fields: { placeholder: '@actions_form_type_action_template_form', language: '@language_ar', value: 'نموذج قالب الإجراء' } }
    - { fields: { placeholder: '@action_forms_names_action_plan', language: '@language_ar', value: 'خطة الإجراء' } }
    - { fields: { placeholder: '@actions_progress_0', language: '@language_ar', value: 0% } }
    - { fields: { placeholder: '@actions_progress_10', language: '@language_ar', value: 10% } }
    - { fields: { placeholder: '@actions_progress_100', language: '@language_ar', value: 100% } }
    - { fields: { placeholder: '@actions_progress_20', language: '@language_ar', value: 20% } }
    - { fields: { placeholder: '@actions_progress_30', language: '@language_ar', value: 30% } }
    - { fields: { placeholder: '@actions_progress_40', language: '@language_ar', value: 40% } }
    - { fields: { placeholder: '@actions_progress_50', language: '@language_ar', value: 50% } }
    - { fields: { placeholder: '@actions_progress_60', language: '@language_ar', value: 60% } }
    - { fields: { placeholder: '@actions_progress_70', language: '@language_ar', value: 70% } }
    - { fields: { placeholder: '@actions_progress_80', language: '@language_ar', value: 80% } }
    - { fields: { placeholder: '@actions_progress_90', language: '@language_ar', value: 90% } }
    - { fields: { placeholder: '@actions_due_basis_chain_attachment', language: '@language_ar', value: 'تاريخ ملحق السلسلة' } }
    - { fields: { placeholder: '@actions_due_basis_previous_action', language: '@language_ar', value: 'تاريخ الانتهاء من الإجراء السابق' } }
    - { fields: { placeholder: '@actions_type_a', language: '@language_ar', value: 'نوع أ' } }
    - { fields: { placeholder: '@actions_type_b', language: '@language_ar', value: 'النوع ب' } }
    - { fields: { placeholder: '@actions_type_c', language: '@language_ar', value: 'النوع  C' } }
    - { fields: { placeholder: '@actions_form_fields_description_label', language: '@language_ar', value: الوصف } }
    - { fields: { placeholder: '@actions_form_fields_due_basis', language: '@language_ar', value: 'أساس الاستحقاق' } }
    - { fields: { placeholder: '@actions_form_fields_due_basis_label', language: '@language_ar', value: 'أساس الاستحقاق' } }
    - { fields: { placeholder: '@actions_form_fields_due_date_label', language: '@language_ar', value: تاريخ استحقاق الإجراء } }
    - { fields: { placeholder: '@actions_form_fields_due_interval', language: '@language_ar', value: 'المدة المستحقة' } }
    - { fields: { placeholder: '@actions_form_fields_due_interval_label', language: '@language_ar', value: 'المدة المستحقة' } }
    - { fields: { placeholder: '@actions_form_fields_priority', language: '@language_ar', value: 'الأولوية' } }
    - { fields: { placeholder: '@actions_form_fields_progress', language: '@language_ar', value: تقدم } }
    - { fields: { placeholder: '@actions_form_fields_progress_label', language: '@language_ar', value: تقدم } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_label', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@actions_form_fields_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@actions_form_fields_title_label', language: '@language_ar', value: عنوان الإجراء } }
    - { fields: { placeholder: '@actions_form_fields_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@actions_form_fields_type_label', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@action_search_table_is_default', language: '@language_ar', value: 'هل هو الافتراضي؟' } }
    - { fields: { placeholder: '@action_search_table_name', language: '@language_ar', value: الإسم } }
    - { fields: { placeholder: '@action_search_table_search_label', language: '@language_ar', value: 'عمل نموذج البحث' } }
    - { fields: { placeholder: '@action_datasource_action_type', language: '@language_ar', value: 'نوع الإجراء' } }
    - { fields: { placeholder: '@action_datasource_action_type_audit', language: '@language_ar', value: التدقيق } }
    - { fields: { placeholder: '@action_datasource_action_type_ce_management_approval', language: '@language_ar', value: 'موافقة الإدارة التنفيذية / الإدارة' } }
    - { fields: { placeholder: '@action_datasource_action_type_committee_steering_group', language: '@language_ar', value: 'اللجنة / المجموعة التوجيهية' } }
    - { fields: { placeholder: '@action_datasource_action_type_documentation', language: '@language_ar', value: التوثيق } }
    - { fields: { placeholder: '@action_datasource_action_type_environment_related', language: '@language_ar', value: 'البيئة ذات الصلة' } }
    - { fields: { placeholder: '@action_datasource_action_type_investigation', language: '@language_ar', value: التحقيق } }
    - { fields: { placeholder: '@action_datasource_action_type_other_type_of_action', language: '@language_ar', value: 'نوع آخر من الإجراء' } }
    - { fields: { placeholder: '@action_datasource_action_type_policy_procedure_guideline_related', language: '@language_ar', value: 'السياسة / الإجراءات / المبادئ التوجيهية ذات الصلة' } }
    - { fields: { placeholder: '@action_datasource_action_type_risk_assessment', language: '@language_ar', value: 'تقييم المخاطر' } }
    - { fields: { placeholder: '@action_datasource_action_type_root_cause_analysis', language: '@language_ar', value: 'تحليل السبب الجذري' } }
    - { fields: { placeholder: '@action_datasource_action_type_training_related', language: '@language_ar', value: 'التدريب ذات الصلة' } }
    - { fields: { placeholder: '@actions_datasource_modules', language: '@language_ar', value: الوحدات } }
    - { fields: { placeholder: '@action_datasource_plan_module', language: '@language_ar', value: 'وحدة خطة الإجراء' } }
    - { fields: { placeholder: '@action_datasource_start_date', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@action_filter_form_description_label', language: '@language_ar', value: 'وصف الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_description_title', language: '@language_ar', value: الوصف } }
    - { fields: { placeholder: '@action_filter_form_title_label', language: '@language_ar', value: 'عنوان الإجراء ' } }
    - { fields: { placeholder: '@action_filter_form_title_title', language: '@language_ar', value: العنوان } }
    - { fields: { placeholder: '@action_filter_form_type_name', language: '@language_ar', value: 'نموذج تصفية الإجراء' } }
    - { fields: { placeholder: '@action_start_date_completion_of_step', language: '@language_ar', value: 'الإنتهاء من الخطوة' } }
    - { fields: { placeholder: '@action_start_date_plan_commencement', language: '@language_ar', value: 'خطة البدء' } }
    - { fields: { placeholder: '@actions_action_plan_manager', language: '@language_ar', value: مدير } }
    - { fields: { placeholder: '@actions_action_plan_manager_label', language: '@language_ar', value: مدير } }
    - { fields: { placeholder: '@actions_action_users_assigned_by', language: '@language_ar', value: 'تم التكليف بواسطة' } }
    - { fields: { placeholder: '@actions_action_users_assigned_to', language: '@language_ar', value: 'تم التكليف إلى' } }
    - { fields: { placeholder: '@actions_action_users_date_assigned', language: '@language_ar', value: 'تاريخ التكليف' } }
    - { fields: { placeholder: '@actions_action_users_title', language: '@language_ar', value: 'المستخدمين المكلفين' } }
    - { fields: { placeholder: '@action_filter_form_due_date_title', language: '@language_ar', value: 'تاريخ الإستحقاق' } }
    - { fields: { placeholder: '@action_filter_form_due_date_label', language: '@language_ar', value: 'تاريخ إستحقاق الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_id_title', language: '@language_ar', value: 'رقم التعريف' } }
    - { fields: { placeholder: '@action_filter_form_id_label', language: '@language_ar', value: 'رقم تعريف الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_module_title', language: '@language_ar', value: الوحدة } }
    - { fields: { placeholder: '@action_filter_form_module_label', language: '@language_ar', value: 'وحدة الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_priority_title', language: '@language_ar', value: الأفضلية } }
    - { fields: { placeholder: '@action_filter_form_priority_label', language: '@language_ar', value: 'أولوية الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_start_date_title', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@action_filter_form_start_date_label', language: '@language_ar', value: 'تاريخ بدء الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_status_title', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@action_filter_form_status_label', language: '@language_ar', value: 'حالة الإجراء' } }
    - { fields: { placeholder: '@action_filter_form_title', language: '@language_ar', value: 'مرشحات الإجراء' } }
    - { fields: { placeholder: '@actions_form_fields_action_type', language: '@language_ar', value: 'نوع الإجراء' } }
    - { fields: { placeholder: '@actions_form_fields_action_type_label', language: '@language_ar', value: نوع الإجراء } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_interval', language: '@language_ar', value: 'تاريخ الإستحقاق الفاصل' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_interval_label', language: '@language_ar', value: 'تاريخ الإستحقاق الفاصل' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date', language: '@language_ar', value: 'تاريخ الإستحقاق الفاصل' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_due_date_label', language: '@language_ar', value: 'تاريخ الإستحقاق الفاصل' } }
    - { fields: { placeholder: '@actions_form_fields_locations', language: '@language_ar', value: المواقع } }
    - { fields: { placeholder: '@actions_form_fields_locations_label', language: '@language_ar', value: المواقع } }
    - { fields: { placeholder: '@actions_form_fields_manager_label', language: '@language_ar', value: مدير } }
    - { fields: { placeholder: '@action_form_fields_module_title', language: '@language_ar', value: 'الوحدة المتاحة' } }
    - { fields: { placeholder: '@action_form_fields_module_label', language: '@language_ar', value: 'الوحدة المتاحة' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_interval', language: '@language_ar', value: 'فترة التذكير' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_interval_label', language: '@language_ar', value: 'فترة التذكير' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_date', language: '@language_ar', value: 'فترة التذكير الزمني' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_reminder_date_label', language: '@language_ar', value: 'فترة التذكير الزمني' } }
    - { fields: { placeholder: '@actions_form_fields_services', language: '@language_ar', value: الخدمات } }
    - { fields: { placeholder: '@actions_form_fields_services_label', language: '@language_ar', value: الخدمات } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_interval', language: '@language_ar', value: 'تاريخ البدء التذكير' } }
    - { fields: { placeholder: '@actions_admin_action_plan_template_form_start_date_interval_label', language: '@language_ar', value: 'تاريخ البدء التذكير' } }
    - { fields: { placeholder: '@actions_form_record_details_module_id', language: '@language_ar', value: 'رقم تعريف الوحدة' } }
    - { fields: { placeholder: '@actions_forms_locations_filter_label', language: '@language_ar', value: المواقع } }
    - { fields: { placeholder: '@actions_forms_locations_filter_title', language: '@language_ar', value: المواقع } }
    - { fields: { placeholder: '@actions_forms_services_filter_label', language: '@language_ar', value: الخدمات } }
    - { fields: { placeholder: '@actions_forms_services_filter_title', language: '@language_ar', value: الخدمات } }
    - { fields: { placeholder: '@actions_forms_user_assignee_filter_label', language: '@language_ar', value: 'تم التكليف إلى' } }
    - { fields: { placeholder: '@actions_action_back_to_record', language: '@language_ar', value: 'العودة للتسجيل' } }
    - { fields: { placeholder: '@actions_forms_user_assignee_filter_title', language: '@language_ar', value: 'تم التكليف إلى' } }
    - { fields: { placeholder: '@actions_forms_user_assigner_filter_label', language: '@language_ar', value: 'تم التكليف بواسطة' } }
    - { fields: { placeholder: '@actions_forms_user_assigner_filter_title', language: '@language_ar', value: 'تم التكليف بواسطة' } }
    - { fields: { placeholder: '@actions_linked_records_linked_record', language: '@language_ar', value: 'السجلات المرتبطة' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_claim', language: '@language_ar', value: 'المطالبة {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_clinical_audit', language: '@language_ar', value: 'التدقيق السريري {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_control', language: '@language_ar', value: 'التحكم {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_feedback', language: '@language_ar', value: 'تجربة المريض {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_incident', language: '@language_ar', value: 'الحدث {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_investigation', language: '@language_ar', value: 'التحقيق {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_learning', language: '@language_ar', value: 'التعلم {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_mortality', language: '@language_ar', value: 'الوفيات {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_programme', language: '@language_ar', value: 'برنامج تقييم الإمتثال {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_recommendation', language: '@language_ar', value: 'التوصية {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_risk', language: '@language_ar', value: 'خطر {{id}}' } }
    - { fields: { placeholder: '@actions_linked_records_record_types_safety_round', language: '@language_ar', value: 'جولة السلامة {{id}}' } }
    - { fields: { placeholder: '@actions_list_columns_action_type', language: '@language_ar', value: 'نوع الإجراء' } }
    - { fields: { placeholder: '@actions_nav_details', language: '@language_ar', value: تفاصيل } }
    - { fields: { placeholder: '@actions_nav_linked_records', language: '@language_ar', value: 'سجل مرتبط' } }
    - { fields: { placeholder: '@actions_plan_calendar_day', language: '@language_ar', value: 'التقويم اليومي' } }
    - { fields: { placeholder: '@actions_plan_previous_step', language: '@language_ar', value: 'خطوة سابقة' } }
    - { fields: { placeholder: '@actions_plan_work_day', language: '@language_ar', value: 'يوم عمل' } }
    - { fields: { placeholder: '@actions_field_progress_note_success', language: '@language_ar', value: 'تم حفظ ملاحظة تقدم العمل بنجاح' } }
    - { fields: { placeholder: '@actions_field_progress_note_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ ملاحظة تقدم العمل' } }
    - { fields: { placeholder: '@actions_errors_not_found', language: '@language_ar', value: 'قد لا يكون هذا الإجراء موجودًا أو قد لا يكون لديك صلاحيات للوصول إليه' } }
    - { fields: { placeholder: '@actions_complete_action', language: '@language_ar', value: 'اكتمل الإجراء' } }
