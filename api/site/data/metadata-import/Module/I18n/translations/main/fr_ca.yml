entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@module_title_acl', language: '@language_fr_ca', value: LCA } }
  - { fields: { placeholder: '@module_title_acl_role', language: '@language_fr_ca', value: 'LCA - Rôle' } }
  - { fields: { placeholder: '@module_title_acl_rule', language: '@language_fr_ca', value: 'LCA - Règle' } }
  - { fields: { placeholder: '@module_title_action', language: '@language_fr_ca', value: Action } }
  - { fields: { placeholder: '@module_title_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@module_title_capture_admin', language: '@language_fr_ca', value: 'Capturer admin' } }
  - { fields: { placeholder: '@module_title_claims', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@module_title_clinical_audit', language: '@language_fr_ca', value: 'Audit clinique' } }
  - { fields: { placeholder: '@module_title_clinical_audit_instance', language: '@language_fr_ca', value: 'Audit clinique' } }
  - { fields: { placeholder: '@module_title_compliance_assessment', language: '@language_fr_ca', value: 'Contrôle de conformité' } }
  - { fields: { placeholder: '@module_title_compliance_assessment_assessment', language: '@language_fr_ca', value: 'Évaluation de conformité - Évaluation' } }
  - { fields: { placeholder: '@module_title_compliance_assessment_programme', language: '@language_fr_ca', value: 'Évaluation de conformité - Programme' } }
  - { fields: { placeholder: '@module_title_compliance_assessment_response', language: '@language_fr_ca', value: 'Évaluation de conformité - Réponse' } }
  - { fields: { placeholder: '@module_title_compliance_assessment_standard', language: '@language_fr_ca', value: 'Évaluation de conformité - Normale' } }
  - { fields: { placeholder: '@module_title_contact', language: '@language_fr_ca', value: Contact } }
  - { fields: { placeholder: '@module_title_contacts', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@module_title_control', language: '@language_fr_ca', value: Contrôle } }
  - { fields: { placeholder: '@module_title_dashboard', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@module_title_distribution_list', language: '@language_fr_ca', value: 'Liste de distribution' } }
  - { fields: { placeholder: '@module_title_distribution_lists', language: '@language_fr_ca', value: 'Listes de distribution' } }
  - { fields: { placeholder: '@module_title_enterprise_risk_manager', language: '@language_fr_ca', value: 'Gestionnaire du risque de l''entreprise' } }
  - { fields: { placeholder: '@module_title_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@module_title_equipment_instance', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@module_title_erm_register', language: '@language_fr_ca', value: 'GED - Inscription' } }
  - { fields: { placeholder: '@module_title_erm_review', language: '@language_fr_ca', value: 'GED - Révision' } }
  - { fields: { placeholder: '@module_title_erm_risk', language: '@language_fr_ca', value: 'GED - Risque' } }
  - { fields: { placeholder: '@module_title_erm_tracker', language: '@language_fr_ca', value: 'GED - Suiveur' } }
  - { fields: { placeholder: '@module_title_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@module_title_form', language: '@language_fr_ca', value: Formulaire } }
  - { fields: { placeholder: '@module_title_form_field', language: '@language_fr_ca', value: 'Champ de formulaire' } }
  - { fields: { placeholder: '@module_title_forms', language: '@language_fr_ca', value: Formulaires } }
  - { fields: { placeholder: '@module_title_incidents', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@module_title_icon_wall', language: '@language_fr_ca', value: Mur d'icônes } }
  - { fields: { placeholder: '@module_title_investigation', language: '@language_fr_ca', value: Enquête } }
  - { fields: { placeholder: '@module_title_investigations', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@module_title_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@module_title_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@module_title_medication', language: '@language_fr_ca', value: Médicament } }
  - { fields: { placeholder: '@module_title_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@module_title_mortality_review', language: '@language_fr_ca', value: 'Examen de mortalité' } }
  - { fields: { placeholder: '@module_title_policies_and_guidelines', language: '@language_fr_ca', value: 'Polices et directives' } }
  - { fields: { placeholder: '@module_title_prince', language: '@language_fr_ca', value: Prince } }
  - { fields: { placeholder: '@module_title_recommendation', language: '@language_fr_ca', value: Recommandation } }
  - { fields: { placeholder: '@module_title_recommendations_and_controls', language: '@language_fr_ca', value: 'Recommandations et contrôles' } }
  - { fields: { placeholder: '@module_title_reportable_incident_briefs', language: '@language_fr_ca', value: 'Dossiers d''incident à signaler' } }
  - { fields: { placeholder: '@module_title_reportable_incident_briefs_instance', language: '@language_fr_ca', value: 'Dossiers d''incident à signaler' } }
  - { fields: { placeholder: '@module_title_safety_alert', language: '@language_fr_ca', value: 'Alerte à la sécurité' } }
  - { fields: { placeholder: '@module_title_safety_alerts', language: '@language_fr_ca', value: 'Alertes de sécurité' } }
  - { fields: { placeholder: '@module_title_safety_alerts_response', language: '@language_fr_ca', value: 'Réponse d''alertes à la sécurité' } }
  - { fields: { placeholder: '@module_title_safety_learning', language: '@language_fr_ca', value: 'Apprentissage de la sécurité' } }
  - { fields: { placeholder: '@module_title_safety_learnings', language: '@language_fr_ca', value: 'Apprentissages de la sécurité' } }
  - { fields: { placeholder: '@module_title_safety_round', language: '@language_fr_ca', value: 'Ronde de sécurité' } }
  - { fields: { placeholder: '@module_title_safety_rounds', language: '@language_fr_ca', value: 'Rondes de sécurité' } }
  - { fields: { placeholder: '@module_title_safety_rounds_response', language: '@language_fr_ca', value: 'Ronde de sécurité - Réponse' } }
  - { fields: { placeholder: '@module_title_safety_rounds_summary', language: '@language_fr_ca', value: 'Rondes de sécurité - Sommaire' } }
  - { fields: { placeholder: '@module_title_safety_rounds_template', language: '@language_fr_ca', value: 'Rondes de sécurité - Modèle' } }
  - { fields: { placeholder: '@module_title_service', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@module_title_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@module_title_splash_page', language: '@language_fr_ca', value: 'Page de garde' } }
  - { fields: { placeholder: '@module_title_survey', language: '@language_fr_ca', value: Sondage } }
  - { fields: { placeholder: '@module_title_survey_response', language: '@language_fr_ca', value: 'Réponse au sondage' } }
  - { fields: { placeholder: '@module_title_surveys', language: '@language_fr_ca', value: Sondages } }
  - { fields: { placeholder: '@module_title_system_admin', language: '@language_fr_ca', value: 'Admin système' } }
  - { fields: { placeholder: '@module_title_user', language: '@language_fr_ca', value: Utilisateur } }
  - { fields: { placeholder: '@module_title_user_group', language: '@language_fr_ca', value: 'Groupe d''utilisateurs' } }
  - { fields: { placeholder: '@module_title_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@module_not_licenced', language: '@language_fr_ca', value: 'La module {{module}} n''a pas de licence et ne peut pas être défini par défaut' } }
  - { fields: { placeholder: '@module_permissions_saved', language: '@language_fr_ca', value: 'Permissions du {{module}} enregistrées avec succès' } }
  - { fields: { placeholder: '@module_permissions_error_saving', language: '@language_fr_ca', value: 'Erreur lors de l''enregistrement du {{module}} permissions' } }
  - { fields: { placeholder: '@module_title_investigation_event', language: '@language_fr_ca', value: 'Événement d''enquête' } }
  - { fields: { placeholder: '@module_my_profile', language: '@language_fr_ca', value: 'Mon profil' } }
  - { fields: { placeholder: '@module_title_todolist', language: '@language_fr_ca', value: 'Liste de choses à faire' } }
  - { fields: { placeholder: '@module_title_redress', language: '@language_fr_ca', value: Redressement } }
  - { fields: { placeholder: '@module_nav_hotspots', language: '@language_fr_ca', value: 'Points chauds' } }
  - { fields: { placeholder: '@module_title_safeguarding', language: '@language_fr_ca', value: Protection } }
  - { fields: { placeholder: '@module_title_dataextractionparent', language: '@language_fr_ca', value: 'Extraction de données' } }
  - { fields: { placeholder: '@module_title_dataextraction', language: '@language_fr_ca', value: 'Service d''extraction de données' } }
  - { fields: { placeholder: '@module_title_organisations', language: '@language_fr_ca', value: 'Organisations' } }
  - { fields: { placeholder: '@module_title_payments', language: '@language_fr_ca', value: 'Paiements' } }
  - { fields: { placeholder: '@module_title_insurance_policy', language: '@language_fr_ca', value: 'Police d''assurance' } }
  - { fields: { placeholder: '@module_title_causal_factors', language: '@language_fr_ca', value: 'Facteurs causals' } }
