entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@module_not_licenced', language: '@language_en_us', value: "The {{module}} module is not licensed, and cannot be set as a default" } }
    - { fields: { placeholder: '@module_permissions_error_saving', language: '@language_en_us', value: "Error saving {{module}} permissions" } }
    - { fields: { placeholder: '@module_permissions_saved', language: '@language_en_us', value: "{{module}} permissions saved successfully" } }
    - { fields: { placeholder: '@module_title_acl', language: '@language_en_us', value: ACL } }
    - { fields: { placeholder: '@module_title_acl_role', language: '@language_en_us', value: ACL - Role } }
    - { fields: { placeholder: '@module_title_acl_rule', language: '@language_en_us', value: ACL - Rule } }
    - { fields: { placeholder: '@module_title_action', language: '@language_en_us', value: Action } }
    - { fields: { placeholder: '@module_title_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@module_title_capture_admin', language: '@language_en_us', value: Capture Admin } }
    - { fields: { placeholder: '@module_title_claims', language: '@language_en_us', value: Claims } }
    - { fields: { placeholder: '@module_title_clinical_audit', language: '@language_en_us', value: Clinical Audit } }
    - { fields: { placeholder: '@module_title_clinical_audit_instance', language: '@language_en_us', value: Clinical Audit } }
    - { fields: { placeholder: '@module_title_compliance_assessment', language: '@language_en_us', value: Compliance Assessment } }
    - { fields: { placeholder: '@module_title_compliance_assessment_assessment', language: '@language_en_us', value: Compliance Assessment - Assessment } }
    - { fields: { placeholder: '@module_title_compliance_assessment_programme', language: '@language_en_us', value: Compliance Assessment - Programme } }
    - { fields: { placeholder: '@module_title_compliance_assessment_response', language: '@language_en_us', value: Compliance Assessment - Response } }
    - { fields: { placeholder: '@module_title_compliance_assessment_standard', language: '@language_en_us', value: Compliance Assessment - Standard } }
    - { fields: { placeholder: '@module_title_contact', language: '@language_en_us', value: Contact } }
    - { fields: { placeholder: '@module_title_contacts', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@module_title_control', language: '@language_en_us', value: Control } }
    - { fields: { placeholder: '@module_title_dashboard', language: '@language_en_us', value: Dashboard } }
    - { fields: { placeholder: '@module_title_distribution_list', language: '@language_en_us', value: Distribution List } }
    - { fields: { placeholder: '@module_title_distribution_lists', language: '@language_en_us', value: Distribution Lists } }
    - { fields: { placeholder: '@module_title_enterprise_risk_manager', language: '@language_en_us', value: Enterprise Risk Manager } }
    - { fields: { placeholder: '@module_title_equipment', language: '@language_en_us', value: Equipment } }
    - { fields: { placeholder: '@module_title_equipment_instance', language: '@language_en_us', value: Equipment } }
    - { fields: { placeholder: '@module_title_erm_register', language: '@language_en_us', value: ERM - Register } }
    - { fields: { placeholder: '@module_title_erm_review', language: '@language_en_us', value: ERM - Review } }
    - { fields: { placeholder: '@module_title_erm_risk', language: '@language_en_us', value: ERM - Risk } }
    - { fields: { placeholder: '@module_title_erm_tracker', language: '@language_en_us', value: ERM - Tracker } }
    - { fields: { placeholder: '@module_title_feedback', language: '@language_en_us', value: Feedback } }
    - { fields: { placeholder: '@module_title_form', language: '@language_en_us', value: Form } }
    - { fields: { placeholder: '@module_title_form_field', language: '@language_en_us', value: Form Field } }
    - { fields: { placeholder: '@module_title_forms', language: '@language_en_us', value: Forms } }
    - { fields: { placeholder: '@module_title_incidents', language: '@language_en_us', value: Incidents } }
    - { fields: { placeholder: '@module_title_investigation', language: '@language_en_us', value: Investigation } }
    - { fields: { placeholder: '@module_title_investigation_event', language: '@language_en_us', value: Investigation Event } }
    - { fields: { placeholder: '@module_title_investigations', language: '@language_en_us', value: Investigations } }
    - { fields: { placeholder: '@module_title_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@module_title_locations', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@module_title_medication', language: '@language_en_us', value: Medication } }
    - { fields: { placeholder: '@module_title_medications', language: '@language_en_us', value: Medications } }
    - { fields: { placeholder: '@module_title_mortality_review', language: '@language_en_us', value: Mortality Review } }
    - { fields: { placeholder: '@module_my_profile', language: '@language_en_us', value: My Profile } }
    - { fields: { placeholder: '@module_title_policies_and_guidelines', language: '@language_en_us', value: Policies & Guidelines } }
    - { fields: { placeholder: '@module_title_prince', language: '@language_en_us', value: Prince } }
    - { fields: { placeholder: '@module_title_recommendation', language: '@language_en_us', value: Recommendation } }
    - { fields: { placeholder: '@module_title_recommendations_and_controls', language: '@language_en_us', value: Recommendations & Controls } }
    - { fields: { placeholder: '@module_title_reportable_incident_briefs', language: '@language_en_us', value: Reportable Incident Briefs } }
    - { fields: { placeholder: '@module_title_reportable_incident_briefs_instance', language: '@language_en_us', value: Reportable Incident Briefs } }
    - { fields: { placeholder: '@module_title_safety_alert', language: '@language_en_us', value: Safety Alert } }
    - { fields: { placeholder: '@module_title_safety_alerts', language: '@language_en_us', value: Safety Alerts } }
    - { fields: { placeholder: '@module_title_safety_learning', language: '@language_en_us', value: Safety Learning } }
    - { fields: { placeholder: '@module_title_safety_learnings', language: '@language_en_us', value: Safety Learnings } }
    - { fields: { placeholder: '@module_title_safety_round', language: '@language_en_us', value: Safety Round } }
    - { fields: { placeholder: '@module_title_safety_rounds', language: '@language_en_us', value: Safety Rounds } }
    - { fields: { placeholder: '@module_title_safety_rounds_response', language: '@language_en_us', value: Safety Rounds - Response } }
    - { fields: { placeholder: '@module_title_safety_rounds_summary', language: '@language_en_us', value: Safety Rounds - Summary } }
    - { fields: { placeholder: '@module_title_safety_rounds_template', language: '@language_en_us', value: Safety Rounds - Template } }
    - { fields: { placeholder: '@module_title_service', language: '@language_en_us', value: Service } }
    - { fields: { placeholder: '@module_title_services', language: '@language_en_us', value: Services } }
    - { fields: { placeholder: '@module_title_splash_page', language: '@language_en_us', value: Splash Page } }
    - { fields: { placeholder: '@module_title_survey', language: '@language_en_us', value: Survey } }
    - { fields: { placeholder: '@module_title_survey_response', language: '@language_en_us', value: Survey Response } }
    - { fields: { placeholder: '@module_title_surveys', language: '@language_en_us', value: Surveys } }
    - { fields: { placeholder: '@module_title_system_admin', language: '@language_en_us', value: System Admin } }
    - { fields: { placeholder: '@module_title_todolist', language: '@language_en_us', value: To Do List } }
    - { fields: { placeholder: '@module_title_user', language: '@language_en_us', value: User } }
    - { fields: { placeholder: '@module_title_user_group', language: '@language_en_us', value: User Group } }
    - { fields: { placeholder: '@module_title_users', language: '@language_en_us', value: Users } }
    - { fields: { placeholder: '@module_nav_hotspots', language: '@language_en_us', value: Hotspots } }
    - { fields: { placeholder: '@module_title_icon_wall', language: '@language_en_us', value: Icon Wall } }
    - { fields: { placeholder: '@module_title_dataextractionparent', language: '@language_en_us', value: Data Extraction } }
    - { fields: { placeholder: '@module_title_dataextraction', language: '@language_en_us', value: Data Extraction Service } }
    - { fields: { placeholder: '@module_title_safety_alerts_response', language: '@language_en_us', value: 'Safety Alerts Response' } }
    - { fields: { placeholder: '@module_title_redress', language: '@language_en_us', value: Redress } }
    - { fields: { placeholder: '@module_title_organisations', language: '@language_en_us', value: Organizations } }
