entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: MODULE.TITLE.ACL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_acl
  -
    fields:
      placeholder: MODULE.TITLE.ACL_ROLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_acl_role
  -
    fields:
      placeholder: MODULE.TITLE.ACL_RULE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_acl_rule
  -
    fields:
      placeholder: MODULE.TITLE.ACTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_action
  -
    fields:
      placeholder: MODULE.TITLE.ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_actions
  -
    fields:
      placeholder: MODULE.TITLE.CAPTURE_ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_capture_admin
  -
    fields:
      placeholder: MODULE.TITLE.CLAIMS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_claims
  -
    fields:
      placeholder: MODULE.TITLE.CLINICAL_AUDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_clinical_audit
  -
    fields:
      placeholder: MODULE.TITLE.CLINICAL_AUDIT_INSTANCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_clinical_audit_instance
  -
    fields:
      placeholder: MODULE.TITLE.COMPLIANCE_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_compliance_assessment
  -
    fields:
      placeholder: MODULE.TITLE.COMPLIANCE_ASSESSMENT_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_compliance_assessment_assessment
  -
    fields:
      placeholder: MODULE.TITLE.COMPLIANCE_ASSESSMENT_PROGRAMME
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_compliance_assessment_programme
  -
    fields:
      placeholder: MODULE.TITLE.COMPLIANCE_ASSESSMENT_RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_compliance_assessment_response
  -
    fields:
      placeholder: MODULE.TITLE.COMPLIANCE_ASSESSMENT_STANDARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_compliance_assessment_standard
  -
    fields:
      placeholder: MODULE.TITLE.CONTACT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_contact
  -
    fields:
      placeholder: MODULE.TITLE.CONTACTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_contacts
  -
    fields:
      placeholder: MODULE.TITLE.CONTROL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_control
  -
    fields:
      placeholder: MODULE.TITLE.DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_dashboard
  -
    fields:
      placeholder: MODULE.TITLE.DISTRIBUTION_LIST
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_distribution_list
  -
    fields:
      placeholder: MODULE.TITLE.DISTRIBUTION_LISTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_distribution_lists
  -
    fields:
      placeholder: MODULE.TITLE.ENTERPRISE_RISK_MANAGER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_enterprise_risk_manager
  -
    fields:
      placeholder: MODULE.TITLE.EQUIPMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_equipment
  -
    fields:
      placeholder: MODULE.TITLE.EQUIPMENT_INSTANCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_equipment_instance
  -
    fields:
      placeholder: MODULE.TITLE.ERM_REGISTER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_erm_register
  -
    fields:
      placeholder: MODULE.TITLE.ERM_REVIEW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_erm_review
  -
    fields:
      placeholder: MODULE.TITLE.ERM_RISK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_erm_risk
  -
    fields:
      placeholder: MODULE.TITLE.ERM_TRACKER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_erm_tracker
  -
    fields:
      placeholder: MODULE.TITLE.FEEDBACK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_feedback
  -
    fields:
      placeholder: MODULE.TITLE.FORM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_form
  -
    fields:
      placeholder: MODULE.TITLE.FORM_FIELD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_form_field
  -
    fields:
      placeholder: MODULE.TITLE.FORMS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_forms
  -
    fields:
      placeholder: MODULE.TITLE.INCIDENTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_incidents
  -
    fields:
      placeholder: MODULE.TITLE.INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_investigation
  -
    fields:
      placeholder: MODULE.TITLE.INVESTIGATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_investigations
  -
    fields:
      placeholder: MODULE.TITLE.LOCATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_location
  -
    fields:
      placeholder: MODULE.TITLE.LOCATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_locations
  -
    fields:
      placeholder: MODULE.TITLE.MEDICATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_medication
  -
    fields:
      placeholder: MODULE.TITLE.MEDICATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_medications
  -
    fields:
      placeholder: MODULE.TITLE.MORTALITY_REVIEW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_mortality_review
  -
    fields:
      placeholder: MODULE.TITLE.POLICIES_AND_GUIDELINES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_policies_and_guidelines
  -
    fields:
      placeholder: MODULE.TITLE.PRINCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_prince
  -
    fields:
      placeholder: MODULE.TITLE.RECOMMENDATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_recommendation
  -
    fields:
      placeholder: MODULE.TITLE.RECOMMENDATIONS_AND_CONTROLS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_recommendations_and_controls
  -
    fields:
      placeholder: MODULE.TITLE.REPORTABLE_INCIDENT_BRIEFS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_reportable_incident_briefs
  -
    fields:
      placeholder: MODULE.TITLE.REPORTABLE_INCIDENT_BRIEFS_INSTANCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_reportable_incident_briefs_instance
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ALERT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_alert
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ALERTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_alerts
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ALERTS_RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_alerts_response
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_LEARNING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_learning
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_LEARNINGS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_learnings
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ROUND
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_round
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ROUNDS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_rounds
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ROUNDS_RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_rounds_response
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ROUNDS_SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_rounds_summary
  -
    fields:
      placeholder: MODULE.TITLE.SAFETY_ROUNDS_TEMPLATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safety_rounds_template
  -
    fields:
      placeholder: MODULE.TITLE.SERVICE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_service
  -
    fields:
      placeholder: MODULE.TITLE.SERVICES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_services
  -
    fields:
      placeholder: MODULE.TITLE.SPLASH_PAGE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_splash_page
  -
    fields:
      placeholder: MODULE.TITLE.SURVEY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_survey
  -
    fields:
      placeholder: MODULE.TITLE.SURVEY_RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_survey_response
  -
    fields:
      placeholder: MODULE.TITLE.SURVEYS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_surveys
  -
    fields:
      placeholder: MODULE.TITLE.SYSTEM_ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_system_admin
  -
    fields:
      placeholder: MODULE.TITLE.USER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_user
  -
    fields:
      placeholder: MODULE.TITLE.USER_GROUP
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_user_group
  -
    fields:
      placeholder: MODULE.TITLE.USERS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_users
  -
    fields:
      placeholder: MODULE.NOT_LICENCED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_not_licenced
  -
    fields:
      placeholder: MODULE.PERMISSIONS.SAVED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_permissions_saved
  -
    fields:
      placeholder: MODULE.PERMISSIONS.ERROR_SAVING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_permissions_error_saving
  -
    fields:
      placeholder: MODULE.TITLE.INVESTIGATION_EVENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_investigation_event
  -
    fields:
      placeholder: MODULE.TITLE.MY_PROFILE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_my_profile
  -
    fields:
      placeholder: MODULE.TITLE.BENCHMARK
      pointer: BENCHMARK.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: module_title_benchmark
  -
    fields:
      placeholder: MODULE.TITLE.BENCHMARK_PROFILE
      pointer: BENCHMARK.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: module_title_benchmark_profile
  -
    fields:
      placeholder: MODULE.TITLE.NOTIFICATION_CENTRE
      pointer: NOTIFICATION_CENTRE.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: module_title_notification_centre
  -
    fields:
      placeholder: MODULE.TITLE.NOTIFICATION_CENTRE_NOTIFICATION
      pointer: NOTIFICATION_CENTRE.MODULE_TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: module_title_notification_centre_notification
  -
    fields:
      placeholder: MODULE.TITLE.TODOLIST
      type: 0
      domains:
        - domain: '@domain_common'
    ref: module_title_todolist
  -
    fields:
      placeholder: MODULE.TITLE.REDRESS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_redress
  -
    fields:
      placeholder: NAV.HOTSPOTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_nav_hotspots
  -
    fields:
      placeholder: MODULE.TITLE.ICON_WALL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_icon_wall
  -
    fields:
      placeholder: MODULE.TITLE.SAFEGUARDING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_safeguarding
  -
    fields:
      placeholder: MODULE.TITLE.DATAEXTRACTIONPARENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_dataextractionparent
  -
    fields:
      placeholder: MODULE.TITLE.DATAEXTRACTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: module_title_dataextraction
  -
    fields:
      placeholder: MODULE.TITLE.ORGANISATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
        -
          domain: '@domain_users'
    ref: module_title_organisations
  -
    fields:
      placeholder: MODULE.TITLE.PAYMENTS
      type: 0
      domains:
        -
          domain: '@domain_common'
        -
          domain: '@domain_users'
    ref: module_title_payments
  -
    fields:
      placeholder: MODULE.TITLE.INSURANCE_POLICY
      type: 0
      domains:
        -
          domain: '@domain_common'
        -
          domain: '@domain_users'
    ref: module_title_insurance_policy
  -
    fields:
      placeholder: MODULE.TITLE.CAUSAL_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_common'
        -
          domain: '@domain_users'
    ref: module_title_causal_factors

  - fields:
      placeholder: MAINTENANCE_MODE.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: maintenance_mode_label

  -
    fields:
      placeholder: MODULE.TITLE.TAGS
      type: 0
      domains:
        -
          domain: '@domain_common'
        -
          domain: '@domain_users'
    ref: module_title_tags
