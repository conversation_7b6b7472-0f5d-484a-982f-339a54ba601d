entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: ERM.NAV.EXPORTS_DASHBOARD
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_exports_dashboard
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.SCHEMA.EXPORTED_AT
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_schema_exported_at
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.SCHEMA.EXPORTED_BY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_schema_exported_by
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.SCHEMA.FIELD_SETS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_schema_field_sets
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.SCHEMA.STATUS
      pointer: COMMON.STATUS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_schema_status
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.LOADING
      pointer: COMMON.LOADING
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_loading
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.LOADING_FAILED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_loading_failed
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.DOWNLOAD_FAILED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_download_failed
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.CANCEL_FAILED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_cancel_failed
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.DELETE_FAILED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_delete_failed
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.ONLY_ONE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_only_one
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.STATE.EXPORT_CREATED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_state_export_created
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.ACTION.DOWNLOAD
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_action_download
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.ACTION.CANCEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_action_cancel
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.ACTION.DELETE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_action_delete
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.ACTION.NEW
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_action_new
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.ACTION.EXPORT
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_action_export
  -
    fields:
      placeholder: ERM.RISK.EXPORTS.FIELD_SET.CORE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_exports_field_set_core
