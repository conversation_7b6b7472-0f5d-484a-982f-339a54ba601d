entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@language_selector_label', language: '@language_en_us', value: 'Choose your display language' } }
  - { fields: { placeholder: '@language_selector_option', language: '@language_en_us', value: Loading } }
  - { fields: { placeholder: '@components_magma_table', language: '@language_en_us', value: 'Magma Table' } }
  - { fields: { placeholder: '@components_magma_table_actions', language: '@language_en_us', value: Actions } }
  - { fields: { placeholder: '@components_magma_table_pagination_first', language: '@language_en_us', value: First } }
  - { fields: { placeholder: '@components_magma_table_pagination_previous', language: '@language_en_us', value: Previous } }
  - { fields: { placeholder: '@components_magma_table_pagination_next', language: '@language_en_us', value: Next } }
  - { fields: { placeholder: '@components_magma_table_pagination_last', language: '@language_en_us', value: Last } }
  - { fields: { placeholder: '@common_optional', language: '@language_en_us', value: Optional } }
  - { fields: { placeholder: '@common_copied', language: '@language_en_us', value: Copied } }
  - { fields: { placeholder: '@components_record_search', language: '@language_en_us', value: 'Record Search' } }
  - { fields: { placeholder: '@components_record_search_back_to_search', language: '@language_en_us', value: 'Back to Search' } }
  - { fields: { placeholder: '@components_record_search_search_results', language: '@language_en_us', value: 'Search Results' } }
  - { fields: { placeholder: '@common_components_attachments_plural', language: '@language_en_us', value: Attachments } }
  - { fields: { placeholder: '@common_components_attachments_loading', language: '@language_en_us', value: 'Loading Attachments' } }
  - { fields: { placeholder: '@common_components_attachments_new', language: '@language_en_us', value: 'New Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_uploaded', language: '@language_en_us', value: Uploaded } }
  - { fields: { placeholder: '@common_components_attachments_download', language: '@language_en_us', value: 'Download Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_edit', language: '@language_en_us', value: Edit } }
  - { fields: { placeholder: '@common_components_attachments_version', language: '@language_en_us', value: 'Version {{number}}' } }
  - { fields: { placeholder: '@common_components_attachments_edit_attachment', language: '@language_en_us', value: 'Edit Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_add', language: '@language_en_us', value: 'Add Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_singular', language: '@language_en_us', value: Attachment } }
  - { fields: { placeholder: '@common_components_attachments_percent_complete', language: '@language_en_us', value: '{{percent}}% Complete' } }
  - { fields: { placeholder: '@common_components_attachments_replace', language: '@language_en_us', value: 'Replace Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_title', language: '@language_en_us', value: Title } }
  - { fields: { placeholder: '@common_components_attachments_description', language: '@language_en_us', value: Description } }
  - { fields: { placeholder: '@common_components_attachments_default_allowed_file_types', language: '@language_en_us', value: 'Attachment files may be images, Word documents, spreadsheets or PDFs' } }
  - { fields: { placeholder: '@common_components_attachments_classification', language: '@language_en_us', value: Classification } }
  - { fields: { placeholder: '@common_components_attachments_select_classification', language: '@language_en_us', value: 'Select Classification' } }
  - { fields: { placeholder: '@common_components_attachments_cancel', language: '@language_en_us', value: Cancel } }
  - { fields: { placeholder: '@common_components_attachments_save', language: '@language_en_us', value: 'Save Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_retry', language: '@language_en_us', value: Retry } }
  - { fields: { placeholder: '@common_components_attachments_error', language: '@language_en_us', value: 'There was an error uploading your file.' } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_image', language: '@language_en_us', value: 'Image / Picture' } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_document', language: '@language_en_us', value: Document } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_spreadsheet', language: '@language_en_us', value: Spreadsheet } }
  - { fields: { placeholder: '@common_components_attachments_audit_title', language: '@language_en_us', value: 'Attachment audit' } }
  - { fields: { placeholder: '@common_components_attachments_audit_version', language: '@language_en_us', value: Version } }
  - { fields: { placeholder: '@common_components_attachments_audit_name', language: '@language_en_us', value: Name } }
  - { fields: { placeholder: '@common_components_attachments_audit_datetime', language: '@language_en_us', value: Date/Time } }
  - { fields: { placeholder: '@common_components_attachments_audit_download', language: '@language_en_us', value: Download } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete', language: '@language_en_us', value: Delete } }
  - { fields: { placeholder: '@common_components_attachments_audit_attachment_deleted', language: '@language_en_us', value: 'Attachment has been deleted' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_title', language: '@language_en_us', value: 'Unable To Delete' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_last_version_required', language: '@language_en_us', value: 'Only the latest attachment version may be deleted' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_permissions_insufficient', language: '@language_en_us', value: 'This attachment version cannot be deleted with your user permissions' } }
  - { fields: { placeholder: '@common_components_attachments_audit_type', language: '@language_en_us', value: Type } }
  - { fields: { placeholder: '@common_components_attachments_audit_type_public', language: '@language_en_us', value: Public } }
  - { fields: { placeholder: '@common_components_attachments_audit_type_private', language: '@language_en_us', value: Private } }
  - { fields: { placeholder: '@common_components_attachments_no_attachments_provided', language: '@language_en_us', value: 'No attachments provided' } }
  - { fields: { placeholder: '@common_components_attachments_email_attachment_label', language: '@language_en_us', value: 'Email Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_email_attachment_title', language: '@language_en_us', value: 'Email Link' } }
  - { fields: { placeholder: '@common_components_attachments_resend_attachment', language: '@language_en_us', value: 'Resend Attachment' } }
  - { fields: { placeholder: '@common_components_attachments_reason_for_resending', language: '@language_en_us', value: 'Reason for Resending' } }
  - { fields: { placeholder: '@common_components_attachments_report_last_sent', language: '@language_en_us', value: 'Report Last Re-Sent: {{date}}' } }
  - { fields: { placeholder: '@common_components_attachments_report_first_sent', language: '@language_en_us', value: 'Report First Sent: {{date}}' } }
  - { fields: { placeholder: '@common_components_attachments_reason_for_resend', language: '@language_en_us', value: 'Report Last Reason for Re-send: {{reason}}' } }
  - { fields: { placeholder: '@common_components_attachments_email_sent', language: '@language_en_us', value: 'The attachment is being sent. You will receive an email confirmation.' } }
  - { fields: { placeholder: '@components_contributory_factors_singular', language: '@language_en_us', value: 'Contributory Factor' } }
  - { fields: { placeholder: '@components_contributory_factors_plural', language: '@language_en_us', value: 'Contributory Factors' } }
  - { fields: { placeholder: '@components_contributory_factors_add_contributory_factor', language: '@language_en_us', value: 'Add Contributory Factor' } }
  - { fields: { placeholder: '@components_contributory_factors_no_contributory_factors', language: '@language_en_us', value: 'This Record doesn''t have any Contributory Factors' } }
  - { fields: { placeholder: '@components_contributory_factors_new_contributory_factor', language: '@language_en_us', value: 'New Contributory Factor' } }
  - { fields: { placeholder: '@components_contributory_factors_category', language: '@language_en_us', value: Category } }
  - { fields: { placeholder: '@components_contributory_factors_sub_category', language: '@language_en_us', value: Sub-Category } }
  - { fields: { placeholder: '@components_contributory_factors_select_type', language: '@language_en_us', value: 'Select Type' } }
  - { fields: { placeholder: '@components_contributory_factors_select_category', language: '@language_en_us', value: 'Select Category' } }
  - { fields: { placeholder: '@components_contributory_factors_select_sub_category', language: '@language_en_us', value: 'Select Sub-Category' } }
  - { fields: { placeholder: '@common_create', language: '@language_en_us', value: Create } }
  - { fields: { placeholder: '@common_new', language: '@language_en_us', value: New } }
  - { fields: { placeholder: '@common_edit', language: '@language_en_us', value: Edit } }
  - { fields: { placeholder: '@common_loading', language: '@language_en_us', value: Loading } }
  - { fields: { placeholder: '@common_id', language: '@language_en_us', value: ID } }
  - { fields: { placeholder: '@common_ref', language: '@language_en_us', value: Ref } }
  - { fields: { placeholder: '@common_title', language: '@language_en_us', value: Title } }
  - { fields: { placeholder: '@common_label', language: '@language_en_us', value: Label } }
  - { fields: { placeholder: '@common_description', language: '@language_en_us', value: Description } }
  - { fields: { placeholder: '@common_modules', language: '@language_en_us', value: Modules } }
  - { fields: { placeholder: '@common_module', language: '@language_en_us', value: Module } }
  - { fields: { placeholder: '@common_status', language: '@language_en_us', value: Status } }
  - { fields: { placeholder: '@common_due', language: '@language_en_us', value: Due } }
  - { fields: { placeholder: '@common_select', language: '@language_en_us', value: Select } }
  - { fields: { placeholder: '@common_type', language: '@language_en_us', value: Type } }
  - { fields: { placeholder: '@common_subtype', language: '@language_en_us', value: Subtype } }
  - { fields: { placeholder: '@common_category', language: '@language_en_us', value: Category } }
  - { fields: { placeholder: '@common_subcategory', language: '@language_en_us', value: Subcategory } }
  - { fields: { placeholder: '@common_filter', language: '@language_en_us', value: Filter } }
  - { fields: { placeholder: '@common_filter_indicator', language: '@language_en_us', value: 'Filter{{ filterIndicator }}' } }
  - { fields: { placeholder: '@common_not_set', language: '@language_en_us', value: 'Not Set' } }
  - { fields: { placeholder: '@placeholder_common_no_value_selected', language: '@language_en_us', value: 'No value selected' } }
  - { fields: { placeholder: '@common_view_details', language: '@language_en_us', value: 'View Details' } }
  - { fields: { placeholder: '@common_save_changes', language: '@language_en_us', value: 'Save Changes' } }
  - { fields: { placeholder: '@common_save', language: '@language_en_us', value: Save } }
  - { fields: { placeholder: '@common_saving', language: '@language_en_us', value: Saving } }
  - { fields: { placeholder: '@common_save_and_close', language: '@language_en_us', value: 'Save and Close' } }
  - { fields: { placeholder: '@common_save_error', language: '@language_en_us', value: 'Save Error' } }
  - { fields: { placeholder: '@common_cancel', language: '@language_en_us', value: Cancel } }
  - { fields: { placeholder: '@common_delete', language: '@language_en_us', value: Delete } }
  - { fields: { placeholder: '@common_close', language: '@language_en_us', value: Close } }
  - { fields: { placeholder: '@common_search', language: '@language_en_us', value: Search } }
  - { fields: { placeholder: '@common_date', language: '@language_en_us', value: Date } }
  - { fields: { placeholder: '@common_clear', language: '@language_en_us', value: Clear } }
  - { fields: { placeholder: '@common_reply', language: '@language_en_us', value: Reply } }
  - { fields: { placeholder: '@common_yes', language: '@language_en_us', value: 'Yes' } }
  - { fields: { placeholder: '@common_no', language: '@language_en_us', value: 'No' } }
  - { fields: { placeholder: '@common_confirm', language: '@language_en_us', value: Confirm } }
  - { fields: { placeholder: '@common_error', language: '@language_en_us', value: Error } }
  - { fields: { placeholder: '@common_generic_error', language: '@language_en_us', value: 'An error occurred' } }
  - { fields: { placeholder: '@common_role', language: '@language_en_us', value: Role } }
  - { fields: { placeholder: '@common_admin', language: '@language_en_us', value: Admin } }
  - { fields: { placeholder: '@common_administration', language: '@language_en_us', value: Administration } }
  - { fields: { placeholder: '@common_administration_tools', language: '@language_en_us', value: Tools } }
  - { fields: { placeholder: '@common_administration_permissions', language: '@language_en_us', value: Permissions } }
  - { fields: { placeholder: '@common_access_control', language: '@language_en_us', value: 'Access Control' } }
  - { fields: { placeholder: '@common_access_control_manual_roles', language: '@language_en_us', value: 'Manual Roles' } }
  - { fields: { placeholder: '@common_access_control_generated_roles', language: '@language_en_us', value: 'Generated Roles' } }
  - { fields: { placeholder: '@common_access_control_no_assignable_roles', language: '@language_en_us', value: 'No Assignable Roles' } }
  - { fields: { placeholder: '@common_form_validation_required', language: '@language_en_us', value: 'This field is required' } }
  - { fields: { placeholder: '@common_form_validation_string_length_min', language: '@language_en_us', value: 'This field must be at least {{min}} characters in length' } }
  - { fields: { placeholder: '@common_form_validation_string_length_max', language: '@language_en_us', value: 'This field may not exceed {{max}} characters in length' } }
  - { fields: { placeholder: '@common_form_validation_email', language: '@language_en_us', value: 'Please enter a valid Email Address' } }
  - { fields: { placeholder: '@common_form_validation_errors', language: '@language_en_us', value: 'The submitted form contains validation errors' } }
  - { fields: { placeholder: '@nav_back_to_dashboard', language: '@language_en_us', value: 'Back to Dashboard' } }
  - { fields: { placeholder: '@nav_dashboard', language: '@language_en_us', value: Dashboard } }
  - { fields: { placeholder: '@nav_capture', language: '@language_en_us', value: Capture } }
  - { fields: { placeholder: '@nav_incidents', language: '@language_en_us', value: Incidents } }
  - { fields: { placeholder: '@nav_feedback', language: '@language_en_us', value: Feedback } }
  - { fields: { placeholder: '@nav_claims', language: '@language_en_us', value: Claims } }
  - { fields: { placeholder: '@nav_mortality', language: '@language_en_us', value: 'Mortality Review' } }
  - { fields: { placeholder: '@nav_evaluate', language: '@language_en_us', value: Evaluate } }
  - { fields: { placeholder: '@nav_risk_register', language: '@language_en_us', value: 'Enterprise Risk Manager' } }
  - { fields: { placeholder: '@nav_investigation', language: '@language_en_us', value: Investigation } }
  - { fields: { placeholder: '@nav_investigations', language: '@language_en_us', value: Investigations } }
  - { fields: { placeholder: '@nav_risk', language: '@language_en_us', value: Risk } }
  - { fields: { placeholder: '@nav_risks', language: '@language_en_us', value: Risks } }
  - { fields: { placeholder: '@nav_reportable_incidents', language: '@language_en_us', value: 'Reportable Incident Briefs' } }
  - { fields: { placeholder: '@nav_strategy', language: '@language_en_us', value: Strategy } }
  - { fields: { placeholder: '@nav_clinical_audit', language: '@language_en_us', value: 'Clinical Audit' } }
  - { fields: { placeholder: '@nav_clinical_audits', language: '@language_en_us', value: 'Clinical Audits' } }
  - { fields: { placeholder: '@nav_controls_and_recommendations', language: '@language_en_us', value: 'Recommendations & Controls' } }
  - { fields: { placeholder: '@nav_safety_learnings', language: '@language_en_us', value: 'Safety Learnings' } }
  - { fields: { placeholder: '@nav_roi_assessment', language: '@language_en_us', value: 'ROI Investment' } }
  - { fields: { placeholder: '@nav_implement', language: '@language_en_us', value: Implement } }
  - { fields: { placeholder: '@nav_policies_and_guidelines', language: '@language_en_us', value: 'Policies & Guidelines' } }
  - { fields: { placeholder: '@nav_assess', language: '@language_en_us', value: Assess } }
  - { fields: { placeholder: '@nav_compliance_assessment', language: '@language_en_us', value: 'Compliance Assessment' } }
  - { fields: { placeholder: '@nav_safety_rounds', language: '@language_en_us', value: 'Safety Rounds' } }
  - { fields: { placeholder: '@nav_my_profile', language: '@language_en_us', value: 'My Profile' } }
  - { fields: { placeholder: '@nav_my_todo', language: '@language_en_us', value: 'To Do List' } }
  - { fields: { placeholder: '@nav_system_admin', language: '@language_en_us', value: 'System Admin' } }
  - { fields: { placeholder: '@nav_logout', language: '@language_en_us', value: Logout } }
  - { fields: { placeholder: '@nav_login', language: '@language_en_us', value: Login } }
  - { fields: { placeholder: '@nav_administration', language: '@language_en_us', value: Administration } }
  - { fields: { placeholder: '@nav_acl_roles', language: '@language_en_us', value: 'ACL Roles' } }
  - { fields: { placeholder: '@nav_acl_groups', language: '@language_en_us', value: 'ACL Groups' } }
  - { fields: { placeholder: '@nav_acl_rules', language: '@language_en_us', value: 'ACL Rules' } }
  - { fields: { placeholder: '@nav_actions', language: '@language_en_us', value: Actions } }
  - { fields: { placeholder: '@nav_checklists', language: '@language_en_us', value: Surveys } }
  - { fields: { placeholder: '@nav_contacts', language: '@language_en_us', value: Contacts } }
  - { fields: { placeholder: '@nav_controls', language: '@language_en_us', value: Controls } }
  - { fields: { placeholder: '@nav_equipment', language: '@language_en_us', value: Equipment } }
  - { fields: { placeholder: '@nav_forms', language: '@language_en_us', value: 'Form Designer' } }
  - { fields: { placeholder: '@nav_form-fields', language: '@language_en_us', value: 'Custom Fields' } }
  - { fields: { placeholder: '@nav_help', language: '@language_en_us', value: Help } }
  - { fields: { placeholder: '@nav_resources_covid', language: '@language_en_us', value: 'COVID-19 Resources' } }
  - { fields: { placeholder: '@nav_locations', language: '@language_en_us', value: Locations } }
  - { fields: { placeholder: '@nav_medications', language: '@language_en_us', value: Medications } }
  - { fields: { placeholder: '@nav_services', language: '@language_en_us', value: Services } }
  - { fields: { placeholder: '@nav_users', language: '@language_en_us', value: Users } }
  - { fields: { placeholder: '@nav_organisations', language: '@language_en_us', value: Organizations } }
  - { fields: { placeholder: '@nav_insurance', language: '@language_en_us', value: Insurance } }
  - { fields: { placeholder: '@nav_payments', language: '@language_en_us', value: Payments } }
  - { fields: { placeholder: '@nav_actions_dashboard', language: '@language_en_us', value: 'Actions Dashboard' } }
  - { fields: { placeholder: '@nav_distributions_lists', language: '@language_en_us', value: 'Distribution Lists' } }
  - { fields: { placeholder: '@nav_reporting', language: '@language_en_us', value: Reporting } }
  - { fields: { placeholder: '@nav_capture_admin', language: '@language_en_us', value: 'Capture Admin' } }
  - { fields: { placeholder: '@nav_capture_user_settings', language: '@language_en_us', value: 'Capture Settings' } }
  - { fields: { placeholder: '@dashboard_singular', language: '@language_en_us', value: Dashboard } }
  - { fields: { placeholder: '@dashboard_plural', language: '@language_en_us', value: Dashboards } }
  - { fields: { placeholder: '@incident_singular', language: '@language_en_us', value: Incident } }
  - { fields: { placeholder: '@incident_plural', language: '@language_en_us', value: Incidents } }
  - { fields: { placeholder: '@claim_singular', language: '@language_en_us', value: Claims } }
  - { fields: { placeholder: '@claim_plural', language: '@language_en_us', value: Claims } }
  - { fields: { placeholder: '@complaint_singular', language: '@language_en_us', value: Feedback } }
  - { fields: { placeholder: '@complaint_plural', language: '@language_en_us', value: Feedback } }
  - { fields: { placeholder: '@mortality_singular', language: '@language_en_us', value: 'Mortality Review' } }
  - { fields: { placeholder: '@mortality_plural', language: '@language_en_us', value: 'Mortality Reviews' } }
  - { fields: { placeholder: '@common_errors_missing_fields', language: '@language_en_us', value: 'Please ensure all required fields have been provided' } }
  - { fields: { placeholder: '@common_errors_get_form', language: '@language_en_us', value: 'An error occurred while retrieving the form' } }
  - { fields: { placeholder: '@common_errors_forbidden', language: '@language_en_us', value: Forbidden } }
  - { fields: { placeholder: '@common_errors_entity_forbidden_entity', language: '@language_en_us', value: 'Forbidden entity' } }
  - { fields: { placeholder: '@common_errors_resource_not_found', language: '@language_en_us', value: 'Resource not found' } }
  - { fields: { placeholder: '@common_errors_entity_not_found', language: '@language_en_us', value: 'Entity not found' } }
  - { fields: { placeholder: '@sidebar_back_to', language: '@language_en_us', value: 'Back To' } }
  - { fields: { placeholder: '@common_errors_field_exceeds_max_length', language: '@language_en_us', value: '{{field}} exceeds max length ({{maxLength}})' } }
  - { fields: { placeholder: '@loading', language: '@language_en_us', value: Loading... } }
  - { fields: { placeholder: '@components_notes', language: '@language_en_us', value: Notes } }
  - { fields: { placeholder: '@components_notes_new_note', language: '@language_en_us', value: 'New Note' } }
  - { fields: { placeholder: '@components_notes_add_a_note', language: '@language_en_us', value: 'Write a Note' } }
  - { fields: { placeholder: '@components_notes_edit_note', language: '@language_en_us', value: 'Edit Note' } }
  - { fields: { placeholder: '@components_notes_from', language: '@language_en_us', value: From } }
  - { fields: { placeholder: '@components_notes_hide_responses', language: '@language_en_us', value: 'Hide Responses' } }
  - { fields: { placeholder: '@components_notes_show_responses', language: '@language_en_us', value: 'Show Responses' } }
  - { fields: { placeholder: '@components_notes_reply', language: '@language_en_us', value: 'Write a response' } }
  - { fields: { placeholder: '@components_notes_delete_option_confirmation', language: '@language_en_us', value: 'Are you sure you want to delete this Note?' } }
  - { fields: { placeholder: '@components_confirm_button', language: '@language_en_us', value: 'Confirm Button' } }
  - { fields: { placeholder: '@components_confirm_button_text', language: '@language_en_us', value: 'Are you sure?' } }
  - { fields: { placeholder: '@components_recommendations_singular', language: '@language_en_us', value: Recommendation } }
  - { fields: { placeholder: '@components_recommendations_plural', language: '@language_en_us', value: Recommendations } }
  - { fields: { placeholder: '@components_recommendations_search', language: '@language_en_us', value: 'Search Recommendations' } }
  - { fields: { placeholder: '@components_recommendations_create', language: '@language_en_us', value: 'Create Recommendation' } }
  - { fields: { placeholder: '@components_recommendations_edit', language: '@language_en_us', value: 'Edit Recommendation' } }
  - { fields: { placeholder: '@components_recommendations_table_column_id', language: '@language_en_us', value: ID } }
  - { fields: { placeholder: '@components_recommendations_table_column_statement_of_intent', language: '@language_en_us', value: 'Statement of Intent' } }
  - { fields: { placeholder: '@components_recommendations_save', language: '@language_en_us', value: Save } }
  - { fields: { placeholder: '@components_recommendations_loading', language: '@language_en_us', value: 'Loading Recommendations' } }
  - { fields: { placeholder: '@components_recommendations_new', language: '@language_en_us', value: 'New Recommendation' } }
  - { fields: { placeholder: '@components_recommendations_controls', language: '@language_en_us', value: Controls } }
  - { fields: { placeholder: '@components_recommendations_contributory_factor', language: '@language_en_us', value: 'Contributory Factors' } }
  - { fields: { placeholder: '@components_recommendations_loading_controls', language: '@language_en_us', value: 'Loading Controls' } }
  - { fields: { placeholder: '@common_components_magma_relationship_loading', language: '@language_en_us', value: Loading } }
  - { fields: { placeholder: '@common_components_magma_relationship_create', language: '@language_en_us', value: Create } }
  - { fields: { placeholder: '@common_components_magma_relationship_save', language: '@language_en_us', value: Save } }
  - { fields: { placeholder: '@common_components_magma_relationship_cancel', language: '@language_en_us', value: Cancel } }
  - { fields: { placeholder: '@common_components_magma_relationship_saving', language: '@language_en_us', value: Saving } }
  - { fields: { placeholder: '@common_components_magma_relationship_close_search', language: '@language_en_us', value: 'Close Search' } }
  - { fields: { placeholder: '@tables_no_results_found', language: '@language_en_us', value: 'No Results Found' } }
  - { fields: { placeholder: '@tables_pagination_per_page', language: '@language_en_us', value: 'Per page' } }
  - { fields: { placeholder: '@tables_actions', language: '@language_en_us', value: Actions } }
  - { fields: { placeholder: '@tables_go_to', language: '@language_en_us', value: 'Go To' } }
  - { fields: { placeholder: '@tables_view', language: '@language_en_us', value: View } }
  - { fields: { placeholder: '@tables_edit', language: '@language_en_us', value: Edit } }
  - { fields: { placeholder: '@common_n_a', language: '@language_en_us', value: N/A } }
  - { fields: { placeholder: '@common_modules_erm', language: '@language_en_us', value: ERM } }
  - { fields: { placeholder: '@common_modules_erm_risk', language: '@language_en_us', value: 'ERM - Risk' } }
  - { fields: { placeholder: '@common_modules_erm_register', language: '@language_en_us', value: 'ERM - Register' } }
  - { fields: { placeholder: '@common_modules_erm_tracker', language: '@language_en_us', value: 'ERM - Tracker' } }
  - { fields: { placeholder: '@common_modules_investigations', language: '@language_en_us', value: Investigations } }
  - { fields: { placeholder: '@common_modules_investigation', language: '@language_en_us', value: Investigation } }
  - { fields: { placeholder: '@common_modules_surveys', language: '@language_en_us', value: Surveys } }
  - { fields: { placeholder: '@common_modules_survey', language: '@language_en_us', value: Survey } }
  - { fields: { placeholder: '@common_modules_clinical_audit', language: '@language_en_us', value: 'Clinical Audit' } }
  - { fields: { placeholder: '@common_modules_controls', language: '@language_en_us', value: Controls } }
  - { fields: { placeholder: '@common_module_control', language: '@language_en_us', value: Control } }
  - { fields: { placeholder: '@common_modules_recommendation', language: '@language_en_us', value: Recommendation } }
  - { fields: { placeholder: '@common_modules_recommendations', language: '@language_en_us', value: Recommendations } }
  - { fields: { placeholder: '@common_modules_compliance_assessment', language: '@language_en_us', value: 'Compliance Assessment' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_programme', language: '@language_en_us', value: 'Compliance Assessment - Program' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_standard', language: '@language_en_us', value: 'Compliance Assessment - Standard' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_assessment', language: '@language_en_us', value: 'Compliance Assessment - Assessment' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_response', language: '@language_en_us', value: 'Compliance Assessment - Response' } }
  - { fields: { placeholder: '@common_modules_safety_rounds', language: '@language_en_us', value: 'Safety Rounds' } }
  - { fields: { placeholder: '@common_modules_safety_round', language: '@language_en_us', value: 'Safety Round' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_template', language: '@language_en_us', value: 'Safety Rounds - Template' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_response', language: '@language_en_us', value: 'Safety Rounds - Response' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_summary', language: '@language_en_us', value: 'Safety Rounds - Summary' } }
  - { fields: { placeholder: '@common_modules_actions', language: '@language_en_us', value: Actions } }
  - { fields: { placeholder: '@common_modules_acl', language: '@language_en_us', value: ACL } }
  - { fields: { placeholder: '@common_modules_acl_rule', language: '@language_en_us', value: 'ACL - Rule' } }
  - { fields: { placeholder: '@common_modules_acl_role', language: '@language_en_us', value: 'ACL - Role' } }
  - { fields: { placeholder: '@common_modules_contacts', language: '@language_en_us', value: Contacts } }
  - { fields: { placeholder: '@common_modules_contact', language: '@language_en_us', value: Contact } }
  - { fields: { placeholder: '@common_modules_equipment', language: '@language_en_us', value: Equipment } }
  - { fields: { placeholder: '@common_modules_forms', language: '@language_en_us', value: Forms } }
  - { fields: { placeholder: '@common_modules_form', language: '@language_en_us', value: Form } }
  - { fields: { placeholder: '@common_modules_form_field', language: '@language_en_us', value: 'Form Field' } }
  - { fields: { placeholder: '@common_modules_locations', language: '@language_en_us', value: Locations } }
  - { fields: { placeholder: '@common_modules_location', language: '@language_en_us', value: Location } }
  - { fields: { placeholder: '@common_modules_medications', language: '@language_en_us', value: Medications } }
  - { fields: { placeholder: '@common_modules_medication', language: '@language_en_us', value: Medication } }
  - { fields: { placeholder: '@common_modules_services', language: '@language_en_us', value: Services } }
  - { fields: { placeholder: '@common_modules_service', language: '@language_en_us', value: Service } }
  - { fields: { placeholder: '@common_modules_users', language: '@language_en_us', value: Users } }
  - { fields: { placeholder: '@common_modules_user', language: '@language_en_us', value: User } }
  - { fields: { placeholder: '@common_modules_user_group', language: '@language_en_us', value: 'User Group' } }
  - { fields: { placeholder: '@common_modules_prince', language: '@language_en_us', value: Prince } }
  - { fields: { placeholder: '@common_modules_claim', language: '@language_en_us', value: Claim } }
  - { fields: { placeholder: '@common_modules_feedback', language: '@language_en_us', value: Feedback } }
  - { fields: { placeholder: '@common_modules_incident', language: '@language_en_us', value: Incident } }
  - { fields: { placeholder: '@common_modules_mortality', language: '@language_en_us', value: Mortality } }
  - { fields: { placeholder: '@common_select_an_option', language: '@language_en_us', value: 'Select an Option' } }
  - { fields: { placeholder: '@common_search_or_select_option', language: '@language_en_us', value: 'Search for or select an option' } }
  - { fields: { placeholder: '@common_help', language: '@language_en_us', value: Help } }
  - { fields: { placeholder: '@common_components_date_filter_start_date', language: '@language_en_us', value: 'Start Date' } }
  - { fields: { placeholder: '@common_components_date_filter_end_date', language: '@language_en_us', value: 'End Date' } }
  - { fields: { placeholder: '@common_components_feedback_button', language: '@language_en_us', value: 'Give Feedback' } }
  - { fields: { placeholder: '@common_components_feedback_rate', language: '@language_en_us', value: 'How would you rate this page?' } }
  - { fields: { placeholder: '@common_components_share', language: '@language_en_us', value: 'What would you like to share with us?' } }
  - { fields: { placeholder: '@common_components_feedback_save_error', language: '@language_en_us', value: 'An error occurred while saving the feedback' } }
  - { fields: { placeholder: '@common_components_feedback_submitted', language: '@language_en_us', value: Submitted } }
  - { fields: { placeholder: '@common_components_feedback_thank_you', language: '@language_en_us', value: 'Thank you for your feedback!' } }
  - { fields: { placeholder: '@common_submit', language: '@language_en_us', value: Submit } }
  - { fields: { placeholder: '@common_are_you_sure', language: '@language_en_us', value: 'Are you sure?' } }
  - { fields: { placeholder: '@common_back', language: '@language_en_us', value: Back } }
  - { fields: { placeholder: '@nav_back_to_admin', language: '@language_en_us', value: 'Back to Admin' } }
  - { fields: { placeholder: '@nav_back_to_control', language: '@language_en_us', value: 'Back to Control' } }
  - { fields: { placeholder: '@common_on', language: '@language_en_us', value: 'on' } }
  - { fields: { placeholder: '@common_inactive', language: '@language_en_us', value: Inactive } }
  - { fields: { placeholder: '@common_active', language: '@language_en_us', value: Active } }
  - { fields: { placeholder: '@common_deleted', language: '@language_en_us', value: Deleted } }
  - { fields: { placeholder: '@common_loading_permissions', language: '@language_en_us', value: 'Loading Permissions' } }
  - { fields: { placeholder: '@common_saved_successfully', language: '@language_en_us', value: 'Saved successfully' } }
  - { fields: { placeholder: '@common_unauthorised', language: '@language_en_us', value: Unauthorized } }
  - { fields: { placeholder: '@common_unauthorised_page_access', language: '@language_en_us', value: 'You do not have permission to access that page' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_human_and_relational', language: '@language_en_us', value: 'Human and relational factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_patient_and_family', language: '@language_en_us', value: 'Patient and family factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_communication', language: '@language_en_us', value: Communication } }
  - { fields: { placeholder: '@components_contributory_factors_classification_consent', language: '@language_en_us', value: Consent } }
  - { fields: { placeholder: '@components_contributory_factors_classification_incapacity', language: '@language_en_us', value: Incapacity } }
  - { fields: { placeholder: '@components_contributory_factors_classification_religious_beliefs', language: '@language_en_us', value: 'Religious or cultural beliefs' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_non_compliance', language: '@language_en_us', value: Non-compliance } }
  - { fields: { placeholder: '@components_contributory_factors_classification_abnormal_physiology', language: '@language_en_us', value: 'Abnormal physiology' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_en_us', value: Complexity } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_support', language: '@language_en_us', value: 'Social support' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_aggression', language: '@language_en_us', value: Aggression } }
  - { fields: { placeholder: '@components_contributory_factors_classification_other', language: '@language_en_us', value: Other } }
  - { fields: { placeholder: '@components_contributory_factors_classification_individual_and_personal', language: '@language_en_us', value: 'Individual and personal factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_skill', language: '@language_en_us', value: Skill } }
  - { fields: { placeholder: '@components_contributory_factors_classification_knowledge', language: '@language_en_us', value: Knowledge } }
  - { fields: { placeholder: '@components_contributory_factors_classification_experience', language: '@language_en_us', value: Experience } }
  - { fields: { placeholder: '@components_contributory_factors_classification_fatigue', language: '@language_en_us', value: Fatigue } }
  - { fields: { placeholder: '@components_contributory_factors_classification_attitudes', language: '@language_en_us', value: Attitudes } }
  - { fields: { placeholder: '@components_contributory_factors_classification_stress', language: '@language_en_us', value: Stress } }
  - { fields: { placeholder: '@components_contributory_factors_classification_morale', language: '@language_en_us', value: 'Morale and motivation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_health', language: '@language_en_us', value: 'Health condition' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_temporary_impairment', language: '@language_en_us', value: 'Temporary impairment' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_alcohol_or_drugs', language: '@language_en_us', value: 'Alcohol or drugs' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_personal_domestic_issues', language: '@language_en_us', value: 'Personal or domestic issues' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_teamwork_leadership_supervision', language: '@language_en_us', value: 'Teamwork, leadership and supervision' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_supervision', language: '@language_en_us', value: Supervision } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staff_support', language: '@language_en_us', value: 'Support for staff' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_shared_understanding', language: '@language_en_us', value: 'Shared understanding and awareness' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_coordination', language: '@language_en_us', value: Coordination } }
  - { fields: { placeholder: '@components_contributory_factors_classification_local_leadership', language: '@language_en_us', value: 'Local leadership' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_psychological_safety', language: '@language_en_us', value: 'Psychological safety and openness' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_mutual_respect', language: '@language_en_us', value: 'Mutual respect' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_effective_communication', language: '@language_en_us', value: 'Effective communication' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_education_assessment', language: '@language_en_us', value: 'Training, education and assessment' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_appropriate_training', language: '@language_en_us', value: 'Appropriate training' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_availability', language: '@language_en_us', value: 'Availability of training' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_monitoring', language: '@language_en_us', value: 'Monitoring of training' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_education_quality', language: '@language_en_us', value: 'Quality of education' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_assessment_and_qualification', language: '@language_en_us', value: 'Assessment and qualification' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_team_training', language: '@language_en_us', value: 'Team training' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_simulation_training', language: '@language_en_us', value: 'Simulation training' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_revalidation_and_competency', language: '@language_en_us', value: 'Revalidation and competency' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_induction', language: '@language_en_us', value: 'Induction and familiarization' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_and_environmental', language: '@language_en_us', value: 'Task and environmental factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_demands', language: '@language_en_us', value: 'Task demands and workload' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_time_of_day', language: '@language_en_us', value: 'Time of day' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_distractions', language: '@language_en_us', value: 'Distractions and interruptions' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_high_workload', language: '@language_en_us', value: 'High workload' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_low_workload', language: '@language_en_us', value: 'Low workload' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_time_availability', language: '@language_en_us', value: 'Time availability' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_complexity', language: '@language_en_us', value: 'Task complexity' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_production_pressure', language: '@language_en_us', value: 'Production pressure' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_practicality', language: '@language_en_us', value: 'Task practicality' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_difficulty', language: '@language_en_us', value: 'Task difficulty' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_conflicting_tasks', language: '@language_en_us', value: 'Conflicting tasks' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_uncertainty_and_variability', language: '@language_en_us', value: 'Uncertainty and variability' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_physical_work_environment', language: '@language_en_us', value: 'Physical work environment' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_housekeeping', language: '@language_en_us', value: Housekeeping } }
  - { fields: { placeholder: '@components_contributory_factors_classification_lighting', language: '@language_en_us', value: Lighting } }
  - { fields: { placeholder: '@components_contributory_factors_classification_noise', language: '@language_en_us', value: Noise } }
  - { fields: { placeholder: '@components_contributory_factors_classification_temperature', language: '@language_en_us', value: Temperature } }
  - { fields: { placeholder: '@components_contributory_factors_classification_layout_and_design', language: '@language_en_us', value: 'Layout and design' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_accessibility', language: '@language_en_us', value: Accessibility } }
  - { fields: { placeholder: '@components_contributory_factors_classification_security', language: '@language_en_us', value: Security } }
  - { fields: { placeholder: '@components_contributory_factors_classification_visibility', language: '@language_en_us', value: 'Visibility and lines of sight' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_fixtures_and_fittings', language: '@language_en_us', value: 'Fixtures and fittings' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_maintenance', language: '@language_en_us', value: Maintenance } }
  - { fields: { placeholder: '@components_contributory_factors_classification_procedures_protocols_and_guidelines', language: '@language_en_us', value: 'Procedures, protocols and guidelines' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_adequacy_and_accuracy', language: '@language_en_us', value: 'Adequacy and accuracy' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_availability_and_accessibility', language: '@language_en_us', value: 'Availability and accessibility' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_currency', language: '@language_en_us', value: Currency } }
  - { fields: { placeholder: '@components_contributory_factors_classification_clarity_and_coherence', language: '@language_en_us', value: 'Clarity and coherence' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_practicality', language: '@language_en_us', value: Practicality } }
  - { fields: { placeholder: '@components_contributory_factors_classification_implementation_and_usage', language: '@language_en_us', value: 'Implementation and usage' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_acceptability_and_agreement', language: '@language_en_us', value: 'Acceptability and agreement' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tools_equipment_and_resources', language: '@language_en_us', value: 'Tools, equipment and resources' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_alarms_and_warnings', language: '@language_en_us', value: 'Alarms and warnings' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_displays_and_interfaces', language: '@language_en_us', value: 'Displays and interfaces' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_functionality', language: '@language_en_us', value: Functionality } }
  - { fields: { placeholder: '@components_contributory_factors_classification_reliability', language: '@language_en_us', value: Reliability } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_availability', language: '@language_en_us', value: Accessibility } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_design', language: '@language_en_us', value: Design } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_installation', language: '@language_en_us', value: Installation } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_maintenance', language: '@language_en_us', value: Maintenance } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_compatibility', language: '@language_en_us', value: Compatibility } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_packaging', language: '@language_en_us', value: Packaging } }
  - { fields: { placeholder: '@components_contributory_factors_classification_quantity_and_quality', language: '@language_en_us', value: 'Quantity and quality' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisational_and_cultural_factors', language: '@language_en_us', value: 'Organizational and cultural factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_and_cultural', language: '@language_en_us', value: 'Social and cultural context' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_norms_and_customs', language: '@language_en_us', value: 'Norms and customs' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_values_and_assumptions', language: '@language_en_us', value: 'Values and assumptions' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_pressure', language: '@language_en_us', value: 'Social pressure' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_diffusion_of_responsibility', language: '@language_en_us', value: 'Diffusion of responsibility' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_rule_behaviour', language: '@language_en_us', value: 'Rule-related behavior' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_risk_perception_and_tolerance', language: '@language_en_us', value: 'Risk perception and tolerance' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_honesty', language: '@language_en_us', value: 'Openness and honesty' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_senior_leadership_commitment', language: '@language_en_us', value: 'Senior leadership commitment' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_blame_and_fear', language: '@language_en_us', value: 'Blame and fear' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisation_governance_and_strategy', language: '@language_en_us', value: 'Organization, governance and strategy' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_lines_of_responsibility', language: '@language_en_us', value: 'Lines of responsibility' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_strategic_leadership', language: '@language_en_us', value: 'Strategic leadership' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_service_planning', language: '@language_en_us', value: 'Service planning and design' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisational_structure', language: '@language_en_us', value: 'Organizational structure' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_risk_management_and_learning', language: '@language_en_us', value: 'Risk management and learning' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_oversight_and_monitoring', language: '@language_en_us', value: 'Oversight and monitoring' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_resource_management', language: '@language_en_us', value: 'Resource management' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_operational_and_people_management', language: '@language_en_us', value: 'Operational and people management' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_bed_management', language: '@language_en_us', value: 'Bed management and flow' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_skill_mix', language: '@language_en_us', value: 'Skill mix' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staffing_levels', language: '@language_en_us', value: 'Staffing levels' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_rota_and_shift_planning', language: '@language_en_us', value: 'Rota and shift planning' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staff_turnover', language: '@language_en_us', value: 'Staff turnover' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_job_roles_and_design', language: '@language_en_us', value: 'Job roles and design' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_recruitment_and_selection', language: '@language_en_us', value: 'Recruitment and selection' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_administration', language: '@language_en_us', value: Administration } }
  - { fields: { placeholder: '@components_contributory_factors_classification_sanctions_and_rewards', language: '@language_en_us', value: 'Sanctions and rewards' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_purchasing_and_procurement', language: '@language_en_us', value: 'Purchasing and procurement' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_and_communication_systems', language: '@language_en_us', value: 'Information and communication systems' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_verbal_communication', language: '@language_en_us', value: 'Verbal communication' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_documentation_and_record_keeping', language: '@language_en_us', value: 'Documentation and record keeping' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_handover_and_briefing', language: '@language_en_us', value: 'Handover and briefing' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_electronic_records_and_it', language: '@language_en_us', value: 'Electronic records and IT systems' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_accuracy', language: '@language_en_us', value: 'Information accuracy' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_clarity_and_ambiguity', language: '@language_en_us', value: 'Information clarity and ambiguity' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_external_and_regulatory_factors', language: '@language_en_us', value: 'External and regulatory factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_policy_and_external', language: '@language_en_us', value: 'Regulatory, policy and external factors' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_national_policy_and_standards', language: '@language_en_us', value: 'National policy and standards' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_commissioning_and_contracting', language: '@language_en_us', value: 'Commissioning and contracting' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_funding_and_resources', language: '@language_en_us', value: 'Funding and resources' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_activities', language: '@language_en_us', value: 'Regulatory activities' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_other_external_events', language: '@language_en_us', value: 'Other external events' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_guidance_not_followed', language: '@language_en_us', value: 'Guidance Not Followed' } }
  - { fields: { placeholder: '@welcome_page_title', language: '@language_en_us', value: 'Welcome to Datix Cloud IQ' } }
  - { fields: { placeholder: '@untitled_section', language: '@language_en_us', value: 'Untitled Section' } }
  - { fields: { placeholder: '@language_label_en', language: '@language_en_us', value: 'English (United Kingdom)' } }
  - { fields: { placeholder: '@language_label_ar', language: '@language_en_us', value: Arabic } }
  - { fields: { placeholder: '@language_label_us', language: '@language_en_us', value: 'English (United States)' } }
  - { fields: { placeholder: '@language_label_de_ch', language: '@language_en_us', value: 'Swiss German' } }
  - { fields: { placeholder: '@splash_page_title', language: '@language_en_us', value: Page } }
  - { fields: { placeholder: '@datasource_language', language: '@language_en_us', value: Language } }
  - { fields: { placeholder: '@datasource_religion', language: '@language_en_us', value: Religion } }
  - { fields: { placeholder: '@common_node_list_no_other_results', language: '@language_en_us', value: 'No Other Results' } }
  - { fields: { placeholder: '@common_other_results', language: '@language_en_us', value: 'Other Results ({{numResults}})' } }
  - { fields: { placeholder: '@common_node_list_view_children', language: '@language_en_us', value: 'View {{numChildren}} Children' } }
  - { fields: { placeholder: '@common_success_save', language: '@language_en_us', value: '{{singularLabel}} saved successfully' } }
  - { fields: { placeholder: '@common_error_save', language: '@language_en_us', value: 'An error occurred while saving the {{singularLabel}}' } }
  - { fields: { placeholder: '@common_success_remove', language: '@language_en_us', value: '{{singularLabel}} removed successfully' } }
  - { fields: { placeholder: '@common_error_remove', language: '@language_en_us', value: 'Error removing {{singularLabel}}' } }
  - { fields: { placeholder: '@common_post_save_no_access', language: '@language_en_us', value: 'Record has been saved, but the access permissions assigned to your user do not permit you to see it' } }
  - { fields: { placeholder: '@common_form_required_fields', language: '@language_en_us', value: 'Required fields' } }
  - { fields: { placeholder: '@common_value_not_set', language: '@language_en_us', value: 'No value has been set' } }
  - { fields: { placeholder: '@common_loading_options', language: '@language_en_us', value: 'Loading Options...' } }
  - { fields: { placeholder: '@common_please_select_option', language: '@language_en_us', value: 'Please select...' } }
  - { fields: { placeholder: '@common_no_options_available', language: '@language_en_us', value: 'No options available' } }
  - { fields: { placeholder: '@common_locale', language: '@language_en_us', value: Locale } }
  - { fields: { placeholder: '@common_select_locale', language: '@language_en_us', value: 'Select Locale' } }
  - { fields: { placeholder: '@common_components_templates', language: '@language_en_us', value: Templates } }
  - { fields: { placeholder: '@common_components_label_template', language: '@language_en_us', value: Template } }
  - { fields: { placeholder: '@common_components_templates_new', language: '@language_en_us', value: 'Create New Document' } }
  - { fields: { placeholder: '@common_components_templates_pdf', language: '@language_en_us', value: 'Create New PDF Document' } }
  - { fields: { placeholder: '@common_components_templates_docx', language: '@language_en_us', value: 'Create New Word Document' } }
  - { fields: { placeholder: '@common_components_templates_close', language: '@language_en_us', value: Close } }
  - { fields: { placeholder: '@common_components_templates_title', language: '@language_en_us', value: Templates } }
  - { fields: { placeholder: '@common_all_modules', language: '@language_en_us', value: 'All Modules' } }
  - { fields: { placeholder: '@common_form_time_is_not_known', language: '@language_en_us', value: 'Time is not known' } }
  - { fields: { placeholder: '@common_form_time', language: '@language_en_us', value: Time } }
  - { fields: { placeholder: '@common_form_start_time', language: '@language_en_us', value: 'Start Time' } }
  - { fields: { placeholder: '@common_form_end_time', language: '@language_en_us', value: 'End Time' } }
  - { fields: { placeholder: '@common_permission_denied', language: '@language_en_us', value: 'Permission Denied' } }
  - { fields: { placeholder: '@common_employee_statuses_label', language: '@language_en_us', value: 'Employee Status' } }
  - { fields: { placeholder: '@common_employee_statuses_active', language: '@language_en_us', value: Active } }
  - { fields: { placeholder: '@common_employee_statuses_terminated', language: '@language_en_us', value: Terminated } }
  - { fields: { placeholder: '@common_employee_statuses_not_started', language: '@language_en_us', value: 'Not Started' } }
  - { fields: { placeholder: '@common_positions_no_positions', language: '@language_en_us', value: 'No positions selected' } }
  - { fields: { placeholder: '@common_exception_investigation_not_found', language: '@language_en_us', value: 'Investigation not found' } }
  - { fields: { placeholder: '@common_exception_maintenance_mode_required', language: '@language_en_us', value: 'Maintenance Mode must be enabled to use this feature' } }
  - { fields: { placeholder: '@common_exception_unknown_template_reference', language: '@language_en_us', value: 'Unknown Template Reference' } }
  - { fields: { placeholder: '@common_exception_template_not_found', language: '@language_en_us', value: 'Template not found' } }
  - { fields: { placeholder: '@common_exception_document_was_not_created_successfully', language: '@language_en_us', value: 'Document was not created successfully' } }
  - { fields: { placeholder: '@common_record_search_no_value_selected', language: '@language_en_us', value: 'No value selected' } }
  - { fields: { placeholder: '@common_error_types_forbidden', language: '@language_en_us', value: 'Action Forbidden' } }
  - { fields: { placeholder: '@common_error_see_the_errors_attribute_for_specific_failures', language: '@language_en_us', value: 'Please see the errors attribute for specific failures' } }
  - { fields: { placeholder: '@common_actions_next', language: '@language_en_us', value: Next } }
  - { fields: { placeholder: '@common_actions_previous', language: '@language_en_us', value: Previous } }
  - { fields: { placeholder: '@common_exception_template_invalid_target_type', language: '@language_en_us', value: 'Template Type was not found' } }
  - { fields: { placeholder: '@common_approved', language: '@language_en_us', value: Approved } }
  - { fields: { placeholder: '@common_rejected', language: '@language_en_us', value: Rejected } }
  - { fields: { placeholder: '@common_document_page_no_of_pages', language: '@language_en_us', value: 'Page {PAGENO} of {nb}' } }
  - { fields: { placeholder: '@common_exception_template_invalid_document_type', language: '@language_en_us', value: 'Invalid Document Type' } }
  - { fields: { placeholder: '@common_form_validation_number_value_min', language: '@language_en_us', value: 'This field has a minimum value of {{min}}' } }
  - { fields: { placeholder: '@common_form_validation_number_value_max', language: '@language_en_us', value: 'This field has a maximum value of {{max}}' } }
  - { fields: { placeholder: '@common_form_validation_number_value_digits', language: '@language_en_us', value: 'This field must only contain digits' } }
  - { fields: { placeholder: '@audit_actions_record_created', language: '@language_en_us', value: 'On {{date}}, {{user}} created this record with the following values' } }
  - { fields: { placeholder: '@audit_actions_record_updated', language: '@language_en_us', value: 'On {{date}}, {{user}} updated this record with the following values' } }
  - { fields: { placeholder: '@audit_actions_record_attached', language: '@language_en_us', value: 'On {{date}}, {{user}} attached the following record' } }
  - { fields: { placeholder: '@audit_actions_record_detached', language: '@language_en_us', value: 'On {{date}}, {{user}} detached the following record' } }
  - { fields: { placeholder: '@audit_no_logs_available', language: '@language_en_us', value: 'Audit logs cannot be displayed for this record. To access this data, please contact an administrator.' } }
  - { fields: { placeholder: '@common_upload_file', language: '@language_en_us', value: 'Upload File' } }
  - { fields: { placeholder: '@common_upload_template', language: '@language_en_us', value: 'Upload Template' } }
  - { fields: { placeholder: '@common_file_upload_percent_complete', language: '@language_en_us', value: 'Percent Complete' } }
  - { fields: { placeholder: '@common_file_upload_cancel', language: '@language_en_us', value: Cancel } }
  - { fields: { placeholder: '@common_file_upload_retry', language: '@language_en_us', value: Retry } }
  - { fields: { placeholder: '@common_file_upload_error', language: '@language_en_us', value: 'There was an error uploading the file' } }
  - { fields: { placeholder: '@common_file_upload_replace_file', language: '@language_en_us', value: 'Replace File' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_twelve', language: '@language_en_us', value: 'UTC-12:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_eleven', language: '@language_en_us', value: 'UTC-11:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_ten', language: '@language_en_us', value: 'UTC-10:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_nine_half', language: '@language_en_us', value: 'UTC-09:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_nine', language: '@language_en_us', value: 'UTC-09:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_eight', language: '@language_en_us', value: 'UTC-08:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_seven', language: '@language_en_us', value: 'UTC-07:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_six', language: '@language_en_us', value: 'UTC-06:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_five', language: '@language_en_us', value: 'UTC-05:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_four', language: '@language_en_us', value: 'UTC-04:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_three_half', language: '@language_en_us', value: 'UTC-03:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_three', language: '@language_en_us', value: 'UTC-03:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_two', language: '@language_en_us', value: 'UTC-02:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_one', language: '@language_en_us', value: 'UTC-01:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_zero', language: '@language_en_us', value: 'UTC±00:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_one', language: '@language_en_us', value: 'UTC+01:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_two', language: '@language_en_us', value: 'UTC+02:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_three', language: '@language_en_us', value: 'UTC+03:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_three_half', language: '@language_en_us', value: 'UTC+03:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_four', language: '@language_en_us', value: 'UTC+04:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_four_half', language: '@language_en_us', value: 'UTC+04:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five', language: '@language_en_us', value: 'UTC+05:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five_half', language: '@language_en_us', value: 'UTC+05:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five_three_quarters', language: '@language_en_us', value: 'UTC+05:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_six', language: '@language_en_us', value: 'UTC+06:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_six_half', language: '@language_en_us', value: 'UTC+06:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_seven', language: '@language_en_us', value: 'UTC+07:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eight', language: '@language_en_us', value: 'UTC+08:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eight_three_quarters', language: '@language_en_us', value: 'UTC+08:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_nine', language: '@language_en_us', value: 'UTC+09:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_nine_half', language: '@language_en_us', value: 'UTC+09:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_ten', language: '@language_en_us', value: 'UTC+10:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_ten_half', language: '@language_en_us', value: 'UTC+10:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eleven', language: '@language_en_us', value: 'UTC+11:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_twelve', language: '@language_en_us', value: 'UTC+12:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_twelve_three_quarters', language: '@language_en_us', value: 'UTC+12:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_thirteen', language: '@language_en_us', value: 'UTC+13:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_fourteen', language: '@language_en_us', value: 'UTC+14:00' } }
  - { fields: { placeholder: '@common_exceptions_inactive_feature', language: '@language_en_us', value: 'Feature is not active' } }
  - { fields: { placeholder: '@common_exceptions_attachments_not_found', language: '@language_en_us', value: 'Attachment was not found' } }
  - { fields: { placeholder: '@common_exceptions_attachments_invalid_template_type', language: '@language_en_us', value: 'Attachment must be an Investigation Report or a RIB Report' } }
  - { fields: { placeholder: '@common_exceptions_attachments_missing_reporter_email', language: '@language_en_us', value: 'Report email is not set' } }
  - { fields: { placeholder: '@common_exceptions_attachments_email_failed_to_send', language: '@language_en_us', value: 'Report email failed to send' } }
  - { fields: { placeholder: '@common_exceptions_validation_error', language: '@language_en_us', value: 'Validation Error' } }
  - { fields: { placeholder: '@nav_redress', language: '@language_en_us', value: Redress } }
  - { fields: { placeholder: '@common_components_tree_status_enabled', language: '@language_en_us', value: Enabled } }
  - { fields: { placeholder: '@common_components_tree_status_deactivated', language: '@language_en_us', value: Deactivated } }
  - { fields: { placeholder: '@common_components_tree_status_disabled', language: '@language_en_us', value: Disabled } }
  - { fields: { placeholder: '@common_components_tree_node_first_page', language: '@language_en_us', value: 'First Page' } }
  - { fields: { placeholder: '@common_components_tree_node_next_page', language: '@language_en_us', value: 'Next Page' } }
  - { fields: { placeholder: '@common_components_tree_node_last_page', language: '@language_en_us', value: 'Last Page' } }
  - { fields: { placeholder: '@common_components_tree_node_previous_page', language: '@language_en_us', value: 'Previous Page' } }
  - { fields: { placeholder: '@common_components_attachments_disabled_for_public', language: '@language_en_us', value: 'File uploads are not available to logged-out users' } }
  - { fields: { placeholder: '@capture_modules_com_label', language: '@language_en_us', value: Feedback } }
  - { fields: { placeholder: '@capture_modules_cla_label', language: '@language_en_us', value: Claims } }
  - { fields: { placeholder: '@capture_modules_inc_label', language: '@language_en_us', value: Incidents } }
  - { fields: { placeholder: '@capture_modules_das_label', language: '@language_en_us', value: Dashboard } }
  - { fields: { placeholder: '@capture_modules_red_label', language: '@language_en_us', value: Redress } }
  - { fields: { placeholder: '@capture_modules_sfg_label', language: '@language_en_us', value: Safeguarding } }
  - { fields: { placeholder: '@placeholder_erm_risk_linked_records_list_status', language: '@language_en_us', value: Status } }
  - { fields: { placeholder: '@common_error_see_the_errors_attribute_for_specific_failures_erm1', language: '@language_en_us', value: '{{ermCode}} Please see the errors attribute for specific failures' } }
  - { fields: { placeholder: '@common_errors_resource_not_found_erm14', language: '@language_en_us', value: '{{ermCode}} Resource not found' } }
  - { fields: { placeholder: '@common_form_validation_errors_erm15', language: '@language_en_us', value: '{{ermCode}} The submitted form contains validation errors' } }
  - { fields: { placeholder: '@common_components_attachments_max_file_size_error', language: '@language_en_us', value: 'File size is too large (>{{fileSizeLimit}}MB), please upload a smaller file' } }
  - { fields: { placeholder: '@common_attachment_saved_successfully', language: '@language_en_us', value: 'Pièce jointe enregistrée avec succès' } }
