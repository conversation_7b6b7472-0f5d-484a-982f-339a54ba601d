entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@components_magma_table_pagination_first'
      language: '@language_en_gb'
      value: First
  -
    fields:
      placeholder: '@language_selector_label'
      language: '@language_en_gb'
      value: Choose your display language
  -
    fields:
      placeholder: '@language_selector_option'
      language: '@language_en_gb'
      value: Loading
  -
    fields:
      placeholder: '@components_magma_table_pagination_previous'
      language: '@language_en_gb'
      value: Previous
  -
    fields:
      placeholder: '@components_magma_table_pagination_next'
      language: '@language_en_gb'
      value: Next
  -
    fields:
      placeholder: '@components_magma_table_pagination_last'
      language: '@language_en_gb'
      value: Last
  -
    fields:
      placeholder: '@common_optional'
      language: '@language_en_gb'
      value: Optional
  -
    fields:
      placeholder: '@common_copied'
      language: '@language_en_gb'
      value: Copied
  -
    fields:
      placeholder: '@components_record_search'
      language: '@language_en_gb'
      value: 'Record Search'
  -
    fields:
      placeholder: '@components_record_search_back_to_search'
      language: '@language_en_gb'
      value: 'Back to Search'
  -
    fields:
      placeholder: '@components_record_search_search_results'
      language: '@language_en_gb'
      value: 'Search Results'
  -
    fields:
      placeholder: '@common_components_attachments_plural'
      language: '@language_en_gb'
      value: Attachments
  -
    fields:
      placeholder: '@common_components_attachments_loading'
      language: '@language_en_gb'
      value: 'Loading Attachments'
  -
    fields:
      placeholder: '@common_components_attachments_new'
      language: '@language_en_gb'
      value: 'New Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_uploaded'
      language: '@language_en_gb'
      value: Uploaded
  -
    fields:
      placeholder: '@common_components_attachments_download'
      language: '@language_en_gb'
      value: 'Download Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_edit'
      language: '@language_en_gb'
      value: Edit
  -
    fields:
      placeholder: '@common_components_attachments_delete_warning'
      language: '@language_en_gb'
      value: 'Deleting an attachment is irreversible. If you are happy to proceed, please confirm.'
  -
    fields:
      placeholder: '@common_components_attachments_version'
      language: '@language_en_gb'
      value: 'Version {{number}}'
  -
    fields:
      placeholder: '@common_components_attachments_edit_attachment'
      language: '@language_en_gb'
      value: 'Edit Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_edit_attachment_success'
      language: '@language_en_gb'
      value: 'Attachment saved'
  -
    fields:
      placeholder: '@common_components_attachments_add'
      language: '@language_en_gb'
      value: 'Add Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_singular'
      language: '@language_en_gb'
      value: Attachment
  -
    fields:
      placeholder: '@common_components_attachments_percent_complete'
      language: '@language_en_gb'
      value: '{{percent}}% Complete'
  -
    fields:
      placeholder: '@common_components_attachments_replace'
      language: '@language_en_gb'
      value: 'Replace Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@common_components_attachments_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@common_components_attachments_default_allowed_file_types'
      language: '@language_en_gb'
      value: 'Attachment files may be images, Word documents, spreadsheets or PDFs'
  -
    fields:
      placeholder: '@common_components_attachments_classification'
      language: '@language_en_gb'
      value: Classification
  -
    fields:
      placeholder: '@common_components_attachments_select_classification'
      language: '@language_en_gb'
      value: 'Select Classification'
  -
    fields:
      placeholder: '@common_components_attachments_cancel'
      language: '@language_en_gb'
      value: Cancel
  -
    fields:
      placeholder: '@common_components_attachments_save'
      language: '@language_en_gb'
      value: 'Save Attachment'
  -
    fields:
      placeholder: '@common_components_attachments_retry'
      language: '@language_en_gb'
      value: Retry
  -
    fields:
      placeholder: '@common_components_attachments_error'
      language: '@language_en_gb'
      value: 'There was an error uploading your file.'
  -
    fields:
      placeholder: '@common_components_attachments_classification_values_image'
      language: '@language_en_gb'
      value: 'Image / Picture'
  -
    fields:
      placeholder: '@common_components_attachments_classification_values_document'
      language: '@language_en_gb'
      value: Document
  -
    fields:
      placeholder: '@common_components_attachments_classification_values_spreadsheet'
      language: '@language_en_gb'
      value: Spreadsheet
  -
    fields:
      placeholder: '@common_components_attachments_audit_title'
      language: '@language_en_gb'
      value: Attachment audit
  -
    fields:
      placeholder: '@common_components_attachments_audit_version'
      language: '@language_en_gb'
      value: Version
  -
    fields:
      placeholder: '@common_components_attachments_audit_name'
      language: '@language_en_gb'
      value: Name
  -
    fields:
      placeholder: '@common_components_attachments_audit_datetime'
      language: '@language_en_gb'
      value: Date/Time
  -
    fields:
      placeholder: '@common_components_attachments_audit_download'
      language: '@language_en_gb'
      value: Download
  -
    fields:
      placeholder: '@common_components_attachments_audit_delete'
      language: '@language_en_gb'
      value: Delete
  -
    fields:
      placeholder: '@common_components_attachments_audit_attachment_deleted'
      language: '@language_en_gb'
      value: Attachment has been deleted
  -
    fields:
      placeholder: '@common_components_attachments_audit_delete_failed_title'
      language: '@language_en_gb'
      value: Unable To Delete
  -
    fields:
      placeholder: '@common_components_attachments_delete_success'
      language: '@language_en_gb'
      value: Attachment has been deleted
  -
    fields:
      placeholder: '@common_components_attachments_delete_error'
      language: '@language_en_gb'
      value: Unable to delete attachment
  -
    fields:
      placeholder: '@common_components_attachments_audit_delete_failed_last_version_required'
      language: '@language_en_gb'
      value: Only the latest attachment version may be deleted
  -
    fields:
      placeholder: '@common_components_attachments_audit_delete_failed_permissions_insufficient'
      language: '@language_en_gb'
      value: This attachment version cannot be deleted with your user permissions
  - fields:
      placeholder: '@common_components_attachments_audit_type'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@common_components_attachments_audit_type_public'
      language: '@language_en_gb'
      value: Public
  - fields:
      placeholder: '@common_components_attachments_audit_type_private'
      language: '@language_en_gb'
      value: Private
  - fields:
      placeholder: '@common_components_attachments_no_attachments_provided'
      language: '@language_en_gb'
      value: No attachments provided
  - fields:
      placeholder: '@common_components_attachments_email_attachment_label'
      language: '@language_en_gb'
      value: Email Attachment
  - fields:
      placeholder: '@common_components_attachments_email_attachment_title'
      language: '@language_en_gb'
      value: Email Link
  - fields:
      placeholder: '@common_components_attachments_resend_attachment'
      language: '@language_en_gb'
      value: Resend Attachment
  - fields:
      placeholder: '@common_components_attachments_reason_for_resending'
      language: '@language_en_gb'
      value: Reason for Resending
  - fields:
      placeholder: '@common_components_attachments_report_last_sent'
      language: '@language_en_gb'
      value: 'Report Last Re-Sent: {{date}}'
  - fields:
      placeholder: '@common_components_attachments_report_first_sent'
      language: '@language_en_gb'
      value: 'Report First Sent: {{date}}'
  - fields:
      placeholder: '@common_components_attachments_reason_for_resend'
      language: '@language_en_gb'
      value: 'Report Last Reason for Re-send: {{reason}}'
  -
    fields:
      placeholder: '@components_contributory_factors_singular'
      language: '@language_en_gb'
      value: 'Contributory Factor'
  -
    fields:
      placeholder: '@components_contributory_factors_plural'
      language: '@language_en_gb'
      value: 'Contributory Factors'
  -
    fields:
      placeholder: '@components_contributory_factors_add_contributory_factor'
      language: '@language_en_gb'
      value: 'Add Contributory Factor'
  -
    fields:
      placeholder: '@components_contributory_factors_no_contributory_factors'
      language: '@language_en_gb'
      value: 'This Record doesn''t have any Contributory Factors'
  -
    fields:
      placeholder: '@components_contributory_factors_new_contributory_factor'
      language: '@language_en_gb'
      value: 'New Contributory Factor'
  -
    fields:
      placeholder: '@components_contributory_factors_category'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@components_contributory_factors_sub_category'
      language: '@language_en_gb'
      value: Sub-Category
  -
    fields:
      placeholder: '@components_contributory_factors_select_type'
      language: '@language_en_gb'
      value: 'Select Type'
  -
    fields:
      placeholder: '@components_contributory_factors_select_category'
      language: '@language_en_gb'
      value: 'Select Category'
  -
    fields:
      placeholder: '@components_contributory_factors_select_sub_category'
      language: '@language_en_gb'
      value: 'Select Sub-Category'
  -
    fields:
      placeholder: '@common_create'
      language: '@language_en_gb'
      value: Create
  -
    fields:
      placeholder: '@common_new'
      language: '@language_en_gb'
      value: New
  -
    fields:
      placeholder: '@common_edit'
      language: '@language_en_gb'
      value: Edit
  -
    fields:
      placeholder: '@common_loading'
      language: '@language_en_gb'
      value: Loading
  -
    fields:
      placeholder: '@common_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@common_ref'
      language: '@language_en_gb'
      value: Ref
  -
    fields:
      placeholder: '@common_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@common_label'
      language: '@language_en_gb'
      value: Label
  -
    fields:
      placeholder: '@common_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@common_modules'
      language: '@language_en_gb'
      value: Modules
  -
    fields:
      placeholder: '@common_module'
      language: '@language_en_gb'
      value: Module
  -
    fields:
      placeholder: '@common_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@common_due'
      language: '@language_en_gb'
      value: Due
  -
    fields:
      placeholder: '@common_select'
      language: '@language_en_gb'
      value: Select
  -
    fields:
      placeholder: '@common_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@common_subtype'
      language: '@language_en_gb'
      value: Subtype
  -
    fields:
      placeholder: '@common_category'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@common_subcategory'
      language: '@language_en_gb'
      value: Subcategory
  -
    fields:
      placeholder: '@common_filter'
      language: '@language_en_gb'
      value: Filter
  -
    fields:
      placeholder: '@common_filter_indicator'
      language: '@language_en_gb'
      value: 'Filter{{ filterIndicator }}'
  -
    fields:
      placeholder: '@common_not_set'
      language: '@language_en_gb'
      value: 'Not Set'
  -
    fields:
      placeholder: '@placeholder_common_no_value_selected'
      language: '@language_en_gb'
      value: 'No value selected'
  -
    fields:
      placeholder: '@common_view_details'
      language: '@language_en_gb'
      value: 'View Details'
  -
    fields:
      placeholder: '@common_save_changes'
      language: '@language_en_gb'
      value: 'Save Changes'
  -
    fields:
      placeholder: '@common_save'
      language: '@language_en_gb'
      value: Save
  -
    fields:
      placeholder: '@common_saving'
      language: '@language_en_gb'
      value: Saving
  -
    fields:
      placeholder: '@common_save_and_close'
      language: '@language_en_gb'
      value: 'Save and Close'
  -
    fields:
      placeholder: '@common_save_error'
      language: '@language_en_gb'
      value: 'Save Error'
  -
    fields:
      placeholder: '@common_cancel'
      language: '@language_en_gb'
      value: Cancel
  -
    fields:
      placeholder: '@common_delete'
      language: '@language_en_gb'
      value: Delete
  -
    fields:
      placeholder: '@common_close'
      language: '@language_en_gb'
      value: Close
  -
    fields:
      placeholder: '@common_search'
      language: '@language_en_gb'
      value: Search
  -
    fields:
      placeholder: '@common_date'
      language: '@language_en_gb'
      value: Date
  -
    fields:
      placeholder: '@common_clear'
      language: '@language_en_gb'
      value: Clear
  -
    fields:
      placeholder: '@common_reply'
      language: '@language_en_gb'
      value: Reply
  -
    fields:
      placeholder: '@common_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@common_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@common_confirm'
      language: '@language_en_gb'
      value: Confirm
  -
    fields:
      placeholder: '@common_error'
      language: '@language_en_gb'
      value: Error
  -
    fields:
      placeholder: '@common_generic_error'
      language: '@language_en_gb'
      value: 'An error occurred'
  -
    fields:
      placeholder: '@common_role'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@common_admin'
      language: '@language_en_gb'
      value: Admin
  -
    fields:
      placeholder: '@common_administration'
      language: '@language_en_gb'
      value: Administration
  -
    fields:
      placeholder: '@common_administration_tools'
      language: '@language_en_gb'
      value: Tools
  -
    fields:
      placeholder: '@common_administration_permissions'
      language: '@language_en_gb'
      value: Permissions
  -
    fields:
      placeholder: '@common_access_control'
      language: '@language_en_gb'
      value: 'Access Control'
  -
    fields:
      placeholder: '@common_access_control_manual_roles'
      language: '@language_en_gb'
      value: 'Manual Roles'
  -
    fields:
      placeholder: '@common_access_control_generated_roles'
      language: '@language_en_gb'
      value: 'Generated Roles'
  -
    fields:
      placeholder: '@common_access_control_no_assignable_roles'
      language: '@language_en_gb'
      value: 'No Assignable Roles'
  -
    fields:
      placeholder: '@common_form_validation_required'
      language: '@language_en_gb'
      value: 'This field is required'
  -
    fields:
      placeholder: '@common_form_validation_string_length_min'
      language: '@language_en_gb'
      value: 'This field must be at least {{min}} characters in length'
  -
    fields:
      placeholder: '@common_form_validation_string_length_max'
      language: '@language_en_gb'
      value: 'This field may not exceed {{max}} characters in length'
  -
    fields:
      placeholder: '@common_form_validation_email'
      language: '@language_en_gb'
      value: 'Please enter a valid Email Address'
  -
    fields:
      placeholder: '@common_form_validation_errors'
      language: '@language_en_gb'
      value: 'The submitted form contains validation errors'
  -
    fields:
      placeholder: '@common_form_validation_cascading_select'
      language: '@language_en_gb'
      value: 'Please select a subtype'
  -
    fields:
      placeholder: '@nav_back_to_dashboard'
      language: '@language_en_gb'
      value: 'Back to Dashboard'
  -
    fields:
      placeholder: '@nav_capture_user_settings'
      language: '@language_en_gb'
      value: Capture Settings
  -
    fields:
      placeholder: '@nav_dashboard'
      language: '@language_en_gb'
      value: Dashboard
  -
    fields:
      placeholder: '@nav_capture'
      language: '@language_en_gb'
      value: Capture
  -
    fields:
      placeholder: '@nav_incidents'
      language: '@language_en_gb'
      value: Incidents
  -
    fields:
      placeholder: '@nav_feedback'
      language: '@language_en_gb'
      value: Feedback
  -
    fields:
      placeholder: '@nav_claims'
      language: '@language_en_gb'
      value: Claims
  -
    fields:
      placeholder: '@nav_mortality'
      language: '@language_en_gb'
      value: 'Mortality Review'
  -
    fields:
      placeholder: '@nav_evaluate'
      language: '@language_en_gb'
      value: Evaluate
  -
    fields:
      placeholder: '@nav_risk_register'
      language: '@language_en_gb'
      value: 'Enterprise Risk Manager'
  -
    fields:
      placeholder: '@nav_investigation'
      language: '@language_en_gb'
      value: Investigation
  -
    fields:
      placeholder: '@nav_investigations'
      language: '@language_en_gb'
      value: Investigations
  -
    fields:
      placeholder: '@nav_risk'
      language: '@language_en_gb'
      value: Risk
  -
    fields:
      placeholder: '@nav_risks'
      language: '@language_en_gb'
      value: Risks
  -
    fields:
      placeholder: '@nav_reportable_incidents'
      language: '@language_en_gb'
      value: Reportable Incident Briefs
  -
    fields:
      placeholder: '@nav_strategy'
      language: '@language_en_gb'
      value: Strategy
  -
    fields:
      placeholder: '@nav_clinical_audit'
      language: '@language_en_gb'
      value: 'Clinical Audit'
  -
    fields:
      placeholder: '@nav_clinical_audits'
      language: '@language_en_gb'
      value: 'Clinical Audits'
  -
    fields:
      placeholder: '@nav_controls_and_recommendations'
      language: '@language_en_gb'
      value: 'Recommendations & Controls'
  -
    fields:
      placeholder: '@nav_safety_learnings'
      language: '@language_en_gb'
      value: 'Safety Learnings'
  -
    fields:
      placeholder: '@nav_roi_assessment'
      language: '@language_en_gb'
      value: 'ROI Investment'
  -
    fields:
      placeholder: '@nav_implement'
      language: '@language_en_gb'
      value: Implement
  -
    fields:
      placeholder: '@nav_policies_and_guidelines'
      language: '@language_en_gb'
      value: 'Policies & Guidelines'
  -
    fields:
      placeholder: '@nav_assess'
      language: '@language_en_gb'
      value: Assess
  -
    fields:
      placeholder: '@nav_compliance_assessment'
      language: '@language_en_gb'
      value: 'Compliance Assessment'
  -
    fields:
      placeholder: '@nav_safety_rounds'
      language: '@language_en_gb'
      value: 'Safety Rounds'
  -
    fields:
      placeholder: '@nav_my_profile'
      language: '@language_en_gb'
      value: 'My Profile'
  -
    fields:
      placeholder: '@nav_my_todo'
      language: '@language_en_gb'
      value: 'To Do List'
  -
    fields:
      placeholder: '@nav_system_admin'
      language: '@language_en_gb'
      value: 'System Admin'
  -
    fields:
      placeholder: '@nav_my_settings'
      language: '@language_en_gb'
      value: 'My Settings'
  -
    fields:
      placeholder: '@nav_logout'
      language: '@language_en_gb'
      value: Logout
  -
    fields:
      placeholder: '@nav_login'
      language: '@language_en_gb'
      value: Login
  -
    fields:
      placeholder: '@nav_administration'
      language: '@language_en_gb'
      value: Administration
  -
    fields:
      placeholder: '@nav_acl_groups'
      language: '@language_en_gb'
      value: 'ACL Groups'
  -
    fields:
      placeholder: '@nav_acl_roles'
      language: '@language_en_gb'
      value: 'ACL Roles'
  -
    fields:
      placeholder: '@nav_acl_rules'
      language: '@language_en_gb'
      value: 'ACL Rules'
  -
    fields:
      placeholder: '@nav_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@nav_checklists'
      language: '@language_en_gb'
      value: Surveys
  -
    fields:
      placeholder: '@nav_contacts'
      language: '@language_en_gb'
      value: Contacts
  -
    fields:
      placeholder: '@nav_controls'
      language: '@language_en_gb'
      value: Controls
  -
    fields:
      placeholder: '@nav_equipment'
      language: '@language_en_gb'
      value: Equipment
  -
    fields:
      placeholder: '@nav_forms'
      language: '@language_en_gb'
      value: 'Form Designer'
  -
    fields:
      placeholder: '@system_admin_nav_config_portation'
      language: '@language_en_gb'
      value: 'Import/Export Configuration'
  -
    fields:
      placeholder: '@nav_form-fields'
      language: '@language_en_gb'
      value: 'Custom Fields'
  -
    fields:
      placeholder: '@nav_help'
      language: '@language_en_gb'
      value: Help
  -
    fields:
      placeholder: '@nav_resources_covid'
      language: '@language_en_gb'
      value: COVID-19 Resources
  -
    fields:
      placeholder: '@nav_locations'
      language: '@language_en_gb'
      value: Locations
  -
    fields:
      placeholder: '@nav_medications'
      language: '@language_en_gb'
      value: Medications
  -
    fields:
      placeholder: '@nav_hotspots'
      language: '@language_en_gb'
      value: Hotspots
  -
    fields:
      placeholder: '@nav_icon_wall'
      language: '@language_en_gb'
      value: Create New Record
  -
    fields:
      placeholder: '@nav_services'
      language: '@language_en_gb'
      value: Services
  -
    fields:
      placeholder: '@nav_users'
      language: '@language_en_gb'
      value: Users
  -
    fields:
      placeholder: '@nav_organisations'
      language: '@language_en_gb'
      value: Organisations
  -
    fields:
      placeholder: '@nav_insurance'
      language: '@language_en_gb'
      value: Insurance
  -
    fields:
      placeholder: '@nav_payments'
      language: '@language_en_gb'
      value: Payments
  -
    fields:
      placeholder: '@nav_actions_dashboard'
      language: '@language_en_gb'
      value: 'Actions Dashboard'
  -
    fields:
      placeholder: '@nav_distributions_lists'
      language: '@language_en_gb'
      value: Distribution Lists
  -
    fields:
      placeholder: '@nav_reporting'
      language: '@language_en_gb'
      value: Reporting
  -
    fields:
      placeholder: '@nav_capture_admin'
      language: '@language_en_gb'
      value: 'Capture Admin'
  -
    fields:
      placeholder: '@dashboard_singular'
      language: '@language_en_gb'
      value: 'Dashboard'
  -
    fields:
      placeholder: '@dashboard_plural'
      language: '@language_en_gb'
      value: 'Dashboards'
  -
    fields:
      placeholder: '@tables_no_results_found'
      language: '@language_en_gb'
      value: 'No Results Found'
  -
    fields:
      placeholder: '@tables_pagination_per_page'
      language: '@language_en_gb'
      value: 'Per page'
  -
    fields:
      placeholder: '@tables_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@tables_go_to'
      language: '@language_en_gb'
      value: 'Go To'
  -
    fields:
      placeholder: '@tables_view'
      language: '@language_en_gb'
      value: View
  -
    fields:
      placeholder: '@tables_edit'
      language: '@language_en_gb'
      value: Edit
  -
    fields:
      placeholder: '@common_n_a'
      language: '@language_en_gb'
      value: N/A
  -
    fields:
      placeholder: '@common_modules_erm'
      language: '@language_en_gb'
      value: ERM
  -
    fields:
      placeholder: '@common_modules_erm_risk'
      language: '@language_en_gb'
      value: 'ERM - Risk'
  -
    fields:
      placeholder: '@common_modules_erm_register'
      language: '@language_en_gb'
      value: 'ERM - Register'
  -
    fields:
      placeholder: '@common_modules_erm_tracker'
      language: '@language_en_gb'
      value: 'ERM - Tracker'
  -
    fields:
      placeholder: '@common_modules_investigations'
      language: '@language_en_gb'
      value: Investigations
  -
    fields:
      placeholder: '@common_modules_investigation'
      language: '@language_en_gb'
      value: Investigation
  -
    fields:
      placeholder: '@common_modules_surveys'
      language: '@language_en_gb'
      value: Surveys
  -
    fields:
      placeholder: '@common_modules_survey'
      language: '@language_en_gb'
      value: Survey
  -
    fields:
      placeholder: '@common_modules_clinical_audit'
      language: '@language_en_gb'
      value: 'Clinical Audit'
  -
    fields:
      placeholder: '@common_modules_controls'
      language: '@language_en_gb'
      value: Controls
  -
    fields:
      placeholder: '@common_module_control'
      language: '@language_en_gb'
      value: Control
  -
    fields:
      placeholder: '@common_modules_recommendation'
      language: '@language_en_gb'
      value: Recommendation
  -
    fields:
      placeholder: '@common_modules_recommendations'
      language: '@language_en_gb'
      value: Recommendations
  -
    fields:
      placeholder: '@common_modules_compliance_assessment'
      language: '@language_en_gb'
      value: 'Compliance Assessment'
  -
    fields:
      placeholder: '@common_modules_compliance_assessment_programme'
      language: '@language_en_gb'
      value: 'Compliance Assessment - Programme'
  -
    fields:
      placeholder: '@common_modules_compliance_assessment_standard'
      language: '@language_en_gb'
      value: 'Compliance Assessment - Standard'
  -
    fields:
      placeholder: '@common_modules_compliance_assessment_assessment'
      language: '@language_en_gb'
      value: 'Compliance Assessment - Assessment'
  -
    fields:
      placeholder: '@common_modules_compliance_assessment_response'
      language: '@language_en_gb'
      value: 'Compliance Assessment - Response'
  -
    fields:
      placeholder: '@common_modules_safety_rounds'
      language: '@language_en_gb'
      value: 'Safety Rounds'
  -
    fields:
      placeholder: '@common_modules_safety_round'
      language: '@language_en_gb'
      value: 'Safety Round'
  -
    fields:
      placeholder: '@common_modules_safety_rounds_template'
      language: '@language_en_gb'
      value: 'Safety Rounds - Template'
  -
    fields:
      placeholder: '@common_modules_safety_rounds_response'
      language: '@language_en_gb'
      value: 'Safety Rounds - Response'
  -
    fields:
      placeholder: '@common_modules_safety_rounds_summary'
      language: '@language_en_gb'
      value: 'Safety Rounds - Summary'
  -
    fields:
      placeholder: '@common_modules_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@common_modules_acl'
      language: '@language_en_gb'
      value: ACL
  -
    fields:
      placeholder: '@common_modules_acl_rule'
      language: '@language_en_gb'
      value: 'ACL - Rule'
  -
    fields:
      placeholder: '@common_modules_acl_role'
      language: '@language_en_gb'
      value: 'ACL - Role'
  -
    fields:
      placeholder: '@common_modules_contacts'
      language: '@language_en_gb'
      value: Contacts
  -
    fields:
      placeholder: '@common_modules_contact'
      language: '@language_en_gb'
      value: Contact
  -
    fields:
      placeholder: '@common_modules_equipment'
      language: '@language_en_gb'
      value: Equipment
  -
    fields:
      placeholder: '@common_modules_forms'
      language: '@language_en_gb'
      value: Forms
  -
    fields:
      placeholder: '@common_modules_form'
      language: '@language_en_gb'
      value: Form
  -
    fields:
      placeholder: '@common_modules_form_field'
      language: '@language_en_gb'
      value: 'Form Field'
  -
    fields:
      placeholder: '@common_modules_locations'
      language: '@language_en_gb'
      value: Locations
  -
    fields:
      placeholder: '@common_modules_location'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@common_modules_medications'
      language: '@language_en_gb'
      value: Medications
  -
    fields:
      placeholder: '@common_modules_medication'
      language: '@language_en_gb'
      value: Medication
  -
    fields:
      placeholder: '@common_modules_services'
      language: '@language_en_gb'
      value: Services
  -
    fields:
      placeholder: '@common_modules_service'
      language: '@language_en_gb'
      value: Service
  -
    fields:
      placeholder: '@common_modules_users'
      language: '@language_en_gb'
      value: Users
  -
    fields:
      placeholder: '@common_modules_user'
      language: '@language_en_gb'
      value: User
  -
    fields:
      placeholder: '@common_modules_user_group'
      language: '@language_en_gb'
      value: 'User Group'
  -
    fields:
      placeholder: '@common_modules_prince'
      language: '@language_en_gb'
      value: Prince
  -
    fields:
      placeholder: '@common_modules_claim'
      language: '@language_en_gb'
      value: Claim
  -
    fields:
      placeholder: '@common_modules_feedback'
      language: '@language_en_gb'
      value: Feedback
  -
    fields:
      placeholder: '@common_modules_incident'
      language: '@language_en_gb'
      value: Incident
  -
    fields:
      placeholder: '@common_modules_mortality'
      language: '@language_en_gb'
      value: Mortality
  -
    fields:
      placeholder: '@common_select_an_option'
      language: '@language_en_gb'
      value: 'Select an Option'
  -
    fields:
      placeholder: '@common_search_or_select_option'
      language: '@language_en_gb'
      value: 'Search for or select an option'
  -
    fields:
      placeholder: '@common_help'
      language: '@language_en_gb'
      value: Help
  -
    fields:
      placeholder: '@common_components_date_filter_start_date'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@common_components_date_filter_end_date'
      language: '@language_en_gb'
      value: 'End Date'
  -
    fields:
      placeholder: '@common_components_feedback_button'
      language: '@language_en_gb'
      value: 'Give Feedback'
  -
    fields:
      placeholder: '@common_components_feedback_rate'
      language: '@language_en_gb'
      value: 'How would you rate this page?'
  -
    fields:
      placeholder: '@common_components_share'
      language: '@language_en_gb'
      value: 'What would you like to share with us?'
  -
    fields:
      placeholder: '@common_components_feedback_save_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the feedback'
  -
    fields:
      placeholder: '@common_components_feedback_submitted'
      language: '@language_en_gb'
      value: Submitted
  -
    fields:
      placeholder: '@common_components_feedback_thank_you'
      language: '@language_en_gb'
      value: 'Thank you for your feedback!'
  -
    fields:
      placeholder: '@common_submit'
      language: '@language_en_gb'
      value: Submit
  -
    fields:
      placeholder: '@common_are_you_sure'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@common_back'
      language: '@language_en_gb'
      value: Back
  -
    fields:
      placeholder: '@nav_back_to_admin'
      language: '@language_en_gb'
      value: 'Back to Admin'
  -
    fields:
      placeholder: '@nav_back_to_control'
      language: '@language_en_gb'
      value: 'Back to Control'
  -
    fields:
      placeholder: '@incident_singular'
      language: '@language_en_gb'
      value: Incident
  -
    fields:
      placeholder: '@incident_plural'
      language: '@language_en_gb'
      value: Incidents
  -
    fields:
      placeholder: '@claim_singular'
      language: '@language_en_gb'
      value: Claims
  -
    fields:
      placeholder: '@claim_plural'
      language: '@language_en_gb'
      value: Claims
  -
    fields:
      placeholder: '@complaint_singular'
      language: '@language_en_gb'
      value: Feedback
  -
    fields:
      placeholder: '@complaint_plural'
      language: '@language_en_gb'
      value: Feedback
  -
    fields:
      placeholder: '@mortality_singular'
      language: '@language_en_gb'
      value: 'Mortality Review'
  -
    fields:
      placeholder: '@mortality_plural'
      language: '@language_en_gb'
      value: 'Mortality Reviews'
  -
    fields:
      placeholder: '@common_errors_missing_fields'
      language: '@language_en_gb'
      value: 'Please ensure all required fields have been provided'
  -
    fields:
      placeholder: '@common_errors_get_form'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the form'
  -
    fields:
      placeholder: '@common_errors_forbidden'
      language: '@language_en_gb'
      value: Forbidden
  -
    fields:
      placeholder: '@common_errors_entity_forbidden_entity'
      language: '@language_en_gb'
      value: 'Forbidden entity'
  -
    fields:
      placeholder: '@common_errors_resource_not_found'
      language: '@language_en_gb'
      value: 'Resource not found'
  -
    fields:
      placeholder: '@common_errors_entity_not_found'
      language: '@language_en_gb'
      value: 'Entity not found'
  -
    fields:
      placeholder: '@sidebar_back_to'
      language: '@language_en_gb'
      value: 'Back To'
  -
    fields:
      placeholder: '@loading'
      language: '@language_en_gb'
      value: Loading...
  -
    fields:
      placeholder: '@components_notes'
      language: '@language_en_gb'
      value: Notes
  -
    fields:
      placeholder: '@components_notes_new_note'
      language: '@language_en_gb'
      value: 'New Note'
  -
    fields:
      placeholder: '@components_notes_add_a_note'
      language: '@language_en_gb'
      value: 'Write a Note'
  -
    fields:
      placeholder: '@components_notes_edit_note'
      language: '@language_en_gb'
      value: 'Edit Note'
  -
    fields:
      placeholder: '@components_notes_from'
      language: '@language_en_gb'
      value: From
  -
    fields:
      placeholder: '@common_on'
      language: '@language_en_gb'
      value: 'on'
  -
    fields:
      placeholder: '@common_inactive'
      language: '@language_en_gb'
      value: 'Inactive'
  -
    fields:
      placeholder: '@common_active'
      language: '@language_en_gb'
      value: 'Active'
  -
    fields:
      placeholder: '@common_deleted'
      language: '@language_en_gb'
      value: 'Deleted'
  -
    fields:
      placeholder: '@components_notes_hide_responses'
      language: '@language_en_gb'
      value: 'Hide Responses'
  -
    fields:
      placeholder: '@components_notes_show_responses'
      language: '@language_en_gb'
      value: 'Show Responses'
  -
    fields:
      placeholder: '@components_notes_reply'
      language: '@language_en_gb'
      value: 'Write a response'
  -
    fields:
      placeholder: '@components_notes_delete_option_confirmation'
      language: '@language_en_gb'
      value: 'Are you sure you want to delete this Note?'
  -
    fields:
      placeholder: '@components_confirm_button'
      language: '@language_en_gb'
      value: 'Confirm Button'
  -
    fields:
      placeholder: '@components_confirm_button_text'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@components_recommendations_singular'
      language: '@language_en_gb'
      value: Recommendation
  -
    fields:
      placeholder: '@components_recommendations_plural'
      language: '@language_en_gb'
      value: Recommendations
  -
    fields:
      placeholder: '@components_recommendations_create'
      language: '@language_en_gb'
      value: 'Create Recommendation'
  -
    fields:
      placeholder: '@components_recommendations_search'
      language: '@language_en_gb'
      value: 'Search Recommendations'
  -
    fields:
      placeholder: '@components_recommendations_edit'
      language: '@language_en_gb'
      value: 'Edit Recommendation'
  -
    fields:
      placeholder: '@components_recommendations_table_column_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@components_recommendations_table_column_statement_of_intent'
      language: '@language_en_gb'
      value: 'Statement of Intent'
  -
    fields:
      placeholder: '@components_recommendations_save'
      language: '@language_en_gb'
      value: Save
  -
    fields:
      placeholder: '@components_recommendations_loading'
      language: '@language_en_gb'
      value: 'Loading Recommendations'
  -
    fields:
      placeholder: '@components_recommendations_new'
      language: '@language_en_gb'
      value: 'New Recommendation'
  -
    fields:
      placeholder: '@components_recommendations_controls'
      language: '@language_en_gb'
      value: Controls
  -
    fields:
      placeholder: '@components_recommendations_contributory_factor'
      language: '@language_en_gb'
      value: 'Contributory Factors'
  -
    fields:
      placeholder: '@components_recommendations_loading_controls'
      language: '@language_en_gb'
      value: 'Loading Controls'
  -
    fields:
      placeholder: '@common_components_magma_relationship_loading'
      language: '@language_en_gb'
      value: Loading
  -
    fields:
      placeholder: '@common_components_magma_relationship_create'
      language: '@language_en_gb'
      value: Create
  -
    fields:
      placeholder: '@common_components_magma_relationship_save'
      language: '@language_en_gb'
      value: Save
  -
    fields:
      placeholder: '@common_components_magma_relationship_cancel'
      language: '@language_en_gb'
      value: Cancel
  -
    fields:
      placeholder: '@common_components_magma_relationship_saving'
      language: '@language_en_gb'
      value: Saving
  -
    fields:
      placeholder: '@common_components_magma_relationship_close_search'
      language: '@language_en_gb'
      value: 'Close Search'
  -
    fields:
      placeholder: '@components_magma_table'
      language: '@language_en_gb'
      value: 'Magma Table'
  -
    fields:
      placeholder: '@components_magma_table_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@erm_admin_organisational_objectives_new_objective'
      language: '@language_en_gb'
      value: 'New Objective'
  -
    fields:
      placeholder: '@erm_risk_filters_risk_review_date'
      language: '@language_en_gb'
      value: 'Risk Review Date'
  -
    fields:
      placeholder: '@erm_risk_filters_risk_review_date_label'
      language: '@language_en_gb'
      value: 'Risk Review Date'
  -
    fields:
      placeholder: '@erm_risk_filters_risk_open_date'
      language: '@language_en_gb'
      value: 'Risk Open Date'
  -
    fields:
      placeholder: '@erm_risk_filters_risk_closed_date'
      language: '@language_en_gb'
      value: 'Risk Closed Date'
  -
    fields:
      placeholder: '@erm_risk_filters_risk_closed_date_label'
      language: '@language_en_gb'
      value: 'Risk Closed Date'
  -
    fields:
      placeholder: '@erm_risk_filters_current_rating'
      language: '@language_en_gb'
      value: 'Current Rating'
  -
    fields:
      placeholder: '@erm_risk_register_filters_heading'
      language: '@language_en_gb'
      value: 'Filter Risk Registers'
  -
    fields:
      placeholder: '@erm_risk_register_filters_title'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@erm_risk_register_filters_id'
      language: '@language_en_gb'
      value: 'Register ID'
  -
    fields:
      placeholder: '@erm_risk_register_filters_active'
      language: '@language_en_gb'
      value: 'Status'
  -
    fields:
      placeholder: '@investigations_loading_objectives'
      language: '@language_en_gb'
      value: 'Loading Objectives'
  -
    fields:
      placeholder: '@investigations_nav_actions_my_actions'
      language: '@language_en_gb'
      value: 'My Actions'
  -
    fields:
      placeholder: '@investigations_nav_actions_all_actions'
      language: '@language_en_gb'
      value: 'All Actions'
  -
    fields:
      placeholder: '@contacts_related_primary_modules'
      language: '@language_en_gb'
      value: 'Related Primary Modules'
  -
    fields:
      placeholder: '@common_loading_permissions'
      language: '@language_en_gb'
      value: 'Loading Permissions'
  -
    fields:
      placeholder: '@contacts_edit_contact'
      language: '@language_en_gb'
      value: 'Edit Contact'
  -
    fields:
      placeholder: '@contacts_view_contact'
      language: '@language_en_gb'
      value: 'View Contact'
  -
    fields:
      placeholder: '@controls_controls_in_place'
      language: '@language_en_gb'
      value: 'Controls In Place'
  -
    fields:
      placeholder: '@controls_gaps_in_controls'
      language: '@language_en_gb'
      value: 'Gaps In Controls'
  -
    fields:
      placeholder: '@actions_record_add_action_plan_to_record'
      language: '@language_en_gb'
      value: 'Add Action Plan to Record'
  -
    fields:
      placeholder: '@actions_admin_add_action_update'
      language: '@language_en_gb'
      value: 'Update Action on Plan'
  -
    fields:
      placeholder: '@common_saved_successfully'
      language: '@language_en_gb'
      value: 'Saved successfully'
  -
    fields:
      placeholder: '@common_unauthorised'
      language: '@language_en_gb'
      value: 'Unauthorised'
  -
    fields:
      placeholder: '@common_unauthorised_page_access'
      language: '@language_en_gb'
      value: 'You do not have permission to access that page'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_human_and_relational'
      language: '@language_en_gb'
      value: 'Human and relational factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_patient_and_family'
      language: '@language_en_gb'
      value: 'Patient and family factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_communication'
      language: '@language_en_gb'
      value: 'Communication'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_consent'
      language: '@language_en_gb'
      value: 'Consent'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_incapacity'
      language: '@language_en_gb'
      value: 'Incapacity'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_religious_beliefs'
      language: '@language_en_gb'
      value: 'Religious or cultural beliefs'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_non_compliance'
      language: '@language_en_gb'
      value: 'Non-compliance'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_abnormal_physiology'
      language: '@language_en_gb'
      value: 'Abnormal physiology'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_complexity'
      language: '@language_en_gb'
      value: 'Complexity'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_social_support'
      language: '@language_en_gb'
      value: 'Social support'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_aggression'
      language: '@language_en_gb'
      value: 'Aggression'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_other'
      language: '@language_en_gb'
      value: 'Other'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_individual_and_personal'
      language: '@language_en_gb'
      value: 'Individual and personal factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_skill'
      language: '@language_en_gb'
      value: 'Skill'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_knowledge'
      language: '@language_en_gb'
      value: 'Knowledge'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_experience'
      language: '@language_en_gb'
      value: 'Experience'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_fatigue'
      language: '@language_en_gb'
      value: 'Fatigue'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_attitudes'
      language: '@language_en_gb'
      value: 'Attitudes'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_stress'
      language: '@language_en_gb'
      value: 'Stress'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_morale'
      language: '@language_en_gb'
      value: 'Morale and motivation'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_health'
      language: '@language_en_gb'
      value: 'Health condition'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_temporary_impairment'
      language: '@language_en_gb'
      value: 'Temporary impairment'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_alcohol_or_drugs'
      language: '@language_en_gb'
      value: 'Alcohol or drugs'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_personal_domestic_issues'
      language: '@language_en_gb'
      value: 'Personal or domestic issues'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_teamwork_leadership_supervision'
      language: '@language_en_gb'
      value: 'Teamwork, leadership and supervision'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_supervision'
      language: '@language_en_gb'
      value: 'Supervision'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_staff_support'
      language: '@language_en_gb'
      value: 'Support for staff'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_shared_understanding'
      language: '@language_en_gb'
      value: 'Shared understanding and awareness'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_coordination'
      language: '@language_en_gb'
      value: 'Coordination'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_local_leadership'
      language: '@language_en_gb'
      value: 'Local leadership'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_psychological_safety'
      language: '@language_en_gb'
      value: 'Psychological safety and openness'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_mutual_respect'
      language: '@language_en_gb'
      value: 'Mutual respect'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_effective_communication'
      language: '@language_en_gb'
      value: 'Effective communication'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_training_education_assessment'
      language: '@language_en_gb'
      value: 'Training, education and assessment'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_appropriate_training'
      language: '@language_en_gb'
      value: 'Appropriate training'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_training_availability'
      language: '@language_en_gb'
      value: 'Availability of training'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_training_monitoring'
      language: '@language_en_gb'
      value: 'Monitoring of training'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_education_quality'
      language: '@language_en_gb'
      value: 'Quality of education'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_assessment_and_qualification'
      language: '@language_en_gb'
      value: 'Assessment and qualification'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_team_training'
      language: '@language_en_gb'
      value: 'Team training'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_simulation_training'
      language: '@language_en_gb'
      value: 'Simulation training'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_revalidation_and_competency'
      language: '@language_en_gb'
      value: 'Revalidation and competency'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_induction'
      language: '@language_en_gb'
      value: 'Induction and familiarisation'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_task_and_environmental'
      language: '@language_en_gb'
      value: 'Task and environmental factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_task_demands'
      language: '@language_en_gb'
      value: 'Task demands and workload'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_time_of_day'
      language: '@language_en_gb'
      value: 'Time of day'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_distractions'
      language: '@language_en_gb'
      value: 'Distractions and interruptions'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_high_workload'
      language: '@language_en_gb'
      value: 'High workload'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_low_workload'
      language: '@language_en_gb'
      value: 'Low workload'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_time_availability'
      language: '@language_en_gb'
      value: 'Time availability'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_task_complexity'
      language: '@language_en_gb'
      value: 'Task complexity'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_production_pressure'
      language: '@language_en_gb'
      value: 'Production pressure'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_task_practicality'
      language: '@language_en_gb'
      value: 'Task practicality'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_task_difficulty'
      language: '@language_en_gb'
      value: 'Task difficulty'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_conflicting_tasks'
      language: '@language_en_gb'
      value: 'Conflicting tasks'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_uncertainty_and_variability'
      language: '@language_en_gb'
      value: 'Uncertainty and variability'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_physical_work_environment'
      language: '@language_en_gb'
      value: 'Physical work environment'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_housekeeping'
      language: '@language_en_gb'
      value: 'Housekeeping'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_lighting'
      language: '@language_en_gb'
      value: 'Lighting'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_noise'
      language: '@language_en_gb'
      value: 'Noise'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_temperature'
      language: '@language_en_gb'
      value: 'Temperature'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_layout_and_design'
      language: '@language_en_gb'
      value: 'Layout and design'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_accessibility'
      language: '@language_en_gb'
      value: 'Accessibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_security'
      language: '@language_en_gb'
      value: 'Security'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_visibility'
      language: '@language_en_gb'
      value: 'Visibility and lines of sight'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_fixtures_and_fittings'
      language: '@language_en_gb'
      value: 'Fixtures and fittings'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_maintenance'
      language: '@language_en_gb'
      value: 'Maintenance'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_procedures_protocols_and_guidelines'
      language: '@language_en_gb'
      value: 'Procedures, protocols and guidelines'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_adequacy_and_accuracy'
      language: '@language_en_gb'
      value: 'Adequacy and accuracy'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_availability_and_accessibility'
      language: '@language_en_gb'
      value: 'Availability and accessibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_currency'
      language: '@language_en_gb'
      value: 'Currency'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_clarity_and_coherence'
      language: '@language_en_gb'
      value: 'Clarity and coherence'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_practicality'
      language: '@language_en_gb'
      value: 'Practicality'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_complexity'
      language: '@language_en_gb'
      value: 'Complexity'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_implementation_and_usage'
      language: '@language_en_gb'
      value: 'Implementation and usage'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_acceptability_and_agreement'
      language: '@language_en_gb'
      value: 'Acceptability and agreement'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tools_equipment_and_resources'
      language: '@language_en_gb'
      value: 'Tools, equipment and resources'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_alarms_and_warnings'
      language: '@language_en_gb'
      value: 'Alarms and warnings'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_displays_and_interfaces'
      language: '@language_en_gb'
      value: 'Displays and interfaces'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_functionality'
      language: '@language_en_gb'
      value: 'Functionality'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_reliability'
      language: '@language_en_gb'
      value: 'Reliability'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_availability'
      language: '@language_en_gb'
      value: 'Accessibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_design'
      language: '@language_en_gb'
      value: 'Design'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_installation'
      language: '@language_en_gb'
      value: 'Installation'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_maintenance'
      language: '@language_en_gb'
      value: 'Maintenance'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_compatibility'
      language: '@language_en_gb'
      value: 'Compatibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_tool_packaging'
      language: '@language_en_gb'
      value: 'Packaging'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_quantity_and_quality'
      language: '@language_en_gb'
      value: 'Quantity and quality'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_organisational_and_cultural_factors'
      language: '@language_en_gb'
      value: 'Organisational and cultural factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_social_and_cultural'
      language: '@language_en_gb'
      value: 'Social and cultural context'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_norms_and_customs'
      language: '@language_en_gb'
      value: 'Norms and customs'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_values_and_assumptions'
      language: '@language_en_gb'
      value: 'Values and assumptions'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_social_pressure'
      language: '@language_en_gb'
      value: 'Social pressure'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_diffusion_of_responsibility'
      language: '@language_en_gb'
      value: 'Diffusion of responsibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_rule_behaviour'
      language: '@language_en_gb'
      value: 'Rule-related behaviour'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_risk_perception_and_tolerance'
      language: '@language_en_gb'
      value: 'Risk perception and tolerance'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_honesty'
      language: '@language_en_gb'
      value: 'Openness and honesty'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_senior_leadership_commitment'
      language: '@language_en_gb'
      value: 'Senior leadership commitment'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_blame_and_fear'
      language: '@language_en_gb'
      value: 'Blame and fear'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_organisation_governance_and_strategy'
      language: '@language_en_gb'
      value: 'Organisation, governance and strategy'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_lines_of_responsibility'
      language: '@language_en_gb'
      value: 'Lines of responsibility'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_strategic_leadership'
      language: '@language_en_gb'
      value: 'Strategic leadership'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_service_planning'
      language: '@language_en_gb'
      value: 'Service planning and design'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_organisational_structure'
      language: '@language_en_gb'
      value: 'Organisational structure'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_risk_management_and_learning'
      language: '@language_en_gb'
      value: 'Risk management and learning'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_oversight_and_monitoring'
      language: '@language_en_gb'
      value: 'Oversight and monitoring'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_resource_management'
      language: '@language_en_gb'
      value: 'Resource management'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_operational_and_people_management'
      language: '@language_en_gb'
      value: 'Operational and people management'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_bed_management'
      language: '@language_en_gb'
      value: 'Bed management and flow'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_skill_mix'
      language: '@language_en_gb'
      value: 'Skill mix'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_staffing_levels'
      language: '@language_en_gb'
      value: 'Staffing levels'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_rota_and_shift_planning'
      language: '@language_en_gb'
      value: 'Rota and shift planning'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_staff_turnover'
      language: '@language_en_gb'
      value: 'Staff turnover'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_job_roles_and_design'
      language: '@language_en_gb'
      value: 'Job roles and design'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_recruitment_and_selection'
      language: '@language_en_gb'
      value: 'Recruitment and selection'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_administration'
      language: '@language_en_gb'
      value: 'Administration'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_sanctions_and_rewards'
      language: '@language_en_gb'
      value: 'Sanctions and rewards'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_purchasing_and_procurement'
      language: '@language_en_gb'
      value: 'Purchasing and procurement'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_information_and_communication_systems'
      language: '@language_en_gb'
      value: 'Information and communication systems'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_verbal_communication'
      language: '@language_en_gb'
      value: 'Verbal communication'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_documentation_and_record_keeping'
      language: '@language_en_gb'
      value: 'Documentation and record keeping'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_handover_and_briefing'
      language: '@language_en_gb'
      value: 'Handover and briefing'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_electronic_records_and_it'
      language: '@language_en_gb'
      value: 'Electronic records and IT systems'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_information_accuracy'
      language: '@language_en_gb'
      value: 'Information accuracy'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_information_clarity_and_ambiguity'
      language: '@language_en_gb'
      value: 'Information clarity and ambiguity'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_external_and_regulatory_factors'
      language: '@language_en_gb'
      value: 'External and regulatory factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_regulatory_policy_and_external'
      language: '@language_en_gb'
      value: 'Regulatory, policy and external factors'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_national_policy_and_standards'
      language: '@language_en_gb'
      value: 'National policy and standards'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_commissioning_and_contracting'
      language: '@language_en_gb'
      value: 'Commissioning and contracting'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_funding_and_resources'
      language: '@language_en_gb'
      value: 'Funding and resources'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_regulatory_activities'
      language: '@language_en_gb'
      value: 'Regulatory activities'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_other_external_events'
      language: '@language_en_gb'
      value: 'Other external events'

  -
    fields:
      placeholder: '@components_contributory_factors_classification_guidance_not_followed'
      language: '@language_en_gb'
      value: 'Guidance Not Followed'

  -
    fields:
      placeholder: '@welcome_page_title'
      language: '@language_en_gb'
      value: 'Welcome to Datix Cloud IQ'
  -
    fields:
      placeholder: '@untitled_section'
      language: '@language_en_gb'
      value: 'Untitled Section'

  -
    fields:
      placeholder: '@language_label_en'
      language: '@language_en_gb'
      value: 'English (United Kingdom)'
  -
    fields:
      placeholder: '@language_label_ar'
      language: '@language_en_gb'
      value: 'Arabic'
  - fields:
      placeholder: '@language_label_us'
      language: '@language_en_gb'
      value: 'English (United States)'
  - fields:
      placeholder: '@datasource_language'
      language: '@language_en_gb'
      value: 'Language'
  - fields:
      placeholder: '@datasource_religion'
      language: '@language_en_gb'
      value: 'Religion'

  - fields:
      placeholder: '@language_label_de_ch'
      language: '@language_en_gb'
      value: 'Swiss German'

  - fields:
      placeholder: '@splash_page_title'
      language: '@language_en_gb'
      value: 'Page'
  - fields:
      placeholder: '@common_node_list_no_other_results'
      language: '@language_en_gb'
      value: 'No Other Results'
  - fields:
      placeholder: '@common_other_results'
      language: '@language_en_gb'
      value: 'Other Results ({{numResults}})'
  - fields:
      placeholder: '@common_node_list_view_children'
      language: '@language_en_gb'
      value: 'View {{numChildren}} Children'
  - fields:
      placeholder: '@common_success_save'
      language: '@language_en_gb'
      value: '{{singularLabel}} saved successfully'
  - fields:
      placeholder: '@common_error_save'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the {{singularLabel}}'
  - fields:
      placeholder: '@common_success_remove'
      language: '@language_en_gb'
      value: '{{singularLabel}} removed successfully'
  - fields:
      placeholder: '@common_error_remove'
      language: '@language_en_gb'
      value: 'Error removing {{singularLabel}}'
  - fields:
      placeholder: '@common_post_save_no_access'
      language: '@language_en_gb'
      value: 'Record has been saved, but the access permissions assigned to your user do not permit you to see it'
  - fields:
      placeholder: '@common_form_required_fields'
      language: '@language_en_gb'
      value: 'Required fields'
  - fields:
      placeholder: '@common_value_not_set'
      language: '@language_en_gb'
      value: 'No value has been set'
  - fields:
      placeholder: '@common_loading_options'
      language: '@language_en_gb'
      value: 'Loading Options...'
  - fields:
      placeholder: '@common_please_select_option'
      language: '@language_en_gb'
      value: 'Please select...'
  - fields:
      placeholder: '@common_no_options_available'
      language: '@language_en_gb'
      value: 'No options available'
  - fields:
      placeholder: '@common_locale'
      language: '@language_en_gb'
      value: 'Locale'
  - fields:
      placeholder: '@common_select_locale'
      language: '@language_en_gb'
      value: 'Select Locale'
  - fields:
      placeholder: '@common_components_templates'
      language: '@language_en_gb'
      value: 'Templates'
  - fields:
      placeholder: '@common_components_label_template'
      language: '@language_en_gb'
      value: 'Template'
  - fields:
      placeholder: '@common_components_templates_new'
      language: '@language_en_gb'
      value: 'Create New Document'
  - fields:
      placeholder: '@common_components_templates_pdf'
      language: '@language_en_gb'
      value: 'Create New PDF Document'
  - fields:
      placeholder: '@common_components_templates_docx'
      language: '@language_en_gb'
      value: 'Create New Word Document'
  - fields:
      placeholder: '@common_components_templates_close'
      language: '@language_en_gb'
      value: 'Close'
  - fields:
      placeholder: '@common_components_templates_title'
      language: '@language_en_gb'
      value: 'Templates'
  - fields:
      placeholder: '@common_all_modules'
      language: '@language_en_gb'
      value: 'All Modules'
  - fields:
      placeholder: '@common_form_time_is_not_known'
      language: '@language_en_gb'
      value: 'Time is not known'
  - fields:
      placeholder: '@common_form_time'
      language: '@language_en_gb'
      value: 'Time'
  - fields:
      placeholder: '@common_form_start_time'
      language: '@language_en_gb'
      value: 'Start Time'
  - fields:
      placeholder: '@common_form_end_time'
      language: '@language_en_gb'
      value: 'End Time'
  - fields:
      placeholder: '@common_permission_denied'
      language: '@language_en_gb'
      value: 'Permission Denied'
  - fields:
      placeholder: '@common_conflict_occurred'
      language: '@language_en_gb'
      value: 'Conflict Occurred'
  - fields:
      placeholder: '@common_employee_statuses_label'
      language: '@language_en_gb'
      value: 'Employee Status'
  - fields:
      placeholder: '@common_employee_statuses_active'
      language: '@language_en_gb'
      value: 'Active'
  - fields:
      placeholder: '@common_employee_statuses_terminated'
      language: '@language_en_gb'
      value: 'Terminated'
  - fields:
      placeholder: '@common_employee_statuses_not_started'
      language: '@language_en_gb'
      value: 'Not Started'
  - fields:
      placeholder: '@common_positions_no_positions'
      language: '@language_en_gb'
      value: 'No positions selected'
  - fields:
      placeholder: '@common_exception_investigation_not_found'
      language: '@language_en_gb'
      value: 'Investigation not found'
  - fields:
      placeholder: '@common_exception_maintenance_mode_required'
      language: '@language_en_gb'
      value: 'Maintenance Mode must be enabled to use this feature'
  - fields:
      placeholder: '@common_exception_unknown_template_reference'
      language: '@language_en_gb'
      value: 'Unknown Template Reference'
  - fields:
      placeholder: '@common_exception_template_not_found'
      language: '@language_en_gb'
      value: 'Template not found'
  - fields:
      placeholder: '@common_exception_document_was_not_created_successfully'
      language: '@language_en_gb'
      value: 'Document was not created successfully'
  - fields:
      placeholder: '@common_record_search_no_value_selected'
      language: '@language_en_gb'
      value: 'No value selected'
  - fields:
      placeholder: '@common_errors_field_exceeds_max_length'
      language: '@language_en_gb'
      value: '{{field}} exceeds max length ({{maxLength}})'
  - fields:
      placeholder: '@common_error_types_forbidden'
      language: '@language_en_gb'
      value: 'Action Forbidden'
  - fields:
      placeholder: '@common_error_see_the_errors_attribute_for_specific_failures'
      language: '@language_en_gb'
      value: 'Please see the errors attribute for specific failures'
  - fields:
      placeholder: '@common_actions_next'
      language: '@language_en_gb'
      value: 'Next'
  - fields:
      placeholder: '@common_actions_previous'
      language: '@language_en_gb'
      value: 'Previous'
  - fields:
      placeholder: '@common_exception_template_invalid_target_type'
      language: '@language_en_gb'
      value: 'Template Type was not found'
  - fields:
      placeholder: '@common_approved'
      language: '@language_en_gb'
      value: 'Approved'
  - fields:
      placeholder: '@common_rejected'
      language: '@language_en_gb'
      value: 'Rejected'
  - fields:
      placeholder: '@common_document_page_no_of_pages'
      language: '@language_en_gb'
      value: 'Page {PAGENO} of {nb}'
  - fields:
      placeholder: '@common_exception_template_invalid_document_type'
      language: '@language_en_gb'
      value: 'Invalid Document Type'
  - fields:
      placeholder: '@common_form_validation_number_value_min'
      language: '@language_en_gb'
      value: 'This field has a minimum value of {{min}}'
  - fields:
      placeholder: '@common_form_validation_number_value_max'
      language: '@language_en_gb'
      value: 'This field has a maximum value of {{max}}'
  - fields:
      placeholder: '@common_form_validation_number_value_digits'
      language: '@language_en_gb'
      value: 'This field must only contain digits'
  - fields:
      placeholder: '@common_form_validation_range_values_invalid'
      language: '@language_en_gb'
      value: 'The Minimum value cannot be larger than the Maximum'
  - fields:
      placeholder: '@audit_actions_record_created'
      language: '@language_en_gb'
      value: 'On {{date}}, {{user}} created this record with the following values'
  - fields:
      placeholder: '@audit_actions_record_updated'
      language: '@language_en_gb'
      value: 'On {{date}}, {{user}} updated this record with the following values'
  - fields:
      placeholder: '@audit_actions_record_attached'
      language: '@language_en_gb'
      value: 'On {{date}}, {{user}} attached the following record'
  - fields:
      placeholder: '@audit_actions_record_detached'
      language: '@language_en_gb'
      value: 'On {{date}}, {{user}} detached the following record'
  - fields:
      placeholder: '@audit_no_logs_available'
      language: '@language_en_gb'
      value: 'Audit logs cannot be displayed for this record. To access this data, please contact an administrator.'
  - fields:
      placeholder: '@common_upload_file'
      language: '@language_en_gb'
      value: 'Upload File'
  - fields:
      placeholder: '@common_upload_template'
      language: '@language_en_gb'
      value: 'Upload Template'
  - fields:
      placeholder: '@common_file_upload_percent_complete'
      language: '@language_en_gb'
      value: 'Percent Complete'
  - fields:
      placeholder: '@common_file_upload_cancel'
      language: '@language_en_gb'
      value: 'Cancel'
  - fields:
      placeholder: '@common_file_upload_retry'
      language: '@language_en_gb'
      value: 'Retry'
  - fields:
      placeholder: '@common_file_upload_error'
      language: '@language_en_gb'
      value: 'There was an error uploading the file'
  - fields:
      placeholder: '@common_file_upload_replace_file'
      language: '@language_en_gb'
      value: 'Replace File'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_twelve'
      language: '@language_en_gb'
      value: 'UTC-12:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_eleven'
      language: '@language_en_gb'
      value: 'UTC-11:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_ten'
      language: '@language_en_gb'
      value: 'UTC-10:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_nine_half'
      language: '@language_en_gb'
      value: 'UTC-09:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_nine'
      language: '@language_en_gb'
      value: 'UTC-09:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_eight'
      language: '@language_en_gb'
      value: 'UTC-08:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_seven'
      language: '@language_en_gb'
      value: 'UTC-07:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_six'
      language: '@language_en_gb'
      value: 'UTC-06:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_five'
      language: '@language_en_gb'
      value: 'UTC-05:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_four'
      language: '@language_en_gb'
      value: 'UTC-04:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_three_half'
      language: '@language_en_gb'
      value: 'UTC-03:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_three'
      language: '@language_en_gb'
      value: 'UTC-03:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_two'
      language: '@language_en_gb'
      value: 'UTC-02:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_minus_one'
      language: '@language_en_gb'
      value: 'UTC-01:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_zero'
      language: '@language_en_gb'
      value: 'UTC±00:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_one'
      language: '@language_en_gb'
      value: 'UTC+01:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_two'
      language: '@language_en_gb'
      value: 'UTC+02:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_three'
      language: '@language_en_gb'
      value: 'UTC+03:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_three_half'
      language: '@language_en_gb'
      value: 'UTC+03:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_four'
      language: '@language_en_gb'
      value: 'UTC+04:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_four_half'
      language: '@language_en_gb'
      value: 'UTC+04:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_five'
      language: '@language_en_gb'
      value: 'UTC+05:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_five_half'
      language: '@language_en_gb'
      value: 'UTC+05:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_five_three_quarters'
      language: '@language_en_gb'
      value: 'UTC+05:45'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_six'
      language: '@language_en_gb'
      value: 'UTC+06:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_six_half'
      language: '@language_en_gb'
      value: 'UTC+06:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_seven'
      language: '@language_en_gb'
      value: 'UTC+07:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_eight'
      language: '@language_en_gb'
      value: 'UTC+08:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_eight_three_quarters'
      language: '@language_en_gb'
      value: 'UTC+08:45'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_nine'
      language: '@language_en_gb'
      value: 'UTC+09:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_nine_half'
      language: '@language_en_gb'
      value: 'UTC+09:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_ten'
      language: '@language_en_gb'
      value: 'UTC+10:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_ten_half'
      language: '@language_en_gb'
      value: 'UTC+10:30'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_eleven'
      language: '@language_en_gb'
      value: 'UTC+11:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_twelve'
      language: '@language_en_gb'
      value: 'UTC+12:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_twelve_three_quarters'
      language: '@language_en_gb'
      value: 'UTC+12:45'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_thirteen'
      language: '@language_en_gb'
      value: 'UTC+13:00'
  - fields:
      placeholder: '@placeholder_utc_offsets_plus_fourteen'
      language: '@language_en_gb'
      value: 'UTC+14:00'
  - fields:
      placeholder: '@common_exceptions_inactive_feature'
      language: '@language_en_gb'
      value: Feature is not active
  - fields:
      placeholder: '@common_exceptions_attachments_not_found'
      language: '@language_en_gb'
      value: Attachment was not found
  - fields:
      placeholder: '@common_components_attachments_email_sent'
      language: '@language_en_gb'
      value: The attachment is being sent. You will receive an email confirmation.
  - fields:
      placeholder: '@common_exceptions_attachments_invalid_template_type'
      language: '@language_en_gb'
      value: Attachment must be an Investigation Report or a RIB Report
  - fields:
      placeholder: '@common_exceptions_attachments_missing_reporter_email'
      language: '@language_en_gb'
      value: Report email is not set
  - fields:
      placeholder: '@common_exceptions_attachments_email_failed_to_send'
      language: '@language_en_gb'
      value: Report email failed to send
  - fields:
      placeholder: '@common_exceptions_validation_error'
      language: '@language_en_gb'
      value: Validation Error
  - fields:
      placeholder: '@nav_redress'
      language: '@language_en_gb'
      value: 'Redress'
  - fields:
      placeholder: '@common_components_tree_status_enabled'
      language: '@language_en_gb'
      value: Enabled
  - fields:
      placeholder: '@common_components_tree_status_deactivated'
      language: '@language_en_gb'
      value: Deactivated
  - fields:
      placeholder: '@common_components_tree_status_disabled'
      language: '@language_en_gb'
      value: Disabled
  - fields:
      placeholder: '@common_components_tree_node_first_page'
      language: '@language_en_gb'
      value: First Page
  - fields:
      placeholder: '@common_components_tree_node_next_page'
      language: '@language_en_gb'
      value: Next Page
  - fields:
      placeholder: '@common_components_tree_node_last_page'
      language: '@language_en_gb'
      value: Last Page
  - fields:
      placeholder: '@common_components_tree_node_previous_page'
      language: '@language_en_gb'
      value: Previous Page
  - fields:
      placeholder: '@common_components_attachments_disabled_for_public'
      language: '@language_en_gb'
      value: File uploads are not available to logged-out users
  -
    fields:
      placeholder: '@email_not_applicable'
      language: '@language_en_gb'
      value: 'N/A'
  -
    fields:
      placeholder: '@email_anonymous_author'
      language: '@language_en_gb'
      value: 'Anonymous'
  -
    fields:
      placeholder: '@nav_safeguarding'
      language: '@language_en_gb'
      value: 'Safeguarding'
  -
    fields:
      placeholder: '@language_label_fr_ca'
      language: '@language_en_gb'
      value: 'French (Canada)'


  ## CAPTURE_MODULES constants
  -
    fields:
      placeholder: '@capture_modules_com_label'
      language: '@language_en_gb'
      value: Feedback
  -
    fields:
      placeholder: '@capture_modules_cla_label'
      language: '@language_en_gb'
      value: Claims
  -
    fields:
      placeholder: '@capture_modules_inc_label'
      language: '@language_en_gb'
      value: Incidents
  -
    fields:
      placeholder: '@capture_modules_das_label'
      language: '@language_en_gb'
      value: Dashboard
  -
    fields:
      placeholder: '@capture_modules_red_label'
      language: '@language_en_gb'
      value: Redress
  -
    fields:
      placeholder: '@capture_modules_sfg_label'
      language: '@language_en_gb'
      value: Safeguarding
  -
    fields:
      placeholder: '@common_modules_safeguarding'
      language: '@language_en_gb'
      value: 'Safeguarding'
  - fields:
      placeholder: '@common_drafts_discard'
      language: '@language_en_gb'
      value: 'Discard'
  - fields:
      placeholder: '@common_drafts_publish_now'
      language: '@language_en_gb'
      value: 'Publish Now'
  - fields:
      placeholder: '@common_drafts_publish_later'
      language: '@language_en_gb'
      value: 'Publish Later'
  - fields:
      placeholder: '@common_drafts_edit_draft'
      language: '@language_en_gb'
      value: 'Edit Draft'
  - fields:
      placeholder: '@common_drafts_show_discard_message'
      language: '@language_en_gb'
      value: Click discard to start configuring your hierarchy
  - fields:
      placeholder: '@common_confirm_close_popup_modal'
      language: '@language_en_gb'
      value: 'Close Modal?'
  - fields:
      placeholder: '@common_invalid_time_message'
      language: '@language_en_gb'
      value: 'Invalid time entered. (Use format HH:mm:ss e.g. 14:22:00)'
  - fields:
      placeholder: '@common_record_nav_title'
      language: '@language_en_gb'
      value: Record Navigation
  - fields:
      placeholder: '@common_record_nav_first'
      language: '@language_en_gb'
      value: First Record
  - fields:
      placeholder: '@common_record_nav_previous'
      language: '@language_en_gb'
      value: Previous Record
  - fields:
      placeholder: '@common_record_nav_next'
      language: '@language_en_gb'
      value: Next Record
  - fields:
      placeholder: '@common_record_nav_last'
      language: '@language_en_gb'
      value: Last Record
  - fields:
      placeholder: '@common_error_see_the_errors_attribute_for_specific_failures_erm1'
      language: '@language_en_gb'
      value: '{{ermCode}} Please see the errors attribute for specific failures'
  -
    fields:
      placeholder: '@common_errors_resource_not_found_erm14'
      language: '@language_en_gb'
      value: '{{ermCode}} Resource not found'
  -
    fields:
      placeholder: '@common_form_validation_errors_erm15'
      language: '@language_en_gb'
      value: '{{ermCode}} The submitted form contains validation errors'
  -
    fields:
      placeholder: '@common_components_range_filter_min_value'
      language: '@language_en_gb'
      value: 'Minimum'
  -
    fields:
      placeholder: '@common_components_range_filter_max_value'
      language: '@language_en_gb'
      value: 'Maximum'
  -
    fields:
      placeholder: '@system_admin_system_configuration_copy_location_service_from_source_to_action'
      language: '@language_en_gb'
      value: 'Enable copying of the source record’s locations and services to the linked action?'
  -
    fields:
      placeholder: '@common_components_attachments_max_file_size_error'
      language: '@language_en_gb'
      value: 'File size is too large (>{{fileSizeLimit}}MB), please upload a smaller file'
  - fields:
      placeholder: '@common_attachment_saved_successfully'
      language: '@language_en_gb'
      value: 'Attachment saved successfully'
