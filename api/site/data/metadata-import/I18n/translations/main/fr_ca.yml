entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@language_selector_label', language: '@language_fr_ca', value: 'Choisissez votre langue d''affichage' } }
  - { fields: { placeholder: '@language_selector_option', language: '@language_fr_ca', value: Chargement } }
  - { fields: { placeholder: '@components_magma_table', language: '@language_fr_ca', value: 'Table Magma' } }
  - { fields: { placeholder: '@components_magma_table_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@components_magma_table_pagination_first', language: '@language_fr_ca', value: Premier } }
  - { fields: { placeholder: '@components_magma_table_pagination_previous', language: '@language_fr_ca', value: Précédent } }
  - { fields: { placeholder: '@components_magma_table_pagination_next', language: '@language_fr_ca', value: Suivant } }
  - { fields: { placeholder: '@components_magma_table_pagination_last', language: '@language_fr_ca', value: <PERSON><PERSON> } }
  - { fields: { placeholder: '@common_optional', language: '@language_fr_ca', value: 'En option' } }
  - { fields: { placeholder: '@common_copied', language: '@language_fr_ca', value: Copié } }
  - { fields: { placeholder: '@components_record_search', language: '@language_fr_ca', value: 'Recherche de dossier' } }
  - { fields: { placeholder: '@components_record_search_back_to_search', language: '@language_fr_ca', value: 'Retour à la recherche' } }
  - { fields: { placeholder: '@components_record_search_search_results', language: '@language_fr_ca', value: 'Résultats de la recherche' } }
  - { fields: { placeholder: '@common_components_attachments_plural', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@common_components_attachments_loading', language: '@language_fr_ca', value: 'Chargement des pièces jointes' } }
  - { fields: { placeholder: '@common_components_attachments_new', language: '@language_fr_ca', value: 'Nouvelle pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_uploaded', language: '@language_fr_ca', value: Téléversée } }
  - { fields: { placeholder: '@common_components_attachments_download', language: '@language_fr_ca', value: 'Télécharger la pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_edit', language: '@language_fr_ca', value: Modifier } }
  - { fields: { placeholder: '@common_components_attachments_version', language: '@language_fr_ca', value: 'Version {{number}}' } }
  - { fields: { placeholder: '@common_components_attachments_edit_attachment', language: '@language_fr_ca', value: 'Modifier la pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_add', language: '@language_fr_ca', value: 'Ajouter une pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_singular', language: '@language_fr_ca', value: 'Pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_percent_complete', language: '@language_fr_ca', value: '{{percent}} % terminé' } }
  - { fields: { placeholder: '@common_components_attachments_replace', language: '@language_fr_ca', value: 'Remplacer la pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@common_components_attachments_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@common_components_attachments_default_allowed_file_types', language: '@language_fr_ca', value: 'Les fichiers de pièce jointe peuvent être des images, des documents Word, des feuilles de calcul ou des PDF' } }
  - { fields: { placeholder: '@common_components_attachments_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@common_components_attachments_select_classification', language: '@language_fr_ca', value: 'Sélectionner la classification' } }
  - { fields: { placeholder: '@common_components_attachments_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@common_components_attachments_save', language: '@language_fr_ca', value: 'Enregistrer la pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_retry', language: '@language_fr_ca', value: Réessayer } }
  - { fields: { placeholder: '@common_components_attachments_error', language: '@language_fr_ca', value: 'Une erreur s''est produite pendant de téléversement de votre fichier.' } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_image', language: '@language_fr_ca', value: 'Image / photo' } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_document', language: '@language_fr_ca', value: Document } }
  - { fields: { placeholder: '@common_components_attachments_classification_values_spreadsheet', language: '@language_fr_ca', value: 'Feuille de calcul' } }
  - { fields: { placeholder: '@common_components_attachments_audit_title', language: '@language_fr_ca', value: 'Vérification de pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_audit_version', language: '@language_fr_ca', value: Version } }
  - { fields: { placeholder: '@common_components_attachments_audit_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@common_components_attachments_audit_datetime', language: '@language_fr_ca', value: 'Date et heure' } }
  - { fields: { placeholder: '@common_components_attachments_audit_download', language: '@language_fr_ca', value: Télécharger } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@common_components_attachments_audit_attachment_deleted', language: '@language_fr_ca', value: 'La pièce jointe a été supprimée' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_title', language: '@language_fr_ca', value: 'Impossible de supprimer' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_last_version_required', language: '@language_fr_ca', value: 'Seule la dernière version de la pièce jointe peut être supprimée' } }
  - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_permissions_insufficient', language: '@language_fr_ca', value: 'Cette version de pièce jointe ne peut pas être supprimée selon vos permissions d''utilisateur' } }
  - { fields: { placeholder: '@common_components_attachments_audit_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@common_components_attachments_audit_type_public', language: '@language_fr_ca', value: Public } }
  - { fields: { placeholder: '@common_components_attachments_audit_type_private', language: '@language_fr_ca', value: Privé } }
  - { fields: { placeholder: '@common_components_attachments_no_attachments_provided', language: '@language_fr_ca', value: 'Aucune pièce jointe fournie' } }
  - { fields: { placeholder: '@common_components_attachments_email_attachment_label', language: '@language_fr_ca', value: 'Pièce jointe de courriel' } }
  - { fields: { placeholder: '@common_components_attachments_email_attachment_title', language: '@language_fr_ca', value: 'Lien de courriel' } }
  - { fields: { placeholder: '@common_components_attachments_resend_attachment', language: '@language_fr_ca', value: 'Envoyer à nouveau la pièce jointe' } }
  - { fields: { placeholder: '@common_components_attachments_reason_for_resending', language: '@language_fr_ca', value: 'Motif de l''envoi à nouveau' } }
  - { fields: { placeholder: '@common_components_attachments_report_last_sent', language: '@language_fr_ca', value: "Rapport du dernier renvoi\_: {{date}}" } }
  - { fields: { placeholder: '@common_components_attachments_report_first_sent', language: '@language_fr_ca', value: "Rapport du premier envoi\_: {{date}}" } }
  - { fields: { placeholder: '@common_components_attachments_reason_for_resend', language: '@language_fr_ca', value: "Rapport du dernier motif de renvoi\_: {{reason}}" } }
  - { fields: { placeholder: '@common_components_attachments_email_sent', language: '@language_fr_ca', value: 'La pièce jointe est en cours d''envoi. Vous recevrez une confirmation par courriel.' } }
  - { fields: { placeholder: '@components_contributory_factors_singular', language: '@language_fr_ca', value: 'Facteur contributif' } }
  - { fields: { placeholder: '@components_contributory_factors_plural', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@components_contributory_factors_add_contributory_factor', language: '@language_fr_ca', value: 'Ajouter un facteur contributif' } }
  - { fields: { placeholder: '@components_contributory_factors_no_contributory_factors', language: '@language_fr_ca', value: 'Ce dossier ne comporte pas de facteurs contributifs' } }
  - { fields: { placeholder: '@components_contributory_factors_new_contributory_factor', language: '@language_fr_ca', value: 'Nouveau facteur contributif' } }
  - { fields: { placeholder: '@components_contributory_factors_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@components_contributory_factors_sub_category', language: '@language_fr_ca', value: Sous-catégorie } }
  - { fields: { placeholder: '@components_contributory_factors_select_type', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@components_contributory_factors_select_category', language: '@language_fr_ca', value: 'Sélectionner la catégorie' } }
  - { fields: { placeholder: '@components_contributory_factors_select_sub_category', language: '@language_fr_ca', value: 'Sélectionnez la sous-catégorie' } }
  - { fields: { placeholder: '@common_create', language: '@language_fr_ca', value: Créer } }
  - { fields: { placeholder: '@common_new', language: '@language_fr_ca', value: Nouveau } }
  - { fields: { placeholder: '@common_edit', language: '@language_fr_ca', value: Modifier } }
  - { fields: { placeholder: '@common_loading', language: '@language_fr_ca', value: Chargement } }
  - { fields: { placeholder: '@common_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@common_ref', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@common_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@common_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@common_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@common_modules', language: '@language_fr_ca', value: Modules } }
  - { fields: { placeholder: '@common_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@common_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@common_due', language: '@language_fr_ca', value: Échéance } }
  - { fields: { placeholder: '@common_select', language: '@language_fr_ca', value: Sélectionner } }
  - { fields: { placeholder: '@common_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@common_subtype', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@common_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@common_subcategory', language: '@language_fr_ca', value: Sous-catégorie } }
  - { fields: { placeholder: '@common_filter', language: '@language_fr_ca', value: Filtre } }
  - { fields: { placeholder: '@common_filter_indicator', language: '@language_fr_ca', value: 'Filtre{{ filterIndicator }}' } }
  - { fields: { placeholder: '@common_not_set', language: '@language_fr_ca', value: 'Non défini' } }
  - { fields: { placeholder: '@placeholder_common_no_value_selected', language: '@language_fr_ca', value: 'Aucune valeur sélectionnée' } }
  - { fields: { placeholder: '@common_view_details', language: '@language_fr_ca', value: 'Afficher les détails' } }
  - { fields: { placeholder: '@common_save_changes', language: '@language_fr_ca', value: 'Enregistrer les modifications' } }
  - { fields: { placeholder: '@common_save', language: '@language_fr_ca', value: Sauvegarder } }
  - { fields: { placeholder: '@common_saving', language: '@language_fr_ca', value: Enregistrement... } }
  - { fields: { placeholder: '@common_save_and_close', language: '@language_fr_ca', value: 'Enregistrer et fermer' } }
  - { fields: { placeholder: '@common_save_error', language: '@language_fr_ca', value: 'Erreur à l''enregistrement' } }
  - { fields: { placeholder: '@common_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@common_delete', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@common_close', language: '@language_fr_ca', value: Fermer } }
  - { fields: { placeholder: '@common_search', language: '@language_fr_ca', value: Rechercher } }
  - { fields: { placeholder: '@common_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@common_clear', language: '@language_fr_ca', value: Effacer } }
  - { fields: { placeholder: '@common_reply', language: '@language_fr_ca', value: Répondre } }
  - { fields: { placeholder: '@common_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@common_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@common_confirm', language: '@language_fr_ca', value: Confirmer } }
  - { fields: { placeholder: '@common_error', language: '@language_fr_ca', value: Erreur } }
  - { fields: { placeholder: '@common_generic_error', language: '@language_fr_ca', value: 'Une erreur s''est produite' } }
  - { fields: { placeholder: '@common_role', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@common_admin', language: '@language_fr_ca', value: Admin } }
  - { fields: { placeholder: '@common_administration', language: '@language_fr_ca', value: Administration } }
  - { fields: { placeholder: '@common_administration_tools', language: '@language_fr_ca', value: Outils } }
  - { fields: { placeholder: '@common_administration_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@common_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@common_access_control_manual_roles', language: '@language_fr_ca', value: 'Rôles manuels' } }
  - { fields: { placeholder: '@common_access_control_generated_roles', language: '@language_fr_ca', value: 'Rôles générés' } }
  - { fields: { placeholder: '@common_access_control_no_assignable_roles', language: '@language_fr_ca', value: 'Aucun rôle attribuable' } }
  - { fields: { placeholder: '@common_form_validation_required', language: '@language_fr_ca', value: 'Ce champ est obligatoire' } }
  - { fields: { placeholder: '@common_form_validation_string_length_min', language: '@language_fr_ca', value: 'La longueur de ce champ doit être d''au moins {{min}} caractères' } }
  - { fields: { placeholder: '@common_form_validation_string_length_max', language: '@language_fr_ca', value: 'La longueur de ce champ ne peut pas dépasser {{max}} caractères' } }
  - { fields: { placeholder: '@common_form_validation_email', language: '@language_fr_ca', value: 'Veuillez entrer une adresse de courriel valide' } }
  - { fields: { placeholder: '@common_form_validation_errors', language: '@language_fr_ca', value: 'Le formulaire soumis contient des erreurs de validation' } }
  - { fields: { placeholder: '@common_form_validation_cascading_select', language: '@language_fr_ca', value: 'Veuillez sélectionner un sous-type' } }
  - { fields: { placeholder: '@nav_back_to_dashboard', language: '@language_fr_ca', value: 'Retour au tableau de bord' } }
  - { fields: { placeholder: '@nav_dashboard', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@nav_capture', language: '@language_fr_ca', value: Capture } }
  - { fields: { placeholder: '@nav_incidents', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@nav_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@nav_claims', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@nav_icon_wall', language: '@language_fr_ca', value: Créer un dossier } }
  - { fields: { placeholder: '@nav_mortality', language: '@language_fr_ca', value: 'Examen de mortalité' } }
  - { fields: { placeholder: '@nav_evaluate', language: '@language_fr_ca', value: Évaluer } }
  - { fields: { placeholder: '@nav_risk_register', language: '@language_fr_ca', value: 'Gestionnaire du risque de l''entreprise' } }
  - { fields: { placeholder: '@nav_investigation', language: '@language_fr_ca', value: Enquête } }
  - { fields: { placeholder: '@nav_investigations', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@nav_risk', language: '@language_fr_ca', value: Risque } }
  - { fields: { placeholder: '@nav_risks', language: '@language_fr_ca', value: Risques } }
  - { fields: { placeholder: '@nav_reportable_incidents', language: '@language_fr_ca', value: 'Dossiers d''incident à signaler' } }
  - { fields: { placeholder: '@nav_strategy', language: '@language_fr_ca', value: Stratégie } }
  - { fields: { placeholder: '@nav_clinical_audit', language: '@language_fr_ca', value: 'Audit clinique' } }
  - { fields: { placeholder: '@nav_clinical_audits', language: '@language_fr_ca', value: 'Vérifications cliniques' } }
  - { fields: { placeholder: '@nav_controls_and_recommendations', language: '@language_fr_ca', value: 'Recommandations et contrôles' } }
  - { fields: { placeholder: '@nav_safety_learnings', language: '@language_fr_ca', value: 'Apprentissages de la sécurité' } }
  - { fields: { placeholder: '@nav_roi_assessment', language: '@language_fr_ca', value: 'Investissement RCI' } }
  - { fields: { placeholder: '@nav_implement', language: '@language_fr_ca', value: Implémenter } }
  - { fields: { placeholder: '@nav_policies_and_guidelines', language: '@language_fr_ca', value: 'Polices et directives' } }
  - { fields: { placeholder: '@nav_assess', language: '@language_fr_ca', value: Évaluer } }
  - { fields: { placeholder: '@nav_compliance_assessment', language: '@language_fr_ca', value: 'Contrôle de conformité' } }
  - { fields: { placeholder: '@nav_safety_rounds', language: '@language_fr_ca', value: 'Rondes de sécurité' } }
  - { fields: { placeholder: '@nav_my_profile', language: '@language_fr_ca', value: 'Mon profil' } }
  - { fields: { placeholder: '@nav_my_todo', language: '@language_fr_ca', value: 'Liste de choses à faire' } }
  - { fields: { placeholder: '@nav_system_admin', language: '@language_fr_ca', value: 'Admin système' } }
  - { fields: { placeholder: '@nav_logout', language: '@language_fr_ca', value: 'Fermeture de session' } }
  - { fields: { placeholder: '@nav_login', language: '@language_fr_ca', value: 'Ouverture de session' } }
  - { fields: { placeholder: '@nav_administration', language: '@language_fr_ca', value: Administration } }
  - { fields: { placeholder: '@nav_acl_roles', language: '@language_fr_ca', value: 'Rôles de LCA' } }
  - { fields: { placeholder: '@nav_acl_groups', language: '@language_fr_ca', value: 'Groupes de LCA' } }
  - { fields: { placeholder: '@nav_acl_rules', language: '@language_fr_ca', value: 'Règles de LCA' } }
  - { fields: { placeholder: '@nav_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@nav_checklists', language: '@language_fr_ca', value: Sondages } }
  - { fields: { placeholder: '@nav_contacts', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@nav_controls', language: '@language_fr_ca', value: Contrôles } }
  - { fields: { placeholder: '@nav_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@nav_forms', language: '@language_fr_ca', value: 'Concepteur du formulaire' } }
  - { fields: { placeholder: '@nav_form-fields', language: '@language_fr_ca', value: 'Champs personnalisés' } }
  - { fields: { placeholder: '@nav_help', language: '@language_fr_ca', value: Aide } }
  - { fields: { placeholder: '@nav_resources_covid', language: '@language_fr_ca', value: 'Ressources sur la COVID-19' } }
  - { fields: { placeholder: '@nav_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@nav_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@nav_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@nav_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@nav_organisations', language: '@language_fr_ca', value: Organisations } }
  - { fields: { placeholder: '@nav_insurance', language: '@language_fr_ca', value: Assurance } }
  - { fields: { placeholder: '@nav_payments', language: '@language_fr_ca', value: Paiements } }
  - { fields: { placeholder: '@nav_actions_dashboard', language: '@language_fr_ca', value: 'Tableau de bord des actions' } }
  - { fields: { placeholder: '@nav_distributions_lists', language: '@language_fr_ca', value: 'Listes de distribution' } }
  - { fields: { placeholder: '@nav_reporting', language: '@language_fr_ca', value: Signalement } }
  - { fields: { placeholder: '@nav_capture_admin', language: '@language_fr_ca', value: 'Capturer admin' } }
  - { fields: { placeholder: '@nav_capture_user_settings', language: '@language_fr_ca', value: 'Paramètres de capture' } }
  - { fields: { placeholder: '@dashboard_singular', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@dashboard_plural', language: '@language_fr_ca', value: 'Tableaux de bord' } }
  - { fields: { placeholder: '@incident_singular', language: '@language_fr_ca', value: Incident } }
  - { fields: { placeholder: '@incident_plural', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@claim_singular', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@claim_plural', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@complaint_singular', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@complaint_plural', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@mortality_singular', language: '@language_fr_ca', value: 'Examen de mortalité' } }
  - { fields: { placeholder: '@mortality_plural', language: '@language_fr_ca', value: 'Examens de mortalité' } }
  - { fields: { placeholder: '@common_errors_missing_fields', language: '@language_fr_ca', value: 'Veuillez vous assurer que tous les champs obligatoires ont été fournis' } }
  - { fields: { placeholder: '@common_errors_get_form', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération du formulaire' } }
  - { fields: { placeholder: '@common_errors_forbidden', language: '@language_fr_ca', value: Interdit } }
  - { fields: { placeholder: '@common_errors_entity_forbidden_entity', language: '@language_fr_ca', value: 'Entité interdite' } }
  - { fields: { placeholder: '@common_errors_resource_not_found', language: '@language_fr_ca', value: 'Ressource introuvable' } }
  - { fields: { placeholder: '@common_errors_entity_not_found', language: '@language_fr_ca', value: 'Entité introuvable' } }
  - { fields: { placeholder: '@sidebar_back_to', language: '@language_fr_ca', value: 'Retour à' } }
  - { fields: { placeholder: '@common_errors_field_exceeds_max_length', language: '@language_fr_ca', value: '{{field}} dépasse la longueur maximum ({{maxLength}})' } }
  - { fields: { placeholder: '@loading', language: '@language_fr_ca', value: Chargement... } }
  - { fields: { placeholder: '@components_notes', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@components_notes_new_note', language: '@language_fr_ca', value: 'Nouvelle note' } }
  - { fields: { placeholder: '@components_notes_add_a_note', language: '@language_fr_ca', value: 'Rédiger une note' } }
  - { fields: { placeholder: '@components_notes_edit_note', language: '@language_fr_ca', value: 'Modifier la note' } }
  - { fields: { placeholder: '@components_notes_from', language: '@language_fr_ca', value: De } }
  - { fields: { placeholder: '@components_notes_hide_responses', language: '@language_fr_ca', value: 'Masquer les réponses' } }
  - { fields: { placeholder: '@components_notes_show_responses', language: '@language_fr_ca', value: 'Afficher les réponses' } }
  - { fields: { placeholder: '@components_notes_reply', language: '@language_fr_ca', value: 'Rédiger une réponse' } }
  - { fields: { placeholder: '@components_notes_delete_option_confirmation', language: '@language_fr_ca', value: 'Voulez-vous vraiment supprimer cette note?' } }
  - { fields: { placeholder: '@components_confirm_button', language: '@language_fr_ca', value: 'Bouton de confirmation' } }
  - { fields: { placeholder: '@components_confirm_button_text', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@components_recommendations_singular', language: '@language_fr_ca', value: Recommandation } }
  - { fields: { placeholder: '@components_recommendations_plural', language: '@language_fr_ca', value: Recommandations } }
  - { fields: { placeholder: '@components_recommendations_search', language: '@language_fr_ca', value: 'Rechercher des recommandations' } }
  - { fields: { placeholder: '@components_recommendations_create', language: '@language_fr_ca', value: 'Créer une recommandation' } }
  - { fields: { placeholder: '@components_recommendations_edit', language: '@language_fr_ca', value: 'Modifier la recommandation' } }
  - { fields: { placeholder: '@components_recommendations_table_column_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@components_recommendations_table_column_statement_of_intent', language: '@language_fr_ca', value: 'Déclaration d''intention' } }
  - { fields: { placeholder: '@components_recommendations_save', language: '@language_fr_ca', value: Sauvegarder } }
  - { fields: { placeholder: '@components_recommendations_loading', language: '@language_fr_ca', value: 'Chargement des recommandations' } }
  - { fields: { placeholder: '@components_recommendations_new', language: '@language_fr_ca', value: 'Nouvelle recommandation' } }
  - { fields: { placeholder: '@components_recommendations_controls', language: '@language_fr_ca', value: Contrôles } }
  - { fields: { placeholder: '@components_recommendations_contributory_factor', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@components_recommendations_loading_controls', language: '@language_fr_ca', value: 'Chargement des contrôles' } }
  - { fields: { placeholder: '@common_components_magma_relationship_loading', language: '@language_fr_ca', value: Chargement } }
  - { fields: { placeholder: '@common_components_magma_relationship_create', language: '@language_fr_ca', value: Créer } }
  - { fields: { placeholder: '@common_components_magma_relationship_save', language: '@language_fr_ca', value: Sauvegarder } }
  - { fields: { placeholder: '@common_components_magma_relationship_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@common_components_magma_relationship_saving', language: '@language_fr_ca', value: Enregistrement... } }
  - { fields: { placeholder: '@common_components_magma_relationship_close_search', language: '@language_fr_ca', value: 'Fermer la recherche' } }
  - { fields: { placeholder: '@tables_no_results_found', language: '@language_fr_ca', value: 'Aucun résultat trouvé' } }
  - { fields: { placeholder: '@tables_pagination_per_page', language: '@language_fr_ca', value: 'Par page' } }
  - { fields: { placeholder: '@tables_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@tables_go_to', language: '@language_fr_ca', value: 'Aller à' } }
  - { fields: { placeholder: '@tables_view', language: '@language_fr_ca', value: Afficher } }
  - { fields: { placeholder: '@tables_edit', language: '@language_fr_ca', value: Modifier } }
  - { fields: { placeholder: '@common_n_a', language: '@language_fr_ca', value: S.O. } }
  - { fields: { placeholder: '@common_modules_erm', language: '@language_fr_ca', value: GED } }
  - { fields: { placeholder: '@common_modules_erm_risk', language: '@language_fr_ca', value: 'GED - Risque' } }
  - { fields: { placeholder: '@common_modules_erm_register', language: '@language_fr_ca', value: 'GED - Inscription' } }
  - { fields: { placeholder: '@common_modules_erm_tracker', language: '@language_fr_ca', value: 'GED - Suiveur' } }
  - { fields: { placeholder: '@common_modules_investigations', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@common_modules_investigation', language: '@language_fr_ca', value: Enquête } }
  - { fields: { placeholder: '@common_modules_surveys', language: '@language_fr_ca', value: Sondages } }
  - { fields: { placeholder: '@common_modules_survey', language: '@language_fr_ca', value: Sondage } }
  - { fields: { placeholder: '@common_modules_clinical_audit', language: '@language_fr_ca', value: 'Audit clinique' } }
  - { fields: { placeholder: '@common_modules_controls', language: '@language_fr_ca', value: Contrôles } }
  - { fields: { placeholder: '@common_module_control', language: '@language_fr_ca', value: Contrôle } }
  - { fields: { placeholder: '@common_modules_recommendation', language: '@language_fr_ca', value: Recommandation } }
  - { fields: { placeholder: '@common_modules_recommendations', language: '@language_fr_ca', value: Recommandations } }
  - { fields: { placeholder: '@common_modules_compliance_assessment', language: '@language_fr_ca', value: 'Contrôle de conformité' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_programme', language: '@language_fr_ca', value: 'Évaluation de conformité - Programme' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_standard', language: '@language_fr_ca', value: 'Évaluation de conformité - Normale' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_assessment', language: '@language_fr_ca', value: 'Évaluation de conformité - Évaluation' } }
  - { fields: { placeholder: '@common_modules_compliance_assessment_response', language: '@language_fr_ca', value: 'Évaluation de conformité - Réponse' } }
  - { fields: { placeholder: '@common_modules_safety_rounds', language: '@language_fr_ca', value: 'Rondes de sécurité' } }
  - { fields: { placeholder: '@common_modules_safety_round', language: '@language_fr_ca', value: 'Ronde de sécurité' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_template', language: '@language_fr_ca', value: 'Rondes de sécurité - Modèle' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_response', language: '@language_fr_ca', value: 'Ronde de sécurité - Réponse' } }
  - { fields: { placeholder: '@common_modules_safety_rounds_summary', language: '@language_fr_ca', value: 'Rondes de sécurité - Sommaire' } }
  - { fields: { placeholder: '@common_modules_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@common_modules_acl', language: '@language_fr_ca', value: LCA } }
  - { fields: { placeholder: '@common_modules_acl_rule', language: '@language_fr_ca', value: 'LCA - Règle' } }
  - { fields: { placeholder: '@common_modules_acl_role', language: '@language_fr_ca', value: 'LCA - Rôle' } }
  - { fields: { placeholder: '@common_modules_contacts', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@common_modules_contact', language: '@language_fr_ca', value: Contact } }
  - { fields: { placeholder: '@common_modules_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@common_modules_forms', language: '@language_fr_ca', value: Formulaires } }
  - { fields: { placeholder: '@common_modules_form', language: '@language_fr_ca', value: Formulaire } }
  - { fields: { placeholder: '@common_modules_form_field', language: '@language_fr_ca', value: 'Champ de formulaire' } }
  - { fields: { placeholder: '@common_modules_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@common_modules_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@common_modules_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@common_modules_medication', language: '@language_fr_ca', value: Médicament } }
  - { fields: { placeholder: '@common_modules_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@common_modules_service', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@common_modules_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@common_modules_user', language: '@language_fr_ca', value: Utilisateur } }
  - { fields: { placeholder: '@common_modules_user_group', language: '@language_fr_ca', value: 'Groupe d''utilisateurs' } }
  - { fields: { placeholder: '@common_modules_prince', language: '@language_fr_ca', value: Prince } }
  - { fields: { placeholder: '@common_modules_claim', language: '@language_fr_ca', value: Réclamation } }
  - { fields: { placeholder: '@common_modules_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@common_modules_incident', language: '@language_fr_ca', value: Incident } }
  - { fields: { placeholder: '@common_modules_mortality', language: '@language_fr_ca', value: Mortalité } }
  - { fields: { placeholder: '@common_select_an_option', language: '@language_fr_ca', value: 'Sélectionnez une option' } }
  - { fields: { placeholder: '@common_search_or_select_option', language: '@language_fr_ca', value: 'Recherchez ou sélectionnez une option' } }
  - { fields: { placeholder: '@common_help', language: '@language_fr_ca', value: Aide } }
  - { fields: { placeholder: '@common_components_date_filter_start_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@common_components_date_filter_end_date', language: '@language_fr_ca', value: 'Date de fin' } }
  - { fields: { placeholder: '@common_components_feedback_button', language: '@language_fr_ca', value: 'Donner une rétroaction' } }
  - { fields: { placeholder: '@common_components_feedback_rate', language: '@language_fr_ca', value: 'Comment évaluez-vous cette page?' } }
  - { fields: { placeholder: '@common_components_share', language: '@language_fr_ca', value: 'Que voudriez-vous nous faire savoir?' } }
  - { fields: { placeholder: '@common_components_feedback_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la rétroaction' } }
  - { fields: { placeholder: '@common_components_feedback_submitted', language: '@language_fr_ca', value: Soumis } }
  - { fields: { placeholder: '@common_components_feedback_thank_you', language: '@language_fr_ca', value: 'Merci pour vos commentaires' } }
  - { fields: { placeholder: '@common_submit', language: '@language_fr_ca', value: Soumettre } }
  - { fields: { placeholder: '@common_are_you_sure', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@common_back', language: '@language_fr_ca', value: Retour } }
  - { fields: { placeholder: '@nav_back_to_admin', language: '@language_fr_ca', value: 'Retour à l''administration' } }
  - { fields: { placeholder: '@nav_back_to_control', language: '@language_fr_ca', value: 'Retour au contrôle' } }
  - { fields: { placeholder: '@common_on', language: '@language_fr_ca', value: le } }
  - { fields: { placeholder: '@common_inactive', language: '@language_fr_ca', value: Inactif } }
  - { fields: { placeholder: '@common_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@common_deleted', language: '@language_fr_ca', value: Supprimé } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_new_objective', language: '@language_fr_ca', value: 'Nouvel objectif' } }
  - { fields: { placeholder: '@erm_risk_filters_risk_review_date', language: '@language_fr_ca', value: 'Date de révision du risque' } }
  - { fields: { placeholder: '@erm_risk_filters_risk_review_date_label', language: '@language_fr_ca', value: 'Date de révision du risque' } }
  - { fields: { placeholder: '@erm_risk_filters_risk_open_date', language: '@language_fr_ca', value: 'Date d''ouverture du risque' } }
  - { fields: { placeholder: '@erm_risk_filters_risk_closed_date', language: '@language_fr_ca', value: 'Date de fermeture du risque' } }
  - { fields: { placeholder: '@erm_risk_filters_risk_closed_date_label', language: '@language_fr_ca', value: 'Date de fermeture du risque' } }
  - { fields: { placeholder: '@erm_risk_filters_current_rating', language: '@language_fr_ca', value: 'Classe actuelle' } }
  - { fields: { placeholder: '@investigations_loading_objectives', language: '@language_fr_ca', value: 'Chargement des objectifs' } }
  - { fields: { placeholder: '@investigations_nav_actions_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@investigations_nav_actions_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@contacts_related_primary_modules', language: '@language_fr_ca', value: 'Modules principaux associés' } }
  - { fields: { placeholder: '@common_loading_permissions', language: '@language_fr_ca', value: 'Chargement des permissions' } }
  - { fields: { placeholder: '@contacts_edit_contact', language: '@language_fr_ca', value: 'Modifier le contact' } }
  - { fields: { placeholder: '@contacts_view_contact', language: '@language_fr_ca', value: 'Afficher le contact' } }
  - { fields: { placeholder: '@controls_controls_in_place', language: '@language_fr_ca', value: 'Contrôles en place' } }
  - { fields: { placeholder: '@controls_gaps_in_controls', language: '@language_fr_ca', value: 'Écarts dans les contrôles' } }
  - { fields: { placeholder: '@actions_record_add_action_plan_to_record', language: '@language_fr_ca', value: 'Ajouter le plan d''action au dossier' } }
  - { fields: { placeholder: '@actions_admin_add_action_update', language: '@language_fr_ca', value: 'Mettre à jour l''action au plan' } }
  - { fields: { placeholder: '@common_saved_successfully', language: '@language_fr_ca', value: 'Enregistré avec succès' } }
  - { fields: { placeholder: '@common_unauthorised', language: '@language_fr_ca', value: 'Non autorisé' } }
  - { fields: { placeholder: '@common_unauthorised_page_access', language: '@language_fr_ca', value: 'Vous n''avez pas la permission d''accéder à cette page' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_human_and_relational', language: '@language_fr_ca', value: 'Facteurs humains et relationnels' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_patient_and_family', language: '@language_fr_ca', value: 'Facteurs liés au patient et à la famille' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_communication', language: '@language_fr_ca', value: Communication } }
  - { fields: { placeholder: '@components_contributory_factors_classification_consent', language: '@language_fr_ca', value: Consentement } }
  - { fields: { placeholder: '@components_contributory_factors_classification_incapacity', language: '@language_fr_ca', value: Incapacité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_religious_beliefs', language: '@language_fr_ca', value: 'Croyances religieuses ou culturelles' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_non_compliance', language: '@language_fr_ca', value: Non-conformité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_abnormal_physiology', language: '@language_fr_ca', value: 'Physiologie anormale' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_fr_ca', value: Complexité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_support', language: '@language_fr_ca', value: 'Soutien social' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_aggression', language: '@language_fr_ca', value: Agression } }
  - { fields: { placeholder: '@components_contributory_factors_classification_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@components_contributory_factors_classification_individual_and_personal', language: '@language_fr_ca', value: 'Facteurs individuels et personnels' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_skill', language: '@language_fr_ca', value: Compétence } }
  - { fields: { placeholder: '@components_contributory_factors_classification_knowledge', language: '@language_fr_ca', value: Connaissances } }
  - { fields: { placeholder: '@components_contributory_factors_classification_experience', language: '@language_fr_ca', value: Expérience } }
  - { fields: { placeholder: '@components_contributory_factors_classification_fatigue', language: '@language_fr_ca', value: Fatigue } }
  - { fields: { placeholder: '@components_contributory_factors_classification_attitudes', language: '@language_fr_ca', value: Attitudes } }
  - { fields: { placeholder: '@components_contributory_factors_classification_stress', language: '@language_fr_ca', value: Stress } }
  - { fields: { placeholder: '@components_contributory_factors_classification_morale', language: '@language_fr_ca', value: 'Moral et motivation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_health', language: '@language_fr_ca', value: 'État de santé' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_temporary_impairment', language: '@language_fr_ca', value: 'Dépréciation temporaire' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_alcohol_or_drugs', language: '@language_fr_ca', value: 'Alcool ou drogues' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_personal_domestic_issues', language: '@language_fr_ca', value: 'Problèmes personnels ou familiaux' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_teamwork_leadership_supervision', language: '@language_fr_ca', value: 'Travail d''équipe, leadership et supervision' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_supervision', language: '@language_fr_ca', value: Surveillance } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staff_support', language: '@language_fr_ca', value: 'Soutien au personnel' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_shared_understanding', language: '@language_fr_ca', value: 'Compréhension et prise de conscience partagées' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_coordination', language: '@language_fr_ca', value: Coordination } }
  - { fields: { placeholder: '@components_contributory_factors_classification_local_leadership', language: '@language_fr_ca', value: 'Leadership local' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_psychological_safety', language: '@language_fr_ca', value: 'Sécurité psychologique et ouverture' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_mutual_respect', language: '@language_fr_ca', value: 'Respect mutuel' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_effective_communication', language: '@language_fr_ca', value: 'Communication efficace' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_education_assessment', language: '@language_fr_ca', value: 'Formation, éducation et évaluation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_appropriate_training', language: '@language_fr_ca', value: 'Une formation appropriée' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_availability', language: '@language_fr_ca', value: 'Disponibilité de la formation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_training_monitoring', language: '@language_fr_ca', value: 'Suivi de la formation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_education_quality', language: '@language_fr_ca', value: 'Qualité de l''éducation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_assessment_and_qualification', language: '@language_fr_ca', value: 'Évaluation et qualification' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_team_training', language: '@language_fr_ca', value: 'Formation en équipe' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_simulation_training', language: '@language_fr_ca', value: 'Formation de simulation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_revalidation_and_competency', language: '@language_fr_ca', value: 'Revalidation et compétence' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_induction', language: '@language_fr_ca', value: 'Induction et familiarisation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_and_environmental', language: '@language_fr_ca', value: 'Tâche et facteurs environnementaux' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_demands', language: '@language_fr_ca', value: 'Demandes des tâches et charge de travail' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_time_of_day', language: '@language_fr_ca', value: 'Moment de la journée' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_distractions', language: '@language_fr_ca', value: 'Distractions et interruptions' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_high_workload', language: '@language_fr_ca', value: 'Forte charge de travail' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_low_workload', language: '@language_fr_ca', value: 'Faible charge de travail' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_time_availability', language: '@language_fr_ca', value: 'Disponibilité temporelle' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_complexity', language: '@language_fr_ca', value: 'Complexité des tâches' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_production_pressure', language: '@language_fr_ca', value: 'Pression de production' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_practicality', language: '@language_fr_ca', value: 'Aspects pratiques de la tâche' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_task_difficulty', language: '@language_fr_ca', value: 'Difficulté de la tâche' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_conflicting_tasks', language: '@language_fr_ca', value: 'Tâches conflictuelles' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_uncertainty_and_variability', language: '@language_fr_ca', value: 'Incertitude et variabilité' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_physical_work_environment', language: '@language_fr_ca', value: 'Environnement de travail physique' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_housekeeping', language: '@language_fr_ca', value: 'Entretien ménager' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_lighting', language: '@language_fr_ca', value: Éclairage } }
  - { fields: { placeholder: '@components_contributory_factors_classification_noise', language: '@language_fr_ca', value: Bruit } }
  - { fields: { placeholder: '@components_contributory_factors_classification_temperature', language: '@language_fr_ca', value: Température } }
  - { fields: { placeholder: '@components_contributory_factors_classification_layout_and_design', language: '@language_fr_ca', value: 'Aménagement et conception' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_accessibility', language: '@language_fr_ca', value: Accessibilité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_security', language: '@language_fr_ca', value: Sécurité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_visibility', language: '@language_fr_ca', value: 'Visibilité et lignes de vue' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_fixtures_and_fittings', language: '@language_fr_ca', value: 'Agencements et installations' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_maintenance', language: '@language_fr_ca', value: Entretien } }
  - { fields: { placeholder: '@components_contributory_factors_classification_procedures_protocols_and_guidelines', language: '@language_fr_ca', value: 'Procédures, protocoles et directives' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_adequacy_and_accuracy', language: '@language_fr_ca', value: 'Adéquation et précision' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_availability_and_accessibility', language: '@language_fr_ca', value: 'Disponibilité et accessibilité' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_currency', language: '@language_fr_ca', value: Devise } }
  - { fields: { placeholder: '@components_contributory_factors_classification_clarity_and_coherence', language: '@language_fr_ca', value: 'Clarté et cohérence' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_practicality', language: '@language_fr_ca', value: Praticité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_implementation_and_usage', language: '@language_fr_ca', value: 'Mise en œuvre et utilisation' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_acceptability_and_agreement', language: '@language_fr_ca', value: 'Acceptabilité et accord' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tools_equipment_and_resources', language: '@language_fr_ca', value: 'Outils, équipements et ressources' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_alarms_and_warnings', language: '@language_fr_ca', value: 'Alarmes et avertissements' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_displays_and_interfaces', language: '@language_fr_ca', value: 'Affichages et interfaces' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_functionality', language: '@language_fr_ca', value: 'Aspects fonctionnels' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_reliability', language: '@language_fr_ca', value: Fiabilité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_availability', language: '@language_fr_ca', value: Accessibilité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_design', language: '@language_fr_ca', value: Conception } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_installation', language: '@language_fr_ca', value: Installation } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_maintenance', language: '@language_fr_ca', value: Entretien } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_compatibility', language: '@language_fr_ca', value: Compatibilité } }
  - { fields: { placeholder: '@components_contributory_factors_classification_tool_packaging', language: '@language_fr_ca', value: Emballage } }
  - { fields: { placeholder: '@components_contributory_factors_classification_quantity_and_quality', language: '@language_fr_ca', value: 'Quantité et qualité' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisational_and_cultural_factors', language: '@language_fr_ca', value: 'Facteurs organisationnels et culturels' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_and_cultural', language: '@language_fr_ca', value: 'Contexte social et culturel' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_norms_and_customs', language: '@language_fr_ca', value: 'Normes et coutumes' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_values_and_assumptions', language: '@language_fr_ca', value: 'Valeurs et hypothèses' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_social_pressure', language: '@language_fr_ca', value: 'Pression sociale' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_diffusion_of_responsibility', language: '@language_fr_ca', value: 'Diffusion de la responsabilité' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_rule_behaviour', language: '@language_fr_ca', value: 'Comportement lié aux règles' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_risk_perception_and_tolerance', language: '@language_fr_ca', value: 'Perception et tolérance des risques' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_honesty', language: '@language_fr_ca', value: 'Ouverture et honnêteté' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_senior_leadership_commitment', language: '@language_fr_ca', value: 'Engagement de la haute direction' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_blame_and_fear', language: '@language_fr_ca', value: 'Blâme et peur' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisation_governance_and_strategy', language: '@language_fr_ca', value: 'Organisation, gouvernance et stratégie' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_lines_of_responsibility', language: '@language_fr_ca', value: 'Lignes de responsabilité' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_strategic_leadership', language: '@language_fr_ca', value: 'Leadership stratégique' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_service_planning', language: '@language_fr_ca', value: 'Planification et conception des services' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_organisational_structure', language: '@language_fr_ca', value: 'Structure organisationnelle' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_risk_management_and_learning', language: '@language_fr_ca', value: 'Gestion des risques et apprentissage' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_oversight_and_monitoring', language: '@language_fr_ca', value: 'Supervision et surveillance' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_resource_management', language: '@language_fr_ca', value: 'Gestion des ressources' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_operational_and_people_management', language: '@language_fr_ca', value: 'Gestion opérationnelle et des personnes' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_bed_management', language: '@language_fr_ca', value: 'Gestion des lits et débits' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_skill_mix', language: '@language_fr_ca', value: 'Mélange de compétences' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staffing_levels', language: '@language_fr_ca', value: 'Niveaux de dotation de personnel' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_rota_and_shift_planning', language: '@language_fr_ca', value: 'Planification des rotations et des équipes' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_staff_turnover', language: '@language_fr_ca', value: 'Roulement de personnel' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_job_roles_and_design', language: '@language_fr_ca', value: 'Rôles et conception du poste' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_recruitment_and_selection', language: '@language_fr_ca', value: 'Recrutement et sélection' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_administration', language: '@language_fr_ca', value: Administration } }
  - { fields: { placeholder: '@components_contributory_factors_classification_sanctions_and_rewards', language: '@language_fr_ca', value: 'Sanctions et récompenses' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_purchasing_and_procurement', language: '@language_fr_ca', value: 'Achats et approvisionnement' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_and_communication_systems', language: '@language_fr_ca', value: 'Systèmes d''information et de communication' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_verbal_communication', language: '@language_fr_ca', value: 'Communication verbale' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_documentation_and_record_keeping', language: '@language_fr_ca', value: 'Documentation et tenue de registres' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_handover_and_briefing', language: '@language_fr_ca', value: 'Mémos et briefing' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_electronic_records_and_it', language: '@language_fr_ca', value: 'Dossiers électroniques et systèmes informatiques' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_accuracy', language: '@language_fr_ca', value: 'Exactitude de l''information' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_information_clarity_and_ambiguity', language: '@language_fr_ca', value: 'Clarté et ambiguïté de l''information' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_external_and_regulatory_factors', language: '@language_fr_ca', value: 'Facteurs externes et réglementaires' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_policy_and_external', language: '@language_fr_ca', value: 'Facteurs réglementaires, politiques et externes' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_national_policy_and_standards', language: '@language_fr_ca', value: 'Politique et normes nationales' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_commissioning_and_contracting', language: '@language_fr_ca', value: 'Mise en service et sous-traitance' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_funding_and_resources', language: '@language_fr_ca', value: 'Financement et ressources' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_activities', language: '@language_fr_ca', value: 'Activités réglementaires' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_other_external_events', language: '@language_fr_ca', value: 'Autres événements externes' } }
  - { fields: { placeholder: '@components_contributory_factors_classification_guidance_not_followed', language: '@language_fr_ca', value: 'Orientation non suivie' } }
  - { fields: { placeholder: '@welcome_page_title', language: '@language_fr_ca', value: 'Bienvenue dans Datix Cloud IQ' } }
  - { fields: { placeholder: '@untitled_section', language: '@language_fr_ca', value: 'Section sans titre' } }
  - { fields: { placeholder: '@language_label_en', language: '@language_fr_ca', value: 'Anglais (Royaume-Uni)' } }
  - { fields: { placeholder: '@language_label_ar', language: '@language_fr_ca', value: Arabe } }
  - { fields: { placeholder: '@language_label_us', language: '@language_fr_ca', value: 'Anglais  (États-Unis)' } }
  - { fields: { placeholder: '@language_label_de_ch', language: '@language_fr_ca', value: 'Suisse allemand' } }
  - { fields: { placeholder: '@splash_page_title', language: '@language_fr_ca', value: Page } }
  - { fields: { placeholder: '@datasource_language', language: '@language_fr_ca', value: Langue } }
  - { fields: { placeholder: '@datasource_religion', language: '@language_fr_ca', value: Religion } }
  - { fields: { placeholder: '@common_node_list_no_other_results', language: '@language_fr_ca', value: 'Pas d''autres résultats' } }
  - { fields: { placeholder: '@common_other_results', language: '@language_fr_ca', value: 'Autres résultats ({{numResults}})' } }
  - { fields: { placeholder: '@common_node_list_view_children', language: '@language_fr_ca', value: 'Afficher {{numChildren}} enfants' } }
  - { fields: { placeholder: '@common_success_save', language: '@language_fr_ca', value: '{{singularLabel}} enregistré avec succès' } }
  - { fields: { placeholder: '@common_error_save', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de {{singularLabel}}' } }
  - { fields: { placeholder: '@common_success_remove', language: '@language_fr_ca', value: '{{singularLabel}} supprimé avec succès' } }
  - { fields: { placeholder: '@common_error_remove', language: '@language_fr_ca', value: 'Erreur lors de la suppression de {{singularLabel}}' } }
  - { fields: { placeholder: '@common_post_save_no_access', language: '@language_fr_ca', value: 'Le dossier a été enregistré, mais les permissions d''accès attribuées à votre utilisateur ne vous permettent pas de le voir' } }
  - { fields: { placeholder: '@common_form_required_fields', language: '@language_fr_ca', value: 'Champs obligatoires' } }
  - { fields: { placeholder: '@common_value_not_set', language: '@language_fr_ca', value: 'Aucune valeur n''a été définie' } }
  - { fields: { placeholder: '@common_loading_options', language: '@language_fr_ca', value: 'Chargement des options...' } }
  - { fields: { placeholder: '@common_please_select_option', language: '@language_fr_ca', value: 'Veuillez sélectionner...' } }
  - { fields: { placeholder: '@common_no_options_available', language: '@language_fr_ca', value: 'Aucune option disponible' } }
  - { fields: { placeholder: '@common_locale', language: '@language_fr_ca', value: Local } }
  - { fields: { placeholder: '@common_select_locale', language: '@language_fr_ca', value: 'Sélectionnez les paramètres régionaux' } }
  - { fields: { placeholder: '@common_components_templates', language: '@language_fr_ca', value: Modèles } }
  - { fields: { placeholder: '@common_components_label_template', language: '@language_fr_ca', value: Modèle } }
  - { fields: { placeholder: '@common_components_templates_new', language: '@language_fr_ca', value: 'Créer un nouveau document' } }
  - { fields: { placeholder: '@common_components_templates_close', language: '@language_fr_ca', value: Fermer } }
  - { fields: { placeholder: '@common_components_templates_title', language: '@language_fr_ca', value: Modèles } }
  - { fields: { placeholder: '@common_all_modules', language: '@language_fr_ca', value: 'Tous les modules' } }
  - { fields: { placeholder: '@common_form_time_is_not_known', language: '@language_fr_ca', value: 'La date/heure n''est pas connue' } }
  - { fields: { placeholder: '@common_form_time', language: '@language_fr_ca', value: Heure } }
  - { fields: { placeholder: '@common_form_start_time', language: '@language_fr_ca', value: 'Date/heure de début' } }
  - { fields: { placeholder: '@common_form_end_time', language: '@language_fr_ca', value: 'Date/heure de fin' } }
  - { fields: { placeholder: '@common_permission_denied', language: '@language_fr_ca', value: 'Permission refusée' } }
  - { fields: { placeholder: '@common_employee_statuses_label', language: '@language_fr_ca', value: 'État de l''employé' } }
  - { fields: { placeholder: '@common_employee_statuses_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@common_employee_statuses_terminated', language: '@language_fr_ca', value: 'A cessé ses fonctions' } }
  - { fields: { placeholder: '@common_employee_statuses_not_started', language: '@language_fr_ca', value: 'Non démarré' } }
  - { fields: { placeholder: '@common_positions_no_positions', language: '@language_fr_ca', value: 'Aucun poste sélectionné' } }
  - { fields: { placeholder: '@common_exception_investigation_not_found', language: '@language_fr_ca', value: 'Enquête introuvable' } }
  - { fields: { placeholder: '@common_exception_unknown_template_reference', language: '@language_fr_ca', value: 'Référence de modèle inconnue' } }
  - { fields: { placeholder: '@common_exception_template_not_found', language: '@language_fr_ca', value: 'Modèle introuvable' } }
  - { fields: { placeholder: '@common_exception_document_was_not_created_successfully', language: '@language_fr_ca', value: 'Le document n''a pas pu être créé' } }
  - { fields: { placeholder: '@common_record_search_no_value_selected', language: '@language_fr_ca', value: 'Aucune valeur sélectionnée' } }
  - { fields: { placeholder: '@common_error_types_forbidden', language: '@language_fr_ca', value: 'Action interdite' } }
  - { fields: { placeholder: '@common_error_see_the_errors_attribute_for_specific_failures', language: '@language_fr_ca', value: 'Veuillez consulter l''attribut des erreurs pour les échecs spécifiques' } }
  - { fields: { placeholder: '@common_actions_next', language: '@language_fr_ca', value: Suivant } }
  - { fields: { placeholder: '@common_actions_previous', language: '@language_fr_ca', value: Précédent } }
  - { fields: { placeholder: '@common_exception_template_invalid_target_type', language: '@language_fr_ca', value: 'Le type de modèle est introuvable' } }
  - { fields: { placeholder: '@common_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@common_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@common_document_page_no_of_pages', language: '@language_fr_ca', value: 'Page {PAGENO} de {nb}' } }
  - { fields: { placeholder: '@common_exception_template_invalid_document_type', language: '@language_fr_ca', value: 'Type de document non valide' } }
  - { fields: { placeholder: '@common_form_validation_number_value_min', language: '@language_fr_ca', value: 'La valeur minimum de ce champ est de {{min}}' } }
  - { fields: { placeholder: '@common_form_validation_number_value_max', language: '@language_fr_ca', value: 'La valeur maximum de ce champ est de {{max}}' } }
  - { fields: { placeholder: '@common_form_validation_number_value_digits', language: '@language_fr_ca', value: 'Ce champ doit contenir uniquement des chiffres' } }
  - { fields: { placeholder: '@audit_actions_record_created', language: '@language_fr_ca', value: 'La {{date}}, {{user}} a créé ce dossier avec les valeurs suivantes' } }
  - { fields: { placeholder: '@audit_actions_record_updated', language: '@language_fr_ca', value: 'Le {{date}}, {{user}} a modifié ce dossier avec les valeurs suivantes' } }
  - { fields: { placeholder: '@audit_actions_record_attached', language: '@language_fr_ca', value: 'Le {{date}}, {{user}} a associé le dossier suivant' } }
  - { fields: { placeholder: '@audit_actions_record_detached', language: '@language_fr_ca', value: 'Le {{date}}, {{user}} a dissocié le dossier suivant' } }
  - { fields: { placeholder: '@audit_no_logs_available', language: '@language_fr_ca', value: 'Les journaux de vérification ne peuvent pas être affichés pour ce dossier. Pour accéder à ces données, veuillez communiquer avec un administrateur.' } }
  - { fields: { placeholder: '@common_upload_file', language: '@language_fr_ca', value: 'Téléverser le fichier' } }
  - { fields: { placeholder: '@common_file_upload_percent_complete', language: '@language_fr_ca', value: 'Pourcentage d''achèvement' } }
  - { fields: { placeholder: '@common_file_upload_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@common_file_upload_retry', language: '@language_fr_ca', value: Réessayer } }
  - { fields: { placeholder: '@common_file_upload_error', language: '@language_fr_ca', value: 'Une erreur s''est produite au téléversement du fichier' } }
  - { fields: { placeholder: '@common_file_upload_replace_file', language: '@language_fr_ca', value: 'Remplacer le fichier' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_twelve', language: '@language_fr_ca', value: 'UTC-12:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_eleven', language: '@language_fr_ca', value: 'UTC-11:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_ten', language: '@language_fr_ca', value: 'UTC-10:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_nine_half', language: '@language_fr_ca', value: 'UTC-09:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_nine', language: '@language_fr_ca', value: 'UTC-09:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_eight', language: '@language_fr_ca', value: 'UTC-08:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_seven', language: '@language_fr_ca', value: 'UTC-07:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_six', language: '@language_fr_ca', value: 'UTC-06:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_five', language: '@language_fr_ca', value: 'UTC-05:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_four', language: '@language_fr_ca', value: 'UTC-04:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_three_half', language: '@language_fr_ca', value: 'UTC-03:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_three', language: '@language_fr_ca', value: 'UTC-03:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_two', language: '@language_fr_ca', value: 'UTC-02:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_minus_one', language: '@language_fr_ca', value: 'UTC-01:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_zero', language: '@language_fr_ca', value: 'UTCÂ±00:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_one', language: '@language_fr_ca', value: 'UTC+01:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_two', language: '@language_fr_ca', value: 'UTC+02:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_three', language: '@language_fr_ca', value: 'UTC+03:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_three_half', language: '@language_fr_ca', value: 'UTC+03:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_four', language: '@language_fr_ca', value: 'UTC+04:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_four_half', language: '@language_fr_ca', value: 'UTC+04:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five', language: '@language_fr_ca', value: 'UTC+05:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five_half', language: '@language_fr_ca', value: 'UTC+05:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_five_three_quarters', language: '@language_fr_ca', value: 'UTC+05:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_six', language: '@language_fr_ca', value: 'UTC+06:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_six_half', language: '@language_fr_ca', value: 'UTC+06:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_seven', language: '@language_fr_ca', value: 'UTC+07:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eight', language: '@language_fr_ca', value: 'UTC+08:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eight_three_quarters', language: '@language_fr_ca', value: 'UTC+08:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_nine', language: '@language_fr_ca', value: 'UTC+09:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_nine_half', language: '@language_fr_ca', value: 'UTC+09:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_ten', language: '@language_fr_ca', value: 'UTC+10:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_ten_half', language: '@language_fr_ca', value: 'UTC+10:30' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_eleven', language: '@language_fr_ca', value: 'UTC+11:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_twelve', language: '@language_fr_ca', value: 'UTC+12:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_twelve_three_quarters', language: '@language_fr_ca', value: 'UTC+12:45' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_thirteen', language: '@language_fr_ca', value: 'UTC+13:00' } }
  - { fields: { placeholder: '@placeholder_utc_offsets_plus_fourteen', language: '@language_fr_ca', value: 'UTC+14:00' } }
  - { fields: { placeholder: '@common_exceptions_inactive_feature', language: '@language_fr_ca', value: 'La fonctionnalité n''est pas active' } }
  - { fields: { placeholder: '@common_exceptions_attachments_not_found', language: '@language_fr_ca', value: 'La pièce jointe est introuvable' } }
  - { fields: { placeholder: '@common_exceptions_attachments_invalid_template_type', language: '@language_fr_ca', value: 'La pièce jointe doit être un rapport d''enquête ou un rapport RIB' } }
  - { fields: { placeholder: '@common_exceptions_attachments_missing_reporter_email', language: '@language_fr_ca', value: 'Le courriel de rapport n''est pas défini' } }
  - { fields: { placeholder: '@common_exceptions_attachments_email_failed_to_send', language: '@language_fr_ca', value: 'Échec de l''envoi du courriel du rapport' } }
  - { fields: { placeholder: '@common_exceptions_validation_error', language: '@language_fr_ca', value: 'Erreur de validation' } }
  - { fields: { placeholder: '@nav_redress', language: '@language_fr_ca', value: Redressement } }
  - { fields: { placeholder: '@common_components_tree_status_enabled', language: '@language_fr_ca', value: Activé } }
  - { fields: { placeholder: '@common_components_tree_status_deactivated', language: '@language_fr_ca', value: Désactivé } }
  - { fields: { placeholder: '@common_components_tree_status_disabled', language: '@language_fr_ca', value: Désactivé } }
  - { fields: { placeholder: '@common_components_tree_node_first_page', language: '@language_fr_ca', value: 'Première page' } }
  - { fields: { placeholder: '@common_components_tree_node_next_page', language: '@language_fr_ca', value: 'Page suivante' } }
  - { fields: { placeholder: '@common_components_tree_node_last_page', language: '@language_fr_ca', value: 'Dernière page' } }
  - { fields: { placeholder: '@common_components_tree_node_previous_page', language: '@language_fr_ca', value: 'Page précédente' } }
  - { fields: { placeholder: '@common_components_attachments_disabled_for_public', language: '@language_fr_ca', value: 'Les téléversements de fichiers ne sont pas disponibles pour les utilisateurs déconnectés' } }
  - { fields: { placeholder: '@email_anonymous_author', language: '@language_fr_ca', value: Anonyme } }
  - { fields: { placeholder: '@email_not_applicable', language: '@language_fr_ca', value: S.O. } }
  - { fields: { placeholder: '@nav_safeguarding', language: '@language_fr_ca', value: Protection } }
  - { fields: { placeholder: '@capture_modules_com_label', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@capture_modules_cla_label', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@capture_modules_inc_label', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@capture_modules_das_label', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@capture_modules_red_label', language: '@language_fr_ca', value: 'Redressement' } }
  - { fields: { placeholder: '@capture_modules_sfg_label', language: '@language_fr_ca', value: 'Protection' } }
  - { fields: { placeholder: '@common_modules_safeguarding', language: '@language_fr_ca', value: Protection } }
  - { fields: { placeholder: '@common_drafts_discard', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@common_drafts_publish_now', language: '@language_fr_ca', value: 'Publier maintenant' } }
  - { fields: { placeholder: '@common_drafts_publish_later', language: '@language_fr_ca', value: 'Publier plus tard' } }
  - { fields: { placeholder: '@common_drafts_edit_draft', language: '@language_fr_ca', value: 'Modifier l''ébauche' } }
  - { fields: { placeholder: '@placeholder_erm_risk_linked_records_list_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@common_error_see_the_errors_attribute_for_specific_failures_erm1', language: '@language_fr_ca', value: '{{ermCode}} Veuillez consulter l''attribut des erreurs pour les échecs spécifiques' } }
  - { fields: { placeholder: '@common_errors_resource_not_found_erm14', language: '@language_fr_ca', value: '{{ermCode}} Ressource introuvable' } }
  - { fields: { placeholder: '@common_form_validation_errors_erm15', language: '@language_fr_ca', value: '{{ermCode}} Le formulaire soumis contient des erreurs de validation' } }
  - { fields: { placeholder: '@common_attachment_saved_successfully', language: '@language_fr_ca', value: 'Pièce jointe enregistrée avec succès' } }
  - { fields: { placeholder: '@system_admin_system_configuration_copy_location_service_from_source_to_action', language: '@language_fr_ca', value: 'Activer la copie des emplacements et services de l’enregistrement source dans l’action liée?' } }
