entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@claim_plural', language: '@language_ar', value: المطالبات } }
    - { fields: { placeholder: '@claim_singular', language: '@language_ar', value: المطالبات } }
    - { fields: { placeholder: '@common_loading_permissions', language: '@language_ar', value: 'تحميل الأذونات' } }
    - { fields: { placeholder: '@common_access_control', language: '@language_ar', value: 'صلاحية الدخول' } }
    - { fields: { placeholder: '@common_access_control_generated_roles', language: '@language_ar', value: ' أدوار مبتكره' } }
    - { fields: { placeholder: '@common_access_control_manual_roles', language: '@language_ar', value: 'أدوار يدوية' } }
    - { fields: { placeholder: '@common_access_control_no_assignable_roles', language: '@language_ar', value: 'لا أدوار قابلة للتخصيص' } }
    - { fields: { placeholder: '@common_administration', language: '@language_ar', value: إدارة } }
    - { fields: { placeholder: '@common_administration_permissions', language: '@language_ar', value: ضوابط } }
    - { fields: { placeholder: '@common_administration_tools', language: '@language_ar', value: أدوات } }
    - { fields: { placeholder: '@common_are_you_sure', language: '@language_ar', value: 'هل أنت متأكد؟' } }
    - { fields: { placeholder: '@common_back', language: '@language_ar', value: 'الى الخلف' } }
    - { fields: { placeholder: '@common_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@common_category', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@common_clear', language: '@language_ar', value: واضح } }
    - { fields: { placeholder: '@common_close', language: '@language_ar', value: أغلق } }
    - { fields: { placeholder: '@common_components_attachments_add', language: '@language_ar', value: 'إضافة مرفق' } }
    - { fields: { placeholder: '@common_components_attachments_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@common_components_attachments_classification', language: '@language_ar', value: تصنيف } }
    - { fields: { placeholder: '@common_components_attachments_classification_values_document', language: '@language_ar', value: وثيقة } }
    - { fields: { placeholder: '@common_components_attachments_classification_values_image', language: '@language_ar', value: صورة } }
    - { fields: { placeholder: '@common_components_attachments_classification_values_spreadsheet', language: '@language_ar', value: جدول } }
    - { fields: { placeholder: '@common_components_attachments_default_allowed_file_types', language: '@language_ar', value: 'ملفات المرفق قد تكون الصور، وثائق وورد ، وجداول البيانات أو ملفات PDF' } }
    - { fields: { placeholder: '@common_components_attachments_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@common_components_attachments_download', language: '@language_ar', value: 'تنزيل المرفقات' } }
    - { fields: { placeholder: '@common_components_attachments_edit', language: '@language_ar', value: تحرير } }
    - { fields: { placeholder: '@common_components_attachments_edit_attachment', language: '@language_ar', value: 'تحرير المرفق' } }
    - { fields: { placeholder: '@common_components_attachments_error', language: '@language_ar', value: 'حدث خطأ في تحميل ملفك.' } }
    - { fields: { placeholder: '@common_components_attachments_loading', language: '@language_ar', value: 'تحميل المرفقات' } }
    - { fields: { placeholder: '@common_components_attachments_new', language: '@language_ar', value: 'مرفق جديد' } }
    - { fields: { placeholder: '@common_components_attachments_percent_complete', language: '@language_ar', value: 'إكتمل {{percent}}%' } }
    - { fields: { placeholder: '@common_components_attachments_plural', language: '@language_ar', value: مرفقات } }
    - { fields: { placeholder: '@common_components_attachments_replace', language: '@language_ar', value: 'استبدال المرفق' } }
    - { fields: { placeholder: '@common_components_attachments_retry', language: '@language_ar', value: 'إعادة المحاولة' } }
    - { fields: { placeholder: '@common_components_attachments_save', language: '@language_ar', value: 'حفظ المرفق' } }
    - { fields: { placeholder: '@common_components_attachments_select_classification', language: '@language_ar', value: 'اختر التصنيف' } }
    - { fields: { placeholder: '@common_components_attachments_singular', language: '@language_ar', value: المرفق } }
    - { fields: { placeholder: '@common_components_attachments_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@common_components_attachments_uploaded', language: '@language_ar', value: 'تم تحميلها' } }
    - { fields: { placeholder: '@common_components_attachments_version', language: '@language_ar', value: 'الإصدار {{number}}' } }
    - { fields: { placeholder: '@common_components_date_filter_end_date', language: '@language_ar', value: 'تاريخ الانتهاء' } }
    - { fields: { placeholder: '@common_components_date_filter_start_date', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@common_components_feedback_button', language: '@language_ar', value: 'إعطاء ردود الفعل' } }
    - { fields: { placeholder: '@common_components_feedback_rate', language: '@language_ar', value: 'كيف تقيم هذه الصفحة؟' } }
    - { fields: { placeholder: '@common_components_feedback_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ ردود الفعل' } }
    - { fields: { placeholder: '@common_components_feedback_submitted', language: '@language_ar', value: قدمت } }
    - { fields: { placeholder: '@common_components_feedback_thank_you', language: '@language_ar', value: 'شكرا لك على ملاحظاتك!' } }
    - { fields: { placeholder: '@common_components_magma_relationship_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@common_components_magma_relationship_close_search', language: '@language_ar', value: 'إغلاق البحث' } }
    - { fields: { placeholder: '@common_components_magma_relationship_create', language: '@language_ar', value: إنشاء } }
    - { fields: { placeholder: '@common_components_magma_relationship_loading', language: '@language_ar', value: 'جار التحميل' } }
    - { fields: { placeholder: '@common_components_magma_relationship_save', language: '@language_ar', value: حفظ } }
    - { fields: { placeholder: '@common_components_magma_relationship_saving', language: '@language_ar', value: 'جاري الحفظ' } }
    - { fields: { placeholder: '@common_components_share', language: '@language_ar', value: 'ما الذي تريد مشاركته معنا؟' } }
    - { fields: { placeholder: '@common_confirm', language: '@language_ar', value: تؤكد } }
    - { fields: { placeholder: '@common_copied', language: '@language_ar', value: نسخ } }
    - { fields: { placeholder: '@common_create', language: '@language_ar', value: إنشاء } }
    - { fields: { placeholder: '@common_date', language: '@language_ar', value: تاريخ } }
    - { fields: { placeholder: '@common_delete', language: '@language_ar', value: حذف } }
    - { fields: { placeholder: '@common_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@common_due', language: '@language_ar', value: بسبب } }
    - { fields: { placeholder: '@common_edit', language: '@language_ar', value: تحرير } }
    - { fields: { placeholder: '@common_error', language: '@language_ar', value: خطأ } }
    - { fields: { placeholder: '@common_errors_entity_forbidden_entity', language: '@language_ar', value: 'كيان ممنوع' } }
    - { fields: { placeholder: '@common_errors_entity_not_found', language: '@language_ar', value: 'الكيان غير موجود' } }
    - { fields: { placeholder: '@common_errors_forbidden', language: '@language_ar', value: ممنوع } }
    - { fields: { placeholder: '@common_errors_get_form', language: '@language_ar', value: 'حدث خطأ أثناء استرداد النموذج' } }
    - { fields: { placeholder: '@common_errors_missing_fields', language: '@language_ar', value: 'يرجى التأكد من توفير جميع الحقول المطلوبة' } }
    - { fields: { placeholder: '@common_errors_resource_not_found', language: '@language_ar', value: 'الموارد غير موجود' } }
    - { fields: { placeholder: '@common_filter', language: '@language_ar', value: منقي } }
    - { fields: { placeholder: '@common_filter_indicator', language: '@language_ar', value: 'منقي{{ filterIndicator }}' } }
    - { fields: { placeholder: '@common_form_validation_email', language: '@language_ar', value: 'رجاء قم بإدخال بريد الكتروني صحيح' } }
    - { fields: { placeholder: '@common_form_validation_errors', language: '@language_ar', value: 'يحتوي النموذج المقدم على أخطاء التحقق' } }
    - { fields: { placeholder: '@common_form_validation_required', language: '@language_ar', value: 'هذه الخانة مطلوبه' } }
    - { fields: { placeholder: '@common_form_validation_string_length_max', language: '@language_ar', value: 'لا يجوز أن يتجاوز هذا الحقل {{max}} حرفًا' } }
    - { fields: { placeholder: '@common_form_validation_string_length_min', language: '@language_ar', value: 'يجب ألا يقل طول هذا الحقل عن {{min}} حرفا' } }
    - { fields: { placeholder: '@common_generic_error', language: '@language_ar', value: 'حدث خطأ' } }
    - { fields: { placeholder: '@common_help', language: '@language_ar', value: مساعدة } }
    - { fields: { placeholder: '@common_id', language: '@language_ar', value: 'هوية شخصية' } }
    - { fields: { placeholder: '@common_loading', language: '@language_ar', value: 'جار التحميل' } }
    - { fields: { placeholder: '@common_module', language: '@language_ar', value: الوحدة } }
    - { fields: { placeholder: '@common_module_control', language: '@language_ar', value: الضبط } }
    - { fields: { placeholder: '@common_modules_acl', language: '@language_ar', value: 'قائمة التحكم في التوصل' } }
    - { fields: { placeholder: '@common_modules_acl_role', language: '@language_ar', value: 'قائمة التحكم في التوصل - دور' } }
    - { fields: { placeholder: '@common_modules_acl_rule', language: '@language_ar', value: 'قائمة التحكم في التوصل - القاعدة' } }
    - { fields: { placeholder: '@common_modules_actions', language: '@language_ar', value: إجراء } }
    - { fields: { placeholder: '@common_modules_claim', language: '@language_ar', value: مطالبة } }
    - { fields: { placeholder: '@common_modules_clinical_audit', language: '@language_ar', value: 'التدقيق السريري' } }
    - { fields: { placeholder: '@common_modules_compliance_assessment', language: '@language_ar', value: 'تقييم الامتثال' } }
    - { fields: { placeholder: '@common_modules_compliance_assessment_assessment', language: '@language_ar', value: 'تقييم الامتثال - تقدير' } }
    - { fields: { placeholder: '@common_modules_compliance_assessment_programme', language: '@language_ar', value: 'تقييم الامتثال - برنامج' } }
    - { fields: { placeholder: '@common_modules_compliance_assessment_response', language: '@language_ar', value: 'تقييم الامتثال - رد' } }
    - { fields: { placeholder: '@common_modules_compliance_assessment_standard', language: '@language_ar', value: 'تقييم الامتثال - اساسي' } }
    - { fields: { placeholder: '@common_modules_contact', language: '@language_ar', value: اتصل } }
    - { fields: { placeholder: '@common_modules_contacts', language: '@language_ar', value: 'جهات الاتصال' } }
    - { fields: { placeholder: '@common_modules_controls', language: '@language_ar', value: ضوابط } }
    - { fields: { placeholder: '@common_modules_equipment', language: '@language_ar', value: معدات } }
    - { fields: { placeholder: '@common_modules_erm', language: '@language_ar', value: 'إدارة المخاطر المؤسسية' } }
    - { fields: { placeholder: '@common_modules_erm_register', language: '@language_ar', value: 'إدارة المخاطر المؤسسية - تسجيل' } }
    - { fields: { placeholder: '@common_modules_erm_risk', language: '@language_ar', value: 'إدارة المخاطر المؤسسية - خطر' } }
    - { fields: { placeholder: '@common_modules_erm_tracker', language: '@language_ar', value: 'إدارة المخاطر المؤسسية - تعقب' } }
    - { fields: { placeholder: '@common_modules_feedback', language: '@language_ar', value: 'ردود الفعل' } }
    - { fields: { placeholder: '@common_modules_form', language: '@language_ar', value: نموذج } }
    - { fields: { placeholder: '@common_modules_form_field', language: '@language_ar', value: 'حقل النموذج' } }
    - { fields: { placeholder: '@common_modules_forms', language: '@language_ar', value: ' النماذج' } }
    - { fields: { placeholder: '@common_modules_incident', language: '@language_ar', value: حادث } }
    - { fields: { placeholder: '@common_modules_investigation', language: '@language_ar', value: تحقيق } }
    - { fields: { placeholder: '@common_modules_investigations', language: '@language_ar', value: تحقيقات } }
    - { fields: { placeholder: '@common_modules_location', language: '@language_ar', value: موقعك } }
    - { fields: { placeholder: '@common_modules_locations', language: '@language_ar', value: مواقع } }
    - { fields: { placeholder: '@common_modules_medication', language: '@language_ar', value: أدوية } }
    - { fields: { placeholder: '@common_modules_medications', language: '@language_ar', value: الأدوية } }
    - { fields: { placeholder: '@common_modules_mortality', language: '@language_ar', value: 'معدل الوفيات' } }
    - { fields: { placeholder: '@common_modules_prince', language: '@language_ar', value: أمير } }
    - { fields: { placeholder: '@common_modules_recommendation', language: '@language_ar', value: توصية } }
    - { fields: { placeholder: '@common_modules_recommendations', language: '@language_ar', value: توصيات } }
    - { fields: { placeholder: '@common_modules_safety_round', language: '@language_ar', value: 'جولة السلامة' } }
    - { fields: { placeholder: '@common_modules_safety_rounds', language: '@language_ar', value: 'جولات السلامة' } }
    - { fields: { placeholder: '@common_modules_safety_rounds_template', language: '@language_ar', value: 'جولات السلامة - القالب' } }
    - { fields: { placeholder: '@common_modules_safety_rounds_response', language: '@language_ar', value: 'جولات السلامة - الاستجابة' } }
    - { fields: { placeholder: '@common_modules_safety_rounds_summary', language: '@language_ar', value: 'جولات السلامة - ملخص' } }
    - { fields: { placeholder: '@common_modules_service', language: '@language_ar', value: الخدمات } }
    - { fields: { placeholder: '@common_modules_services', language: '@language_ar', value: خدمات } }
    - { fields: { placeholder: '@common_modules_survey', language: '@language_ar', value: استبيان } }
    - { fields: { placeholder: '@common_modules_surveys', language: '@language_ar', value: الاستبيانات } }
    - { fields: { placeholder: '@common_modules_user', language: '@language_ar', value: المستخدم } }
    - { fields: { placeholder: '@common_modules_user_group', language: '@language_ar', value: 'مجموعة المستخدم' } }
    - { fields: { placeholder: '@common_modules_users', language: '@language_ar', value: المستخدمين } }
    - { fields: { placeholder: '@common_n_a', language: '@language_ar', value: 'غير مصرح' } }
    - { fields: { placeholder: '@common_new', language: '@language_ar', value: الجديد } }
    - { fields: { placeholder: '@common_no', language: '@language_ar', value: لا } }
    - { fields: { placeholder: '@common_not_set', language: '@language_ar', value: 'غير مضبوط' } }
    - { fields: { placeholder: '@common_on', language: '@language_ar', value: لا } }
    - { fields: { placeholder: '@common_optional', language: '@language_ar', value: اختياري } }
    - { fields: { placeholder: '@common_ref', language: '@language_ar', value: المرجع } }
    - { fields: { placeholder: '@common_reply', language: '@language_ar', value: الرد } }
    - { fields: { placeholder: '@common_role', language: '@language_ar', value: دور } }
    - { fields: { placeholder: '@common_save', language: '@language_ar', value: حفظ } }
    - { fields: { placeholder: '@common_save_and_close', language: '@language_ar', value: 'حفظ وإغلاق' } }
    - { fields: { placeholder: '@common_save_changes', language: '@language_ar', value: 'حفظ التغييرات' } }
    - { fields: { placeholder: '@common_save_error', language: '@language_ar', value: 'حفظ الخطأ' } }
    - { fields: { placeholder: '@common_saved_successfully', language: '@language_ar', value: 'حفظ بنجاح' } }
    - { fields: { placeholder: '@common_saving', language: '@language_ar', value: 'جاري الحفظ' } }
    - { fields: { placeholder: '@common_search', language: '@language_ar', value: بحث } }
    - { fields: { placeholder: '@common_select', language: '@language_ar', value: تحديد } }
    - { fields: { placeholder: '@common_select_an_option', language: '@language_ar', value: 'حدد اختيارا' } }
    - { fields: { placeholder: '@common_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@common_subcategory', language: '@language_ar', value: فرعية } }
    - { fields: { placeholder: '@common_submit', language: '@language_ar', value: قدم } }
    - { fields: { placeholder: '@common_subtype', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@common_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@common_type', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@common_unauthorised', language: '@language_ar', value: 'غير مصرح' } }
    - { fields: { placeholder: '@common_unauthorised_page_access', language: '@language_ar', value: 'ليس لديك إذن للوصول إلى هذه الصفحة' } }
    - { fields: { placeholder: '@common_view_details', language: '@language_ar', value: 'عرض التفاصيل' } }
    - { fields: { placeholder: '@common_yes', language: '@language_ar', value: 'نعم ' } }
    - { fields: { placeholder: '@complaint_plural', language: '@language_ar', value: 'ردود الفعل' } }
    - { fields: { placeholder: '@complaint_singular', language: '@language_ar', value: 'ردود الفعل' } }
    - { fields: { placeholder: '@components_confirm_button', language: '@language_ar', value: 'زرالتأكيد ' } }
    - { fields: { placeholder: '@components_confirm_button_text', language: '@language_ar', value: 'هل أنت متأكد؟' } }
    - { fields: { placeholder: '@components_contributory_factors_add_contributory_factor', language: '@language_ar', value: 'إضافة عامل المساهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_category', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_abnormal_physiology', language: '@language_ar', value: 'فسيولوجيا غير طبيعية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_acceptability_and_agreement', language: '@language_ar', value: 'إمكانية القبول و الاتفاقية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_accessibility', language: '@language_ar', value: الوصول } }
    - { fields: { placeholder: '@components_contributory_factors_classification_adequacy_and_accuracy', language: '@language_ar', value: 'الكفاية والدقة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_administration', language: '@language_ar', value: إدارة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_aggression', language: '@language_ar', value: التعدي } }
    - { fields: { placeholder: '@components_contributory_factors_classification_alarms_and_warnings', language: '@language_ar', value: 'الإنذارات والتحذيرات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_alcohol_or_drugs', language: '@language_ar', value: 'الكحول أو المخدرات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_appropriate_training', language: '@language_ar', value: 'التدريب المناسب' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_assessment_and_qualification', language: '@language_ar', value: 'التقييم والتأهيل' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_attitudes', language: '@language_ar', value: 'موقف سلوك' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_availability_and_accessibility', language: '@language_ar', value: 'التوفر وإمكانية الوصول' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_bed_management', language: '@language_ar', value: 'إدارة السرير و تدفقه' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_blame_and_fear', language: '@language_ar', value: 'اللوم والخوف' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_clarity_and_coherence', language: '@language_ar', value: 'الوضوح والترابط' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_commissioning_and_contracting', language: '@language_ar', value: 'التكليف والتعاقد' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_communication', language: '@language_ar', value: الاتصالات } }
    - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_ar', value: تعقيد } }
    - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_ar', value: تعقيد } }
    - { fields: { placeholder: '@components_contributory_factors_classification_conflicting_tasks', language: '@language_ar', value: 'المهام المتضاربة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_consent', language: '@language_ar', value: موافقة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_coordination', language: '@language_ar', value: تنسيق } }
    - { fields: { placeholder: '@components_contributory_factors_classification_currency', language: '@language_ar', value: دقة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_diffusion_of_responsibility', language: '@language_ar', value: 'نشر المسؤولية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_displays_and_interfaces', language: '@language_ar', value: 'يعرض واجهات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_distractions', language: '@language_ar', value: 'الانحرافات والانقطاعات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_documentation_and_record_keeping', language: '@language_ar', value: 'الوثائق وحفظ السجلات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_education_quality', language: '@language_ar', value: 'جودة التعليم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_effective_communication', language: '@language_ar', value: 'التواصل الفعال' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_electronic_records_and_it', language: '@language_ar', value: 'السجلات الإلكترونية وأنظمة تكنولوجيا المعلومات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_experience', language: '@language_ar', value: تجربة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_external_and_regulatory_factors', language: '@language_ar', value: 'العوامل الخارجية والتنظيمية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_fatigue', language: '@language_ar', value: تعب } }
    - { fields: { placeholder: '@components_contributory_factors_classification_fixtures_and_fittings', language: '@language_ar', value: 'تركيبات والتجهيزات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_functionality', language: '@language_ar', value: وظائف } }
    - { fields: { placeholder: '@components_contributory_factors_classification_funding_and_resources', language: '@language_ar', value: 'التمويل والموارد' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_handover_and_briefing', language: '@language_ar', value: 'التسليم والإحاطة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_health', language: '@language_ar', value: 'الحالة الصحية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_high_workload', language: '@language_ar', value: 'ارتفاع عبء العمل' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_honesty', language: '@language_ar', value: 'الانفتاح والصدق' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_housekeeping', language: '@language_ar', value: 'التدبير المنزلي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_human_and_relational', language: '@language_ar', value: 'العوامل البشرية والعلائقية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_implementation_and_usage', language: '@language_ar', value: 'التنفيذ والاستخدام' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_incapacity', language: '@language_ar', value: 'عدم القدرة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_individual_and_personal', language: '@language_ar', value: 'العوامل الفردية والشخصية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_induction', language: '@language_ar', value: 'التحريض والتعرف' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_information_accuracy', language: '@language_ar', value: 'دقة المعلومات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_information_and_communication_systems', language: '@language_ar', value: 'نظم المعلومات والاتصالات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_information_clarity_and_ambiguity', language: '@language_ar', value: 'وضوح المعلومات والغموض' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_job_roles_and_design', language: '@language_ar', value: 'أدوار الوظيفة والتصميم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_knowledge', language: '@language_ar', value: المعرفه } }
    - { fields: { placeholder: '@components_contributory_factors_classification_layout_and_design', language: '@language_ar', value: 'تخطيط وتصميم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_lighting', language: '@language_ar', value: إضاءة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_lines_of_responsibility', language: '@language_ar', value: 'خطوط المسؤولية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_local_leadership', language: '@language_ar', value: 'القيادة المحلية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_low_workload', language: '@language_ar', value: 'عبء عمل منخفض' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_maintenance', language: '@language_ar', value: 'اعمال صيانة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_morale', language: '@language_ar', value: 'المعنويات والدوافع' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_mutual_respect', language: '@language_ar', value: 'احترام متبادل' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_national_policy_and_standards', language: '@language_ar', value: 'السياسة والمعايير الوطنية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_noise', language: '@language_ar', value: الضوضاء } }
    - { fields: { placeholder: '@components_contributory_factors_classification_non_compliance', language: '@language_ar', value: 'عدم الامتثال' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_norms_and_customs', language: '@language_ar', value: 'القواعد والعادات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_operational_and_people_management', language: '@language_ar', value: 'التشغيلية وإدارة الأفراد' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_organisation_governance_and_strategy', language: '@language_ar', value: 'التنظيم والحوكمة والاستراتيجية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_organisational_and_cultural_factors', language: '@language_ar', value: 'العوامل التنظيمية والثقافية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_organisational_structure', language: '@language_ar', value: 'الهيكل التنظيمي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@components_contributory_factors_classification_other_external_events', language: '@language_ar', value: 'أحداث خارجية أخرى' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_oversight_and_monitoring', language: '@language_ar', value: 'الرقابة والمراقبة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_patient_and_family', language: '@language_ar', value: 'عوامل المريض والعائلة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_personal_domestic_issues', language: '@language_ar', value: 'القضايا الشخصية أو المحلية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_physical_work_environment', language: '@language_ar', value: 'بيئة العمل المادية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_practicality', language: '@language_ar', value: 'التطبيق العملي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_procedures_protocols_and_guidelines', language: '@language_ar', value: 'الإجراءات والبروتوكولات والمبادئ التوجيهية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_production_pressure', language: '@language_ar', value: 'ضغط الإنتاج' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_psychological_safety', language: '@language_ar', value: 'السلامة النفسية والانفتاح' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_purchasing_and_procurement', language: '@language_ar', value: 'الشراء والشراء' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_quantity_and_quality', language: '@language_ar', value: 'كمية ونوعية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_recruitment_and_selection', language: '@language_ar', value: 'التوظيف والاختيار' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_activities', language: '@language_ar', value: 'الأنشطة التنظيمية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_regulatory_policy_and_external', language: '@language_ar', value: 'العوامل التنظيمية والسياسة والعوامل الخارجية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_reliability', language: '@language_ar', value: الموثوقية } }
    - { fields: { placeholder: '@components_contributory_factors_classification_religious_beliefs', language: '@language_ar', value: 'المعتقدات الدينية أو الثقافية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_resource_management', language: '@language_ar', value: 'إدارة الموارد' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_revalidation_and_competency', language: '@language_ar', value: 'إعادة التأهيل والكفاءة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_risk_management_and_learning', language: '@language_ar', value: 'إدارة المخاطر والتعلم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_risk_perception_and_tolerance', language: '@language_ar', value: 'إدراك المخاطر والتسامح' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_rota_and_shift_planning', language: '@language_ar', value: 'روتا والتخطيط التحول' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_rule_behaviour', language: '@language_ar', value: 'السلوك ذو الصلة بالقاعدة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_sanctions_and_rewards', language: '@language_ar', value: 'العقوبات والمكافآت' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_security', language: '@language_ar', value: الأمان } }
    - { fields: { placeholder: '@components_contributory_factors_classification_senior_leadership_commitment', language: '@language_ar', value: 'التزام القيادة العليا' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_service_planning', language: '@language_ar', value: 'تخطيط الخدمة والتصميم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_shared_understanding', language: '@language_ar', value: 'الفهم المشترك والوعي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_simulation_training', language: '@language_ar', value: 'تدريب المحاكاة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_skill', language: '@language_ar', value: مهارة } }
    - { fields: { placeholder: '@components_contributory_factors_classification_skill_mix', language: '@language_ar', value: 'مزيج المهارة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_social_and_cultural', language: '@language_ar', value: 'السياق الاجتماعي والثقافي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_social_pressure', language: '@language_ar', value: 'ضغط اجتماعي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_social_support', language: '@language_ar', value: 'دعم اجتماعي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_staff_support', language: '@language_ar', value: 'دعم للموظفين' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_staff_turnover', language: '@language_ar', value: 'انقلاب الموظفين' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_staffing_levels', language: '@language_ar', value: 'مستويات التوظيف' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_strategic_leadership', language: '@language_ar', value: 'القيادة الاستراتيجية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_stress', language: '@language_ar', value: 'ضغط عصبى' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_supervision', language: '@language_ar', value: إشراف } }
    - { fields: { placeholder: '@components_contributory_factors_classification_task_and_environmental', language: '@language_ar', value: 'العوامل المهمة والبيئية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_task_complexity', language: '@language_ar', value: 'تعقيد المهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_task_demands', language: '@language_ar', value: 'متطلبات المهمة وعبء العمل' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_task_difficulty', language: '@language_ar', value: 'صعوبة المهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_task_practicality', language: '@language_ar', value: 'المهمة العملية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_team_training', language: '@language_ar', value: 'تدريب الفريق' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_teamwork_leadership_supervision', language: '@language_ar', value: 'العمل الجماعي والقيادة والإشراف' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_temperature', language: '@language_ar', value: 'درجة الحرارة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_temporary_impairment', language: '@language_ar', value: 'ضعف مؤقت' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_time_availability', language: '@language_ar', value: 'توفر الوقت' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_time_of_day', language: '@language_ar', value: 'وقت اليوم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_availability', language: '@language_ar', value: الوصول } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_compatibility', language: '@language_ar', value: التوافق } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_design', language: '@language_ar', value: التصميم } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_installation', language: '@language_ar', value: التركيب } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_maintenance', language: '@language_ar', value: 'اعمال صيانة' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tool_packaging', language: '@language_ar', value: 'التعبئة والتغليف' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_tools_equipment_and_resources', language: '@language_ar', value: 'الأدوات والمعدات والموارد' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_training_availability', language: '@language_ar', value: 'التدريب المتاحه' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_training_education_assessment', language: '@language_ar', value: 'التدريب والتعليم والتقييم' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_training_monitoring', language: '@language_ar', value: 'رصد التدريب' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_uncertainty_and_variability', language: '@language_ar', value: 'عدم اليقين والتنوع' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_values_and_assumptions', language: '@language_ar', value: 'القيم والافتراضات' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_verbal_communication', language: '@language_ar', value: 'التواصل اللفظي' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_visibility', language: '@language_ar', value: 'الرؤية وخطوط الرؤية' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_ar', value: تعقيد } }
    - { fields: { placeholder: '@components_contributory_factors_classification_complexity', language: '@language_ar', value: تعقيد } }
    - { fields: { placeholder: '@components_contributory_factors_classification_guidance_not_followed', language: '@language_ar', value: 'التوجيه غير متابع' } }
    - { fields: { placeholder: '@components_contributory_factors_classification_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@components_contributory_factors_new_contributory_factor', language: '@language_ar', value: 'اكتب ملاحظة' } }
    - { fields: { placeholder: '@components_contributory_factors_no_contributory_factors', language: '@language_ar', value: 'لا يحتوي هذا السجل على أية عوامل مساهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_plural', language: '@language_ar', value: 'العوامل المساهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_select_category', language: '@language_ar', value: 'ختر الفئة' } }
    - { fields: { placeholder: '@components_contributory_factors_select_sub_category', language: '@language_ar', value: 'اختر الفئة الفرعية' } }
    - { fields: { placeholder: '@components_contributory_factors_select_type', language: '@language_ar', value: 'اختر صنف' } }
    - { fields: { placeholder: '@components_contributory_factors_singular', language: '@language_ar', value: 'عامل المساهمة' } }
    - { fields: { placeholder: '@components_contributory_factors_sub_category', language: '@language_ar', value: 'الفئة الفرعية' } }
    - { fields: { placeholder: '@components_magma_table', language: '@language_ar', value: 'جدول الرواسب' } }
    - { fields: { placeholder: '@components_magma_table_actions', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@components_magma_table_pagination_first', language: '@language_ar', value: أول } }
    - { fields: { placeholder: '@components_magma_table_pagination_last', language: '@language_ar', value: الاخير } }
    - { fields: { placeholder: '@components_magma_table_pagination_next', language: '@language_ar', value: التالى } }
    - { fields: { placeholder: '@components_magma_table_pagination_previous', language: '@language_ar', value: سابق } }
    - { fields: { placeholder: '@components_notes', language: '@language_ar', value: ملاحظات } }
    - { fields: { placeholder: '@components_notes_add_a_note', language: '@language_ar', value: 'اكتب ملاحظة' } }
    - { fields: { placeholder: '@components_notes_delete_option_confirmation', language: '@language_ar', value: 'هل أنت متأكد أنك تريد حذف هذه الملاحظة؟' } }
    - { fields: { placeholder: '@components_notes_edit_note', language: '@language_ar', value: 'تحرير مذكرة' } }
    - { fields: { placeholder: '@components_notes_from', language: '@language_ar', value: من } }
    - { fields: { placeholder: '@components_notes_hide_responses', language: '@language_ar', value: 'إخفاء الردود' } }
    - { fields: { placeholder: '@components_notes_new_note', language: '@language_ar', value: 'ملاحظة جديدة' } }
    - { fields: { placeholder: '@components_notes_reply', language: '@language_ar', value: 'اكتب ردا' } }
    - { fields: { placeholder: '@components_notes_show_responses', language: '@language_ar', value: 'عرض الردود' } }
    - { fields: { placeholder: '@components_recommendations_contributory_factor', language: '@language_ar', value: 'العوامل المساهمة' } }
    - { fields: { placeholder: '@components_recommendations_controls', language: '@language_ar', value: ضوابط } }
    - { fields: { placeholder: '@components_recommendations_create', language: '@language_ar', value: 'إنشاء توصية' } }
    - { fields: { placeholder: '@components_recommendations_edit', language: '@language_ar', value: 'تحرير التوصية' } }
    - { fields: { placeholder: '@components_recommendations_loading', language: '@language_ar', value: 'تحميل التوصيات' } }
    - { fields: { placeholder: '@components_recommendations_loading_controls', language: '@language_ar', value: 'تحميل عناصر التحكم' } }
    - { fields: { placeholder: '@components_recommendations_new', language: '@language_ar', value: 'توصية جديدة' } }
    - { fields: { placeholder: '@components_recommendations_plural', language: '@language_ar', value: توصيات } }
    - { fields: { placeholder: '@components_recommendations_save', language: '@language_ar', value: حفظ } }
    - { fields: { placeholder: '@components_recommendations_search', language: '@language_ar', value: 'توصيات البحث' } }
    - { fields: { placeholder: '@components_recommendations_singular', language: '@language_ar', value: توصية } }
    - { fields: { placeholder: '@components_recommendations_table_column_id', language: '@language_ar', value: 'هوية شخصية' } }
    - { fields: { placeholder: '@components_recommendations_table_column_statement_of_intent', language: '@language_ar', value: 'بيان النية' } }
    - { fields: { placeholder: '@components_record_search', language: '@language_ar', value: 'سجل البحث' } }
    - { fields: { placeholder: '@components_record_search_back_to_search', language: '@language_ar', value: 'العودة الى البحث' } }
    - { fields: { placeholder: '@components_record_search_search_results', language: '@language_ar', value: 'نتائج البحث' } }
    - { fields: { placeholder: '@dashboard_plural', language: '@language_ar', value: لوحات } }
    - { fields: { placeholder: '@dashboard_singular', language: '@language_ar', value: 'لوحة القيادة' } }
    - { fields: { placeholder: '@incident_plural', language: '@language_ar', value: حوادث } }
    - { fields: { placeholder: '@incident_singular', language: '@language_ar', value: حادث } }
    - { fields: { placeholder: '@loading', language: '@language_ar', value: 'جار التحميل...' } }
    - { fields: { placeholder: '@mortality_plural', language: '@language_ar', value: 'معدل الوفيات' } }
    - { fields: { placeholder: '@mortality_singular', language: '@language_ar', value: 'مراجعة الوفيات' } }
    - { fields: { placeholder: '@sidebar_back_to', language: '@language_ar', value: 'ارجع الى' } }
    - { fields: { placeholder: '@splash_page_title', language: '@language_ar', value: 'صفحة البداية' } }
    - { fields: { placeholder: '@tables_actions', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@tables_edit', language: '@language_ar', value: تصحيح } }
    - { fields: { placeholder: '@tables_go_to', language: '@language_ar', value: 'اذهب إلى' } }
    - { fields: { placeholder: '@tables_no_results_found', language: '@language_ar', value: 'لا توجد نتائج' } }
    - { fields: { placeholder: '@tables_pagination_per_page', language: '@language_ar', value: 'لكل صفحة' } }
    - { fields: { placeholder: '@tables_view', language: '@language_ar', value: المشاهدة } }
    - { fields: { placeholder: '@nav_acl_groups', language: '@language_ar', value: 'مجموعات قائمة التحكم في التوصل' } }
    - { fields: { placeholder: '@nav_acl_rules', language: '@language_ar', value: 'قواعد قائمة التحكم في التوصل' } }
    - { fields: { placeholder: '@nav_actions', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@nav_actions_dashboard', language: '@language_ar', value: 'لوحة تحكم الإجراءات' } }
    - { fields: { placeholder: '@nav_administration', language: '@language_ar', value: الادارة } }
    - { fields: { placeholder: '@nav_assess', language: '@language_ar', value: تقييم } }
    - { fields: { placeholder: '@nav_back_to_admin', language: '@language_ar', value: 'العودة إلى لوحة المشرف' } }
    - { fields: { placeholder: '@nav_back_to_control', language: '@language_ar', value: 'العودة إلى التحكم' } }
    - { fields: { placeholder: '@nav_back_to_dashboard', language: '@language_ar', value: 'رجوع إلى لوحة المعلومات' } }
    - { fields: { placeholder: '@nav_capture', language: '@language_ar', value: 'جمع و إدارة التقارير' } }
    - { fields: { placeholder: '@nav_capture_admin', language: '@language_ar', value: 'إدارة نظام جمع و إدارة التقارير' } }
    - { fields: { placeholder: '@nav_capture_user_settings', language: '@language_ar', value: 'إعدادات الالتقاط' } }
    - { fields: { placeholder: '@nav_checklists', language: '@language_ar', value: الاستطلاعات } }
    - { fields: { placeholder: '@nav_claims', language: '@language_ar', value: 'المطالبات المالية' } }
    - { fields: { placeholder: '@nav_clinical_audit', language: '@language_ar', value: 'التدقيق السريري' } }
    - { fields: { placeholder: '@nav_clinical_audits', language: '@language_ar', value: 'عمليات التدقيق السريرية' } }
    - { fields: { placeholder: '@nav_compliance_assessment', language: '@language_ar', value: 'تقييم الامتثال' } }
    - { fields: { placeholder: '@nav_contacts', language: '@language_ar', value: 'جهات الاتصال' } }
    - { fields: { placeholder: '@nav_controls', language: '@language_ar', value: التحكم } }
    - { fields: { placeholder: '@nav_controls_and_recommendations', language: '@language_ar', value: 'توصيات و التحكم' } }
    - { fields: { placeholder: '@nav_dashboard', language: '@language_ar', value: 'إدارة التقارير' } }
    - { fields: { placeholder: '@nav_equipment', language: '@language_ar', value: المعدات } }
    - { fields: { placeholder: '@nav_evaluate', language: '@language_ar', value: تقييم } }
    - { fields: { placeholder: '@nav_feedback', language: '@language_ar', value: 'تجربة المريض' } }
    - { fields: { placeholder: '@nav_form-fields', language: '@language_ar', value: 'الحقول المخصصة' } }
    - { fields: { placeholder: '@nav_forms', language: '@language_ar', value: 'مصمم النموذج' } }
    - { fields: { placeholder: '@nav_implement', language: '@language_ar', value: تنفيذ } }
    - { fields: { placeholder: '@nav_incidents', language: '@language_ar', value: 'الأحداث' } }
    - { fields: { placeholder: '@nav_insurance', language: '@language_ar', value: تأمين } }
    - { fields: { placeholder: '@nav_investigation', language: '@language_ar', value: تحقيق } }
    - { fields: { placeholder: '@nav_investigations', language: '@language_ar', value: تحقيقات } }
    - { fields: { placeholder: '@nav_resources_covid', language: '@language_ar', value: مصادر جائحة كوفيد 19}}
    - { fields: { placeholder: '@nav_locations', language: '@language_ar', value: مواقع } }
    - { fields: { placeholder: '@nav_logout', language: '@language_ar', value: الخروج } }
    - { fields: { placeholder: '@nav_medications', language: '@language_ar', value: الأدوية } }
    - { fields: { placeholder: '@nav_mortality', language: '@language_ar', value: 'مراجعة حالات الوفيات' } }
    - { fields: { placeholder: '@nav_my_profile', language: '@language_ar', value: 'ملفي الشخصي' } }
    - { fields: { placeholder: '@nav_organisations', language: '@language_ar', value: المنظمات } }
    - { fields: { placeholder: '@nav_payments', language: '@language_ar', value: المدفوعات } }
    - { fields: { placeholder: '@nav_policies_and_guidelines', language: '@language_ar', value: 'السياسات والإرشادات' } }
    - { fields: { placeholder: '@nav_reporting', language: '@language_ar', value: التقارير } }
    - { fields: { placeholder: '@nav_risk', language: '@language_ar', value: خطر } }
    - { fields: { placeholder: '@nav_risk_register', language: '@language_ar', value: 'مدير المخاطر المؤسسية' } }
    - { fields: { placeholder: '@nav_risks', language: '@language_ar', value: المخاطر } }
    - { fields: { placeholder: '@nav_reportable_incidents', language: '@language_ar', value: حادث يمكن الإبلاغ عنه } }
    - { fields: { placeholder: '@nav_roi_assessment', language: '@language_ar', value: 'الاستثمار العائد على الاستثمار' } }
    - { fields: { placeholder: '@nav_safety_rounds', language: '@language_ar', value: 'جولات السلامة' } }
    - { fields: { placeholder: '@nav_services', language: '@language_ar', value: خدمات } }
    - { fields: { placeholder: '@nav_strategy', language: '@language_ar', value: الإستراتيجية } }
    - { fields: { placeholder: '@nav_system_admin', language: '@language_ar', value: 'مسؤول النظام' } }
    - { fields: { placeholder: '@nav_users', language: '@language_ar', value: المستخدمين } }
    - { fields: { placeholder: '@common_modules_actions', language: '@language_ar', value: ' إجراءات' } }
    - { fields: { placeholder: '@untitled_section', language: '@language_ar', value: 'قسم بدون عنوان' } }
    - { fields: { placeholder: '@components_magma_table_actions', language: '@language_ar', value: ' إجراءات' } }
    - { fields: { placeholder: '@datasource_language', language: '@language_ar', value: لغة } }
    - { fields: { placeholder: '@datasource_religion', language: '@language_ar', value: دين } }
    - { fields: { placeholder: '@welcome_page_title', language: '@language_ar', value: 'مرحبا بكم في Datix Cloud IQ' } }
    - { fields: { placeholder: '@language_label_ar', language: '@language_ar', value: عربى } }
    - { fields: { placeholder: '@language_label_de_ch', language: '@language_ar', value: 'الألمانية السويسرية' } }
    - { fields: { placeholder: '@language_label_en', language: '@language_ar', value: 'الإنجليزية البريطانية' } }
    - { fields: { placeholder: '@language_selector_label', language: '@language_ar', value: 'اختر لغة العرض الخاصة بك' } }
    - { fields: { placeholder: '@language_selector_option', language: '@language_ar', value: 'جار التحميل' } }
    - { fields: { placeholder: '@nav_dashboard', language: '@language_ar', value: 'لوحة القيادة' } }
    - { fields: { placeholder: '@nav_distributions_lists', language: '@language_ar', value: 'قوائم التوزيع' } }
    - { fields: { placeholder: '@common_form_required_fields', language: '@language_ar', value: 'الحقول المطلوبة' } }
    - { fields: { placeholder: '@nav_my_todo', language: '@language_ar', value: 'قائمة الأعمال' } }
    - { fields: { placeholder: '@common_active', language: '@language_ar', value: فعال } }
    - { fields: { placeholder: '@common_admin', language: '@language_ar', value: المشرف } }
    - { fields: { placeholder: '@common_all_modules', language: '@language_ar', value: 'جميع الوحدات' } }
    - { fields: { placeholder: '@common_components_attachments_audit_datetime', language: '@language_ar', value: 'التاريخ / الوقت' } }
    - { fields: { placeholder: '@common_components_attachments_audit_delete', language: '@language_ar', value: حذف } }
    - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_last_version_required', language: '@language_ar', value: 'يمكن حذف أحدث إصدار من المرفقات فقط' } }
    - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_permissions_insufficient', language: '@language_ar', value: 'لا يمكن حذف إصدار المرفق هذا باستخدام صلاحيات المستخدم الخاصة بك' } }
    - { fields: { placeholder: '@common_components_attachments_audit_delete_failed_title', language: '@language_ar', value: 'غير قادر على الحذف' } }
    - { fields: { placeholder: '@common_components_attachments_audit_download', language: '@language_ar', value: تحميل } }
    - { fields: { placeholder: '@common_components_attachments_audit_name', language: '@language_ar', value: الإسم } }
    - { fields: { placeholder: '@common_components_attachments_audit_title', language: '@language_ar', value: 'مراجعة المرفقات' } }
    - { fields: { placeholder: '@common_components_attachments_audit_version', language: '@language_ar', value: الإصدار } }
    - { fields: { placeholder: '@common_components_attachments_audit_type', language: '@language_ar', value: النوع } }
    - { fields: { placeholder: '@common_components_attachments_audit_type_private', language: '@language_ar', value: خاص } }
    - { fields: { placeholder: '@common_components_attachments_audit_type_public', language: '@language_ar', value: عامة } }
    - { fields: { placeholder: '@common_components_label_template', language: '@language_ar', value: قالب } }
    - { fields: { placeholder: '@common_components_templates', language: '@language_ar', value: قوالب } }
    - { fields: { placeholder: '@common_components_templates_close', language: '@language_ar', value: قريب } }
    - { fields: { placeholder: '@common_components_templates_new', language: '@language_ar', value: 'إنشاء مستند جديد' } }
    - { fields: { placeholder: '@common_components_templates_title', language: '@language_ar', value: قوالب } }
    - { fields: { placeholder: '@common_deleted', language: '@language_ar', value: 'تم الحذف' } }
    - { fields: { placeholder: '@common_employee_statuses_active', language: '@language_ar', value: فعال } }
    - { fields: { placeholder: '@common_employee_statuses_label', language: '@language_ar', value: 'حالة الموظف' } }
    - { fields: { placeholder: '@common_employee_statuses_not_started', language: '@language_ar', value: 'لم يبدأ' } }
    - { fields: { placeholder: '@common_employee_statuses_terminated', language: '@language_ar', value: إنهاء } }
    - { fields: { placeholder: '@common_error_remove', language: '@language_ar', value: 'خطأ في إزالة {{singularLabel}}' } }
    - { fields: { placeholder: '@common_error_save', language: '@language_ar', value: 'حدث خطأ أثناء حفظ {{singularLabel}}' } }
    - { fields: { placeholder: '@common_errors_field_exceeds_max_length', language: '@language_ar', value: '{{field}} يتجاوز الحد الأقصى للطول ({{maxLength}})' } }
    - { fields: { placeholder: '@common_exception_document_was_not_created_successfully', language: '@language_ar', value: 'لم يتم إنشاء المستند بنجاح' } }
    - { fields: { placeholder: '@common_exception_investigation_not_found', language: '@language_ar', value: 'التحقيق غير موجود' } }
    - { fields: { placeholder: '@common_exception_template_not_found', language: '@language_ar', value: 'لم يتم العثور على القالب' } }
    - { fields: { placeholder: '@common_exception_unknown_template_reference', language: '@language_ar', value: 'مرجع القالب غير معروف' } }
    - { fields: { placeholder: '@common_form_end_time', language: '@language_ar', value: 'وقت النهاية' } }
    - { fields: { placeholder: '@common_form_required_fields', language: '@language_ar', value: 'الحقول المطلوبة' } }
    - { fields: { placeholder: '@common_form_start_time', language: '@language_ar', value: 'وقت البدء' } }
    - { fields: { placeholder: '@common_form_time', language: '@language_ar', value: الوقت } }
    - { fields: { placeholder: '@common_form_time_is_not_known', language: '@language_ar', value: 'الوقت غير معروف' } }
    - { fields: { placeholder: '@common_inactive', language: '@language_ar', value: 'غير فعال' } }
    - { fields: { placeholder: '@common_loading_options', language: '@language_ar', value: 'جارٍ تحميل الخيارات ...' } }
    - { fields: { placeholder: '@common_locale', language: '@language_ar', value: المحلية } }
    - { fields: { placeholder: '@common_modules', language: '@language_ar', value: الوحدات } }
    - { fields: { placeholder: '@common_node_list_no_other_results', language: '@language_ar', value: 'لا توجد نتائج أخرى' } }
    - { fields: { placeholder: '@common_node_list_view_children', language: '@language_ar', value: 'عرض {{nu,Children}} الأطفال' } }
    - { fields: { placeholder: '@common_other_results', language: '@language_ar', value: 'نتائج أخرى ({{numResults}})' } }
    - { fields: { placeholder: '@common_permission_denied', language: '@language_ar', value: 'طلب الصلاحيات مرفوض' } }
    - { fields: { placeholder: '@common_positions_no_positions', language: '@language_ar', value: 'لم يتم اختيار المناصب' } }
    - { fields: { placeholder: '@common_post_save_no_access', language: '@language_ar', value: 'تم حفظ السجل ، لكن صلاحيات الوصول المعينة للمستخدم لا تسمح لك برؤيته' } }
    - { fields: { placeholder: '@common_record_search_no_value_selected', language: '@language_ar', value: 'لم يتم إختيار القيمة' } }
    - { fields: { placeholder: '@common_select_locale', language: '@language_ar', value: 'إختيار محلي ' } }
    - { fields: { placeholder: '@common_success_remove', language: '@language_ar', value: 'تمت إزالة {{singularLabel}} بنجاح' } }
    - { fields: { placeholder: '@common_success_save', language: '@language_ar', value: 'تم حفظ {{singularLabel}} بنجاح' } }
    - { fields: { placeholder: '@common_value_not_set', language: '@language_ar', value: 'لم يتم تعيين قيمة' } }
    - { fields: { placeholder: '@common_actions_next', language: '@language_ar', value: 'التالي' } }
    - { fields: { placeholder: '@common_actions_previous', language: '@language_ar', value: 'السابق' } }
    - { fields: { placeholder: '@common_search_or_select_option', language: '@language_ar', value: البحث عن أو تحديد الخيار } }
    - { fields: { placeholder: '@language_label_us', language: '@language_ar', value: اللغة الإنجليزية (الولايات المتحدة الأمريكية) } }
    - { fields: { placeholder: '@common_components_tree_node_first_page', language: '@language_ar', value: الصفحة الأولى } }
    - { fields: { placeholder: '@common_components_tree_node_next_page', language: '@language_ar', value: الصفحة التالية } }
    - { fields: { placeholder: '@common_components_tree_node_last_page', language: '@language_ar', value: الصفحة الأخيرة } }
    - { fields: { placeholder: '@common_components_tree_node_previous_page', language: '@language_ar', value: الصفحة السابقة } }
    - { fields: { placeholder: '@email_not_applicable', language: '@language_ar', value: 'غير متاح' } }
    - { fields: { placeholder: '@email_anonymous_author', language: '@language_ar', value: 'مجهول' } }
    - { fields: { placeholder: '@nav_hotspots', language: '@language_ar', value: 'نقاط المراقبة' } }
    - { fields: { placeholder: '@capture_modules_com_label', language: '@language_ar', value: 'ردود الفعل' } }
    - { fields: { placeholder: '@capture_modules_cla_label', language: '@language_ar', value: 'مطالبات' } }
    - { fields: { placeholder: '@capture_modules_inc_label', language: '@language_ar', value: 'حوادث' } }
    - { fields: { placeholder: '@capture_modules_das_label', language: '@language_ar', value: 'لوحة التحكم الرئيسية' } }
    - { fields: { placeholder: '@placeholder_erm_risk_linked_records_list_status', language: '@language_ar', value: الحالة } }
    - { fields: { placeholder: '@common_errors_resource_not_found_erm14', language: '@language_ar', value: '{{ermCode}} الموارد غير موجود' } }
    - { fields: { placeholder: '@common_form_validation_errors_erm15', language: '@language_ar', value: '{{ermCode}} يحتوي النموذج المقدم على أخطاء التحقق' } }
    - { fields: { placeholder: '@common_saved_successfully', language: '@language_ar', value: 'تم حفظ المرفق بنجاح' } }
    - { fields: { placeholder: '@system_admin_system_configuration_copy_location_service_from_source_to_action', language: '@language_ar', value: 'هل تريد تمكين نسخ مواقع السجلات المصدر وخدماتها إلى الإجراء المرتبط؟' } }
