entityClass: I18n\Entity\Domain
priority: 1.5
data:
  -
    fields:
      name: common
      type: 0
    ref: domain_common
  -
    fields:
      name: admin
      type: 0
    ref: domain_admin
  -
    fields:
      name: accreditation
      type: 0
    ref: domain_accreditation
  -
    fields:
      name: acl
      type: 0
    ref: domain_acl
  -
    fields:
      name: acl-groups
      type: 0
    ref: domain_acl_groups
  -
    fields:
      name: acl-rules
      type: 0
    ref: domain_acl_rules
  -
    fields:
      name: actions
      type: 0
    ref: domain_actions
  -
    fields:
      name: checklists
      type: 0
    ref: domain_checklists
  -
    fields:
      name: clinical-audit
      type: 0
    ref: domain_clinical_audit
  -
    fields:
      name: contacts
      type: 0
    ref: domain_contacts
  -
    fields:
      name: controls
      type: 0
    ref: domain_controls
  -
    fields:
      name: devices
      type: 0
    ref: domain_devices
  -
    fields:
      name: investigations
      type: 0
    ref: domain_investigations
  -
    fields:
      name: risk-register
      type: 0
    ref: domain_enterprise_risk_manager
  -
    fields:
      name: roundings
      type: 0
    ref: domain_roundings
  -
    fields:
      name: locations
      type: 0
    ref: domain_locations
  -
    fields:
      name: medications
      type: 0
    ref: domain_medications
  -
    fields:
      name: services
      type: 0
    ref: domain_services
  -
    fields:
      name: users
      type: 0
    ref: domain_users
  -
    fields:
      name: components
      type: 0
    ref: domain_components
  -
    fields:
      name: components.confirm_button
      type: 0
    ref: domain_components_confirm_button
  -
    fields:
      name: components.magma_table
      type: 0
    ref: domain_components_magma_table
  -
    fields:
      name: components.contributory_factors
      type: 0
    ref: domain_components_contributory_factors
  -
    fields:
      name: components.notes
      type: 0
    ref: domain_components_notes
  -
    fields:
      name: components.record_search
      type: 0
    ref: domain_components_record_search
  -
    fields:
      name: recommendations
      type: 0
    ref: domain_recommendations
  -
    fields:
      name: recommendations_cdm
      type: 0
    ref: domain_recommendations_cdm
  -
    fields:
      name: investigation_tool
      type: 0
    ref: domain_investigation_tool
  -
    fields:
      name: safety_alerts
    ref: domain_safety_alerts
  -
    fields:
      name: admin.field_maintenance
      type: 0
    ref: domain_field_maintenance
  -
    fields:
      name: investigations.process_maps
      type: 0
    ref: domain_investigations_process_maps
  -
    fields:
      name: my_preferences
      type: 0
    ref: domain_my_preferences
  -
    fields:
      name: safety_learnings
      type: 0
    ref: domain_safety_learnings
  -
    fields:
      name: form.type
      type: 0
    ref: domain_form_types
  -
    fields:
      name: form.panel
      type: 0
    ref: domain_form_panels
  -
    fields:
      name: location-list
      type: 0
    ref: domain_location_list
  -
      fields:
        name: location-draft-list
        type: 0
      ref: domain_draft_location_list
  -
    fields:
      name: service-list
      type: 0
    ref: domain_service_list
  -
    fields:
      name: service-tag
      type: 0
    ref: domain_service_tag
  -
    fields:
      name: location-tag
      type: 0
    ref: domain_location_tag
  -
    fields:
      name: control-comparator
      type: 0
    ref: domain_control_comparator
  -
    fields:
      name: reportable_incidents
      type: 0
    ref: domain_reportable_incidents
  -
    fields:
      name: distribution_list
      type: 0
    ref: domain_distribution_list
  -
    fields:
      name: notifications
      type: 0
    ref: domain_notifications
  -
    fields:
      name: benchmarks
      type: 0
    ref: domain_benchmarks
  -
    fields:
      name: notification_centre
      type: 0
    ref: domain_notification_centre
  -
    fields:
      name: email
      type: 0
    ref: domain_email
  -
    fields:
      name: config-portation
      type: 0
    ref: domain_config_portation
  -
    fields:
      name: incidents
      type: 0
    ref: domain_incidents
  -
    fields:
      name: inactivity_timeout
      type: 0
    ref: domain_inactivity_timeout
