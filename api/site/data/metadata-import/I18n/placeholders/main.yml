entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: LANGUAGE.SELECTOR.LABEL
      domains:
        -
          domain: '@domain_common'
    ref: language_selector_label
  -
    fields:
      placeholder: LANGUAGE.SELECTOR.LOADING_OPTIONS
      domains:
        -
          domain: '@domain_common'
    ref: language_selector_option
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE.ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table_actions
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE.PAGINATION.FIRST
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table_pagination_first
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE.PAGINATION.PREVIOUS
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table_pagination_previous
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE.PAGINATION.NEXT
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table_pagination_next
  -
    fields:
      placeholder: COMPONENTS.MAGMA_TABLE.PAGINATION.LAST
      type: 0
      domains:
        -
          domain: '@domain_components_magma_table'
    ref: components_magma_table_pagination_last
  -
    fields:
      placeholder: COMMON.OPTIONAL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_optional
  -
    fields:
      placeholder: COMMON.COPIED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_copied
  -
    fields:
      placeholder: COMPONENTS.RECORD_SEARCH
      type: 0
      domains:
        -
          domain: '@domain_components_record_search'
    ref: components_record_search
  -
    fields:
      placeholder: COMPONENTS.RECORD_SEARCH.BACK_TO_SEARCH
      type: 0
      domains:
        -
          domain: '@domain_components_record_search'
    ref: components_record_search_back_to_search
  -
    fields:
      placeholder: COMPONENTS.RECORD_SEARCH.SEARCH_RESULTS
      type: 0
      domains:
        -
          domain: '@domain_components_record_search'
    ref: components_record_search_search_results
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_plural
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.LOADING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_loading
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.NEW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_new
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.UPLOADED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_uploaded
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DOWNLOAD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_download
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_edit
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DELETE
      pointer: COMMON.DELETE
      type: 0
      domains:
        -
          domain: '@domain_common'
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DELETE.WARNING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_delete_warning
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.ATTACHMENT_DELETED
      pointer: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.ATTACHMENT_DELETED
      type: 0
      domains:
        -
          domain: '@domain_common'
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.VERSION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_version
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EDIT_ATTACHMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_edit_attachment
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EDIT_ATTACHMENT_SUCCESS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_edit_attachment_success
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.ADD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_add
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_singular
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.PERCENT_COMPLETE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_percent_complete
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.REPLACE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_replace
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_title
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DESCRIPTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_description
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DEFAULT_ALLOWED_FILE_TYPES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_default_allowed_file_types
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.CLASSIFICATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_classification
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.SELECT_CLASSIFICATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_select_classification
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.CANCEL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_cancel
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_save
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.RETRY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_retry
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_error
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.MAX_FILE_SIZE_ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_max_file_size_error
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.CLASSIFICATION.VALUES.IMAGE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_classification_values_image
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.CLASSIFICATION.VALUES.DOCUMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_classification_values_document
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.CLASSIFICATION.VALUES.SPREADSHEET
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_classification_values_spreadsheet
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_title
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.VERSION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_version
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.NAME
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_name
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DATETIME
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_datetime
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DOWNLOAD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_download
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DELETE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_delete
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.ATTACHMENT_DELETED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_attachment_deleted
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DELETE_FAILED.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_delete_failed_title
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DELETE_SUCCESS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_delete_success
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DELETE_ERROR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_delete_error
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DELETE_FAILED.LAST_VERSION_REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_delete_failed_last_version_required
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.AUDIT.DELETE_FAILED.PERMISSIONS_INSUFFICIENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_audit_delete_failed_permissions_insufficient
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_audit_type
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.TYPE.PUBLIC
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_audit_type_public
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.TYPE.PRIVATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_audit_type_private
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.NO_ATTACHMENTS_PROVIDED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_attachments_no_attachments_provided
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EMAIL_ATTACHMENT_LABEL
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_email_attachment_label
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EMAIL_ATTACHMENT_TITLE
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_email_attachment_title
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.RESEND_ATTACHMENT
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_resend_attachment
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.REASON_FOR_RESENDING
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_reason_for_resending
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.REPORT_LAST_SENT
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_report_last_sent
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.REPORT_FIRST_SENT
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_report_first_sent
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.REASON_FOR_RESEND
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_reason_for_resend
  - fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.EMAIL_SENT
      type: 0
      domains:
        - domain: '@domain_investigations'
        - domain: '@domain_reportable_incidents'
    ref: common_components_attachments_email_sent
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_singular
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_controls'
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_plural
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.ADD_CONTRIBUTORY_FACTOR
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_add_contributory_factor
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.NO_CONTRIBUTORY_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_no_contributory_factors
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.NEW_CONTRIBUTORY_FACTOR
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_new_contributory_factor
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_category
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.SUB_CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_sub_category
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.SELECT_TYPE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_select_type
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.SELECT_CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_select_category
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.SELECT_SUB_CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_select_sub_category
  -
    fields:
      placeholder: COMMON.CREATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_create
  -
    fields:
      placeholder: COMMON.NEW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_new
  -
    fields:
      placeholder: COMMON.EDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_edit
  -
    fields:
      placeholder: COMMON.LOADING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_loading
  -
    fields:
      placeholder: COMMON.ID
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_id
  -
    fields:
      placeholder: COMMON.REF
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_ref
  -
    fields:
      placeholder: COMMON.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_title
  -
    fields:
      placeholder: COMMON.LABEL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_label
  -
    fields:
      placeholder: COMMON.DESCRIPTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_description
  -
    fields:
      placeholder: COMMON.MODULES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules
  -
    fields:
      placeholder: COMMON.MODULE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_module
  -
    fields:
      placeholder: COMMON.STATUS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_status
  -
    fields:
      placeholder: COMMON.DUE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_due
  -
    fields:
      placeholder: COMMON.SELECT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_select
  -
    fields:
      placeholder: COMMON.TYPE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_type
  -
    fields:
      placeholder: COMMON.SUBTYPE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_subtype
  -
    fields:
      placeholder: COMMON.CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_category
  -
    fields:
      placeholder: COMMON.SUBCATEGORY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_subcategory
  -
    fields:
      placeholder: COMMON.FILTER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_filter
  -
    fields:
      placeholder: COMMON.FILTER_INDICATOR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_filter_indicator
  -
    fields:
      placeholder: COMMON.NOT_SET
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_not_set
  -
    fields:
      placeholder: COMMON.NO_VALUE_SELECTED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: placeholder_common_no_value_selected
  -
    fields:
      placeholder: COMMON.VIEW_DETAILS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_view_details
  -
    fields:
      placeholder: COMMON.SAVE_CHANGES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_save_changes
  -
    fields:
      placeholder: COMMON.SAVE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_save
  -
    fields:
      placeholder: COMMON.SAVING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_saving
  -
    fields:
      placeholder: COMMON.SAVE_AND_CLOSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_save_and_close
  -
    fields:
      placeholder: COMMON.SAVE_ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_save_error
  -
    fields:
      placeholder: COMMON.CANCEL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_cancel
  -
    fields:
      placeholder: COMMON.DELETE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_delete
  -
    fields:
      placeholder: COMMON.CLOSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_close
  -
    fields:
      placeholder: COMMON.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_search
  -
    fields:
      placeholder: COMMON.DATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_date
  -
    fields:
      placeholder: COMMON.CLEAR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_clear
  -
    fields:
      placeholder: COMMON.REPLY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_reply
  -
    fields:
      placeholder: COMMON.YES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_yes
  -
    fields:
      placeholder: COMMON.NO
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_no
  -
    fields:
      placeholder: COMMON.CONFIRM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_confirm
  -
    fields:
      placeholder: COMMON.ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_error
  -
    fields:
      placeholder: COMMON.GENERIC_ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_generic_error
  -
    fields:
      placeholder: COMMON.ROLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_role
  -
    fields:
      placeholder: COMMON.ADMIN
      type: 0
      domains:
      -
        domain: '@domain_common'
    ref: common_admin
  -
    fields:
      placeholder: COMMON.ADMINISTRATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_administration
  -
    fields:
      placeholder: COMMON.ADMINISTRATION.TOOLS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_administration_tools
  -
    fields:
      placeholder: COMMON.ADMINISTRATION.PERMISSIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_administration_permissions
  -
    fields:
      placeholder: COMMON.ACCESS_CONTROL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_access_control
  -
    fields:
      placeholder: COMMON.ACCESS_CONTROL.MANUAL_ROLES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_access_control_manual_roles
  -
    fields:
      placeholder: COMMON.ACCESS_CONTROL.GENERATED_ROLES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_access_control_generated_roles
  -
    fields:
      placeholder: COMMON.ACCESS_CONTROL.NO_ASSIGNABLE_ROLES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_access_control_no_assignable_roles
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_required
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.STRING_LENGTH.MIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_string_length_min
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.STRING_LENGTH.MAX
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_string_length_max
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.EMAIL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_email
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.ERRORS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_errors
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.CASCADING_SELECT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_form_validation_cascading_select
  -
    fields:
      placeholder: NAV.BACK_TO_DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_back_to_dashboard
  -
    fields:
      placeholder: NAV.DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_dashboard
  -
    fields:
      placeholder: NAV.CAPTURE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_capture
  -
    fields:
      placeholder: NAV.INCIDENTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_incidents
  -
    fields:
      placeholder: NAV.FEEDBACK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_feedback
  -
    fields:
      placeholder: NAV.CLAIMS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_claims
  -
    fields:
      placeholder: NAV.MORTALITY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_mortality
  -
    fields:
      placeholder: NAV.EVALUATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_evaluate
  -
    fields:
      placeholder: NAV.RISK_REGISTER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_risk_register
  -
    fields:
      placeholder: NAV.INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_investigation
  -
    fields:
      placeholder: NAV.INVESTIGATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_investigations
  -
    fields:
      placeholder: NAV.RISK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_risk
  -
    fields:
      placeholder: NAV.RISKS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_risks
  -
    fields:
      placeholder: NAV.REPORTABLE_INCIDENTS
      domains:
        - domain: '@domain_common'
    ref: nav_reportable_incidents
  -
    fields:
      placeholder: NAV.STRATEGY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_strategy
  -
    fields:
      placeholder: NAV.CLINICAL_AUDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_clinical_audit
  -
    fields:
      placeholder: NAV.CLINICAL_AUDITS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_clinical_audits
  -
    fields:
      placeholder: NAV.CONTROLS_AND_RECOMMENDATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_controls_and_recommendations
  -
    fields:
      placeholder: NAV.SAFETY_LEARNINGS
      domains:
        -
          domain: '@domain_common'
    ref: nav_safety_learnings
  -
    fields:
      placeholder: NAV.ROI_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_roi_assessment
  -
    fields:
      placeholder: NAV.IMPLEMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_implement
  -
    fields:
      placeholder: NAV.POLICIES_AND_GUIDELINES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_policies_and_guidelines
  -
    fields:
      placeholder: NAV.ASSESS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_assess
  -
    fields:
      placeholder: NAV.COMPLIANCE_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_compliance_assessment
  -
    fields:
      placeholder: NAV.SAFETY_ROUNDS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_safety_rounds
  -
    fields:
      placeholder: NAV.MY_PROFILE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_my_profile
  -
    fields:
      placeholder: NAV.MY_TODO
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_my_todo
  -
    fields:
      placeholder: NAV.MY_PREFERENCES
      type: 0
      pointer: MY_PREFERENCES.TITLE
      domains:
        -
          domain: '@domain_common'
    ref: nav_my_preferences
  -
    fields:
      placeholder: NAV.SYSTEM_ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_system_admin
  -
    fields:
      placeholder: NAV.MY_SETTINGS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_my_settings
  -
    fields:
      placeholder: NAV.LOGOUT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_logout
  -
    fields:
      placeholder: NAV.LOGIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_login
  -
    fields:
      placeholder: nav.administration
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_administration
  -
    fields:
      placeholder: NAV.ACL_ROLES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: nav_acl_roles
  -
    fields:
      placeholder: NAV.ACL_GROUPS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_acl_groups
  -
    fields:
      placeholder: NAV.ACL_RULES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_acl_rules
  -
    fields:
      placeholder: nav.actions
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_actions
  -
    fields:
      placeholder: nav.checklists
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_checklists
  -
    fields:
      placeholder: nav.contacts
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_contacts
  -
    fields:
      placeholder: nav.controls
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_controls
  -
    fields:
      placeholder: nav.equipment
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_equipment
  -
    fields:
      placeholder: nav.forms
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_forms
  -
    fields:
      placeholder: nav.form-fields
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_form-fields
  -
    fields:
      placeholder: NAV.HELP
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_help
  -
    fields:
      placeholder: NAV.RESOURCES.COVID
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_resources_covid
  -
    fields:
      placeholder: nav.locations
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_locations
  -
    fields:
      placeholder: nav.medications
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_medications
  -
    fields:
      placeholder: nav.services
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_services
  -
    fields:
      placeholder: nav.users
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_users
  -
    fields:
      placeholder: NAV.ORGANISATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_organisations
  -
    fields:
      placeholder: NAV.INSURANCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_insurance
  -
    fields:
      placeholder: NAV.PAYMENTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_payments
  -
    fields:
      placeholder: NAV.ACTIONS_DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_actions_dashboard
  -
    fields:
      placeholder: NAV.DISTRIBUTIONS_LISTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_distributions_lists
  -
    fields:
      placeholder: NAV.REPORTING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_reporting
  -
    fields:
      placeholder: NAV.CAPTURE.ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_capture_admin
  -
    fields:
      placeholder: NAV.CAPTURE.USER.SETTINGS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_capture_user_settings
  -
    fields:
      placeholder: dashboard.singular
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: dashboard_singular
  -
    fields:
      placeholder: dashboard.plural
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: dashboard_plural
  -
    fields:
      placeholder: incident.singular
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: incident_singular
  -
    fields:
      placeholder: incident.plural
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: incident_plural
  -
    fields:
      placeholder: claim.singular
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: claim_singular
  -
    fields:
      placeholder: claim.plural
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: claim_plural
  -
    fields:
      placeholder: complaint.singular
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: complaint_singular
  -
    fields:
      placeholder: complaint.plural
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: complaint_plural
  -
    fields:
      placeholder: mortality.singular
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: mortality_singular
  -
    fields:
      placeholder: mortality.plural
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: mortality_plural
  -
    fields:
      placeholder: COMMON.ERRORS.MISSING_FIELDS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_missing_fields
  -
    fields:
      placeholder: COMMON.ERRORS.GET_FORM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_get_form
  -
    fields:
      placeholder: COMMON.ERRORS.FORBIDDEN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_forbidden
  -
    fields:
      placeholder: COMMON.ERRORS.ENTITY_FORBIDDEN_ENTITY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_entity_forbidden_entity
  -
    fields:
      placeholder: COMMON.ERRORS.RESOURCE_NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_resource_not_found
  -
    fields:
      placeholder: COMMON.ERRORS.ENTITY_NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_entity_not_found
  -
    fields:
      placeholder: SIDEBAR.BACK_TO
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: sidebar_back_to
  -
    fields:
      placeholder: COMMON.ERRORS.FIELD_EXCEEDS_MAX_LENGTH
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_field_exceeds_max_length
  -
    fields:
      placeholder: LOADING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: loading
  -
    fields:
      placeholder: COMPONENTS.NOTES
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes
  -
    fields:
      placeholder: COMPONENTS.NOTES.NEW_NOTE
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_new_note
  -
    fields:
      placeholder: COMPONENTS.NOTES.ADD_A_NOTE
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_add_a_note
  -
    fields:
      placeholder: COMPONENTS.NOTES.EDIT_NOTE
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_edit_note
  -
    fields:
      placeholder: COMPONENTS.NOTES.FROM
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_from
  -
    fields:
      placeholder: COMPONENTS.NOTES.HIDE_RESPONSES
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_hide_responses
  -
    fields:
      placeholder: COMPONENTS.NOTES.SHOW_RESPONSES
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_show_responses
  -
    fields:
      placeholder: COMPONENTS.NOTES.REPLY
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_reply
  -
    fields:
      placeholder: COMPONENTS.NOTES.DELETE_OPTION_CONFIRMATION
      type: 0
      domains:
        -
          domain: '@domain_components_notes'
    ref: components_notes_delete_option_confirmation
  -
    fields:
      placeholder: COMPONENTS.CONFIRM_BUTTON
      type: 0
      domains:
        -
          domain: '@domain_components_confirm_button'
    ref: components_confirm_button
  -
    fields:
      placeholder: COMPONENTS.CONFIRM_BUTTON.TEXT
      type: 0
      domains:
        -
          domain: '@domain_components_confirm_button'
    ref: components_confirm_button_text
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_singular
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_plural
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_search
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.CREATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_create
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.EDIT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_edit
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.TABLE.COLUMN.ID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_table_column_id
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.TABLE.COLUMN.STATEMENT_OF_INTENT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_table_column_statement_of_intent
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_save
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.LOADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_loading
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.NEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_new
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.CONTROLS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_controls
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_contributory_factor
  -
    fields:
      placeholder: COMPONENTS.RECOMMENDATIONS.LOADING_CONTROLS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_recommendations'
    ref: components_recommendations_loading_controls
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.LOADING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_loading
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.CREATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_create
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.SAVE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_save
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.CANCEL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_cancel
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.SAVING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_saving
  -
    fields:
      placeholder: COMMON.COMPONENTS.MAGMA_RELATIONSHIP.CLOSE_SEARCH
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_magma_relationship_close_search
  -
    fields:
      placeholder: TABLES.NO_RESULTS_FOUND
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_no_results_found
  -
    fields:
      placeholder: TABLES.PAGINATION.PER_PAGE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_pagination_per_page
  -
    fields:
      placeholder: TABLES.ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_actions
  -
    fields:
      placeholder: TABLES.GO_TO
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_go_to
  -
    fields:
      placeholder: TABLES.VIEW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_view
  -
    fields:
      placeholder: TABLES.EDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: tables_edit
  -
    fields:
      placeholder: COMMON.N_A
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_n_a
  -
    fields:
      placeholder: COMMON.MODULES.ERM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_erm
  -
    fields:
      placeholder: COMMON.MODULES.ERM_RISK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_erm_risk
  -
    fields:
      placeholder: COMMON.MODULES.ERM_REGISTER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_erm_register
  -
    fields:
      placeholder: COMMON.MODULES.ERM_TRACKER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_erm_tracker
  -
    fields:
      placeholder: COMMON.MODULES.INVESTIGATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_investigations
  -
    fields:
      placeholder: COMMON.MODULES.INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_investigation
  -
    fields:
      placeholder: COMMON.MODULES.SURVEYS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_surveys
  -
    fields:
      placeholder: COMMON.MODULES.SURVEY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_survey
  -
    fields:
      placeholder: COMMON.MODULES.CLINICAL_AUDIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_clinical_audit
  -
    fields:
      placeholder: COMMON.MODULES.CONTROLS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_controls
  -
    fields:
      placeholder: COMMON.MODULE.CONTROL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_module_control
  -
    fields:
      placeholder: COMMON.MODULES.RECOMMENDATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_recommendation
  -
    fields:
      placeholder: COMMON.MODULES.RECOMMENDATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_recommendations
  -
    fields:
      placeholder: COMMON.MODULES.COMPLIANCE_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_compliance_assessment
  -
    fields:
      placeholder: COMMON.MODULES.COMPLIANCE_ASSESSMENT_PROGRAMME
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_compliance_assessment_programme
  -
    fields:
      placeholder: COMMON.MODULES.COMPLIANCE_ASSESSMENT_STANDARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_compliance_assessment_standard
  -
    fields:
      placeholder: COMMON.MODULES.COMPLIANCE_ASSESSMENT_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_compliance_assessment_assessment
  -
    fields:
      placeholder: COMMON.MODULES.COMPLIANCE_ASSESSMENT_RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_compliance_assessment_response
  -
    fields:
      placeholder: COMMON.MODULES.SAFETY_ROUNDS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safety_rounds
  -
    fields:
      placeholder: COMMON.MODULES.SAFETY_ROUND
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safety_round
  -
    fields:
      placeholder: COMMON.MODULES.SAFETY_ROUNDS_TEMPLATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safety_rounds_template
  -
    fields:
      placeholder: COMMON.MODULES.SAFETY_ROUNDS.RESPONSE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safety_rounds_response
  -
    fields:
      placeholder: COMMON.MODULES.SAFETY_ROUNDS.SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safety_rounds_summary
  -
    fields:
      placeholder: COMMON.MODULES.ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_actions
  -
    fields:
      placeholder: COMMON.MODULES.ACL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_acl
  -
    fields:
      placeholder: COMMON.MODULES.ACL_RULE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_acl_rule
  -
    fields:
      placeholder: COMMON.MODULES.ACL_ROLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_acl_role
  -
    fields:
      placeholder: COMMON.MODULES.CONTACTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_contacts
  -
    fields:
      placeholder: COMMON.MODULES.CONTACT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_contact
  -
    fields:
      placeholder: COMMON.MODULES.EQUIPMENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_equipment
  -
    fields:
      placeholder: COMMON.MODULES.FORMS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_forms
  -
    fields:
      placeholder: COMMON.MODULES.FORM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_form
  -
    fields:
      placeholder: COMMON.MODULES.FORM_FIELD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_form_field
  -
    fields:
      placeholder: COMMON.MODULES.LOCATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_locations
  -
    fields:
      placeholder: COMMON.MODULES.LOCATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_location
  -
    fields:
      placeholder: COMMON.MODULES.MEDICATIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_medications
  -
    fields:
      placeholder: COMMON.MODULES.MEDICATION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_medication
  -
    fields:
      placeholder: COMMON.MODULES.SERVICES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_services
  -
    fields:
      placeholder: COMMON.MODULES.SERVICE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_service
  -
    fields:
      placeholder: COMMON.MODULES.USERS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_users
  -
    fields:
      placeholder: COMMON.MODULES.USER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_user
  -
    fields:
      placeholder: COMMON.MODULES.USER_GROUP
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_user_group
  -
    fields:
      placeholder: COMMON.MODULES.PRINCE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_prince
  -
    fields:
      placeholder: COMMON.MODULES.CLAIM
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_claim
  -
    fields:
      placeholder: COMMON.MODULES.FEEDBACK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_feedback
  -
    fields:
      placeholder: COMMON.MODULES.INCIDENT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_incident
  -
    fields:
      placeholder: COMMON.MODULES.MORTALITY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_mortality
  -
    fields:
      placeholder: COMMON.SELECT_AN_OPTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_select_an_option
  -
    fields:
      placeholder: COMMON.SEARCH_OR_SELECT_OPTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_search_or_select_option
  -
    fields:
      placeholder: COMMON.HELP
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_help
  -
    fields:
      placeholder: COMMON.COMPONENTS.DATE_FILTER.START_DATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_date_filter_start_date
  -
    fields:
      placeholder: COMMON.COMPONENTS.DATE_FILTER.END_DATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_date_filter_end_date
  -
    fields:
      placeholder: COMMON.COMPONENTS.FEEDBACK.BUTTON
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_feedback_button
  -
    fields:
      placeholder: COMMON.COMPONENTS.FEEDBACK.RATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_feedback_rate
  -
    fields:
      placeholder: COMMON.COMPONENTS.SHARE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_share
  -
    fields:
      placeholder: COMMON.COMPONENTS.FEEDBACK.SAVE_ERROR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_feedback_save_error
  -
    fields:
      placeholder: COMMON.COMPONENTS.FEEDBACK.SUBMITTED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_feedback_submitted
  -
    fields:
      placeholder: COMMON.COMPONENTS.FEEDBACK.THANK_YOU
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_feedback_thank_you
  -
    fields:
      placeholder: COMMON.SUBMIT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_submit
  -
    fields:
      placeholder: COMMON.ARE_YOU_SURE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_are_you_sure
  -
    fields:
      placeholder: COMMON.BACK
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_back
  -
    fields:
      placeholder: NAV.BACK_TO_ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_back_to_admin
  -
    fields:
      placeholder: NAV.BACK_TO_CONTROL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_back_to_control
  -
    fields:
      placeholder: COMMON.ON
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_on
  -
    fields:
      placeholder: COMMON.INACTIVE
      type: 0
      domains:
      -
        domain: '@domain_common'
    ref: common_inactive
  -
    fields:
      placeholder: COMMON.ACTIVE
      type: 0
      domains:
      -
        domain: '@domain_common'
    ref: common_active
  -
    fields:
      placeholder: COMMON.DELETED
      type: 0
      domains:
      -
        domain: '@domain_common'
    ref: common_deleted
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.NEW_OBJECTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_new_objective
  -
    fields:
      placeholder: ERM.RISK.FILTERS.RISK_REVIEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_risk_review_date
  -
    fields:
      placeholder: ERM.RISK.FILTERS.RISK_REVIEW_DATE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_risk_review_date_label
  -
    fields:
      placeholder: ERM.RISK.FILTERS.RISK_OPEN_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_risk_open_date
  -
    fields:
      placeholder: ERM.RISK.FILTERS.RISK_CLOSED_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_filters_risk_closed_date
  -
    fields:
      placeholder: ERM.RISK.FILTERS.RISK_CLOSED_DATE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_filters_risk_closed_date_label
  -
    fields:
      placeholder: ERM.RISK.FILTERS.CURRENT_RATING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_current_rating
  -
    fields:
      placeholder: ERM.RISK.FILTERS.CURRENT_RATING.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_current_rating_label
  -
    fields:
      placeholder: ERM.RISK.REGISTER.FILTERS.HEADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_filters_heading
  -
    fields:
      placeholder: ERM.RISK.REGISTER.FILTERS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_filters_title
  -
    fields:
      placeholder: ERM.RISK.REGISTER.FILTERS.ID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_filters_id
  -
    fields:
      placeholder: ERM.RISK.REGISTER.FILTERS.ACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_filters_active
  -
    fields:
      placeholder: INVESTIGATIONS.LOADING_OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_loading_objectives
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ACTIONS.MY_ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_actions_my_actions
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ACTIONS.ALL_ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_actions_all_actions
  -
    fields:
      placeholder: CONTACTS.RELATED_PRIMARY_MODULES
      type: 0
      domains:
        -
          domain: '@domain_contacts'
    ref: contacts_related_primary_modules
  -
    fields:
      placeholder: COMMON.LOADING_PERMISSIONS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_loading_permissions
  -
    fields:
      placeholder: CONTACTS.EDIT_CONTACT
      type: 0
      domains:
        -
          domain: '@domain_contacts'
    ref: contacts_edit_contact
  -
    fields:
      placeholder: CONTACTS.VIEW_CONTACT
      type: 0
      domains:
        -
          domain: '@domain_contacts'
    ref: contacts_view_contact
  -
    fields:
      placeholder: CONTROLS.CONTROLS_IN_PLACE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: controls_controls_in_place
  -
    fields:
      placeholder: CONTROLS.GAPS_IN_CONTROLS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: controls_gaps_in_controls
  -
    fields:
      placeholder: ACTIONS.RECORD.ADD_ACTION_PLAN_TO_RECORD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: actions_record_add_action_plan_to_record
  -
    fields:
      placeholder: ACTIONS.ADMIN.ADD_ACTION.UPDATE
      type: 0
      domains:
        -
          domain: '@domain_actions'
        -
          domain: '@domain_admin'
    ref: actions_admin_add_action_update
  -
    fields:
      placeholder: COMMON.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_saved_successfully
  -
    fields:
      placeholder: COMMON.UNAUTHORISED
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_unauthorised
  -
    fields:
      placeholder: COMMON.UNAUTHORISED_PAGE_ACCESS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_unauthorised_page_access

  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HUMAN_AND_RELATIONAL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_human_and_relational
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PATIENT_AND_FAMILY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_patient_and_family
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.COMMUNICATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_communication
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.CONSENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_consent
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INCAPACITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_incapacity
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RELIGIOUS_BELIEFS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_religious_beliefs
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.NON_COMPLIANCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_non_compliance
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ABNORMAL_PHYSIOLOGY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_abnormal_physiology
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.COMPLEXITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_complexity
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SOCIAL_SUPPORT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_social_support
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.AGGRESSION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_aggression
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.OTHER
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_other
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INDIVIDUAL_AND_PERSONAL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_individual_and_personal
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SKILL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_skill
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.KNOWLEDGE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_knowledge
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.EXPERIENCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_experience
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.FATIGUE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_fatigue
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ATTITUDES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_attitudes
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.STRESS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_stress
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.MORALE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_morale
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HEALTH
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_health
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TEMPORARY_IMPAIRMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_temporary_impairment
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ALCOHOL_OR_DRUGS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_alcohol_or_drugs
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PERSONAL_DOMESTIC_ISSUES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_personal_domestic_issues
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TEAMWORK_LEADERSHIP_SUPERVISION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_teamwork_leadership_supervision
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SUPERVISION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_supervision
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.STAFF_SUPPORT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_staff_support
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SHARED_UNDERSTANDING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_shared_understanding
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.COORDINATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_coordination
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.LOCAL_LEADERSHIP
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_local_leadership
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PSYCHOLOGICAL_SAFETY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_psychological_safety
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.MUTUAL_RESPECT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_mutual_respect
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.EFFECTIVE_COMMUNICATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_effective_communication
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TRAINING_EDUCATION_ASSESSMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_training_education_assessment
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.APPROPRIATE_TRAINING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_appropriate_training
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TRAINING_AVAILABILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_training_availability
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TRAINING_MONITORING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_training_monitoring
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.EDUCATION_QUALITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_education_quality
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ASSESSMENT_AND_QUALIFICATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_assessment_and_qualification
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TEAM_TRAINING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_team_training
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SIMULATION_TRAINING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_simulation_training
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.REVALIDATION_AND_COMPETENCY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_revalidation_and_competency
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INDUCTION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_induction
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TASK_AND_ENVIRONMENTAL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_task_and_environmental
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TASK_DEMANDS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_task_demands
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TIME_OF_DAY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_time_of_day
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.DISTRACTIONS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_distractions
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HIGH_WORKLOAD
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_high_workload
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.LOW_WORKLOAD
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_low_workload
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TIME_AVAILABILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_time_availability
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TASK_COMPLEXITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_task_complexity
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PRODUCTION_PRESSURE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_production_pressure
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TASK_PRACTICALITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_task_practicality
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TASK_DIFFICULTY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_task_difficulty
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.CONFLICTING_TASKS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_conflicting_tasks
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.UNCERTAINTY_AND_VARIABILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_uncertainty_and_variability
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PHYSICAL_WORK_ENVIRONMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_physical_work_environment
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HOUSEKEEPING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_housekeeping
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.LIGHTING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_lighting
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.NOISE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_noise
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TEMPERATURE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_temperature
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.LAYOUT_AND_DESIGN
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_layout_and_design
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ACCESSIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_accessibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SECURITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_security
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.VISIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_visibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.FIXTURES_AND_FITTINGS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_fixtures_and_fittings
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.MAINTENANCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_maintenance
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PROCEDURES_PROTOCOLS_AND_GUIDELINES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_procedures_protocols_and_guidelines
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ADEQUACY_AND_ACCURACY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_adequacy_and_accuracy
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.AVAILABILITY_AND_ACCESSIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_availability_and_accessibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.CURRENCY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_currency
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.CLARITY_AND_COHERENCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_clarity_and_coherence
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PRACTICALITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_practicality
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.IMPLEMENTATION_AND_USAGE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_implementation_and_usage
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ACCEPTABILITY_AND_AGREEMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_acceptability_and_agreement
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOLS_EQUIPMENT_AND_RESOURCES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tools_equipment_and_resources
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ALARMS_AND_WARNINGS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_alarms_and_warnings
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.DISPLAYS_AND_INTERFACES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_displays_and_interfaces
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.FUNCTIONALITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_functionality
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RELIABILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_reliability
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_AVAILABILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_availability
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_DESIGN
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_design
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_INSTALLATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_installation
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_MAINTENANCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_maintenance
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_COMPATIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_compatibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.TOOL_PACKAGING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_tool_packaging
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.QUANTITY_AND_QUALITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_quantity_and_quality
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ORGANISATIONAL_AND_CULTURAL_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_organisational_and_cultural_factors
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SOCIAL_AND_CULTURAL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_social_and_cultural
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.NORMS_AND_CUSTOMS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_norms_and_customs
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.VALUES_AND_ASSUMPTIONS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_values_and_assumptions
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SOCIAL_PRESSURE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_social_pressure
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.DIFFUSION_OF_RESPONSIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_diffusion_of_responsibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RULE_BEHAVIOUR
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_rule_behaviour
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RISK_PERCEPTION_AND_TOLERANCE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_risk_perception_and_tolerance
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HONESTY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_honesty
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SENIOR_LEADERSHIP_COMMITMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_senior_leadership_commitment
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.BLAME_AND_FEAR
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_blame_and_fear
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ORGANISATION_GOVERNANCE_AND_STRATEGY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_organisation_governance_and_strategy
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.LINES_OF_RESPONSIBILITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_lines_of_responsibility
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.STRATEGIC_LEADERSHIP
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_strategic_leadership
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SERVICE_PLANNING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_service_planning
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ORGANISATIONAL_STRUCTURE
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_organisational_structure
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RISK_MANAGEMENT_AND_LEARNING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_risk_management_and_learning
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.OVERSIGHT_AND_MONITORING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_oversight_and_monitoring
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RESOURCE_MANAGEMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_resource_management
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.OPERATIONAL_AND_PEOPLE_MANAGEMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_operational_and_people_management
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.BED_MANAGEMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_bed_management
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SKILL_MIX
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_skill_mix
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.STAFFING_LEVELS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_staffing_levels
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ROTA_AND_SHIFT_PLANNING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_rota_and_shift_planning
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.STAFF_TURNOVER
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_staff_turnover
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.JOB_ROLES_AND_DESIGN
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_job_roles_and_design
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.RECRUITMENT_AND_SELECTION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_recruitment_and_selection
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ADMINISTRATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_administration
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.SANCTIONS_AND_REWARDS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_sanctions_and_rewards
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.PURCHASING_AND_PROCUREMENT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_purchasing_and_procurement
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INFORMATION_AND_COMMUNICATION_SYSTEMS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_information_and_communication_systems
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.VERBAL_COMMUNICATION
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_verbal_communication
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.DOCUMENTATION_AND_RECORD_KEEPING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_documentation_and_record_keeping
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.HANDOVER_AND_BRIEFING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_handover_and_briefing
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.ELECTRONIC_RECORDS_AND_IT
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_electronic_records_and_it
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INFORMATION_ACCURACY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_information_accuracy
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.INFORMATION_CLARITY_AND_AMBIGUITY
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_information_clarity_and_ambiguity
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.EXTERNAL_AND_REGULATORY_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_external_and_regulatory_factors
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.REGULATORY_POLICY_AND_EXTERNAL
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_regulatory_policy_and_external
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.NATIONAL_POLICY_AND_STANDARDS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_national_policy_and_standards
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.COMMISSIONING_AND_CONTRACTING
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_commissioning_and_contracting
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.FUNDING_AND_RESOURCES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_funding_and_resources
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.REGULATORY_ACTIVITIES
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_regulatory_activities
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.OTHER_EXTERNAL_EVENTS
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_other_external_events
  -
    fields:
      placeholder: COMPONENTS.CONTRIBUTORY_FACTORS.CLASSIFICATION.GUIDANCE_NOT_FOLLOWED
      type: 0
      domains:
        -
          domain: '@domain_components_contributory_factors'
    ref: components_contributory_factors_classification_guidance_not_followed
  -
    fields:
      placeholder: WELCOME_PAGE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: welcome_page_title
  -
    fields:
      placeholder: COMMON.UNTITLED_SECTION
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: untitled_section
  - fields:
      placeholder: LANGUAGE.LABEL.EN
      type: 0
      domains:
      - domain: '@domain_common'
    ref: language_label_en
  - fields:
      placeholder: LANGUAGE.LABEL.AR
      type: 0
      domains:
      - domain: '@domain_common'
    ref: language_label_ar
  - fields:
      placeholder: LANGUAGE.LABEL.US
      type: 0
      domains:
        - domain: '@domain_common'
    ref: language_label_us
  - fields:
      placeholder: LANGUAGE.LABEL.DE_CH
      type: 0
      domains:
      - domain: '@domain_common'
    ref: language_label_de_ch
  - fields:
      placeholder: SPLASH_PAGE.TITLE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: splash_page_title
  - fields:
      placeholder: DATASOURCE.LANGUAGE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: datasource_language
  - fields:
      placeholder: DATASOURCE.RELIGION
      type: 0
      domains:
      - domain: '@domain_common'
    ref: datasource_religion
  - fields:
      placeholder: COMMON.NODE_LIST.NO_OTHER_RESULTS
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_node_list_no_other_results
  - fields:
      placeholder: COMMON.OTHER_RESULTS
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_other_results
  - fields:
      placeholder: COMMON.NODE_LIST.VIEW_CHILDREN
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_node_list_view_children
  - fields:
      placeholder: COMMON.SUCCESS.SAVE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_success_save
  - fields:
      placeholder: COMMON.ERROR.SAVE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_error_save
  - fields:
      placeholder: COMMON.SUCCESS.REMOVE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_success_remove
  - fields:
      placeholder: COMMON.ERROR.REMOVE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_error_remove
  - fields:
      placeholder: COMMON.POST_SAVE.NO_ACCESS
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_post_save_no_access

  - fields:
      placeholder: COMMON.FORM.REQUIRED_FIELDS
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_form_required_fields

  - fields:
      placeholder: COMMON.VALUE_NOT_SET
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_value_not_set

  - fields:
      placeholder: COMMON.LOADING_OPTIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_loading_options
  - fields:
      placeholder: COMMON.PLEASE_SELECT_OPTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_please_select_option
  - fields:
      placeholder: COMMON.NO_OPTIONS_AVAILABLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_no_options_available
  - fields:
      placeholder: COMMON.LOCALE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_locale
  - fields:
      placeholder: COMMON.SELECT.LOCALE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_select_locale
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates
  - fields:
      placeholder: COMMON.COMPONENTS.LABEL.TEMPLATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_label_template
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES.PDF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates_pdf
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES.DOCX
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates_docx
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES.NEW
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates_new
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES.CLOSE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates_close
  - fields:
      placeholder: COMMON.COMPONENTS.TEMPLATES.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_templates_title
  - fields:
      placeholder: COMMON.ALL_MODULES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_all_modules
  - fields:
      placeholder: COMMON.FORM.TIME_IS_NOT_KNOWN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_time_is_not_known
  - fields:
      placeholder: COMMON.FORM.TIME
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_time
  - fields:
      placeholder: COMMON.FORM.START_TIME
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_start_time
  - fields:
      placeholder: COMMON.FORM.END_TIME
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_end_time
  - fields:
      placeholder: COMMON.PERMISSION_DENIED
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_permission_denied
  - fields:
      placeholder: COMMON.CONFLICT_OCCURRED
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_conflict_occurred
  - fields:
      placeholder: COMMON.EMPLOYEE_STATUSES.LABEL
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_employee_statuses_label
  - fields:
      placeholder: COMMON.EMPLOYEE_STATUSES.ACTIVE
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_employee_statuses_active
  - fields:
      placeholder: COMMON.EMPLOYEE_STATUSES.TERMINATED
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_employee_statuses_terminated
  - fields:
      placeholder: COMMON.EMPLOYEE_STATUSES.NOT_STARTED
      type: 0
      domains:
      - domain: '@domain_common'
    ref: common_employee_statuses_not_started
  - fields:
      placeholder: COMMON.POSITIONS.NO_POSITIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_positions_no_positions
  - fields:
      placeholder: COMMON.EXCEPTION.INVESTIGATION_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_investigation_not_found
  - fields:
      placeholder: COMMON.EXCEPTION.MAINTENANCE_MODE_REQUIRED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_maintenance_mode_required

  - fields:
      placeholder: COMMON.EXCEPTION.UNKNOWN_TEMPLATE_REFERENCE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_unknown_template_reference
  - fields:
      placeholder: COMMON.EXCEPTION.TEMPLATE_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_template_not_found
  - fields:
      placeholder: COMMON.EXCEPTION.DOCUMENT_WAS_NOT_CREATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_document_was_not_created_successfully
  - fields:
      placeholder: COMMON.RECORD_SEARCH.NO_VALUE_SELECTED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_search_no_value_selected
  - fields:
      placeholder: COMMON.ERROR_TYPES.FORBIDDEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_error_types_forbidden
  - fields:
      placeholder: COMMON.ERROR_TYPES.SEE_THE_ERRORS_ATTRIBUTE_FOR_SPECIFIC_FAILURES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_error_see_the_errors_attribute_for_specific_failures
  - fields:
      placeholder: COMMON.NEXT
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_medications'
        - domain: '@domain_devices'
    ref: common_actions_next
  - fields:
      placeholder: COMMON.PREVIOUS
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_medications'
        - domain: '@domain_devices'
    ref: common_actions_previous

  - fields:
      placeholder: COMMON.EXCEPTION.TEMPLATE_INVALID_TARGET_TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_template_invalid_target_type
  - fields:
      placeholder: COMMON.APPROVED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_approved
  - fields:
      placeholder: COMMON.REJECTED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_rejected
  - fields:
      placeholder: COMMON.DOCUMENT.PAGE_NO_OF_PAGES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_document_page_no_of_pages
  - fields:
      placeholder: COMMON.EXCEPTION.TEMPLATE_INVALID_DOCUMENT_TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exception_template_invalid_document_type
  - fields:
      placeholder: COMMON.FORM.VALIDATION.NUMBER_VALUE.MIN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_validation_number_value_min
  - fields:
      placeholder: COMMON.FORM.VALIDATION.NUMBER_VALUE.MAX
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_validation_number_value_max
  - fields:
      placeholder: COMMON.FORM.VALIDATION.NUMBER_VALUE.DIGITS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_validation_number_value_digits
  - fields:
      placeholder: COMMON.FORM.VALIDATION.RANGE_VALUES_INVALID
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_validation_range_values_invalid
  - fields:
      placeholder: AUDIT.ACTIONS.RECORD_CREATED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: audit_actions_record_created
  - fields:
      placeholder: AUDIT.ACTIONS.RECORD_UPDATED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: audit_actions_record_updated
  - fields:
      placeholder: AUDIT.ACTIONS.RECORD_ATTACHED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: audit_actions_record_attached
  - fields:
      placeholder: AUDIT.ACTIONS.RECORD_DETACHED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: audit_actions_record_detached
  - fields:
      placeholder: AUDIT.NO_LOGS_AVAILABLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: audit_no_logs_available
  - fields:
      placeholder: COMMON.UPLOAD.FILE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_upload_file
  - fields:
      placeholder: COMMON.UPLOAD.TEMPLATE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_upload_template
  - fields:
      placeholder: COMMON.FILE.UPLOAD.PERCENT_COMPLETE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_file_upload_percent_complete
  - fields:
      placeholder: COMMON.FILE.UPLOAD.CANCEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_file_upload_cancel
  - fields:
      placeholder: COMMON.FILE.UPLOAD.RETRY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_file_upload_retry
  - fields:
      placeholder: COMMON.FILE.UPLOAD.ERROR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_file_upload_error
  - fields:
      placeholder: COMMON.FILE.UPLOAD.REPLACE.FILE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_file_upload_replace_file
  - fields:
      placeholder: UTC_OFFSETS.MINUS_TWELVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_twelve
  - fields:
      placeholder: UTC_OFFSETS.MINUS_ELEVEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_eleven
  - fields:
      placeholder: UTC_OFFSETS.MINUS_TEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_ten
  - fields:
      placeholder: UTC_OFFSETS.MINUS_NINE_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_nine_half
  - fields:
      placeholder: UTC_OFFSETS.MINUS_NINE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_nine
  - fields:
      placeholder: UTC_OFFSETS.MINUS_EIGHT
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_eight
  - fields:
      placeholder: UTC_OFFSETS.MINUS_SEVEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_seven
  - fields:
      placeholder: UTC_OFFSETS.MINUS_SIX
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_six
  - fields:
      placeholder: UTC_OFFSETS.MINUS_FIVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_five
  - fields:
      placeholder: UTC_OFFSETS.MINUS_FOUR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_four
  - fields:
      placeholder: UTC_OFFSETS.MINUS_THREE_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_three_half
  - fields:
      placeholder: UTC_OFFSETS.MINUS_THREE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_three
  - fields:
      placeholder: UTC_OFFSETS.MINUS_TWO
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_two
  - fields:
      placeholder: UTC_OFFSETS.MINUS_ONE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_minus_one
  - fields:
      placeholder: UTC_OFFSETS.ZERO
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_zero
  - fields:
      placeholder: UTC_OFFSETS.PLUS_ONE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_one
  - fields:
      placeholder: UTC_OFFSETS.PLUS_TWO
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_two
  - fields:
      placeholder: UTC_OFFSETS.PLUS_THREE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_three
  - fields:
      placeholder: UTC_OFFSETS.PLUS_THREE_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_three_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FOUR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_four
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FOUR_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_four_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FIVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_five
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FIVE_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_five_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FIVE_THREE_QUARTERS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_five_three_quarters
  - fields:
      placeholder: UTC_OFFSETS.PLUS_SIX
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_six
  - fields:
      placeholder: UTC_OFFSETS.PLUS_SIX_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_six_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_SEVEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_seven
  - fields:
      placeholder: UTC_OFFSETS.PLUS_EIGHT
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_eight
  - fields:
      placeholder: UTC_OFFSETS.PLUS_EIGHT_THREE_QUARTERS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_eight_three_quarters
  - fields:
      placeholder: UTC_OFFSETS.PLUS_NINE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_nine
  - fields:
      placeholder: UTC_OFFSETS.PLUS_NINE_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_nine_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_TEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_ten
  - fields:
      placeholder: UTC_OFFSETS.PLUS_TEN_HALF
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_ten_half
  - fields:
      placeholder: UTC_OFFSETS.PLUS_ELEVEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_eleven
  - fields:
      placeholder: UTC_OFFSETS.PLUS_TWELVE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_twelve
  - fields:
      placeholder: UTC_OFFSETS.PLUS_TWELVE_THREE_QUARTERS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_twelve_three_quarters
  - fields:
      placeholder: UTC_OFFSETS.PLUS_THIRTEEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_thirteen
  - fields:
      placeholder: UTC_OFFSETS.PLUS_FOURTEEN
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_utc_offsets_plus_fourteen
  - fields:
      placeholder: COMMON.EXCEPTIONS.INACTIVE_FEATURE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_inactive_feature
  - fields:
      placeholder: COMMON.EXCEPTIONS.ATTACHMENTS_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_attachments_not_found
  - fields:
      placeholder: COMMON.EXCEPTIONS.ATTACHMENTS_INVALID_TEMPLATE_TYPE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_attachments_invalid_template_type
  - fields:
      placeholder: COMMON.EXCEPTIONS.ATTACHMENTS_MISSING_REPORTER_EMAIL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_attachments_missing_reporter_email
  - fields:
      placeholder: COMMON.EXCEPTIONS.ATTACHMENTS_EMAIL_FAILED_TO_SEND
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_attachments_email_failed_to_send
  - fields:
      placeholder: COMMON.EXCEPTIONS.VALIDATION_ERROR
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_exceptions_validation_error
  - fields:
      placeholder: NAV.REDRESS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: nav_redress
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.STATUS.ENABLED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_status_enabled
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.STATUS.DEACTIVATED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_status_deactivated
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.STATUS.DISABLED
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_status_disabled
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.NODE.FIRST_PAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_node_first_page
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.NODE.NEXT_PAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_node_next_page
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.NODE.LAST_PAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_node_last_page
  - fields:
      placeholder: COMMON.COMPONENTS.TREE.NODE.PREVIOUS_PAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_tree_node_previous_page
  -
    fields:
      placeholder: COMMON.COMPONENTS.ATTACHMENTS.DISABLED_FOR_PUBLIC
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_components_attachments_disabled_for_public
  -
    fields:
      placeholder: EMAIL.ANONYMOUS_AUTHOR
      domains:
        - domain: '@domain_email'
    ref: email_anonymous_author
  -
    fields:
      placeholder: EMAIL.NOT_APPLICABLE
      domains:
        - domain: '@domain_email'
    ref: email_not_applicable
  -
    fields:
      placeholder: NAV.HOTSPOTS
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_hotspots
  -
    fields:
      placeholder: NAV.ICON_WALL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: nav_icon_wall
  -
    fields:
      placeholder: NAV.SAFEGUARDING
      type: 0
      domains:
        - domain: '@domain_common'
    ref: nav_safeguarding
  -
    fields:
      placeholder: LANGUAGE.LABEL.FR_CA
      type: 0
      domains:
        - domain: '@domain_common'
    ref: language_label_fr_ca

## CAPTURE_MODULES constants
  -
    fields:
      placeholder: CAPTURE_MODULES.COM.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_com_label
  -
    fields:
      placeholder: CAPTURE_MODULES.CLA.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_cla_label
  -
    fields:
      placeholder: CAPTURE_MODULES.INC.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_inc_label
  -
    fields:
      placeholder: CAPTURE_MODULES.DAS.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_das_label
  -
    fields:
      placeholder: CAPTURE_MODULES.RED.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_red_label
  -
    fields:
      placeholder: CAPTURE_MODULES.SFG.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: capture_modules_sfg_label
  -
    fields:
      placeholder: COMMON.MODULES.SAFEGUARDING
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_modules_safeguarding
  -
    fields:
      placeholder: DRAFTS.DISCARD
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_drafts_discard
  -
    fields:
      placeholder: DRAFTS.PUBLISH_NOW
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_drafts_publish_now
  -
    fields:
      placeholder: DRAFTS.PUBLISH_LATER
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_drafts_publish_later
  -
    fields:
      placeholder: DRAFTS.EDIT_DRAFT
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_drafts_edit_draft
  -
    fields:
      placeholder: DRAFTS.SHOW_DISCARD_MESSAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_drafts_show_discard_message
  -
    fields:
      placeholder: COMMON.CONFIRM_CLOSE.MESSAGE
      type: 0
      domains:
       - domain: '@domain_common'
    ref: common_confirm_close_popup_modal
  -
    fields:
      placeholder: COMMON.INVALID_TIME.MESSAGE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_invalid_time_message
  -
    fields:
      placeholder: COMMON.RECORD_NAV.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_nav_title
  -
    fields:
      placeholder: COMMON.RECORD_NAV.FIRST
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_nav_first
  -
    fields:
      placeholder: COMMON.RECORD_NAV.PREVIOUS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_nav_previous
  -
    fields:
      placeholder: COMMON.RECORD_NAV.NEXT
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_nav_next
  -
    fields:
      placeholder: COMMON.RECORD_NAV.LAST
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_record_nav_last
  - fields:
      placeholder: COMMON.ERROR_TYPES.SEE_THE_ERRORS_ATTRIBUTE_FOR_SPECIFIC_FAILURES_ERM1
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_error_see_the_errors_attribute_for_specific_failures_erm1
  -
    fields:
      placeholder: COMMON.ERRORS.RESOURCE_NOT_FOUND_ERM14
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: common_errors_resource_not_found_erm14
  -
    fields:
      placeholder: COMMON.FORM.VALIDATION.ERRORS_ERM15
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_form_validation_errors_erm15
  -
    fields:
      placeholder: COMMON.COMPONENTS.RANGE_FILTER.MIN_VALUE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_range_filter_min_value
  -
    fields:
      placeholder: COMMON.COMPONENTS.RANGE_FILTER.MAX_VALUE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_components_range_filter_max_value
  - fields:
      placeholder: COMMON.ATTACHMENT_SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: common_attachment_saved_successfully

