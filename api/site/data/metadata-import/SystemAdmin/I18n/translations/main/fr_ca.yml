entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@system_admin_tag_management', language: '@language_fr_ca', value: 'Gestion des balises' } }
  - { fields: { placeholder: '@system_admin_system_configuration', language: '@language_fr_ca', value: 'Configuration du système' } }
  - { fields: { placeholder: '@system_admin_system_configuration_default_module_label', language: '@language_fr_ca', value: 'Module à afficher à la connexion' } }
  - { fields: { placeholder: '@system_admin_system_configuration_logout_redirect_target_label', language: '@language_fr_ca', value: 'Page à afficher à la déconnexion' } }
  - { fields: { placeholder: '@system_admin_system_configuration_logout_redirect_login_page_label', language: '@language_fr_ca', value: 'Page de connexion' } }
  - { fields: { placeholder: '@system_admin_system_configuration_logout_redirect_dif1_label', language: '@language_fr_ca', value: 'Formulaire d''incident de Niveau 1 par défaut' } }
  - { fields: { placeholder: '@system_admin_system_configuration_utc_offset_label', language: '@language_fr_ca', value: 'Décalage UTC du système' } }
  - { fields: { placeholder: '@system_admin_error_invalid_logout_redirect_target', language: '@language_fr_ca', value: 'Veuillez choisir une page à afficher à la déconnexion' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_delete_contacts_label', language: '@language_fr_ca', value: 'Autoriser la suppression de la personne-ressource' } }
  - { fields: { placeholder: '@system_admin_system_configuration_available_languages_label', language: '@language_fr_ca', value: 'Langues disponibles' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enabled', language: '@language_fr_ca', value: Activé } }
  - { fields: { placeholder: '@system_admin_system_configuration_disabled', language: '@language_fr_ca', value: Désactivé } }
  - { fields: { placeholder: '@system_admin_nav_acl', language: '@language_fr_ca', value: LCA } }
  - { fields: { placeholder: '@system_admin_nav_custom_fields', language: '@language_fr_ca', value: 'Maintenance de champ' } }
  - { fields: { placeholder: '@system_admin_nav_form_designer', language: '@language_fr_ca', value: 'Concepteur du formulaire' } }
  - { fields: { placeholder: '@system_admin_nav_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@system_admin_nav_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@system_admin_nav_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@system_admin_nav_actions_configuration', language: '@language_fr_ca', value: 'Configuration d''actions' } }
  - { fields: { placeholder: '@system_admin_nav_capture_admin', language: '@language_fr_ca', value: 'Capturer admin' } }
  - { fields: { placeholder: '@system_admin_nav_config_portation', language: '@language_fr_ca', value: 'Importer ou exporter la configuration' } }
  - { fields: { placeholder: '@system_admin_nav_benchmarking', language: '@language_fr_ca', value: Étalonnage } }
  - { fields: { placeholder: '@system_admin_tag_management_tags', language: '@language_fr_ca', value: Balises } }
  - { fields: { placeholder: '@system_admin_tag_management_new_tag', language: '@language_fr_ca', value: 'Nouvelle balise' } }
  - { fields: { placeholder: '@system_admin_tag_management_add_tag', language: '@language_fr_ca', value: 'Ajouter une balise' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_loading', language: '@language_fr_ca', value: 'Chargement des balises...' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_columns_priority', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_columns_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_columns_action', language: '@language_fr_ca', value: Action } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_no_tags_found', language: '@language_fr_ca', value: 'Aucune balise trouvée' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_location_tags', language: '@language_fr_ca', value: 'Balises d''emplacement' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_service_tags', language: '@language_fr_ca', value: 'Balises de service' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_errors_no_tag_entered', language: '@language_fr_ca', value: 'Veuillez entrer une balise' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_created_successfully', language: '@language_fr_ca', value: 'Balise créée avec succès' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_updated_successfully', language: '@language_fr_ca', value: 'Balise mise à jour avec succès' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_deleted_successfully', language: '@language_fr_ca', value: 'Balise supprimée avec succès' } }
  - { fields: { placeholder: '@system_admin_nav_audit_log', language: '@language_fr_ca', value: 'Journal d''audit' } }
  - { fields: { placeholder: '@system_admin_audit_log_record_id', language: '@language_fr_ca', value: 'Id de dossier' } }
  - { fields: { placeholder: '@system_admin_audit_log_record_name', language: '@language_fr_ca', value: 'Nom du dossier' } }
  - { fields: { placeholder: '@system_admin_audit_log_module_name', language: '@language_fr_ca', value: 'Nom du module' } }
  - { fields: { placeholder: '@system_admin_audit_log_timestamp', language: '@language_fr_ca', value: Horodatage } }
  - { fields: { placeholder: '@system_admin_audit_log_action_type', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@system_admin_audit_log_user_name', language: '@language_fr_ca', value: 'Nom d''utilisateur' } }
  - { fields: { placeholder: '@system_admin_audit_log_user_id', language: '@language_fr_ca', value: 'Id d''utilisateur' } }
  - { fields: { placeholder: '@system_admin_audit_log_changes', language: '@language_fr_ca', value: Modifications } }
  - { fields: { placeholder: '@system_admin_nav_main_audit', language: '@language_fr_ca', value: 'Vérification principale' } }
  - { fields: { placeholder: '@system_admin_nav_login_audit', language: '@language_fr_ca', value: 'Vériication de connexion' } }
  - { fields: { placeholder: '@system_admin_audit_log_login_audit_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@system_admin_audit_log_login_audit_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@system_admin_system_configuration_logo_url', language: '@language_fr_ca', value: 'URL du logo' } }
  - { fields: { placeholder: '@system_admin_system_configuration_validation_logo_url_invalid', language: '@language_fr_ca', value: 'L''URL du logo doit être en https' } }
  - { fields: { placeholder: '@system_admin_system_configuration_login_message', language: '@language_fr_ca', value: 'Message de connexion' } }
  - { fields: { placeholder: '@system_admin_system_configuration_agreement_message', language: '@language_fr_ca', value: 'Message d''accord' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_login_message', language: '@language_fr_ca', value: 'Message de connexion activé?' } }
  - { fields: { placeholder: '@placeholder_system_admin_audit_log_log_title', language: '@language_fr_ca', value: '{{action}} - {{module}} - {{recordType}} Nº {{recordId}}' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_agreement_message', language: '@language_fr_ca', value: 'Message d''accord activé?' } }
  - { fields: { placeholder: '@system_admin_system_configuration_non_capture_reporting_status', language: '@language_fr_ca', value: 'État de rapport sans capture' } }
  - { fields: { placeholder: '@system_admin_tag_management_tags_save_tags', language: '@language_fr_ca', value: 'Enregistrer les balises' } }
  - { fields: { placeholder: '@system_admin_audit_log_filter_title', language: '@language_fr_ca', value: 'Filtrer le journal de vérification' } }
  - { fields: { placeholder: '@system_admin_audit_log_filter_audit_entry_date', language: '@language_fr_ca', value: 'Date de l''entrée de vérification' } }
  - { fields: { placeholder: '@system_admin_audit_log_filter_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@system_admin_audit_log_filter_user', language: '@language_fr_ca', value: Utilisateur } }
  - { fields: { placeholder: '@system_admin_audit_log_filter_record_id', language: '@language_fr_ca', value: 'ID d''enregistrement' } }
  - { fields: { placeholder: '@system_admin_report_email_label', language: '@language_fr_ca', value: 'Adresse courriel de rapport' } }
  - { fields: { placeholder: '@system_admin_report_download_limit_label', language: '@language_fr_ca', value: 'Limite de téléchargement de rapport' } }
  - { fields: { placeholder: '@system_admin_report_expiry_hours_label', language: '@language_fr_ca', value: 'Heures d''expiration du rapport' } }
  - { fields: { placeholder: '@system_admin_report_email_invalid', language: '@language_fr_ca', value: 'Le courriel du rapport n''est pas valide' } }
  - { fields: { placeholder: '@system_admin_report_download_limit_invalid', language: '@language_fr_ca', value: 'La limite de téléchargement du rapport doit être supérieure à zéro et inférieure à 30' } }
  - { fields: { placeholder: '@system_admin_report_expire_invalid', language: '@language_fr_ca', value: 'L''expiration du rapport doit être supérieure à zéro et inférieure à 720' } }
  - { fields: { placeholder: '@system_admin_error_invalid_non_capture_reporting_status', language: '@language_fr_ca', value: 'L''État de rapport sans capture indiqué n''est pas valide' } }
  - { fields: { placeholder: '@system_admin_system_configuration_non_capture_reporting_disabled', language: '@language_fr_ca', value: Désactivé } }
  - { fields: { placeholder: '@system_admin_system_configuration_non_capture_reporting_enabled_for_admins', language: '@language_fr_ca', value: 'Activé pour les administrateurs seulement' } }
  - { fields: { placeholder: '@system_admin_system_configuration_non_capture_reporting_enabled_for_all_users', language: '@language_fr_ca', value: 'Activé pour tous les utilisateurs' } }
  - { fields: { placeholder: '@system_admin_audit_log_meta_details', language: '@language_fr_ca', value: '{{ loggedAt }} par #{{ userId }} {{ userName }} {{ delegate }}' } }
  - { fields: { placeholder: '@system_admin_system_configuration_user_delegation_options', language: '@language_fr_ca', value: 'Options de délégation d''utilisateur' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_mandatory_dates', language: '@language_fr_ca', value: 'Date de début et de fin obligatoires' } }
  - { fields: { placeholder: '@system_admin_environment', language: '@language_fr_ca', value: 'Environnement système' } }
  - { fields: { placeholder: '@local_admin_system_configuration_restrict_location_assigment_label', language: '@language_fr_ca', value: 'Restreindre l’administrateur local, attribuer ses propres emplacements' } }
  - { fields: { placeholder: '@local_admin_system_configuration_restrict_service_assignment_label', language: '@language_fr_ca', value: 'Restreindre l’administrateur local, attribuer ses propres services' } }
  - { fields: { placeholder: '@system_admin_system_configuration_inactivity_timeout_label', language: '@language_fr_ca', value: 'Délai d’inactivité en minutes (min. {{minimum}}, max. {{maximum}})' } }
  - { fields: { placeholder: '@system_admin_system_configuration_max_file_upload_limit_label', language: '@language_fr_ca', value: 'Définir la limite maximale de téléversement (min. {{minimum}} MB, max. {{maximum}} MB)' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enabled_display_previous_login_attempts', language: '@language_fr_ca', value: 'Afficher les tentatives de connexion précédentes?' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_footer_text', language: '@language_fr_ca', value: 'Activer le texte de pied de page' } }
  - { fields: { placeholder: '@system_admin_system_configuration_footer_text', language: '@language_fr_ca', value: 'Zone de texte du pied de page' } }
  - { fields: { placeholder: '@system_admin_system_configuration_maintenance_mode_label', language: '@language_fr_ca', value: 'Mode de maintenance' } }
  - { fields: { placeholder: '@local_admin_system_configuration_maintenance_mode_label', language: '@language_fr_ca', value: 'Mode de maintenance de l’administrateur local' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_delete_investigations_label', language: '@language_fr_ca', value: 'Autoriser la suppression d’enquête' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_delete_risks_label', language: '@language_fr_ca', value: 'Autoriser la suppression de risque' } }
  - { fields: { placeholder: '@system_admin_system_configuration_enable_delete_safety_alerts_label', language: '@language_fr_ca', value: 'Autoriser la suppression des alertes de sécurité' } }
  - { fields: { placeholder: '@system_admin_my_settings_enabled_label', language: '@language_fr_ca', value: 'Mes paramètres activés' } }
