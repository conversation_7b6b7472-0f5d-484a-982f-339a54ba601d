entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@checklists_question_form_fields_allow_attachments', language: '@language_ar', value: 'السماح بالمرفقات؟' } }
    - { fields: { placeholder: '@checklists_checklist', language: '@language_ar', value: 'الدراسة الاستقصائية' } }
    - { fields: { placeholder: '@checklists_checklist_details_edit', language: '@language_ar', value: 'تحرير الاستبيان' } }
    - { fields: { placeholder: '@checklists_checklist_nav_access_control', language: '@language_ar', value: 'صلاحية الدخول' } }
    - { fields: { placeholder: '@checklists_checklist_nav_checklist_number', language: '@language_ar', value: 'الإستبيان  #{{id}}' } }
    - { fields: { placeholder: '@checklists_checklist_nav_details', language: '@language_ar', value: 'تفاصيل استبيان' } }
    - { fields: { placeholder: '@checklists_checklist_nav_questions', language: '@language_ar', value: الأسئلة } }
    - { fields: { placeholder: '@checklists_checklist_questions_groups', language: '@language_ar', value: 'مجموعة الاستبيان' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details', language: '@language_ar', value: 'تفاصيل المجموعة' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_delete', language: '@language_ar', value: 'حذف المجموعة' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_name', language: '@language_ar', value: اسم } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_summary', language: '@language_ar', value: ملخص } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_save', language: '@language_ar', value: 'حفظ المجموعة' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions', language: '@language_ar', value: الأسئلة } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_add_new', language: '@language_ar', value: 'إضافة سؤال جديد' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_actions', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_allow_attachments', language: '@language_ar', value: 'السماح بالمرفقات؟' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_mandatory', language: '@language_ar', value: إلزامي؟ } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_question', language: '@language_ar', value: الأسئلة } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_no_records', language: '@language_ar', value: 'هذه المجموعة لا تحتوي على أسئلة' } }
    - { fields: { placeholder: '@checklists_checklist_questions_title', language: '@language_ar', value: الأسئلة } }
    - { fields: { placeholder: '@checklists_checklists', language: '@language_ar', value: الاستبيانات } }
    - { fields: { placeholder: '@checklists_common_back_dashboard', language: '@language_ar', value: 'العودة إلى لوحة المعلومات' } }
    - { fields: { placeholder: '@checklists_common_collect_user_data', language: '@language_ar', value: 'جمع بيانات المستخدم؟' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_all_templates', language: '@language_ar', value: 'جميع القوالب' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_browse_checklists', language: '@language_ar', value: 'تصفح الاستبيان' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_distribute', language: '@language_ar', value: نشر } }
    - { fields: { placeholder: '@checklists_dashboard_nav_my_distributions', language: '@language_ar', value: توزيعاتي } }
    - { fields: { placeholder: '@checklists_dashboard_nav_new_template', language: '@language_ar', value: 'قالب جديد' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_permissions', language: '@language_ar', value: ضوابط } }
    - { fields: { placeholder: '@checklists_distribute_checklist_questions', language: '@language_ar', value: 'أسئلة الاستبيان' } }
    - { fields: { placeholder: '@checklists_distribute_checklist_summary', language: '@language_ar', value: 'ملخص الاستبيان' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_actions', language: '@language_ar', value: الإجراءات } }
    - { fields: { placeholder: '@checklists_distribute_recipients_add_responder', language: '@language_ar', value: 'إضافة المستجيب' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_datix_users', language: '@language_ar', value: 'مستخدمي داتكس' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_distribute_survey', language: '@language_ar', value: ' نشر استبيان' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_email', language: '@language_ar', value: 'البريد الإلكتروني' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_existing_recipients', language: '@language_ar', value: 'المستلمون الحاليون ({{numRecipients}})' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users', language: '@language_ar', value: 'مستخدمين خارجيين' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_no_external_users', language: '@language_ar', value: 'لم يتم توزيع قائمة التحقق هذه على أي مستلم خارجي' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_use_form', language: '@language_ar', value: 'استخدم النموذج أعلاه لإضافة مستلمين خارجيين جدد' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_add_user', language: '@language_ar', value: 'هل ترغب في إضافة {{userName}} إلى قائمة المستخدمين الاختياريين؟' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_description', language: '@language_ar', value: 'عنوان البريد الإلكتروني الذي تم إدخاله يطابق مستخدم Datix موجود' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_title', language: '@language_ar', value: 'العثور على المستخدم المطابق' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_invited_on', language: '@language_ar', value: المدعوة } }
    - { fields: { placeholder: '@checklists_distribute_recipients_loading', language: '@language_ar', value: 'تحميل المستلمين الاستبيان' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_name', language: '@language_ar', value: اسم } }
    - { fields: { placeholder: '@checklists_distribute_recipients_new_recipients', language: '@language_ar', value: 'المستلمون الجدد ({{numRecipients}})' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_optional_response', language: '@language_ar', value: 'استجابة اختيارية' } }
    - { fields: { placeholder: '@checklists_distribute_recipients_required_response', language: '@language_ar', value: 'الاستجابة المطلوبة' } }
    - { fields: { placeholder: '@checklists_error_expired', language: '@language_ar', value: 'انتهت صلاحية الاستبيان ولم يعد من الممكن الرد عليها' } }
    - { fields: { placeholder: '@checklists_error_loading', language: '@language_ar', value: 'حدث خطأ أثناء استرداد الاستبيان' } }
    - { fields: { placeholder: '@checklists_error_not_found', language: '@language_ar', value: 'الاستبيان غير موجود' } }
    - { fields: { placeholder: '@checklists_export_csv', language: '@language_ar', value: 'تصدير إلى' } }
    - { fields: { placeholder: '@checklists_form_fields_category_empty_value', language: '@language_ar', value: 'اختر فئة' } }
    - { fields: { placeholder: '@checklists_form_fields_category_label', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_a', language: '@language_ar', value: 'الفئة أ' } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_b', language: '@language_ar', value: 'الفئة ب' } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_c', language: '@language_ar', value: 'الفئة ج' } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_d', language: '@language_ar', value: 'الفئة د' } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_e', language: '@language_ar', value: 'الفئة هـ' } }
    - { fields: { placeholder: '@checklists_form_fields_description_label', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@checklists_form_fields_due_date_label', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@checklists_form_fields_title_label', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@checklists_form_fields_type_empty_value', language: '@language_ar', value: 'اختر النوع' } }
    - { fields: { placeholder: '@checklists_form_fields_type_label', language: '@language_ar', value: نوع } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_a', language: '@language_ar', value: 'نوع أ' } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_b', language: '@language_ar', value: 'نوع ب' } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_c', language: '@language_ar', value: 'نوع ج' } }
    - { fields: { placeholder: '@checklists_form_published_successfully', language: '@language_ar', value: 'الاستبيان المنشورة بنجاح' } }
    - { fields: { placeholder: '@checklists_form_save', language: '@language_ar', value: 'حفظ الاستبيان' } }
    - { fields: { placeholder: '@checklists_form_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ الاستبيان' } }
    - { fields: { placeholder: '@checklists_form_saved_successfully', language: '@language_ar', value: 'الاستبيان حفظ بنجاح' } }
    - { fields: { placeholder: '@checklists_form_user_access_all_datix_users', language: '@language_ar', value: 'جميع مستخدمي داتكس' } }
    - { fields: { placeholder: '@checklists_form_user_access_collect_user_data', language: '@language_ar', value: 'جمع بيانات المستخدم؟' } }
    - { fields: { placeholder: '@checklists_form_user_access_open_access', language: '@language_ar', value: 'الوصول المفتوح' } }
    - { fields: { placeholder: '@checklists_form_user_access_require_captcha', language: '@language_ar', value: 'يتطلب اختبار كلمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_user_access_selected_users_only', language: '@language_ar', value: 'المستخدمين المحددين فقط' } }
    - { fields: { placeholder: '@checklists_form_user_access_title', language: '@language_ar', value: 'وصول المستخدم' } }
    - { fields: { placeholder: '@checklists_forms_response_public_submit_success', language: '@language_ar', value: 'تم إرسال ردك بنجاح. يمكنك الآن إغلاق هذه النافذة' } }
    - { fields: { placeholder: '@checklists_forms_response_submit', language: '@language_ar', value: 'إرسال الرد' } }
    - { fields: { placeholder: '@checklists_forms_response_submit_success', language: '@language_ar', value: 'أرسل الاستبيان بنجاح' } }
    - { fields: { placeholder: '@checklists_new', language: '@language_ar', value: 'استبيان جديد' } }
    - { fields: { placeholder: '@checklists_question_form_add', language: '@language_ar', value: 'إضافة سؤال' } }
    - { fields: { placeholder: '@checklists_question_form_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@checklists_question_form_edit', language: '@language_ar', value: 'تحرير السؤال' } }
    - { fields: { placeholder: '@checklists_question_form_fields_add_another', language: '@language_ar', value: 'إضافة سؤال آخر؟' } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_label', language: '@language_ar', value: 'نوع الحقل' } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_date', language: '@language_ar', value: تاريخ } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_long_text', language: '@language_ar', value: 'نص (طويل)' } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_selection', language: '@language_ar', value: اختيار } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_short_text', language: '@language_ar', value: 'نص (قصير)' } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_checkboxes', language: '@language_ar', value: 'خانات الاختيار' } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_dropdown', language: '@language_ar', value: اسقاط } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_label', language: '@language_ar', value: 'نوع المدخلات' } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_radio_buttons', language: '@language_ar', value: 'أزرار الراديو' } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_range', language: '@language_ar', value: نطاق } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_smiley_scale', language: '@language_ar', value: 'مقياس مبتسم' } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_yes_no', language: '@language_ar', value: 'نعم لا' } }
    - { fields: { placeholder: '@checklists_question_form_fields_introductory_text', language: '@language_ar', value: 'نص تمهيدي' } }
    - { fields: { placeholder: '@checklists_question_form_fields_label', language: '@language_ar', value: ملصق } }
    - { fields: { placeholder: '@checklists_question_form_fields_mandatory', language: '@language_ar', value: إلزامي؟ } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_label', language: '@language_ar', value: 'اختيار نوع' } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_multiple', language: '@language_ar', value: 'استجابات متعددة' } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_single', language: '@language_ar', value: 'رد واحد' } }
    - { fields: { placeholder: '@checklists_question_form_save', language: '@language_ar', value: 'حفظ السؤال' } }
    - { fields: { placeholder: '@checklists_recipient', language: '@language_ar', value: مستلم } }
    - { fields: { placeholder: '@checklists_recipients', language: '@language_ar', value: المستفيدين } }
    - { fields: { placeholder: '@checklists_responses', language: '@language_ar', value: استجابات } }
    - { fields: { placeholder: '@checklists_responses_nav_checklist_summary', language: '@language_ar', value: 'ملخص الاستبيان' } }
    - { fields: { placeholder: '@checklists_responses_nav_recipients', language: '@language_ar', value: المستفيدين } }
    - { fields: { placeholder: '@checklists_responses_nav_responses', language: '@language_ar', value: استجابات } }
    - { fields: { placeholder: '@checklists_tables_columns_due', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@checklists_tables_columns_id', language: '@language_ar', value: 'رقم التعريف' } }
    - { fields: { placeholder: '@checklists_tables_columns_published', language: '@language_ar', value: نشرت } }
    - { fields: { placeholder: '@checklists_tables_columns_responder', language: '@language_ar', value: الرد } }
    - { fields: { placeholder: '@checklists_tables_columns_submission_date', language: '@language_ar', value: 'تاريخ التقديم' } }
    - { fields: { placeholder: '@checklists_tables_columns_title', language: '@language_ar', value: العنوان } }
    - { fields: { placeholder: '@checklists_tables_columns_expired', language: '@language_ar', value: منتهية الصلاحية } }
    - { fields: { placeholder: '@checklists_yes_no_na', language: '@language_ar', value: 'غير قابل للتطبيق' } }
    - { fields: { placeholder: '@checklists_yes_no_no', language: '@language_ar', value: لا } }
    - { fields: { placeholder: '@checklists_yes_no_yes', language: '@language_ar', value: 'نعم ' } }
    - { fields: { placeholder: '@checklists_checklist_1_country_of_residence', language: '@language_ar', value: 'بلد الإقامة' } }
    - { fields: { placeholder: '@checklists_checklist_1_country_of_residence_label', language: '@language_ar', value: 'بلد الإقامة' } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence', language: '@language_ar', value: 'بلد الإقامة' } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence_label', language: '@language_ar', value: 'بلد الإقامة' } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_name', language: '@language_ar', value: اسم } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_name_label', language: '@language_ar', value: اسم } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox', language: '@language_ar', value: 'حقل خانة الاختيار' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox_label', language: '@language_ar', value: 'حقل خانة الاختيار' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_date', language: '@language_ar', value: 'حقل التاريخ' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_date_label', language: '@language_ar', value: 'حقل التاريخ' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown', language: '@language_ar', value: 'حقل منسدل' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown_label', language: '@language_ar', value: 'حقل منسدل' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect', language: '@language_ar', value: 'حقل متعدد الاختيارات' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect_label', language: '@language_ar', value: 'حقل متعدد الاختيارات' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_radio', language: '@language_ar', value: 'حقل راديو' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_radio_label', language: '@language_ar', value: 'حقل راديو' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_range', language: '@language_ar', value: 'حقل المدى' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_range_label', language: '@language_ar', value: 'حقل المدى' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale', language: '@language_ar', value: 'مقياس مبتسم' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale_label', language: '@language_ar', value: 'مقياس مبتسم' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_text', language: '@language_ar', value: 'حقل النص' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_text_label', language: '@language_ar', value: 'حقل النص' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_textarea', language: '@language_ar', value: 'حقل منطقة النص' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_textarea_label', language: '@language_ar', value: 'حقل منطقة النص' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no', language: '@language_ar', value: 'نعم / لا حقل' } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no_label', language: '@language_ar', value: 'نعم / لا حقل' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_1', language: '@language_ar', value: 'تأكيد الهوية' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_1_label', language: '@language_ar', value: 'تأكيد الهوية' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_2', language: '@language_ar', value: 'تأكيد الموافقة' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_2_label', language: '@language_ar', value: 'تأكيد الموافقة' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_3', language: '@language_ar', value: 'موقع جراحة ملحوظ' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_3_label', language: '@language_ar', value: 'موقع جراحة ملحوظ' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_4', language: '@language_ar', value: 'التخدير والأدوية الاختيار' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_4_label', language: '@language_ar', value: 'التخدير والأدوية الاختيار' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_5', language: '@language_ar', value: 'التخدير والأدوية الاختيار' } }
    - { fields: { placeholder: '@checklists_checklist_4_field_5_label', language: '@language_ar', value: 'التخدير والأدوية الاختيار' } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_1', language: '@language_ar', value: 'خانة الاختيار 1' } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_2', language: '@language_ar', value: 'خانة الاختيار 2' } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_3', language: '@language_ar', value: 'خانة الاختيار 3' } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_1', language: '@language_ar', value: 'خيار القائمة المنسدلة 1' } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_2', language: '@language_ar', value: 'خيار القائمة المنسدلة 2' } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_3', language: '@language_ar', value: 'خيار القائمة المنسدلة 4' } }
    - { fields: { placeholder: '@checklists_datasource_item_france', language: '@language_ar', value: فرنسا } }
    - { fields: { placeholder: '@checklists_datasource_item_italy', language: '@language_ar', value: إيطاليا } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_1', language: '@language_ar', value: 'الاختيار المتعدد الخيار 1' } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_2', language: '@language_ar', value: 'الاختيار المتعدد الخيار 2' } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_3', language: '@language_ar', value: 'الاختيار المتعدد الخيار 3' } }
    - { fields: { placeholder: '@checklists_datasource_item_no', language: '@language_ar', value: لا } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_1', language: '@language_ar', value: 'الخيار الاذاعي 1' } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_2', language: '@language_ar', value: 'الخيار الاذاعي 2' } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_3', language: '@language_ar', value: 'الخيار الاذاعي  3' } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_1', language: '@language_ar', value: 'نطاق الخيار 1' } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_2', language: '@language_ar', value: 'نطاق الخيار 3' } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_3', language: '@language_ar', value: 'نطاق الخيار 3' } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_4', language: '@language_ar', value: 'نطاق الخيار 4' } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_5', language: '@language_ar', value: 'نطاق الخيار 5' } }
    - { fields: { placeholder: '@checklists_datasource_item_uk', language: '@language_ar', value: 'المملكة المتحدة' } }
    - { fields: { placeholder: '@checklists_datasource_item_yes', language: '@language_ar', value: 'نعم ' } }
    - { fields: { placeholder: '@checklists_datasource_checkbox_options', language: '@language_ar', value: 'خيارات خانة الاختيار' } }
    - { fields: { placeholder: '@checklists_datasource_checklist_access_level', language: '@language_ar', value: 'مستوى وصول الاستطلاع' } }
    - { fields: { placeholder: '@checklists_datasource_checklist_category', language: '@language_ar', value: 'فئة الاستطلاع' } }
    - { fields: { placeholder: '@checklists_datasource_checklist_subcategory', language: '@language_ar', value: 'فئة الاستطلاع الفرعية' } }
    - { fields: { placeholder: '@checklists_datasource_checklist_type', language: '@language_ar', value: 'نوع الاستطلاع' } }
    - { fields: { placeholder: '@checklists_datasource_countries_of_residence', language: '@language_ar', value: 'دول الاقامة' } }
    - { fields: { placeholder: '@checklists_datasource_dropdown_options', language: '@language_ar', value: 'خيارات المنسدلة' } }
    - { fields: { placeholder: '@checklists_datasource_multiselect_options', language: '@language_ar', value: 'خيارات متعددة الخيارات' } }
    - { fields: { placeholder: '@checklists_datasource_radio_options', language: '@language_ar', value: 'خيارات الراديو' } }
    - { fields: { placeholder: '@checklists_datasource_range_options', language: '@language_ar', value: 'خيارات النطاق' } }
    - { fields: { placeholder: '@checklists_datasource_yes_no', language: '@language_ar', value: 'نعم لا' } }
    - { fields: { placeholder: '@checklists_datasource_yes_no_na', language: '@language_ar', value: 'نعم / لا / غير موجود' } }
    - { fields: { placeholder: '@checklists_datasource_yes_no_options', language: '@language_ar', value: 'نعم / لا' } }
    - { fields: { placeholder: '@checklists_form_type_survey_response', language: '@language_ar', value: 'استجابة الاستطلاع' } }
    - { fields: { placeholder: '@checklists_form_field_access_level', language: '@language_ar', value: 'قائمة التحقق مستوى الوصول' } }
    - { fields: { placeholder: '@checklists_form_field_access_level_label', language: '@language_ar', value: 'قائمة التحقق مستوى الوصول' } }
    - { fields: { placeholder: '@checklists_form_field_category', language: '@language_ar', value: 'فئة قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_category_label', language: '@language_ar', value: 'فئة قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_description', language: '@language_ar', value: 'وصف قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_description_label', language: '@language_ar', value: 'وصف قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_due_date', language: '@language_ar', value: 'قائمة التحقق الفرعية' } }
    - { fields: { placeholder: '@checklists_form_field_due_date_label', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@checklists_form_field_sub_category', language: '@language_ar', value: 'قائمة التحقق الفرعية' } }
    - { fields: { placeholder: '@checklists_form_field_sub_category_label', language: '@language_ar', value: 'تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@checklists_form_field_title', language: '@language_ar', value: 'عنوان قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_title_label', language: '@language_ar', value: 'عنوان قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_type', language: '@language_ar', value: 'نوع قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_field_type_label', language: '@language_ar', value: 'وصف قائمة التحقق' } }
    - { fields: { placeholder: '@checklists_form_name_checklist_1_form', language: '@language_ar', value: 'نموذج قائمة المراجعة 1' } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_all_datix_users_user_access', language: '@language_ar', value: 'قائمة مراجعة مع جميع مستخدمي Datix (وصول المستخدم)' } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_open_access_user_access', language: '@language_ar', value: 'قائمة تدقيق مع وصول مفتوح (وصول المستخدم)' } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_selected_users_only_user_access', language: '@language_ar', value: 'قائمة التحقق مع المستخدمين المحددين فقط (وصول المستخدم)' } }
    - { fields: { placeholder: '@checklists_form_name_example_checklist', language: '@language_ar', value: 'مثال على قائمة التحقق' } }
    - { fields: { placeholder: '@checklist_response_filter_submission_date', language: '@language_ar', value: 'تاريخ التقديم' } }
    - { fields: { placeholder: '@checklist_response_filter_submission_date_label', language: '@language_ar', value: 'تاريخ التقديم' } }
