entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@checklists_add_the_first_survey_group', language: '@language_en_us', value: Add the first Survey Group } }
    - { fields: { placeholder: '@checklists_checklist', language: '@language_en_us', value: Survey } }
    - { fields: { placeholder: '@checklists_checklist_1_country_of_residence', language: '@language_en_us', value: Country of Residence } }
    - { fields: { placeholder: '@checklists_checklist_1_country_of_residence_label', language: '@language_en_us', value: Country of Residence } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence', language: '@language_en_us', value: Country of Residence } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence_label', language: '@language_en_us', value: Country of Residence } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@checklists_checklist_1_fields_name_label', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox', language: '@language_en_us', value: Checkbox Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox_label', language: '@language_en_us', value: Checkbox Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_date', language: '@language_en_us', value: Date Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_date_label', language: '@language_en_us', value: Date Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown', language: '@language_en_us', value: Dropdown Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown_label', language: '@language_en_us', value: Dropdown Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect', language: '@language_en_us', value: Multiselect Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect_label', language: '@language_en_us', value: Multiselect Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_radio', language: '@language_en_us', value: Radio Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_radio_label', language: '@language_en_us', value: Radio Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_range', language: '@language_en_us', value: Range Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_range_label', language: '@language_en_us', value: Range Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale', language: '@language_en_us', value: Smiley Scale } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale_label', language: '@language_en_us', value: Smiley Scale } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_text', language: '@language_en_us', value: Text Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_text_label', language: '@language_en_us', value: Text Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_textarea', language: '@language_en_us', value: Textarea Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_textarea_label', language: '@language_en_us', value: Textarea Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no', language: '@language_en_us', value: Yes / No Field } }
    - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no_label', language: '@language_en_us', value: Yes / No Field } }
    - { fields: { placeholder: '@checklists_checklist_4_field_1', language: '@language_en_us', value: Confirmation of identity } }
    - { fields: { placeholder: '@checklists_checklist_4_field_1_label', language: '@language_en_us', value: Confirmation of identity } }
    - { fields: { placeholder: '@checklists_checklist_4_field_2', language: '@language_en_us', value: Confirmation of consent } }
    - { fields: { placeholder: '@checklists_checklist_4_field_2_label', language: '@language_en_us', value: Confirmation of consent } }
    - { fields: { placeholder: '@checklists_checklist_4_field_3', language: '@language_en_us', value: Surgery site marked } }
    - { fields: { placeholder: '@checklists_checklist_4_field_3_label', language: '@language_en_us', value: Surgery site marked } }
    - { fields: { placeholder: '@checklists_checklist_4_field_4', language: '@language_en_us', value: Anesthesia and medication check } }
    - { fields: { placeholder: '@checklists_checklist_4_field_4_label', language: '@language_en_us', value: Anesthesia and medication check } }
    - { fields: { placeholder: '@checklists_checklist_4_field_5', language: '@language_en_us', value: Anesthesia and medication check } }
    - { fields: { placeholder: '@checklists_checklist_4_field_5_label', language: '@language_en_us', value: Anesthesia and medication check } }
    - { fields: { placeholder: '@checklists_checklist_details_edit', language: '@language_en_us', value: Edit Survey } }
    - { fields: { placeholder: '@checklists_checklist_has_expired_notice', language: '@language_en_us', value: "This Survey expired on {{expiryDate}}" } }
    - { fields: { placeholder: '@checklists_checklist_nav_access_control', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@checklists_checklist_nav_checklist_number', language: '@language_en_us', value: "Survey #{{id}}" } }
    - { fields: { placeholder: '@checklists_checklist_nav_details', language: '@language_en_us', value: Survey Details } }
    - { fields: { placeholder: '@checklists_checklist_nav_questions', language: '@language_en_us', value: Questions } }
    - { fields: { placeholder: '@checklists_checklist_questions_groups', language: '@language_en_us', value: Survey Groups } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details', language: '@language_en_us', value: Group Details } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_delete', language: '@language_en_us', value: Delete Group } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_summary', language: '@language_en_us', value: Summary } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_save', language: '@language_en_us', value: Save Group } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions', language: '@language_en_us', value: Questions } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_add_new', language: '@language_en_us', value: Add New Question } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_allow_attachments', language: '@language_en_us', value: 'Allow Attachments?' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_mandatory', language: '@language_en_us', value: 'Mandatory?' } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_question', language: '@language_en_us', value: Questions } }
    - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_no_records', language: '@language_en_us', value: This group contains no questions } }
    - { fields: { placeholder: '@checklists_checklist_questions_title', language: '@language_en_us', value: Questions } }
    - { fields: { placeholder: '@checklists_checklists', language: '@language_en_us', value: Surveys } }
    - { fields: { placeholder: '@checklists_common_back_dashboard', language: '@language_en_us', value: Back to Dashboard } }
    - { fields: { placeholder: '@checklists_common_collect_user_data', language: '@language_en_us', value: 'Collect User Data?' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_all_templates', language: '@language_en_us', value: Survey Dashboard } }
    - { fields: { placeholder: '@checklists_dashboard_nav_browse_checklists', language: '@language_en_us', value: Browse Surveys } }
    - { fields: { placeholder: '@checklists_dashboard_nav_distribute', language: '@language_en_us', value: Distribute a Survey } }
    - { fields: { placeholder: '@checklists_dashboard_nav_my_responses', language: '@language_en_us', value: My Responses } }
    - { fields: { placeholder: '@checklists_dashboard_nav_my_distributions', language: '@language_en_us', value: View Survey Responses } }
    - { fields: { placeholder: '@checklists_dashboard_nav_new_template', language: '@language_en_us', value: Create a new Survey } }
    - { fields: { placeholder: '@checklists_dashboard_nav_permissions', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_1', language: '@language_en_us', value: Checkbox Option 1 } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_2', language: '@language_en_us', value: Checkbox Option 2 } }
    - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_3', language: '@language_en_us', value: Checkbox Option 3 } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_1', language: '@language_en_us', value: Dropdown Option 1 } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_2', language: '@language_en_us', value: Dropdown Option 2 } }
    - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_3', language: '@language_en_us', value: Dropdown Option 4 } }
    - { fields: { placeholder: '@checklists_datasource_item_france', language: '@language_en_us', value: France } }
    - { fields: { placeholder: '@checklists_datasource_item_italy', language: '@language_en_us', value: Italy } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_1', language: '@language_en_us', value: Multiselect Option 1 } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_2', language: '@language_en_us', value: Multiselect Option 2 } }
    - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_3', language: '@language_en_us', value: Multiselect Option 3 } }
    - { fields: { placeholder: '@checklists_datasource_item_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_1', language: '@language_en_us', value: Radio Option 1 } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_2', language: '@language_en_us', value: Radio Option 2 } }
    - { fields: { placeholder: '@checklists_datasource_item_radio_option_3', language: '@language_en_us', value: Radio Option 3 } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_1', language: '@language_en_us', value: Range Option 1 } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_2', language: '@language_en_us', value: Range Option 3 } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_3', language: '@language_en_us', value: Range Option 3 } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_4', language: '@language_en_us', value: Range Option 4 } }
    - { fields: { placeholder: '@checklists_datasource_item_range_option_5', language: '@language_en_us', value: Range Option 5 } }
    - { fields: { placeholder: '@checklists_datasource_item_uk', language: '@language_en_us', value: UK } }
    - { fields: { placeholder: '@checklists_datasource_item_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@checklists_datasource_checkbox_options', language: '@language_en_us', value: Checkbox Options } }
    - { fields: { placeholder: '@checklists_datasource_checklist_access_level', language: '@language_en_us', value: Survey Access Level } }
    - { fields: { placeholder: '@checklists_datasource_checklist_category', language: '@language_en_us', value: Survey Category } }
    - { fields: { placeholder: '@checklists_datasource_checklist_subcategory', language: '@language_en_us', value: Survey Subcategory } }
    - { fields: { placeholder: '@checklists_datasource_checklist_type', language: '@language_en_us', value: Survey Type } }
    - { fields: { placeholder: '@checklists_datasource_countries_of_residence', language: '@language_en_us', value: Countries of Residence } }
    - { fields: { placeholder: '@checklists_datasource_dropdown_options', language: '@language_en_us', value: Dropdown Options } }
    - { fields: { placeholder: '@checklists_datasource_multiselect_options', language: '@language_en_us', value: Multiselect Options } }
    - { fields: { placeholder: '@checklists_datasource_radio_options', language: '@language_en_us', value: Radio Options } }
    - { fields: { placeholder: '@checklists_datasource_range_options', language: '@language_en_us', value: Range Options } }
    - { fields: { placeholder: '@checklists_datasource_yes_no', language: '@language_en_us', value: Yes / No } }
    - { fields: { placeholder: '@checklists_datasource_yes_no_na', language: '@language_en_us', value: Yes / No / N/A } }
    - { fields: { placeholder: '@checklists_datasource_yes_no_options', language: '@language_en_us', value: Yes / No } }
    - { fields: { placeholder: '@checklists_distribute_checklist_questions', language: '@language_en_us', value: Survey Questions } }
    - { fields: { placeholder: '@checklists_distribute_checklist_summary', language: '@language_en_us', value: Survey Summary } }
    - { fields: { placeholder: '@checklists_distribute_recipients_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@checklists_distribute_recipients_add_responder', language: '@language_en_us', value: Add Responder } }
    - { fields: { placeholder: '@checklists_distribute_recipients_datix_users', language: '@language_en_us', value: Datix Users } }
    - { fields: { placeholder: '@checklists_distribute_recipients_distribute_survey', language: '@language_en_us', value: Distribute Survey } }
    - { fields: { placeholder: '@checklists_distribute_recipients_email', language: '@language_en_us', value: Email } }
    - { fields: { placeholder: '@checklists_distribute_recipients_existing_recipients', language: '@language_en_us', value: "Existing Recipients ({{numRecipients}})" } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users', language: '@language_en_us', value: External Users } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_no_external_users', language: '@language_en_us', value: This checklist has not been distributed to any external recipients } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_use_form', language: '@language_en_us', value: Use the form above to add new external recipients } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_add_user', language: '@language_en_us', value: "Would you like to add {{userName}} to the list of optional users?" } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_description', language: '@language_en_us', value: The entered email address matches an existing Datix User } }
    - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_title', language: '@language_en_us', value: Matching User Found } }
    - { fields: { placeholder: '@checklists_distribute_recipients_invited_on', language: '@language_en_us', value: Invited On } }
    - { fields: { placeholder: '@checklists_distribute_recipients_loading', language: '@language_en_us', value: Loading Survey Recipients } }
    - { fields: { placeholder: '@checklists_distribute_recipients_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@checklists_distribute_recipients_new_recipients', language: '@language_en_us', value: "New Recipients ({{numRecipients}})" } }
    - { fields: { placeholder: '@checklists_distribute_recipients_optional_response', language: '@language_en_us', value: Optional Response } }
    - { fields: { placeholder: '@checklists_distribute_recipients_required_response', language: '@language_en_us', value: Required Response } }
    - { fields: { placeholder: '@checklists_error_expired', language: '@language_en_us', value: "Survey has expired, and can no longer be responded to" } }
    - { fields: { placeholder: '@checklists_error_loading', language: '@language_en_us', value: An error occurred whilst retrieving the survey } }
    - { fields: { placeholder: '@checklists_error_not_found', language: '@language_en_us', value: Survey not found } }
    - { fields: { placeholder: '@checklists_error_questions_required', language: '@language_en_us', value: Survey cannot be released without questions } }
    - { fields: { placeholder: '@checklists_export_csv', language: '@language_en_us', value: Export to CSV } }
    - { fields: { placeholder: '@checklists_form_type_survey_response', language: '@language_en_us', value: Survey Response } }
    - { fields: { placeholder: '@checklists_form_field_access_level', language: '@language_en_us', value: Checklist Access Level } }
    - { fields: { placeholder: '@checklists_form_field_access_level_label', language: '@language_en_us', value: Checklist Access Level } }
    - { fields: { placeholder: '@checklists_form_field_category', language: '@language_en_us', value: Checklist Category } }
    - { fields: { placeholder: '@checklists_form_field_category_label', language: '@language_en_us', value: Checklist Category } }
    - { fields: { placeholder: '@checklists_form_field_description', language: '@language_en_us', value: Checklist Description } }
    - { fields: { placeholder: '@checklists_form_field_description_label', language: '@language_en_us', value: Checklist Description } }
    - { fields: { placeholder: '@checklists_form_field_due_date', language: '@language_en_us', value: Checklist SubCategory } }
    - { fields: { placeholder: '@checklists_form_field_due_date_label', language: '@language_en_us', value: Checklist Due Date } }
    - { fields: { placeholder: '@checklists_form_field_sub_category', language: '@language_en_us', value: Checklist SubCategory } }
    - { fields: { placeholder: '@checklists_form_field_sub_category_label', language: '@language_en_us', value: Checklist Due Date } }
    - { fields: { placeholder: '@checklists_form_field_title', language: '@language_en_us', value: Checklist Title } }
    - { fields: { placeholder: '@checklists_form_field_title_label', language: '@language_en_us', value: Checklist Title } }
    - { fields: { placeholder: '@checklists_form_field_type', language: '@language_en_us', value: Checklist Type } }
    - { fields: { placeholder: '@checklists_form_field_type_label', language: '@language_en_us', value: Checklist Description } }
    - { fields: { placeholder: '@checklists_form_fields_category_empty_value', language: '@language_en_us', value: Select a Category } }
    - { fields: { placeholder: '@checklists_form_fields_category_label', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_a', language: '@language_en_us', value: Category A } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_b', language: '@language_en_us', value: Category B } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_c', language: '@language_en_us', value: Category C } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_d', language: '@language_en_us', value: Category D } }
    - { fields: { placeholder: '@checklists_form_fields_category_options_e', language: '@language_en_us', value: Category E } }
    - { fields: { placeholder: '@checklists_form_fields_description_label', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@checklists_form_fields_due_date_label', language: '@language_en_us', value: Response Due Date } }
    - { fields: { placeholder: '@checklists_form_fields_title_label', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@checklists_form_fields_type_empty_value', language: '@language_en_us', value: Select a Type } }
    - { fields: { placeholder: '@checklists_form_fields_type_label', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_a', language: '@language_en_us', value: Type A } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_b', language: '@language_en_us', value: Type B } }
    - { fields: { placeholder: '@checklists_form_fields_type_options_c', language: '@language_en_us', value: Type C } }
    - { fields: { placeholder: '@checklists_form_name_checklist_1_form', language: '@language_en_us', value: Checklist 1 Form } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_all_datix_users_user_access', language: '@language_en_us', value: Checklist with All Datix Users (user access) } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_open_access_user_access', language: '@language_en_us', value: Checklist with Open access (user access) } }
    - { fields: { placeholder: '@checklists_form_name_checklist_with_selected_users_only_user_access', language: '@language_en_us', value: Checklist with Selected Users Only (user access) } }
    - { fields: { placeholder: '@checklists_form_name_example_checklist', language: '@language_en_us', value: Example Checklist } }
    - { fields: { placeholder: '@checklists_form_published_successfully', language: '@language_en_us', value: Survey published successfully } }
    - { fields: { placeholder: '@checklists_form_save', language: '@language_en_us', value: Save Survey } }
    - { fields: { placeholder: '@checklists_form_save_error', language: '@language_en_us', value: An error occurred whilst saving the Survey } }
    - { fields: { placeholder: '@checklists_form_saved_successfully', language: '@language_en_us', value: Survey saved successfully } }
    - { fields: { placeholder: '@checklists_form_user_access_all_datix_users', language: '@language_en_us', value: All Datix Users } }
    - { fields: { placeholder: '@checklists_form_user_access_collect_user_data', language: '@language_en_us', value: 'Collect User Data?' } }
    - { fields: { placeholder: '@checklists_form_user_access_open_access', language: '@language_en_us', value: Open Access } }
    - { fields: { placeholder: '@checklists_form_user_access_require_captcha', language: '@language_en_us', value: 'Require CAPTCHA?' } }
    - { fields: { placeholder: '@checklists_form_user_access_selected_users_only', language: '@language_en_us', value: Selected Users Only } }
    - { fields: { placeholder: '@checklists_form_user_access_title', language: '@language_en_us', value: User Access } }
    - { fields: { placeholder: '@checklists_forms_response_public_submit_success', language: '@language_en_us', value: Your response has been submitted successfully. You may now close this window. } }
    - { fields: { placeholder: '@checklists_forms_response_submit', language: '@language_en_us', value: Submit Response } }
    - { fields: { placeholder: '@checklists_forms_response_submit_success', language: '@language_en_us', value: Survey Response submitted successfully } }
    - { fields: { placeholder: '@checklists_new', language: '@language_en_us', value: New Survey } }
    - { fields: { placeholder: '@checklists_question_form_add', language: '@language_en_us', value: Add Question } }
    - { fields: { placeholder: '@checklists_question_form_cancel', language: '@language_en_us', value: Cancel } }
    - { fields: { placeholder: '@checklists_question_form_edit', language: '@language_en_us', value: Edit Question } }
    - { fields: { placeholder: '@checklists_question_form_fields_add_another', language: '@language_en_us', value: 'Add Another Question?' } }
    - { fields: { placeholder: '@checklists_question_form_fields_allow_attachments', language: '@language_en_us', value: 'Allow Attachments?' } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_label', language: '@language_en_us', value: Field Type } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_date', language: '@language_en_us', value: Date } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_long_text', language: '@language_en_us', value: Text (Long) } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_selection', language: '@language_en_us', value: Selection } }
    - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_short_text', language: '@language_en_us', value: Text (Short) } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_checkboxes', language: '@language_en_us', value: Checkboxes } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_dropdown', language: '@language_en_us', value: Dropdown } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_label', language: '@language_en_us', value: Input Type } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_radio_buttons', language: '@language_en_us', value: Radio Buttons } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_range', language: '@language_en_us', value: Range } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_smiley_scale', language: '@language_en_us', value: Smiley Scale } }
    - { fields: { placeholder: '@checklists_question_form_fields_input_type_yes_no', language: '@language_en_us', value: Yes / No } }
    - { fields: { placeholder: '@checklists_question_form_fields_introductory_text', language: '@language_en_us', value: Introductory Text } }
    - { fields: { placeholder: '@checklists_question_form_fields_label', language: '@language_en_us', value: Label } }
    - { fields: { placeholder: '@checklists_question_form_fields_mandatory', language: '@language_en_us', value: 'Mandatory?' } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_label', language: '@language_en_us', value: Selection Type } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_multiple', language: '@language_en_us', value: Multiple Responses } }
    - { fields: { placeholder: '@checklists_question_form_fields_selection_type_single', language: '@language_en_us', value: Single Response } }
    - { fields: { placeholder: '@checklists_question_form_fields_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@checklists_question_form_save', language: '@language_en_us', value: Save Question } }
    - { fields: { placeholder: '@checklists_recipient', language: '@language_en_us', value: Recipient } }
    - { fields: { placeholder: '@checklists_recipients', language: '@language_en_us', value: Recipients } }
    - { fields: { placeholder: '@checklist_response_filter_submission_date', language: '@language_en_us', value: Submission Date } }
    - { fields: { placeholder: '@checklist_response_filter_submission_date_label', language: '@language_en_us', value: Submission Date } }
    - { fields: { placeholder: '@checklists_responses', language: '@language_en_us', value: Responses } }
    - { fields: { placeholder: '@checklists_responses_nav_checklist_summary', language: '@language_en_us', value: Survey Summary } }
    - { fields: { placeholder: '@checklists_responses_nav_recipients', language: '@language_en_us', value: Recipients } }
    - { fields: { placeholder: '@checklists_responses_nav_responses', language: '@language_en_us', value: Responses } }
    - { fields: { placeholder: '@checklists_tables_columns_due', language: '@language_en_us', value: Response Due Date } }
    - { fields: { placeholder: '@checklists_tables_columns_expired', language: '@language_en_us', value: Expired } }
    - { fields: { placeholder: '@checklists_tables_columns_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@checklists_tables_columns_published', language: '@language_en_us', value: Published } }
    - { fields: { placeholder: '@checklists_tables_columns_responder', language: '@language_en_us', value: Responder } }
    - { fields: { placeholder: '@checklists_tables_columns_submission_date', language: '@language_en_us', value: Submission Date } }
    - { fields: { placeholder: '@checklists_tables_columns_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@checklists_untitled_group', language: '@language_en_us', value: "Untitled Group {{index}}" } }
    - { fields: { placeholder: '@checklists_yes_no_na', language: '@language_en_us', value: Not Applicable } }
    - { fields: { placeholder: '@checklists_yes_no_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@checklists_yes_no_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@checklists_dashboard_nav_my_responses_responded_to', language: '@language_en_us', value: 'Responded to' } }
    - { fields: { placeholder: '@checklists_dashboard_nav_my_responses_ready_for_response', language: '@language_en_us', value: 'Ready for Response' } }
    - { fields: { placeholder: '@checklists_error_unable_to_create_recipient', language: '@language_en_us', value: 'Unable to create Survey Recipient' } }
    - { fields: { placeholder: '@checklists_error_recipient_already_assigned', language: '@language_en_us', value: 'The specified User has already been assigned as a recipient to this Survey' } }
    - { fields: { placeholder: '@checklists_events_distributed_successfully', language: '@language_en_us', value: 'Survey distributed successfully' } }
    - { fields: { placeholder: '@checklists_form_field_category_title', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@checklists_form_field_type_title', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@checklists_events_checklist_group_saved_successfully', language: '@language_en_us', value: 'Survey Group saved successfully' } }
    - { fields: { placeholder: '@checklists_events_checklist_group_deleted_successfully', language: '@language_en_us', value: 'Survey Group deleted successfully' } }
    - { fields: { placeholder: '@checklists_events_error_deleting_checklist_group', language: '@language_en_us', value: 'An error occurred while deleting the Survey Group' } }
