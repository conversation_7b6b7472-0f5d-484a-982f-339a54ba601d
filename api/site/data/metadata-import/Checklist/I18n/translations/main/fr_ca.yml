entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@checklists_dashboard_nav_all_templates', language: '@language_fr_ca', value: 'Tableau de bord du sondage' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_distribute', language: '@language_fr_ca', value: 'Distribuer un sondage' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_my_distributions', language: '@language_fr_ca', value: 'Afficher les réponses du sondage' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_my_responses', language: '@language_fr_ca', value: 'Mes réponses' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_my_responses_responded_to', language: '@language_fr_ca', value: 'Répondu à' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_my_responses_ready_for_response', language: '@language_fr_ca', value: 'Prêt pour la réponse' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_browse_checklists', language: '@language_fr_ca', value: 'Parcourir les sondages' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_new_template', language: '@language_fr_ca', value: 'Créer un nouveau sondage' } }
  - { fields: { placeholder: '@checklists_dashboard_nav_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@checklists_responses_nav_checklist_summary', language: '@language_fr_ca', value: 'Sommaire du sondage' } }
  - { fields: { placeholder: '@checklists_responses_nav_recipients', language: '@language_fr_ca', value: Destinataires } }
  - { fields: { placeholder: '@checklists_responses_nav_responses', language: '@language_fr_ca', value: Réponses } }
  - { fields: { placeholder: '@checklists_checklists', language: '@language_fr_ca', value: Sondages } }
  - { fields: { placeholder: '@checklists_checklist', language: '@language_fr_ca', value: Sondage } }
  - { fields: { placeholder: '@checklists_recipients', language: '@language_fr_ca', value: Destinataires } }
  - { fields: { placeholder: '@checklists_recipient', language: '@language_fr_ca', value: Destinataire } }
  - { fields: { placeholder: '@checklists_responses', language: '@language_fr_ca', value: Réponses } }
  - { fields: { placeholder: '@checklists_export_csv', language: '@language_fr_ca', value: 'Exporter en CSV' } }
  - { fields: { placeholder: '@checklists_distribute_checklist_summary', language: '@language_fr_ca', value: 'Sommaire du sondage' } }
  - { fields: { placeholder: '@checklists_distribute_checklist_questions', language: '@language_fr_ca', value: 'Questions du sondage' } }
  - { fields: { placeholder: '@checklists_common_collect_user_data', language: '@language_fr_ca', value: 'Collecter les données d''utilisateur?' } }
  - { fields: { placeholder: '@checklists_common_back_dashboard', language: '@language_fr_ca', value: 'Retour au tableau de bord' } }
  - { fields: { placeholder: '@checklists_yes_no_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@checklists_yes_no_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@checklists_yes_no_na', language: '@language_fr_ca', value: 'Sans objet' } }
  - { fields: { placeholder: '@checklists_tables_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@checklists_tables_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@checklists_tables_columns_expired', language: '@language_fr_ca', value: Expiré } }
  - { fields: { placeholder: '@checklists_tables_columns_published', language: '@language_fr_ca', value: Publié } }
  - { fields: { placeholder: '@checklists_tables_columns_due', language: '@language_fr_ca', value: 'Date d''échéance de réponse' } }
  - { fields: { placeholder: '@checklists_tables_columns_submission_date', language: '@language_fr_ca', value: 'Date de soumission' } }
  - { fields: { placeholder: '@checklists_tables_columns_responder', language: '@language_fr_ca', value: Répondant } }
  - { fields: { placeholder: '@checklists_forms_response_submit', language: '@language_fr_ca', value: 'Soumettre la réponse' } }
  - { fields: { placeholder: '@checklists_forms_response_submit_success', language: '@language_fr_ca', value: 'Réponse au sondage soumise avec succès' } }
  - { fields: { placeholder: '@checklists_forms_response_public_submit_success', language: '@language_fr_ca', value: 'Votre réponse a été envoyée avec succès. Vous pouvez maintenant fermer cette fenêtre.' } }
  - { fields: { placeholder: '@checklists_new', language: '@language_fr_ca', value: 'Nouveau sondage' } }
  - { fields: { placeholder: '@checklists_form_fields_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@checklists_form_fields_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@checklists_form_fields_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@checklists_form_fields_type_empty_value', language: '@language_fr_ca', value: 'Veuillez sélectionner...' } }
  - { fields: { placeholder: '@checklists_form_fields_type_options_a', language: '@language_fr_ca', value: 'Type A' } }
  - { fields: { placeholder: '@checklists_form_fields_type_options_b', language: '@language_fr_ca', value: 'Type B' } }
  - { fields: { placeholder: '@checklists_form_fields_type_options_c', language: '@language_fr_ca', value: 'Type C' } }
  - { fields: { placeholder: '@checklists_form_fields_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@checklists_form_fields_category_empty_value', language: '@language_fr_ca', value: 'Sélectionnez une catégorie' } }
  - { fields: { placeholder: '@checklists_form_fields_category_options_a', language: '@language_fr_ca', value: 'Catégorie A' } }
  - { fields: { placeholder: '@checklists_form_fields_category_options_b', language: '@language_fr_ca', value: 'Catégorie B' } }
  - { fields: { placeholder: '@checklists_form_fields_category_options_c', language: '@language_fr_ca', value: 'Catégorie C' } }
  - { fields: { placeholder: '@checklists_form_fields_category_options_d', language: '@language_fr_ca', value: 'Catégorie D' } }
  - { fields: { placeholder: '@checklists_form_fields_category_options_e', language: '@language_fr_ca', value: 'Catégorie E' } }
  - { fields: { placeholder: '@checklists_form_fields_due_date_label', language: '@language_fr_ca', value: 'Date d''échéance de réponse' } }
  - { fields: { placeholder: '@checklists_form_user_access_title', language: '@language_fr_ca', value: 'Accès utilisateur' } }
  - { fields: { placeholder: '@checklists_form_user_access_open_access', language: '@language_fr_ca', value: 'Accès ouvert' } }
  - { fields: { placeholder: '@checklists_form_user_access_all_datix_users', language: '@language_fr_ca', value: 'Tous utilisateurs Datix' } }
  - { fields: { placeholder: '@checklists_form_user_access_selected_users_only', language: '@language_fr_ca', value: 'Utilisateurs sélectionnés seuls' } }
  - { fields: { placeholder: '@checklists_form_user_access_collect_user_data', language: '@language_fr_ca', value: 'Collecter les données d''utilisateur?' } }
  - { fields: { placeholder: '@checklists_form_user_access_require_captcha', language: '@language_fr_ca', value: 'Nécessite un CAPTCHA?' } }
  - { fields: { placeholder: '@checklists_form_save', language: '@language_fr_ca', value: 'Enregistrer le sondage' } }
  - { fields: { placeholder: '@checklists_form_saved_successfully', language: '@language_fr_ca', value: 'Sondage enregistré avec succès' } }
  - { fields: { placeholder: '@checklists_form_save_error', language: '@language_fr_ca', value: 'Erreur pendant l''enregistrement du sondage' } }
  - { fields: { placeholder: '@checklists_form_published_successfully', language: '@language_fr_ca', value: 'Sondage publié avec succès' } }
  - { fields: { placeholder: '@checklists_checklist_nav_checklist_number', language: '@language_fr_ca', value: 'Sondage nº{{id}}' } }
  - { fields: { placeholder: '@checklists_checklist_nav_details', language: '@language_fr_ca', value: 'Détails du sondage' } }
  - { fields: { placeholder: '@checklists_checklist_nav_questions', language: '@language_fr_ca', value: Questions } }
  - { fields: { placeholder: '@checklists_checklist_nav_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@checklists_checklist_details_edit', language: '@language_fr_ca', value: 'Modifier le sondage' } }
  - { fields: { placeholder: '@checklists_checklist_questions_groups', language: '@language_fr_ca', value: 'Groupes de sondage' } }
  - { fields: { placeholder: '@checklists_checklist_questions_title', language: '@language_fr_ca', value: Questions } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details', language: '@language_fr_ca', value: 'Détails du groupe' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions', language: '@language_fr_ca', value: Questions } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_save', language: '@language_fr_ca', value: 'Enregistrer le groupe' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_group_details_form_delete', language: '@language_fr_ca', value: 'Supprimer le groupe' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_add_new', language: '@language_fr_ca', value: 'Ajouter une question' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_question', language: '@language_fr_ca', value: Questions } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_mandatory', language: '@language_fr_ca', value: 'Obligatoire?' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_allow_attachments', language: '@language_fr_ca', value: 'Autoriser les pièces jointes?' } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@checklists_checklist_questions_tabs_questions_list_no_records', language: '@language_fr_ca', value: 'Ce groupe ne contient aucune question' } }
  - { fields: { placeholder: '@checklists_question_form_add', language: '@language_fr_ca', value: 'Ajouter une question' } }
  - { fields: { placeholder: '@checklists_question_form_edit', language: '@language_fr_ca', value: 'Modifier la question' } }
  - { fields: { placeholder: '@checklists_question_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@checklists_question_form_fields_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@checklists_question_form_fields_introductory_text', language: '@language_fr_ca', value: 'Texte d''introduction' } }
  - { fields: { placeholder: '@checklists_question_form_fields_mandatory', language: '@language_fr_ca', value: 'Obligatoire?' } }
  - { fields: { placeholder: '@checklists_question_form_fields_allow_attachments', language: '@language_fr_ca', value: 'Autoriser les pièces jointes?' } }
  - { fields: { placeholder: '@checklists_question_form_fields_add_another', language: '@language_fr_ca', value: 'Ajouter une autre question?' } }
  - { fields: { placeholder: '@checklists_question_form_fields_field_type_label', language: '@language_fr_ca', value: 'Type de champ' } }
  - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_short_text', language: '@language_fr_ca', value: 'Texte (court)' } }
  - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_long_text', language: '@language_fr_ca', value: 'Texte (long)' } }
  - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@checklists_question_form_fields_field_type_options_selection', language: '@language_fr_ca', value: Sélection } }
  - { fields: { placeholder: '@checklists_question_form_fields_selection_type_label', language: '@language_fr_ca', value: 'Type de sélection' } }
  - { fields: { placeholder: '@checklists_question_form_fields_selection_type_single', language: '@language_fr_ca', value: 'Réponse unique' } }
  - { fields: { placeholder: '@checklists_question_form_fields_selection_type_multiple', language: '@language_fr_ca', value: 'Réponses multiples' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_label', language: '@language_fr_ca', value: 'Type d''entrée' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_radio_buttons', language: '@language_fr_ca', value: 'Cases d''option' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_range', language: '@language_fr_ca', value: Plage } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_dropdown', language: '@language_fr_ca', value: 'Liste déroulante' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_yes_no', language: '@language_fr_ca', value: 'Oui / Non' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_smiley_scale', language: '@language_fr_ca', value: 'Échelle de binette' } }
  - { fields: { placeholder: '@checklists_question_form_fields_input_type_checkboxes', language: '@language_fr_ca', value: 'Cases à cocher' } }
  - { fields: { placeholder: '@checklists_question_form_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@checklists_question_form_save', language: '@language_fr_ca', value: 'Enregistrer la question' } }
  - { fields: { placeholder: '@checklists_error_not_found', language: '@language_fr_ca', value: 'Sondage non trouvé' } }
  - { fields: { placeholder: '@checklists_error_expired', language: '@language_fr_ca', value: 'Le sondage est périmé et il n''est plus possible d''y répondre' } }
  - { fields: { placeholder: '@checklists_error_loading', language: '@language_fr_ca', value: 'Erreur pendant la récupération du sondage' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_loading', language: '@language_fr_ca', value: 'Chargement des destinataires du sondage' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_optional_response', language: '@language_fr_ca', value: 'Réponse facultative' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_required_response', language: '@language_fr_ca', value: 'Réponse obligatoire' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_datix_users', language: '@language_fr_ca', value: 'Utilisateurs de Datix' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users', language: '@language_fr_ca', value: 'Utilisateurs externes' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_new_recipients', language: '@language_fr_ca', value: 'Nouveaux destinataires ({{numRecipients}})' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_existing_recipients', language: '@language_fr_ca', value: 'Destinataires existants ({{numRecipients}})' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_distribute_survey', language: '@language_fr_ca', value: 'Distribuer le sondage' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_add_responder', language: '@language_fr_ca', value: 'Ajouter un répondant' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@checklists_distribute_recipients_email', language: '@language_fr_ca', value: 'Adresse courriel' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_invited_on', language: '@language_fr_ca', value: Invité } }
  - { fields: { placeholder: '@checklists_distribute_recipients_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users_no_external_users', language: '@language_fr_ca', value: 'La liste de vérification n''a été distribuée à aucun destinataire externe' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users_use_form', language: '@language_fr_ca', value: 'Utilisez le formulaire ci-dessus pour ajouter des destinataires externes' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_title', language: '@language_fr_ca', value: 'Utilisateur correspondant trouvé' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_description', language: '@language_fr_ca', value: 'L''adresse de courriel entrée correspond à un utilisateur de Datrix' } }
  - { fields: { placeholder: '@checklists_distribute_recipients_external_users_user_found_add_user', language: '@language_fr_ca', value: 'Voulez-vous ajouter {{userName}} à la liste des utilisateurs facultatifs?' } }
  - { fields: { placeholder: '@checklists_checklist_1_fields_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@checklists_checklist_1_fields_name_label', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence', language: '@language_fr_ca', value: 'Pays de résidence' } }
  - { fields: { placeholder: '@checklists_checklist_1_fields_country_of_residence_label', language: '@language_fr_ca', value: 'Pays de résidence' } }
  - { fields: { placeholder: '@checklists_checklist_1_country_of_residence', language: '@language_fr_ca', value: 'Pays de résidence' } }
  - { fields: { placeholder: '@checklists_checklist_1_country_of_residence_label', language: '@language_fr_ca', value: 'Pays de résidence' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_text', language: '@language_fr_ca', value: 'Champ texte' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_text_label', language: '@language_fr_ca', value: 'Champ texte' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_textarea', language: '@language_fr_ca', value: 'Champ texte multiligne' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_textarea_label', language: '@language_fr_ca', value: 'Champ texte multiligne' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_date', language: '@language_fr_ca', value: 'Champ date' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_date_label', language: '@language_fr_ca', value: 'Champ date' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_radio', language: '@language_fr_ca', value: 'Champ radio' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_radio_label', language: '@language_fr_ca', value: 'Champ radio' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_range', language: '@language_fr_ca', value: 'Champ plage de valeurs' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_range_label', language: '@language_fr_ca', value: 'Champ plage de valeurs' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown', language: '@language_fr_ca', value: 'Champ liste déroulante' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_dropdown_label', language: '@language_fr_ca', value: 'Champ liste déroulante' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no', language: '@language_fr_ca', value: 'Champ Oui / Non' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_yes_no_label', language: '@language_fr_ca', value: 'Champ Oui / Non' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale', language: '@language_fr_ca', value: 'Échelle de binette' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_smiley_scale_label', language: '@language_fr_ca', value: 'Échelle de binette' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox', language: '@language_fr_ca', value: 'Champ case à cocher' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_checkbox_label', language: '@language_fr_ca', value: 'Champ case à cocher' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect', language: '@language_fr_ca', value: 'Champ de sélections multiples' } }
  - { fields: { placeholder: '@checklists_checklist_3_fields_multiselect_label', language: '@language_fr_ca', value: 'Champ de sélections multiples' } }
  - { fields: { placeholder: '@checklist_response_filter_submission_date', language: '@language_fr_ca', value: 'Date de soumission' } }
  - { fields: { placeholder: '@checklist_response_filter_submission_date_label', language: '@language_fr_ca', value: 'Date de soumission' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_1', language: '@language_fr_ca', value: 'Confirmation d''identité' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_1_label', language: '@language_fr_ca', value: 'Confirmation d''identité' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_2', language: '@language_fr_ca', value: 'Confirmation de consentement' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_2_label', language: '@language_fr_ca', value: 'Confirmation de consentement' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_3', language: '@language_fr_ca', value: 'Site chirurgical marqué' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_3_label', language: '@language_fr_ca', value: 'Site chirurgical marqué' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_4', language: '@language_fr_ca', value: 'Vérification d''anesthésie et de médicaments' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_4_label', language: '@language_fr_ca', value: 'Vérification d''anesthésie et de médicaments' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_5', language: '@language_fr_ca', value: 'Vérification d''anesthésie et de médicaments' } }
  - { fields: { placeholder: '@checklists_checklist_4_field_5_label', language: '@language_fr_ca', value: 'Vérification d''anesthésie et de médicaments' } }
  - { fields: { placeholder: '@checklists_form_field_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@checklists_form_field_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@checklists_form_field_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@checklists_form_field_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@checklists_form_field_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@checklists_form_field_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@checklists_form_field_sub_category', language: '@language_fr_ca', value: 'Sous-catégorie de liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_field_sub_category_label', language: '@language_fr_ca', value: 'Date d''échéance de liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_field_due_date', language: '@language_fr_ca', value: 'Sous-catégorie de liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_field_due_date_label', language: '@language_fr_ca', value: 'Date d''échéance de liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_field_access_level', language: '@language_fr_ca', value: 'Niveau d''accès de la liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_field_access_level_label', language: '@language_fr_ca', value: 'Niveau d''accès de la liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_type_survey_response', language: '@language_fr_ca', value: 'Réponse au sondage' } }
  - { fields: { placeholder: '@checklists_datasource_yes_no', language: '@language_fr_ca', value: 'Oui / Non' } }
  - { fields: { placeholder: '@checklists_datasource_yes_no_na', language: '@language_fr_ca', value: 'Oui / Non / S.O.' } }
  - { fields: { placeholder: '@checklists_datasource_countries_of_residence', language: '@language_fr_ca', value: 'Pays de résidence' } }
  - { fields: { placeholder: '@checklists_datasource_radio_options', language: '@language_fr_ca', value: 'Options de cases d''option' } }
  - { fields: { placeholder: '@checklists_datasource_range_options', language: '@language_fr_ca', value: 'Options de plage' } }
  - { fields: { placeholder: '@checklists_datasource_dropdown_options', language: '@language_fr_ca', value: 'Options de liste déroulante' } }
  - { fields: { placeholder: '@checklists_datasource_yes_no_options', language: '@language_fr_ca', value: 'Oui / Non' } }
  - { fields: { placeholder: '@checklists_datasource_checkbox_options', language: '@language_fr_ca', value: 'Options de case à cocher' } }
  - { fields: { placeholder: '@checklists_datasource_multiselect_options', language: '@language_fr_ca', value: 'Options de choix multiple' } }
  - { fields: { placeholder: '@checklists_datasource_checklist_type', language: '@language_fr_ca', value: 'Type de sondage' } }
  - { fields: { placeholder: '@checklists_datasource_checklist_category', language: '@language_fr_ca', value: 'Catégorie de sondage' } }
  - { fields: { placeholder: '@checklists_datasource_checklist_subcategory', language: '@language_fr_ca', value: 'Sous-catégorie de sondage' } }
  - { fields: { placeholder: '@checklists_datasource_checklist_access_level', language: '@language_fr_ca', value: 'Niveau d''accès au sondage' } }
  - { fields: { placeholder: '@checklists_datasource_item_uk', language: '@language_fr_ca', value: R.-U. } }
  - { fields: { placeholder: '@checklists_datasource_item_france', language: '@language_fr_ca', value: France } }
  - { fields: { placeholder: '@checklists_datasource_item_italy', language: '@language_fr_ca', value: Italie } }
  - { fields: { placeholder: '@checklists_datasource_item_radio_option_1', language: '@language_fr_ca', value: "Case d'option\_1" } }
  - { fields: { placeholder: '@checklists_datasource_item_radio_option_2', language: '@language_fr_ca', value: "Case d'option\_2" } }
  - { fields: { placeholder: '@checklists_datasource_item_radio_option_3', language: '@language_fr_ca', value: "Case d'option\_3" } }
  - { fields: { placeholder: '@checklists_datasource_item_range_option_1', language: '@language_fr_ca', value: "Option de plage\_1" } }
  - { fields: { placeholder: '@checklists_datasource_item_range_option_2', language: '@language_fr_ca', value: "Option de plage\_3" } }
  - { fields: { placeholder: '@checklists_datasource_item_range_option_3', language: '@language_fr_ca', value: "Option de plage\_3" } }
  - { fields: { placeholder: '@checklists_datasource_item_range_option_4', language: '@language_fr_ca', value: "Option de plage\_4" } }
  - { fields: { placeholder: '@checklists_datasource_item_range_option_5', language: '@language_fr_ca', value: "Option de plage\_5" } }
  - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_1', language: '@language_fr_ca', value: "Option de liste déroulante\_1" } }
  - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_2', language: '@language_fr_ca', value: "Option de liste déroulante\_2" } }
  - { fields: { placeholder: '@checklists_datasource_item_dropdown_option_3', language: '@language_fr_ca', value: "Option de liste déroulante\_4" } }
  - { fields: { placeholder: '@checklists_datasource_item_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@checklists_datasource_item_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_1', language: '@language_fr_ca', value: "Option de case à cocher\_1" } }
  - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_2', language: '@language_fr_ca', value: "Option de case à cocher\_2" } }
  - { fields: { placeholder: '@checklists_datasource_item_checkbox_option_3', language: '@language_fr_ca', value: "Option de case à cocher\_3" } }
  - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_1', language: '@language_fr_ca', value: "Option de choix multiple\_1" } }
  - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_2', language: '@language_fr_ca', value: "Option de choix multiple\_2" } }
  - { fields: { placeholder: '@checklists_datasource_item_multiselect_option_3', language: '@language_fr_ca', value: "Option de choix multiple\_3" } }
  - { fields: { placeholder: '@checklists_form_name_checklist_1_form', language: '@language_fr_ca', value: "Formulaire de liste de vérification\_1" } }
  - { fields: { placeholder: '@checklists_form_name_example_checklist', language: '@language_fr_ca', value: 'Exemple de liste de vérification' } }
  - { fields: { placeholder: '@checklists_form_name_checklist_with_open_access_user_access', language: '@language_fr_ca', value: 'Liste de vérification avec accès ouvert (accès utilisateur)' } }
  - { fields: { placeholder: '@checklists_form_name_checklist_with_all_datix_users_user_access', language: '@language_fr_ca', value: 'Liste de vérification avec Tous les utilisateurs Datix (accès utilisateur)' } }
  - { fields: { placeholder: '@checklists_form_name_checklist_with_selected_users_only_user_access', language: '@language_fr_ca', value: 'Liste de vérification avec les utilisateurs sélectionnés seuls (accès utilisateur)' } }
  - { fields: { placeholder: '@checklists_untitled_group', language: '@language_fr_ca', value: 'Groupe sans titre {{index}}' } }
  - { fields: { placeholder: '@checklists_add_the_first_survey_group', language: '@language_fr_ca', value: 'Ajouter le premier groupe de sondage' } }
  - { fields: { placeholder: '@checklists_checklist_has_expired_notice', language: '@language_fr_ca', value: 'Ce sondage est périmé depuis le {{expiryDate}}' } }
  - { fields: { placeholder: '@checklists_error_questions_required', language: '@language_fr_ca', value: 'Le sondage ne peut être publié sans contenir de questions' } }
  - { fields: { placeholder: '@checklists_error_unable_to_create_recipient', language: '@language_fr_ca', value: 'Impossible de créer le destinataire du sondage' } }
  - { fields: { placeholder: '@checklists_error_recipient_already_assigned', language: '@language_fr_ca', value: 'L''utilisateur spécifié a déjà été affecté en tant que destinataire de ce sondage' } }
  - { fields: { placeholder: '@checklists_events_distributed_successfully', language: '@language_fr_ca', value: 'Sondage distribué avec succès' } }
  - { fields: { placeholder: '@checklists_form_field_category_title', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@checklists_form_field_type_title', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@checklists_events_checklist_group_saved_successfully', language: '@language_fr_ca', value: 'Groupe de sondage enregistré avec succès' } }
  - { fields: { placeholder: '@checklists_events_checklist_group_deleted_successfully', language: '@language_fr_ca', value: 'Groupe de sondage supprimé avec succès' } }
  - { fields: { placeholder: '@checklists_events_error_deleting_checklist_group', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression du groupe d''enquête' } }
