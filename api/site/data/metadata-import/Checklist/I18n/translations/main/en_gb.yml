entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@checklists_dashboard_nav_all_templates'
      language: '@language_en_gb'
      value: 'Survey Dashboard'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_distribute'
      language: '@language_en_gb'
      value: Distribute a Survey
  -
    fields:
      placeholder: '@checklists_dashboard_nav_my_distributions'
      language: '@language_en_gb'
      value: 'View Survey Responses'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_my_responses'
      language: '@language_en_gb'
      value: 'My Responses'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_my_responses_responded_to'
      language: '@language_en_gb'
      value: 'Responded to'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_my_responses_ready_for_response'
      language: '@language_en_gb'
      value: 'Ready for Response'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_browse_checklists'
      language: '@language_en_gb'
      value: 'Browse Surveys'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_new_template'
      language: '@language_en_gb'
      value: 'Create a new Survey'
  -
    fields:
      placeholder: '@checklists_dashboard_nav_permissions'
      language: '@language_en_gb'
      value: 'Permissions'
  -
    fields:
      placeholder: '@checklists_responses_nav_checklist_summary'
      language: '@language_en_gb'
      value: 'Survey Summary'
  -
    fields:
      placeholder: '@checklists_responses_nav_recipients'
      language: '@language_en_gb'
      value: Recipients
  -
    fields:
      placeholder: '@checklists_responses_nav_responses'
      language: '@language_en_gb'
      value: Responses
  -
    fields:
      placeholder: '@checklists_checklists'
      language: '@language_en_gb'
      value: Surveys
  -
    fields:
      placeholder: '@checklists_checklist'
      language: '@language_en_gb'
      value: Survey
  -
    fields:
      placeholder: '@checklists_recipients'
      language: '@language_en_gb'
      value: Recipients
  -
    fields:
      placeholder: '@checklists_recipient'
      language: '@language_en_gb'
      value: Recipient
  -
    fields:
      placeholder: '@checklists_responses'
      language: '@language_en_gb'
      value: Responses
  -
    fields:
      placeholder: '@checklists_export_csv'
      language: '@language_en_gb'
      value: 'Export to CSV'
  -
    fields:
      placeholder: '@checklists_distribute_checklist_summary'
      language: '@language_en_gb'
      value: 'Survey Summary'
  -
    fields:
      placeholder: '@checklists_distribute_checklist_questions'
      language: '@language_en_gb'
      value: 'Survey Questions'
  -
    fields:
      placeholder: '@checklists_common_collect_user_data'
      language: '@language_en_gb'
      value: 'Collect User Data?'
  -
    fields:
      placeholder: '@checklists_common_back_dashboard'
      language: '@language_en_gb'
      value: 'Back to Dashboard'
  -
    fields:
      placeholder: '@checklists_yes_no_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@checklists_yes_no_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@checklists_yes_no_na'
      language: '@language_en_gb'
      value: 'Not Applicable'
  -
    fields:
      placeholder: '@checklists_tables_columns_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@checklists_tables_columns_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@checklists_tables_columns_expired'
      language: '@language_en_gb'
      value: Expired
  -
    fields:
      placeholder: '@checklists_tables_columns_published'
      language: '@language_en_gb'
      value: Published
  -
    fields:
      placeholder: '@checklists_tables_columns_due'
      language: '@language_en_gb'
      value: 'Response Due Date'
  -
    fields:
      placeholder: '@checklists_tables_columns_submission_date'
      language: '@language_en_gb'
      value: 'Submission Date'
  -
    fields:
      placeholder: '@checklists_tables_columns_responder'
      language: '@language_en_gb'
      value: Responder
  -
    fields:
      placeholder: '@checklists_forms_response_submit'
      language: '@language_en_gb'
      value: 'Submit Response'
  -
    fields:
      placeholder: '@checklists_forms_response_submit_success'
      language: '@language_en_gb'
      value: 'Survey Response submitted successfully'
  -
    fields:
      placeholder: '@checklists_forms_response_public_submit_success'
      language: '@language_en_gb'
      value: 'Your response has been submitted successfully. You may now close this window.'
  -
    fields:
      placeholder: '@checklists_new'
      language: '@language_en_gb'
      value: 'New Survey'
  -
    fields:
      placeholder: '@checklists_form_fields_title_label'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@checklists_form_fields_description_label'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@checklists_form_fields_type_label'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@checklists_form_fields_type_empty_value'
      language: '@language_en_gb'
      value: 'Please select...'
  -
    fields:
      placeholder: '@checklists_form_fields_type_options_a'
      language: '@language_en_gb'
      value: 'Type A'
  -
    fields:
      placeholder: '@checklists_form_fields_type_options_b'
      language: '@language_en_gb'
      value: 'Type B'
  -
    fields:
      placeholder: '@checklists_form_fields_type_options_c'
      language: '@language_en_gb'
      value: 'Type C'
  -
    fields:
      placeholder: '@checklists_form_fields_category_label'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@checklists_form_fields_category_empty_value'
      language: '@language_en_gb'
      value: 'Select a Category'
  -
    fields:
      placeholder: '@checklists_form_fields_category_options_a'
      language: '@language_en_gb'
      value: 'Category A'
  -
    fields:
      placeholder: '@checklists_form_fields_category_options_b'
      language: '@language_en_gb'
      value: 'Category B'
  -
    fields:
      placeholder: '@checklists_form_fields_category_options_c'
      language: '@language_en_gb'
      value: 'Category C'
  -
    fields:
      placeholder: '@checklists_form_fields_category_options_d'
      language: '@language_en_gb'
      value: 'Category D'
  -
    fields:
      placeholder: '@checklists_form_fields_category_options_e'
      language: '@language_en_gb'
      value: 'Category E'
  -
    fields:
      placeholder: '@checklists_form_fields_due_date_label'
      language: '@language_en_gb'
      value: 'Response Due Date'
  -
    fields:
      placeholder: '@checklists_form_user_access_title'
      language: '@language_en_gb'
      value: 'User Access'
  -
    fields:
      placeholder: '@checklists_form_user_access_open_access'
      language: '@language_en_gb'
      value: 'Open Access'
  -
    fields:
      placeholder: '@checklists_form_user_access_all_datix_users'
      language: '@language_en_gb'
      value: 'All Datix Users'
  -
    fields:
      placeholder: '@checklists_form_user_access_selected_users_only'
      language: '@language_en_gb'
      value: 'Selected Users Only'
  -
    fields:
      placeholder: '@checklists_form_user_access_collect_user_data'
      language: '@language_en_gb'
      value: 'Collect User Data?'
  -
    fields:
      placeholder: '@checklists_form_user_access_require_captcha'
      language: '@language_en_gb'
      value: 'Require CAPTCHA?'
  -
    fields:
      placeholder: '@checklists_form_save'
      language: '@language_en_gb'
      value: 'Save Survey'
  -
    fields:
      placeholder: '@checklists_form_saved_successfully'
      language: '@language_en_gb'
      value: 'Survey saved successfully'
  -
    fields:
      placeholder: '@checklists_form_save_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Survey'
  -
    fields:
      placeholder: '@checklists_form_published_successfully'
      language: '@language_en_gb'
      value: 'Survey published successfully'
  -
    fields:
      placeholder: '@checklists_checklist_nav_checklist_number'
      language: '@language_en_gb'
      value: 'Survey #{{id}}'
  -
    fields:
      placeholder: '@checklists_checklist_nav_details'
      language: '@language_en_gb'
      value: 'Survey Details'
  -
    fields:
      placeholder: '@checklists_checklist_nav_questions'
      language: '@language_en_gb'
      value: Questions
  -
    fields:
      placeholder: '@checklists_checklist_nav_access_control'
      language: '@language_en_gb'
      value: 'Access Control'
  -
    fields:
      placeholder: '@checklists_checklist_details_edit'
      language: '@language_en_gb'
      value: 'Edit Survey'
  -
    fields:
      placeholder: '@checklists_checklist_questions_groups'
      language: '@language_en_gb'
      value: 'Survey Groups'
  -
    fields:
      placeholder: '@checklists_checklist_questions_title'
      language: '@language_en_gb'
      value: Questions
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_group_details'
      language: '@language_en_gb'
      value: 'Group Details'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions'
      language: '@language_en_gb'
      value: Questions
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_name'
      language: '@language_en_gb'
      value: Name
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_group_details_form_fields_summary'
      language: '@language_en_gb'
      value: Summary
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_group_details_form_save'
      language: '@language_en_gb'
      value: 'Save Group'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_group_details_form_delete'
      language: '@language_en_gb'
      value: 'Delete Group'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_add_new'
      language: '@language_en_gb'
      value: 'Add New Question'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_question'
      language: '@language_en_gb'
      value: Questions
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_mandatory'
      language: '@language_en_gb'
      value: 'Mandatory?'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_allow_attachments'
      language: '@language_en_gb'
      value: 'Allow Attachments?'
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_list_columns_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@checklists_checklist_questions_tabs_questions_list_no_records'
      language: '@language_en_gb'
      value: 'This group contains no questions'
  -
    fields:
      placeholder: '@checklists_question_form_add'
      language: '@language_en_gb'
      value: 'Add Question'
  -
    fields:
      placeholder: '@checklists_question_form_edit'
      language: '@language_en_gb'
      value: 'Edit Question'
  -
    fields:
      placeholder: '@checklists_question_form_fields_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@checklists_question_form_fields_label'
      language: '@language_en_gb'
      value: Label
  -
    fields:
      placeholder: '@checklists_question_form_fields_introductory_text'
      language: '@language_en_gb'
      value: 'Introductory Text'
  -
    fields:
      placeholder: '@checklists_question_form_fields_mandatory'
      language: '@language_en_gb'
      value: 'Mandatory?'
  -
    fields:
      placeholder: '@checklists_question_form_fields_allow_attachments'
      language: '@language_en_gb'
      value: 'Allow Attachments?'
  -
    fields:
      placeholder: '@checklists_question_form_fields_add_another'
      language: '@language_en_gb'
      value: 'Add Another Question?'
  -
    fields:
      placeholder: '@checklists_question_form_fields_field_type_label'
      language: '@language_en_gb'
      value: 'Field Type'
  -
    fields:
      placeholder: '@checklists_question_form_fields_field_type_options_short_text'
      language: '@language_en_gb'
      value: 'Text (Short)'
  -
    fields:
      placeholder: '@checklists_question_form_fields_field_type_options_long_text'
      language: '@language_en_gb'
      value: 'Text (Long)'
  -
    fields:
      placeholder: '@checklists_question_form_fields_field_type_options_date'
      language: '@language_en_gb'
      value: Date
  -
    fields:
      placeholder: '@checklists_question_form_fields_field_type_options_selection'
      language: '@language_en_gb'
      value: Selection
  -
    fields:
      placeholder: '@checklists_question_form_fields_selection_type_label'
      language: '@language_en_gb'
      value: 'Selection Type'
  -
    fields:
      placeholder: '@checklists_question_form_fields_selection_type_single'
      language: '@language_en_gb'
      value: 'Single Response'
  -
    fields:
      placeholder: '@checklists_question_form_fields_selection_type_multiple'
      language: '@language_en_gb'
      value: 'Multiple Responses'
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_label'
      language: '@language_en_gb'
      value: 'Input Type'
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_radio_buttons'
      language: '@language_en_gb'
      value: 'Radio Buttons'
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_range'
      language: '@language_en_gb'
      value: Range
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_dropdown'
      language: '@language_en_gb'
      value: Dropdown
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_yes_no'
      language: '@language_en_gb'
      value: 'Yes / No'
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_smiley_scale'
      language: '@language_en_gb'
      value: 'Smiley Scale'
  -
    fields:
      placeholder: '@checklists_question_form_fields_input_type_checkboxes'
      language: '@language_en_gb'
      value: Checkboxes
  -
    fields:
      placeholder: '@checklists_question_form_cancel'
      language: '@language_en_gb'
      value: Cancel
  -
    fields:
      placeholder: '@checklists_question_form_save'
      language: '@language_en_gb'
      value: 'Save Question'
  -
    fields:
      placeholder: '@checklists_error_not_found'
      language: '@language_en_gb'
      value: 'Survey not found'
  -
    fields:
      placeholder: '@checklists_error_expired'
      language: '@language_en_gb'
      value: 'Survey has expired, and can no longer be responded to'
  -
    fields:
      placeholder: '@checklists_error_loading'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the survey'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_loading'
      language: '@language_en_gb'
      value: 'Loading Survey Recipients'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_optional_response'
      language: '@language_en_gb'
      value: 'Optional Response'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_required_response'
      language: '@language_en_gb'
      value: 'Required Response'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_datix_users'
      language: '@language_en_gb'
      value: 'Datix Users'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users'
      language: '@language_en_gb'
      value: 'External Users'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_new_recipients'
      language: '@language_en_gb'
      value: 'New Recipients ({{numRecipients}})'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_existing_recipients'
      language: '@language_en_gb'
      value: 'Existing Recipients ({{numRecipients}})'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_distribute_survey'
      language: '@language_en_gb'
      value: 'Distribute Survey'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_add_responder'
      language: '@language_en_gb'
      value: 'Add Responder'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_name'
      language: '@language_en_gb'
      value: 'Name'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_email'
      language: '@language_en_gb'
      value: 'Email'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_invited_on'
      language: '@language_en_gb'
      value: 'Invited On'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_actions'
      language: '@language_en_gb'
      value: 'Actions'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users_no_external_users'
      language: '@language_en_gb'
      value: 'This checklist has not been distributed to any external recipients'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users_use_form'
      language: '@language_en_gb'
      value: 'Use the form above to add new external recipients'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users_user_found_title'
      language: '@language_en_gb'
      value: 'Matching User Found'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users_user_found_description'
      language: '@language_en_gb'
      value: 'The entered email address matches an existing Datix User'
  -
    fields:
      placeholder: '@checklists_distribute_recipients_external_users_user_found_add_user'
      language: '@language_en_gb'
      value: 'Would you like to add {{userName}} to the list of optional users?'
# Checklist 1
  -
    fields:
      placeholder: '@checklists_checklist_1_fields_name'
      language: '@language_en_gb'
      value: 'Name'
  -
    fields:
      placeholder: '@checklists_checklist_1_fields_name_label'
      language: '@language_en_gb'
      value: 'Name'
  -
    fields:
      placeholder: '@checklists_checklist_1_fields_country_of_residence'
      language: '@language_en_gb'
      value: 'Country of Residence'
  -
    fields:
      placeholder: '@checklists_checklist_1_fields_country_of_residence_label'
      language: '@language_en_gb'
      value: 'Country of Residence'
  -
    fields:
      placeholder: '@checklists_checklist_1_country_of_residence'
      language: '@language_en_gb'
      value: 'Country of Residence'
  -
    fields:
      placeholder: '@checklists_checklist_1_country_of_residence_label'
      language: '@language_en_gb'
      value: 'Country of Residence'
# Checklist 3
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_text'
      language: '@language_en_gb'
      value: 'Text Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_text_label'
      language: '@language_en_gb'
      value: 'Text Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_textarea'
      language: '@language_en_gb'
      value: 'Textarea Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_textarea_label'
      language: '@language_en_gb'
      value: 'Textarea Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_date'
      language: '@language_en_gb'
      value: 'Date Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_date_label'
      language: '@language_en_gb'
      value: 'Date Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_radio'
      language: '@language_en_gb'
      value: 'Radio Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_radio_label'
      language: '@language_en_gb'
      value: 'Radio Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_range'
      language: '@language_en_gb'
      value: 'Range Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_range_label'
      language: '@language_en_gb'
      value: 'Range Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_dropdown'
      language: '@language_en_gb'
      value: 'Dropdown Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_dropdown_label'
      language: '@language_en_gb'
      value: 'Dropdown Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_yes_no'
      language: '@language_en_gb'
      value: 'Yes / No Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_yes_no_label'
      language: '@language_en_gb'
      value: 'Yes / No Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_smiley_scale'
      language: '@language_en_gb'
      value: 'Smiley Scale'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_smiley_scale_label'
      language: '@language_en_gb'
      value: 'Smiley Scale'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_checkbox'
      language: '@language_en_gb'
      value: 'Checkbox Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_checkbox_label'
      language: '@language_en_gb'
      value: 'Checkbox Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_multiselect'
      language: '@language_en_gb'
      value: 'Multiselect Field'
  -
    fields:
      placeholder: '@checklists_checklist_3_fields_multiselect_label'
      language: '@language_en_gb'
      value: 'Multiselect Field'
  -
    fields:
      placeholder: '@checklist_response_filter_submission_date'
      language: '@language_en_gb'
      value: 'Submission Date'
  -
    fields:
      placeholder: '@checklist_response_filter_submission_date_label'
      language: '@language_en_gb'
      value: 'Submission Date'
# Checklist 4
  -
    fields:
      placeholder: '@checklists_checklist_4_field_1'
      language: '@language_en_gb'
      value: 'Confirmation of identity'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_1_label'
      language: '@language_en_gb'
      value: 'Confirmation of identity'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_2'
      language: '@language_en_gb'
      value: 'Confirmation of consent'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_2_label'
      language: '@language_en_gb'
      value: 'Confirmation of consent'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_3'
      language: '@language_en_gb'
      value: 'Surgery site marked'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_3_label'
      language: '@language_en_gb'
      value: 'Surgery site marked'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_4'
      language: '@language_en_gb'
      value: 'Anesthesia and medication check'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_4_label'
      language: '@language_en_gb'
      value: 'Anesthesia and medication check'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_5'
      language: '@language_en_gb'
      value: 'Anesthesia and medication check'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_5_label'
      language: '@language_en_gb'
      value: 'Anesthesia and medication check'
# Form
  -
    fields:
      placeholder: '@checklists_form_field_title'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@checklists_form_field_title_label'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@checklists_checklist_4_field_5_label'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@checklists_form_field_description'
      language: '@language_en_gb'
      value: 'Description'
  -
    fields:
      placeholder: '@checklists_form_field_description_label'
      language: '@language_en_gb'
      value: 'Description'
  -
    fields:
      placeholder: '@checklists_form_field_type_title'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@checklists_form_field_type_label'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@checklists_form_field_category_title'
      language: '@language_en_gb'
      value: 'Category'
  -
    fields:
      placeholder: '@checklists_form_field_category_label'
      language: '@language_en_gb'
      value: 'Category'
  -
    fields:
      placeholder: '@checklists_form_field_sub_category'
      language: '@language_en_gb'
      value: 'Checklist SubCategory'
  -
    fields:
      placeholder: '@checklists_form_field_sub_category_label'
      language: '@language_en_gb'
      value: 'Checklist Due Date'
  -
    fields:
      placeholder: '@checklists_form_field_due_date'
      language: '@language_en_gb'
      value: 'Checklist SubCategory'
  -
    fields:
      placeholder: '@checklists_form_field_due_date_label'
      language: '@language_en_gb'
      value: 'Checklist Due Date'
  -
    fields:
      placeholder: '@checklists_form_field_access_level'
      language: '@language_en_gb'
      value: 'Checklist Access Level'
  -
    fields:
      placeholder: '@checklists_form_field_access_level_label'
      language: '@language_en_gb'
      value: 'Checklist Access Level'
  -
    fields:
      placeholder: '@checklists_form_type_survey_response'
      language: '@language_en_gb'
      value: 'Survey Response'
  -
    fields:
      placeholder: '@checklists_datasource_yes_no'
      language: '@language_en_gb'
      value: Yes / No
  -
    fields:
      placeholder: '@checklists_datasource_yes_no_na'
      language: '@language_en_gb'
      value: Yes / No / N/A
  -
    fields:
      placeholder: '@checklists_datasource_countries_of_residence'
      language: '@language_en_gb'
      value: Countries of Residence
  -
    fields:
      placeholder: '@checklists_datasource_radio_options'
      language: '@language_en_gb'
      value: Radio Options
  -
    fields:
      placeholder: '@checklists_datasource_range_options'
      language: '@language_en_gb'
      value: Range Options
  -
    fields:
      placeholder: '@checklists_datasource_dropdown_options'
      language: '@language_en_gb'
      value: Dropdown Options
  -
    fields:
      placeholder: '@checklists_datasource_yes_no_options'
      language: '@language_en_gb'
      value: Yes / No
  -
    fields:
      placeholder: '@checklists_datasource_checkbox_options'
      language: '@language_en_gb'
      value: Checkbox Options
  -
    fields:
      placeholder: '@checklists_datasource_multiselect_options'
      language: '@language_en_gb'
      value: Multiselect Options
  -
    fields:
      placeholder: '@checklists_datasource_checklist_type'
      language: '@language_en_gb'
      value: Survey Type
  -
    fields:
      placeholder: '@checklists_datasource_checklist_category'
      language: '@language_en_gb'
      value: Survey Category
  -
    fields:
      placeholder: '@checklists_datasource_checklist_subcategory'
      language: '@language_en_gb'
      value: Survey Subcategory
  -
    fields:
      placeholder: '@checklists_datasource_checklist_access_level'
      language: '@language_en_gb'
      value: Survey Access Level
  -
    fields:
      placeholder: '@checklists_datasource_item_uk'
      language: '@language_en_gb'
      value: UK
  -
    fields:
      placeholder: '@checklists_datasource_item_france'
      language: '@language_en_gb'
      value: France
  -
    fields:
      placeholder: '@checklists_datasource_item_italy'
      language: '@language_en_gb'
      value: Italy
  -
    fields:
      placeholder: '@checklists_datasource_item_radio_option_1'
      language: '@language_en_gb'
      value: 'Radio Option 1'
  -
    fields:
      placeholder: '@checklists_datasource_item_radio_option_2'
      language: '@language_en_gb'
      value: 'Radio Option 2'
  -
    fields:
      placeholder: '@checklists_datasource_item_radio_option_3'
      language: '@language_en_gb'
      value: 'Radio Option 3'
  -
    fields:
      placeholder: '@checklists_datasource_item_range_option_1'
      language: '@language_en_gb'
      value: 'Range Option 1'
  -
    fields:
      placeholder: '@checklists_datasource_item_range_option_2'
      language: '@language_en_gb'
      value: 'Range Option 3'
  -
    fields:
      placeholder: '@checklists_datasource_item_range_option_3'
      language: '@language_en_gb'
      value: 'Range Option 3'
  -
    fields:
      placeholder: '@checklists_datasource_item_range_option_4'
      language: '@language_en_gb'
      value: 'Range Option 4'
  -
    fields:
      placeholder: '@checklists_datasource_item_range_option_5'
      language: '@language_en_gb'
      value: 'Range Option 5'
  -
    fields:
      placeholder: '@checklists_datasource_item_dropdown_option_1'
      language: '@language_en_gb'
      value: 'Dropdown Option 1'
  -
    fields:
      placeholder: '@checklists_datasource_item_dropdown_option_2'
      language: '@language_en_gb'
      value: 'Dropdown Option 2'
  -
    fields:
      placeholder: '@checklists_datasource_item_dropdown_option_3'
      language: '@language_en_gb'
      value: 'Dropdown Option 4'
  -
    fields:
      placeholder: '@checklists_datasource_item_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@checklists_datasource_item_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@checklists_datasource_item_checkbox_option_1'
      language: '@language_en_gb'
      value: 'Checkbox Option 1'
  -
    fields:
      placeholder: '@checklists_datasource_item_checkbox_option_2'
      language: '@language_en_gb'
      value: 'Checkbox Option 2'
  -
    fields:
      placeholder: '@checklists_datasource_item_checkbox_option_3'
      language: '@language_en_gb'
      value: 'Checkbox Option 3'
  -
    fields:
      placeholder: '@checklists_datasource_item_multiselect_option_1'
      language: '@language_en_gb'
      value: 'Multiselect Option 1'
  -
    fields:
      placeholder: '@checklists_datasource_item_multiselect_option_2'
      language: '@language_en_gb'
      value: 'Multiselect Option 2'
  -
    fields:
      placeholder: '@checklists_datasource_item_multiselect_option_3'
      language: '@language_en_gb'
      value: 'Multiselect Option 3'
  -
    fields:
      placeholder: '@checklists_form_name_checklist_1_form'
      language: '@language_en_gb'
      value: 'Checklist 1 Form'
  -
    fields:
      placeholder: '@checklists_form_name_example_checklist'
      language: '@language_en_gb'
      value: 'Example Checklist'
  -
    fields:
      placeholder: '@checklists_form_name_checklist_with_open_access_user_access'
      language: '@language_en_gb'
      value: 'Checklist with Open access (user access)'
  -
    fields:
      placeholder: '@checklists_form_name_checklist_with_all_datix_users_user_access'
      language: '@language_en_gb'
      value: 'Checklist with All Datix Users (user access)'
  -
    fields:
      placeholder: '@checklists_form_name_checklist_with_selected_users_only_user_access'
      language: '@language_en_gb'
      value: 'Checklist with Selected Users Only (user access)'
  -
    fields:
      placeholder: '@checklists_untitled_group'
      language: '@language_en_gb'
      value: 'Untitled Group {{index}}'
  -
    fields:
      placeholder: '@checklists_add_the_first_survey_group'
      language: '@language_en_gb'
      value: 'Add the first Survey Group'
  -
    fields:
      placeholder: '@checklists_checklist_has_expired_notice'
      language: '@language_en_gb'
      value: 'This Survey expired on {{expiryDate}}'
  -
    fields:
      placeholder: '@checklists_error_questions_required'
      language: '@language_en_gb'
      value: 'Survey cannot be released without questions'
  -
    fields:
      placeholder: '@checklists_error_unable_to_create_recipient'
      language: '@language_en_gb'
      value: 'Unable to create Survey Recipient'
  -
    fields:
      placeholder: '@checklists_error_recipient_already_assigned'
      language: '@language_en_gb'
      value: 'The specified User has already been assigned as a recipient to this Survey'
  -
    fields:
      placeholder: '@checklists_events_distributed_successfully'
      language: '@language_en_gb'
      value: 'Survey distributed successfully'
  -
    fields:
      placeholder: '@checklists_events_checklist_group_saved_successfully'
      language: '@language_en_gb'
      value: 'Survey Group saved successfully'
  -
    fields:
      placeholder: '@checklists_events_checklist_group_deleted_successfully'
      language: '@language_en_gb'
      value: 'Survey Group deleted successfully'
  -
    fields:
      placeholder: '@checklists_events_error_deleting_checklist_group'
      language: '@language_en_gb'
      value: 'An error occurred whilst deleting the Survey Group'
