entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.ALL_TEMPLATES
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_all_templates
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.DISTRIBUTE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_distribute
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.MY_DISTRIBUTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_my_distributions
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.MY_RESPONSES
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_my_responses
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.MY_RESPONSES.RESPONDED_TO
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_my_responses_responded_to
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.MY_RESPONSES.READY_FOR_RESPONSE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_my_responses_ready_for_response
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.BROWSE_CHECKLISTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_browse_checklists
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.NEW_TEMPLATE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_new_template
  -
    fields:
      placeholder: CHECKLISTS.DASHBOARD.NAV.PERMISSIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_dashboard_nav_permissions
  -
    fields:
      placeholder: CHECKLISTS.RESPONSES.NAV.CHECKLIST_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_responses_nav_checklist_summary
  -
    fields:
      placeholder: CHECKLISTS.RESPONSES.NAV.RECIPIENTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_responses_nav_recipients
  -
    fields:
      placeholder: CHECKLISTS.RESPONSES.NAV.RESPONSES
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_responses_nav_responses
  -
    fields:
      placeholder: CHECKLISTS.CHECKLISTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklists
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist
  -
    fields:
      placeholder: CHECKLISTS.RECIPIENTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_recipients
  -
    fields:
      placeholder: CHECKLISTS.RECIPIENT
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_recipient
  -
    fields:
      placeholder: CHECKLISTS.RESPONSES
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_responses
  -
    fields:
      placeholder: CHECKLISTS.EXPORT_CSV
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_export_csv
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.CHECKLIST_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_checklist_summary
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.CHECKLIST_QUESTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_checklist_questions
  -
    fields:
      placeholder: CHECKLISTS.COMMON.COLLECT_USER_DATA
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_common_collect_user_data
  -
    fields:
      placeholder: CHECKLISTS.COMMON.BACK.DASHBOARD
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_common_back_dashboard
  -
    fields:
      placeholder: CHECKLISTS.YES_NO.YES
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_yes_no_yes
  -
    fields:
      placeholder: CHECKLISTS.YES_NO.NO
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_yes_no_no
  -
    fields:
      placeholder: CHECKLISTS.YES_NO.NA
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_yes_no_na
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.ID
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_id
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.TITLE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_title
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.EXPIRED
      domains:
      - domain: '@domain_checklists'
    ref: checklists_tables_columns_expired
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.PUBLISHED
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_published
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.DUE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_due
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.SUBMISSION_DATE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_submission_date
  -
    fields:
      placeholder: CHECKLISTS.TABLES.COLUMNS.RESPONDER
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_tables_columns_responder
  -
    fields:
      placeholder: CHECKLISTS.FORMS.RESPONSE.SUBMIT
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_forms_response_submit
  -
    fields:
      placeholder: CHECKLISTS.FORMS.RESPONSE.SUBMIT_SUCCESS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_forms_response_submit_success
  -
    fields:
      placeholder: CHECKLISTS.FORMS.RESPONSE.PUBLIC.SUBMIT_SUCCESS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_forms_response_public_submit_success
  -
    fields:
      placeholder: CHECKLISTS.NEW
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_new
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TITLE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_title_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.DESCRIPTION.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_description_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TYPE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_type_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TYPE.EMPTY_VALUE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_type_empty_value
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TYPE.OPTIONS.A
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_type_options_a
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TYPE.OPTIONS.B
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_type_options_b
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.TYPE.OPTIONS.C
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_type_options_c
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.EMPTY_VALUE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_empty_value
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.OPTIONS.A
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_options_a
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.OPTIONS.B
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_options_b
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.OPTIONS.C
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_options_c
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.OPTIONS.D
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_options_d
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.CATEGORY.OPTIONS.E
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_category_options_e
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELDS.DUE_DATE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_fields_due_date_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.TITLE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_title
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.OPEN_ACCESS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_open_access
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.ALL_DATIX_USERS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_all_datix_users
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.SELECTED_USERS_ONLY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_selected_users_only
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.COLLECT_USER_DATA
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_collect_user_data
  -
    fields:
      placeholder: CHECKLISTS.FORM.USER_ACCESS.REQUIRE_CAPTCHA
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_user_access_require_captcha
  -
    fields:
      placeholder: CHECKLISTS.FORM.SAVE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_save
  -
    fields:
      placeholder: CHECKLISTS.FORM.SAVED_SUCCESSFULLY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_saved_successfully
  -
    fields:
      placeholder: CHECKLISTS.FORM.SAVE_ERROR
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_save_error
  -
    fields:
      placeholder: CHECKLISTS.FORM.PUBLISHED_SUCCESSFULLY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_published_successfully
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.NAV.CHECKLIST_NUMBER
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_nav_checklist_number
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.NAV.DETAILS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_nav_details
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.NAV.QUESTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_nav_questions
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.NAV.ACCESS_CONTROL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_nav_access_control
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.DETAILS.EDIT
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_details_edit
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.GROUPS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_groups
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TITLE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_title
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.GROUP_DETAILS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_group_details
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.GROUP_DETAILS.FORM.FIELDS.NAME
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_group_details_form_fields_name
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.GROUP_DETAILS.FORM.FIELDS.SUMMARY
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_group_details_form_fields_summary
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.GROUP_DETAILS.FORM.SAVE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_group_details_form_save
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.GROUP_DETAILS.FORM.DELETE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_group_details_form_delete
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.ADD_NEW
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_add_new
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.LIST.COLUMNS.QUESTION
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_list_columns_question
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.LIST.COLUMNS.MANDATORY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_list_columns_mandatory
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.LIST.COLUMNS.ALLOW_ATTACHMENTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_list_columns_allow_attachments
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.LIST.COLUMNS.ACTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_list_columns_actions
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST.QUESTIONS.TABS.QUESTIONS.LIST.NO_RECORDS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_questions_tabs_questions_list_no_records
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.ADD
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_add
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.EDIT
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_edit
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.TITLE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_title
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_label
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INTRODUCTORY_TEXT
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_introductory_text
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.MANDATORY
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_mandatory
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.ALLOW_ATTACHMENTS
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_allow_attachments
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.ADD_ANOTHER
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_add_another
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.FIELD_TYPE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_field_type_label
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.FIELD_TYPE.OPTIONS.SHORT_TEXT
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_field_type_options_short_text
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.FIELD_TYPE.OPTIONS.LONG_TEXT
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_field_type_options_long_text
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.FIELD_TYPE.OPTIONS.DATE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_field_type_options_date
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.FIELD_TYPE.OPTIONS.SELECTION
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_field_type_options_selection
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.SELECTION_TYPE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_selection_type_label
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.SELECTION_TYPE.SINGLE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_selection_type_single
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.SELECTION_TYPE.MULTIPLE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_selection_type_multiple
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_label
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.RADIO_BUTTONS
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_radio_buttons
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.RANGE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_range
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.DROPDOWN
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_dropdown
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.YES_NO
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_yes_no
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.SMILEY_SCALE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_smiley_scale
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.FIELDS.INPUT_TYPE.CHECKBOXES
      type: 1
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_fields_input_type_checkboxes
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.CANCEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_cancel
  -
    fields:
      placeholder: CHECKLISTS.QUESTION.FORM.SAVE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_roundings'
    ref: checklists_question_form_save
  -
    fields:
      placeholder: CHECKLISTS.ERROR.NOT_FOUND
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_error_not_found
  -
    fields:
      placeholder: CHECKLISTS.ERROR.EXPIRED
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_error_expired
  -
    fields:
      placeholder: CHECKLISTS.ERROR.LOADING
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_error_loading
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.LOADING
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_loading
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.OPTIONAL_RESPONSE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_optional_response
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.REQUIRED_RESPONSE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_required_response
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.DATIX_USERS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_datix_users
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.NEW_RECIPIENTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_new_recipients
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXISTING_RECIPIENTS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_existing_recipients
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.DISTRIBUTE_SURVEY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_distribute_survey
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.ADD_RESPONDER
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_add_responder
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.NAME
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_name
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EMAIL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_email
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.INVITED_ON
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_invited_on
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.ACTIONS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_actions
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS.NO_EXTERNAL_USERS
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users_no_external_users
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS.USE_FORM
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users_use_form
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS.USER_FOUND.TITLE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users_user_found_title
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS.USER_FOUND.DESCRIPTION
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users_user_found_description
  -
    fields:
      placeholder: CHECKLISTS.DISTRIBUTE.RECIPIENTS.EXTERNAL_USERS.USER_FOUND.ADD_USER
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_distribute_recipients_external_users_user_found_add_user
  # checklist_1
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.FIELDS.NAME
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_fields_name
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.FIELDS.NAME.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_fields_name_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.FIELDS.COUNTRY_OF_RESIDENCE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_fields_country_of_residence
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.FIELDS.COUNTRY_OF_RESIDENCE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_fields_country_of_residence_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.COUNTRY_OF_RESIDENCE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_country_of_residence
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_1.COUNTRY_OF_RESIDENCE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_1_country_of_residence_label
  # Checklist 3
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.TEXT
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_text
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.TEXT.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_text_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.TEXTAREA
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_textarea
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.TEXTAREA.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_textarea_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.DATE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_date
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.DATE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_date_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.RADIO
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_radio
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.RADIO.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_radio_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.RANGE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_range
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.RANGE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_range_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.DROPDOWN
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_dropdown
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.DROPDOWN.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_dropdown_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.YES_NO
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_yes_no
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.YES_NO.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_yes_no_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.SMILEY_SCALE
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_smiley_scale
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.SMILEY_SCALE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_smiley_scale_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.CHECKBOX
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_checkbox
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.CHECKBOX.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_checkbox_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.MULTISELECT
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_multiselect
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_3.FIELDS.MULTISELECT.LABEL
      type: 1
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_3_fields_multiselect_label
  -
    fields:
      placeholder: CHECKLISTS.RESPONSE.FILTER.SUBMISSION_DATE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklist_response_filter_submission_date
  -
    fields:
      placeholder: CHECKLISTS.RESPONSE.FILTER.SUBMISSION_DATE.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklist_response_filter_submission_date_label
  # Checklist 4
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_1
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_1
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_1.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_1_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_2
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_2
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_2.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_2_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_3
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_3
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_3.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_3_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_4
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_4
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_4.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_4_label
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_5
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_5
  -
    fields:
      placeholder: CHECKLISTS.CHECKLIST_4.FIELD_5.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_checklist_4_field_5_label
  # Form
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.TITLE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_title
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.TITLE.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_title_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.DESCRIPTION
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_description
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.DESCRIPTION.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_description_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.TYPE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_type
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.TYPE.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_field_maintenance'
      -
        domain: '@domain_notification_centre'
    ref: checklists_form_field_type_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.CATEGORY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_field_maintenance'
    ref: checklists_form_field_category
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.CATEGORY.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_notification_centre'
    ref: checklists_form_field_category_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.SUB_CATEGORY
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_sub_category
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.SUB_CATEGORY.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_sub_category_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.DUE_DATE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_due_date
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.DUE_DATE.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_due_date_label
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.ACCESS_LEVEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_access_level
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.ACCESS_LEVEL.LABEL
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_form_field_access_level_label
  -
    fields:
      placeholder: CHECKLISTS.FORM_TYPE.SURVEY_RESPONSE
      type: 0
      domains:
      -
        domain: '@domain_checklists'
      -
        domain: '@domain_form_types'
    ref: checklists_form_type_survey_response
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.YES_NO
      type: 0
      domains:
      -
        domain: '@domain_checklists'
    ref: checklists_datasource_yes_no
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.YES_NO_NA
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_yes_no_na
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.COUNTRIES_OF_RESIDENCE
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_countries_of_residence
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.RADIO_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_radio_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.RANGE_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_range_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.DROPDOWN_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_dropdown_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.YES_NO_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_yes_no_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.CHECKBOX_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_checkbox_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.MULTISELECT_OPTIONS
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_multiselect_options
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.CHECKLIST_TYPE
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_checklist_type
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.CHECKLIST_CATEGORY
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_checklist_category
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.CHECKLIST_SUBCATEGORY
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_checklist_subcategory
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE.CHECKLIST_ACCESS_LEVEL
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_checklist_access_level
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.UK
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_uk
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.FRANCE
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_france
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.ITALY
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_italy
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RADIO_OPTION_1
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_radio_option_1
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RADIO_OPTION_2
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_radio_option_2
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RADIO_OPTION_3
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_radio_option_3
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RANGE_OPTION_1
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_range_option_1
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RANGE_OPTION_2
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_range_option_2
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RANGE_OPTION_3
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_range_option_3
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RANGE_OPTION_4
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_range_option_4
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.RANGE_OPTION_5
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_range_option_5
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.DROPDOWN_OPTION_1
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_dropdown_option_1
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.DROPDOWN_OPTION_2
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_dropdown_option_2
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.DROPDOWN_OPTION_3
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_dropdown_option_3
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.YES
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_yes
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.NO
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_no
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.CHECKBOX_OPTION_1
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_checkbox_option_1
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.CHECKBOX_OPTION_2
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_checkbox_option_2
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.CHECKBOX_OPTION_3
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_checkbox_option_3
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.MULTISELECT_OPTION_1
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_multiselect_option_1
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.MULTISELECT_OPTION_2
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_multiselect_option_2
  -
    fields:
      placeholder: CHECKLISTS.DATASOURCE_ITEM.MULTISELECT_OPTION_3
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_datasource_item_multiselect_option_3
  -
    fields:
      placeholder: CHECKLISTS.FORM.NAME.CHECKLIST_1_FORM
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_form_name_checklist_1_form
  -
    fields:
      placeholder: CHECKLISTS.FORM.NAME.EXAMPLE_CHECKLIST
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_form_name_example_checklist
  -
    fields:
      placeholder: CHECKLISTS.FORM.NAME.CHECKLIST_WITH_OPEN_ACCESS_USER_ACCESS
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_form_name_checklist_with_open_access_user_access
  -
    fields:
      placeholder: CHECKLISTS.FORM.NAME.CHECKLIST_WITH_ALL_DATIX_USERS_USER_ACCESS
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_form_name_checklist_with_all_datix_users_user_access
  -
    fields:
      placeholder: CHECKLISTS.FORM.NAME.CHECKLIST_WITH_SELECTED_USERS_ONLY_USER_ACCESS
      type: 1
      domains:
      - domain: '@domain_checklists'
    ref: checklists_form_name_checklist_with_selected_users_only_user_access
  -
    fields:
      placeholder: CHECKLISTS.UNTITLED_GROUP
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_untitled_group
  -
    fields:
      placeholder: CHECKLISTS.ADD_THE_FIRST_SURVEY_GROUP
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_add_the_first_survey_group
  -
    fields:
      placeholder: 'CHECKLISTS.CHECKLIST.HAS.EXPIRED.NOTICE'
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_checklist_has_expired_notice
  -
    fields:
      placeholder: CHECKLISTS.ERROR.QUESTIONS_REQUIRED
      type: 0
      domains:
      - domain: '@domain_checklists'
    ref: checklists_error_questions_required
  - fields:
      placeholder: CHECKLISTS.ERROR.UNABLE_TO_CREATE_RECIPIENT
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_error_unable_to_create_recipient
  - fields:
      placeholder: CHECKLISTS.ERROR.RECIPIENT_ALREADY_ASSIGNED
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_error_recipient_already_assigned
  - fields:
      placeholder: CHECKLISTS.EVENTS.DISTRIBUTED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_events_distributed_successfully
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.CATEGORY.TITLE
      type: 0
      domains:
        - domain: '@domain_checklists'
        - domain: '@domain_field_maintenance'
    ref: checklists_form_field_category_title
  -
    fields:
      placeholder: CHECKLISTS.FORM.FIELD.TYPE.TITLE
      type: 0
      domains:
        - domain: '@domain_checklists'
        - domain: '@domain_field_maintenance'
    ref: checklists_form_field_type_title
  - fields:
      placeholder: CHECKLISTS.EVENTS.CHECKLIST_GROUP_SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_events_checklist_group_saved_successfully
  - fields:
      placeholder: CHECKLISTS.EVENTS.CHECKLIST_GROUP_DELETED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_events_checklist_group_deleted_successfully
  - fields:
      placeholder: CHECKLISTS.EVENTS.ERROR_DELETING_CHECKLIST_GROUP
      type: 0
      domains:
        - domain: '@domain_checklists'
    ref: checklists_events_error_deleting_checklist_group
