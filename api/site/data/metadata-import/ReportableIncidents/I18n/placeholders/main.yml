entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MODULE_TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_module_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.BACK_TO_DASHBOARD
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_back_to_dashboard
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.ALL_RIBS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_all_ribs
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.IN_PROGRESS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_in_progress
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.APPROVED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_approved
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.SUBMITTED_TO_MOH
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_submitted_to_moh
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.FINALISED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_finalised
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SIDEBAR.REJECTED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_sidebar_rejected
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.LABEL.SINGULAR
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_label_singular
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.LABEL.PLURAL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_label_plural
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.RIB_ID
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_rib_id
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.MOH_RIB_NUMBER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_moh_rib_number
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.SOURCE_RECORD_REF
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_source_record_ref
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.TYPE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_columns_type
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.SAC_SCORE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_columns_sac_score
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.PRA_REQUIRED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_columns_pra_required
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.DRAFT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_draft
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.APPROVED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_approved
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.FINALISED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_finalised
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.IN_PROGRESS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_in_progress
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.REJECTED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_rejected
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.STATUS.SUBMITTED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_status_submitted
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.OWNER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_owner
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.CREATED_AT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_created_at
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.LOCATION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_location
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.COLUMNS.REASON_FOR_REPORTING
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_columns_reason_for_reporting
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.LOADING.REPORTABLE_INCIDENTS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_loading_reportable_incidents
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.HEADING
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_heading
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.DETAILS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_details
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.ACCESS_CONTROL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_access_control
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.ACTIONS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_actions
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.MY_ACTIONS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_my_actions
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.ALL_ACTIONS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_all_actions
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.EDIT.SIDEBAR.ATTACHMENTS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_edit_sidebar_attachments
# RIB Field Options
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.MATERNAL_DEATH
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_maternal_death
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.SAFE_WORK
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_safe_work
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.CHASM
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_chasm
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.SCIDUA
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_scidua
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.MEDIA_TEAM
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_media_team
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.PERINATAL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_perinatal
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.OTHER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_other
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.OPTION.STATE_WIDE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_state_wide
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.OPTION.LEGAL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_legal
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.OPTION.SAC2-4
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_sac2_4
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.OPTION.REQUIRED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_option_required
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SECTION.MAIN
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_section_main
# RIB Main Form
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.RIB_NUMBER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_rib_number
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.RIB_NUMBER.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_rib_number_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OWNER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_owner
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OWNER.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_owner_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.DISCLOSURE_COMMENCED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_disclosure_commenced
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.DISCLOSURE_COMMENCED.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_disclosure_commenced_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.CREATED_DATE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_created_date
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.CREATED_DATE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_created_date_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTING_REASON
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_reporting_reason
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTING_REASON.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_reporting_reason_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.DISCLOSURE_REASON
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_disclosure_reason
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.DISCLOSURE_REASON.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_disclosure_reason_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.ACTION_TAKEN
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_action_taken
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.ACTION_TAKEN.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_action_taken_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.FURTHER_ACTION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_further_action
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.FURTHER_ACTION.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_further_action_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_agency_referral
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_agency_referral_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_AGENCY_REFERRAL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_agency_referral
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_AGENCY_REFERRAL.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_agency_referral_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_reported_reason
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_reported_reason_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SAC_SCORE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_sac_score
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SAC_SCORE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_sac_score_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MESSAGE.REPORTABLE_INCIDENT_SAVED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_message_reportable_incident_saved
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.AGENCY_REFERRAL.OPTION.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_fields_agency_option_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_REASON.OPTION.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_fields_reported_reason_option_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.STATUS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_status
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.STATUS.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_status_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REJECTION_REASON
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_rejection_reason
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REJECTION_REASON.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_rejection_reason_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FORM_TYPE.MAIN
      domains:
        -
          domain: '@domain_reportable_incidents'
        -
          domain: '@domain_admin'
    ref: reportable_incidents_main_form_type_main
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_TYPE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_type
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_TYPE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_type_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_ID
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_id
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_ID.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_id_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_REF
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_ref
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SOURCE_REF.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_source_ref_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.LOCATION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_location
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.LOCATION.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_location_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_LOCATION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_location
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_LOCATION.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_location_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SERVICE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_service
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.SERVICE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_service_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_SERVICE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_service
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.OTHER_SERVICE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_other_service_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.TYPE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_type
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.TYPE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_type_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.CATEGORY
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_category
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.CATEGORY.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_category_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_DATE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_date
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_DATE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_date_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_TIME
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_time
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_TIME.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_time_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_SUMMARY
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_summary
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.INCIDENT_SUMMARY.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_incident_summary_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_DATE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_reported_date
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.REPORTED_DATE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_reported_date_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.COMMENCEMENT_DATE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_commencement_date
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.COMMENCEMENT_DATE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_commencement_date_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.EVENT_SYNOPSIS.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_event_synopsis_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.MAIN_FORM.FIELDS.EVENT_SYNOPSIS.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_field_event_synopsis_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SECTION.SOURCE_DETAILS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_main_form_section_source_details
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_DRAFT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_draft
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_IN_PROGRESS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_in_progress
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_REJECTED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_rejected
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_APPROVED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_approved
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_SUBMITTED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_submitted
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.STATUS_FINALISED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_status_finalised
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER.FORM.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.RIB_ID
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_rib_id
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.RIB_ID.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_rib_id_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.RIB_NUMBER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_rib_number
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.RIB_NUMBER.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_rib_number_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.CREATED_AT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_created_at
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.CREATED_AT.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_created_at_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.REPORTING_REASON
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_reporting_reason
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.REPORTING_REASON.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_reporting_reason_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.STATUS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_status
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.STATUS.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_status_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.OWNER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_owner
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.OWNER.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_owner_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.LOCATION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_location
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.LOCATION.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_location_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SOURCE_TYPE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_source_type
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SOURCE_TYPE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_source_type_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SOURCE_REF
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_source_ref
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SOURCE_REF.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_source_ref_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.CATEGORY
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_category
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.CATEGORY.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_category_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SAC_SCORE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_sac_score
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FILTER_FORM.FIELDS.SAC_SCORE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_filter_form_field_sac_score_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ACTION_FORM_TYPE.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_action_form_type_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ACTION_FORM.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_action_form_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FORM.MAIN
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_form_main_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.FORM_TYPE.FILTER
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_form_type_filter
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.SUCCESS.ATTACHMENT.SAVE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_success_attachment_save
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ATTACHMENTS.NO_ATTACHMENTS
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_attachments_no_attachments
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.LABEL
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.DEFAULT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_default
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.RIB_CONTACT
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_rib_contact
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.INVESTIGATION_LEAD
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_investigation_lead
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.CONTACT.ROLE.PERSON_INVOLVED
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_contact_role_person_involved
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.REJECTED_NOTICE
      type: 0
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_rejected_notice
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ERRORS.CANNOT_REMOVE_OWNER
      type: 0
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_errors_cannot_remove_owner

  - fields:
      placeholder: REPORTABLE_INCIDENT.SUCCESS.TEMPLATE_DOCUMENT_ATTACHED
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_success_template_document_attached
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.REPORTABLE_INCIDENT_BRIEF
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_reportable_incident_brief
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.SOURCE_RECORD_ID
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_source_record_id
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_RECORD_ID
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_record_id
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.MOH_RECORD_NO
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_moh_rib_no
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.SEVERITY_ASSESSMENT_CODE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_severity_assessment_code
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.THIS_INCIDENT_IS
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_this_incident_is
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.IF_NOT_SAC_REASONS
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_if_not_sac_reasons
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.DATE_OF_INCIDENT_IOMS
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_date_of_incident_ioms
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.PRINCIPAL_INCIDENT_TYPE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_principal_incident_type
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.TIME_OF_INCIDENT
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_time_of_incident
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.DATE_OF_INCIDENT
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_date_of_incident
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.LOCATION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_location
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.SPECIFIC_SERVICE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_specific_service
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.REASON_FOR_REPORTING
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_reason_for_reporting
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.SYNOPSIS_OF_THE_EVENT
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_synopsis_of_the_event
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.ACTION_TAKEN
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_action_taken
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.HAS_OPEN_DISCLOSE_PROCESS_COMMENCED
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_has_open_disclose_process_commenced
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.IF_NO_PLEASE_COMMENT
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_if_no_please_comment
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.DATE_INITIAL_OPEN_DISCLOSE_COMMENCED
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_date_initial_open_disclosure_commenced
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.FURTHER_PLANNED_ACTION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_further_planned_action
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.TYPE_OF_INVESTIGATION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_type_of_investigation
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.REFERRAL_OTHER_AGENCIES
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_referral_other_agencies
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CONTACT_NAME
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_contact_name
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.POSITION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_position
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CONTACT_NUMBER
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_contact_number
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.EMAIL_ADDRESS
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_email_address
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CLINICAL_INCIDENTS_STATEMENT_1
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_clinical_incidents_statement_1
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CLINICAL_INCIDENTS_STATEMENT_2
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_clinical_incidents_statement_2
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.PERSON_TO_BE_RESPONSIBLE_FOR_INVESTIGATION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_person_to_be_responsible_for_investigation
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CONTACT_NO
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_contact_no
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.REPORTABLE_INCIDENT_HAS_BEEN_APPROVED
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_reportable_incident_has_been_approved
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.THE_ACCURACY_AND_CONTDENT_IS_ENDORSED
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_the_accuracy_and_content_is_endorsed
  - fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.REPORTABLE_INCIDENT_BRIEF_TITLE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_reportable_incident_brief_title
  - fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.REPORTABLE_INCIDENT_BRIEF_DESCRIPTION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_reportable_incident_brief_description
  - fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.REPORTABLE_INCIDENT_BRIEF_FILENAME
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_reportable_incident_brief_filename
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_A_QUESTIONS_1__10_TO
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_part_a_questions_1__10_to
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.IF_NOT_HARM_SCORE_1_WHAT_ARE_THE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_if_not_harm_score_1_what_are_the
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.WHERE_AND_WHEN_DID_THE_INCIDENT_HAPPEN
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_where_and_when_did_the_incident_happen
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.WHAT_HAPPENED_WHAT_ACTIONS_WERE_TAKEN_IN_RESPONSE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_what_happened_what_actions_were_taken_in_response
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.HAS_CLINICAL_DISCLOSURE_BEEN_INITIATED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_has_bclinical_disclosureb_been_initiated
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.ANY_OTHER_IMMEDIATE_CONCERNSRISKS_ADDRESSED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_any_other_immediate_concernsrisks_addressed
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.EXTERNAL_NOTIFICATIONS
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_external_notifications
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.MEDIA_INTEREST
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_media_interest
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.THE_ACCURACY_AND_CONTENT_OF_THE_DOCUMENT_IS
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_the_accuracy_and_content_of_the_document_is
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_B_QUESTIONS_11__16_TO
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_part_b_questions_11__16_to
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.PATIENT_CARER_AND_FAMILY
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_patient_carer_and_family
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.CONFIRM_A_DEDICATED_FAMILY_CONTACT_PERSON_HAS_BEEN
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_confirm_a_dedicated_family_contact_person_has_been
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.STAFF
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_staff
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.TYPE_OF_INVESTIGATION_PLANNED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_type_of_investigation_planned
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.ORGANISATION
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_organisation
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.OTHER_COMMENTS
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_other_comments
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.DATE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_date
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ERRORS.USER_CANNOT_REJECT
      type: 0
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_errors_user_cannot_reject
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.ERRORS.INVALID_STATUS
      type: 0
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_errors_invalid_status
  -
    fields:
      placeholder: REPORTABLE_INCIDENTS.BANNERS.LOCKED
      type: 0
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incidents_banners_locked
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.PRA_TEMPLATE_TITLE
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_pra_template_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.PRA_TEMPLATE_DESCRIPTION
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_pra_template_description
  -
    fields:
      placeholder: REPORTABLEINCIDENT_PRA.TEMPLATE_FILENAME
      domains:
        -
          domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_pra_template_filename
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.PRA_REQUIRED.YES
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_required_yes
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.PRA_REQUIRED.NO
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_required_no
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.PRA_REQUIRED.OTHER
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_required_other
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.PRA_STATUS.IN_PROGRESS
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_status_in_progress
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.PRA_STATUS.SENT_TO_CE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_status_sent_to_ce
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_REQUIRED.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_required_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_REQUIRED.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_required_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_STATUS.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_status_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_STATUS.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_status_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_DATE_REPORT_SENT.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_date_report_sent_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_DATE_REPORT_SENT.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_date_report_sent_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_MEETING_DATE.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_meeting_date_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.PRA_MEETING_DATE.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incidents_pra_meeting_date_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_NAME.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_name_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_NAME.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_name_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_NUMBER.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_number_title
  - fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_NUMBER.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_number_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_EMAIL.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_email_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_CONTACT_EMAIL.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_contact_email_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_DATE_APPROVED_PART_A.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_date_approved_part_a_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_DATE_APPROVED_PART_A.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_date_approved_part_a_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_DATE_APPROVED_PART_AB.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_date_approved_part_ab_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_DATE_APPROVED_PART_AB.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_date_approved_part_ab_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_A_SUBMISSION_TYPE.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_a_submission_type_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_A_SUBMISSION_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_a_submission_type_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_REPORTING_LOWER_HARM.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_reporting_lower_harm_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_REPORTING_LOWER_HARM.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_reporting_lower_harm_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.READY_FOR_RIB_PART_B.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_ready_for_rib_part_b_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.READY_FOR_RIB_PART_B.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_ready_for_rib_part_b_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TYPE_OF_REVIEW_PLANNED.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_type_of_review_planned_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TYPE_OF_REVIEW_PLANNED.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_type_of_review_planned_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_B_FINALISED.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_b_finalised_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_B_FINALISED.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_b_finalised_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_AB_SUBMISSION_TYPE.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_ab_submission_type_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.RIB_PART_AB_SUBMISSION_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_rib_part_ab_submission_type_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_RIB_PART_A.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_rib_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_RIB_PART_A.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_rib_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.DELEGATE_APPROVER_PART_A.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_delegate_approver_part_a_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.DELEGATE_APPROVER_PART_A.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_delegate_approver_part_a_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TYPE_OF_REVIEW_PLANNED_OTHER_OR_NR.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_type_of_review_planned_other_or_nr_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TYPE_OF_REVIEW_PLANNED_OTHER_OR_NR.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_type_of_review_planned_other_or_nr_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_RIB_PART_AB_UPDATE.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_rib_part_ab_update_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.REASON_FOR_RIB_PART_AB_UPDATE.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_reason_for_rib_part_ab_update_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.CE_DELEGATE_APPROVER_PART_AB.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_delegate_approver_part_ab_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.CE_DELEGATE_APPROVER_PART_AB.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_delegate_approver_part_ab_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TIME_PRA_SENT_TO_CE.TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_time_pra_sent_to_ce_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.MAIN_FORM.TIME_PRA_SENT_TO_CE.LABEL
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_main_form_time_pra_sent_to_ce_label
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_description
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_IS_PRELIMINARY_RISK_REQUIRED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_is_preliminary_risk_required
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_SUBMISSION_OR_UPDATE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_submission_or_update
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_REASON_FOR_UPDATE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_reason_for_update
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_RESPONSES_CAN_BE_NA
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_responses_can_be_na
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_DATE_AND_TIME_OF_PRA_OR_SAFETY_CHECK
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_date_and_time_of_pra_or_safety_check
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_PATIENT_CARER_FAMILY
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_patient_carer_family
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_CONFIRM_FAMILY_CONTACT
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_confirm_family_contact
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_TYPE_OF_REVIEW_PLANNED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_type_of_review_planned
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_STATEMENT
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_statement
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_STATEMENT_ACCURACY
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_statement_accuracy
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.RIB_AB_TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_template_rib_ab_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.RIB_AB_DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_template_rib_ab_description
  -
    fields:
      placeholder: REPORTABLEINCIDENT_RIB_AB.TEMPLATE_FILENAME
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_rib_ab_template_filename
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_PART_AB_TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_rib_part_ab_title
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.ORGANISATIONAL_RISKS
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_organisational_risks
  -
    fields:
      placeholder: REPORTABLE_INCIDENT_DOCUMENT.TIME
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: placeholder_reportable_incident_document_time
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.PART_A_TEMPLATE_TITLE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_part_a_template_title
  -
    fields:
      placeholder: REPORTABLE_INCIDENT.TEMPLATE.PART_A_TEMPLATE_DESCRIPTION
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_part_a_template_description
  -
    fields:
      placeholder: REPORTABLEINCIDENT_PART_A.TEMPLATE_FILENAME
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_template_part_a_template_filename
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_TEMPLATE_TITLE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_title
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_TEMPLATE_DESCRIPTION
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_description
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_INCIDENT_NUMBER
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_incident_number
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_SEVERITY_ASSESSMENT_CODE
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_severity_assessment_code
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_IF_NOT_HARM_SCORE_1_WHAT_ARE_THE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_if_not_harm_score_1_what_are_the
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_ORIGINAL_SUBMISSION
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_original_submission
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_ORIGINAL_SUBMISSION_UPDATE
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_original_submission_update
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_CLINICAL_INCIDENTS_STATEMENT
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_clinical_incidents_statement
  - fields:
      placeholder: REPORTABLE_INCIDENT.DOCUMENT.RIB_NEW_PART_A_THE_ACCURACY_AND_CONTDENT_IS_ENDORSED
      type: 0
      domains:
        - domain: '@domain_reportable_incidents'
    ref: reportable_incident_document_rib_new_part_a_the_accuracy_and_content_is_endorsed

