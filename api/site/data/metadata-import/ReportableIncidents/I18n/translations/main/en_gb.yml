entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@reportable_incidents_module_title'
      language: '@language_en_gb'
      value: Reportable Incident Briefs
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_back_to_dashboard'
      language: '@language_en_gb'
      value: Back to Dashboard
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_all_ribs'
      language: '@language_en_gb'
      value: All RIBs
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_in_progress'
      language: '@language_en_gb'
      value: In Progress
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_submitted_to_moh'
      language: '@language_en_gb'
      value: Submitted to MoH
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_finalised'
      language: '@language_en_gb'
      value: Finalised
  -
    fields:
      placeholder: '@reportable_incidents_sidebar_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@reportable_incidents_label_singular'
      language: '@language_en_gb'
      value: Reportable Incident Brief
  -
    fields:
      placeholder: '@reportable_incidents_label_plural'
      language: '@language_en_gb'
      value: Reportable Incident Briefs
  -
    fields:
      placeholder: '@reportable_incidents_columns_rib_id'
      language: '@language_en_gb'
      value: 'RIB #'
  -
    fields:
      placeholder: '@reportable_incidents_columns_moh_rib_number'
      language: '@language_en_gb'
      value: MoH RIB Number
  -
    fields:
      placeholder: '@reportable_incidents_columns_source_record_ref'
      language: '@language_en_gb'
      value: Source Record Ref
  -
    fields:
      placeholder: '@reportable_incidents_columns_columns_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@reportable_incidents_columns_columns_sac_score'
      language: '@language_en_gb'
      value: Harm Score
  -
    fields:
      placeholder: '@reportable_incidents_columns_columns_pra_required'
      language: '@language_en_gb'
      value: PRA or Safety Check?
  -
    fields:
      placeholder: '@reportable_incidents_columns_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_draft'
      language: '@language_en_gb'
      value: Draft
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_finalised'
      language: '@language_en_gb'
      value: Finalised
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_in_progress'
      language: '@language_en_gb'
      value: In Progress
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@reportable_incidents_columns_status_submitted'
      language: '@language_en_gb'
      value: Submitted
  -
    fields:
      placeholder: '@reportable_incidents_columns_owner'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@reportable_incidents_columns_created_at'
      language: '@language_en_gb'
      value: Created At
  -
    fields:
      placeholder: '@reportable_incidents_columns_location'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@reportable_incidents_columns_reason_for_reporting'
      language: '@language_en_gb'
      value: Reason for Reporting
  -
    fields:
      placeholder: '@reportable_incidents_loading_reportable_incidents'
      language: '@language_en_gb'
      value: Loading Reportable Incidents
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_heading'
      language: '@language_en_gb'
      value: 'RIB #{{id}}'
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_details'
      language: '@language_en_gb'
      value: Details
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_access_control'
      language: '@language_en_gb'
      value: Access Control
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_my_actions'
      language: '@language_en_gb'
      value: My Actions
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_all_actions'
      language: '@language_en_gb'
      value: All Actions
  -
    fields:
      placeholder: '@reportable_incidents_edit_sidebar_attachments'
      language: '@language_en_gb'
      value: Attachments
  -
    fields:
      placeholder: '@reportable_incidents_option_maternal_death'
      language: '@language_en_gb'
      value: Maternal Death
  -
    fields:
      placeholder: '@reportable_incidents_option_safe_work'
      language: '@language_en_gb'
      value: SafeWork NSW
  -
    fields:
      placeholder: '@reportable_incidents_option_chasm'
      language: '@language_en_gb'
      value: CHASM
  -
    fields:
      placeholder: '@reportable_incidents_option_scidua'
      language: '@language_en_gb'
      value: SCIDUA
  -
    fields:
      placeholder: '@reportable_incidents_option_media_team'
      language: '@language_en_gb'
      value: Media Team notification
  -
    fields:
      placeholder: '@reportable_incidents_option_other'
      language: '@language_en_gb'
      value: Other
  -
    fields:
      placeholder: '@reportable_incidents_option_state_wide'
      language: '@language_en_gb'
      value: Possible Statewide Implication
  -
    fields:
      placeholder: '@reportable_incidents_option_legal'
      language: '@language_en_gb'
      value: Legal Significance
  -
    fields:
      placeholder: '@reportable_incidents_option_sac2_4'
      language: '@language_en_gb'
      value: SAC2-4 Privileged Investigation
  -
    fields:
      placeholder: '@reportable_incidents_option_required'
      language: '@language_en_gb'
      value: Required by policy
  -
    fields:
      placeholder: '@reportable_incidents_option_perinatal'
      language: '@language_en_gb'
      value: Perinatal
  -
    fields:
      placeholder: '@reportable_incidents_section_main'
      language: '@language_en_gb'
      value: Details
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_rib_number'
      language: '@language_en_gb'
      value: MoH RIB No.
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_rib_number_label'
      language: '@language_en_gb'
      value: MoH RIB No.
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_owner'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_owner_label'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_disclosure_commenced'
      language: '@language_en_gb'
      value: Disclosure Process Commenced?
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_disclosure_commenced_label'
      language: '@language_en_gb'
      value: Disclosure Process Commenced?
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_created_date'
      language: '@language_en_gb'
      value: Created Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_created_date_label'
      language: '@language_en_gb'
      value: Created Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_reporting_reason'
      language: '@language_en_gb'
      value: Reason for Reporting
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_reporting_reason_label'
      language: '@language_en_gb'
      value: Reason for Reporting
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_disclosure_reason'
      language: '@language_en_gb'
      value: Reason for not commencing open disclosure
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_disclosure_reason_label'
      language: '@language_en_gb'
      value: Reason for not commencing open disclosure
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_action_taken'
      language: '@language_en_gb'
      value: Action Taken (summary)
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_action_taken_label'
      language: '@language_en_gb'
      value: Action Taken (summary)
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_status_label'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_rejection_reason'
      language: '@language_en_gb'
      value: Reason For Rejection
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_rejection_reason_label'
      language: '@language_en_gb'
      value: Reason For Rejection
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_further_action'
      language: '@language_en_gb'
      value: Further planned action
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_further_action_label'
      language: '@language_en_gb'
      value: Further planned action
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_agency_referral'
      language: '@language_en_gb'
      value: Referral to other agencies
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_agency_referral_label'
      language: '@language_en_gb'
      value: Referral to other agencies
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_agency_referral'
      language: '@language_en_gb'
      value: Other referred agencies
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_agency_referral_label'
      language: '@language_en_gb'
      value: Other referred agencies
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_reported_reason'
      language: '@language_en_gb'
      value: What are the reasons this incident is being reported?
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_reported_reason_label'
      language: '@language_en_gb'
      value: What are the reasons this incident is being reported?
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_sac_score'
      language: '@language_en_gb'
      value: SAC Score
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_sac_score_label'
      language: '@language_en_gb'
      value: SAC Score
  -
    fields:
      placeholder: '@reportable_incidents_message_reportable_incident_saved'
      language: '@language_en_gb'
      value: Reportable Incident Brief Saved Successfully
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_type'
      language: '@language_en_gb'
      value: Record Source Type
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_type_label'
      language: '@language_en_gb'
      value: Record Source Type
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_id'
      language: '@language_en_gb'
      value: Record Source ID
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_id_label'
      language: '@language_en_gb'
      value: Record Source ID
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_ref'
      language: '@language_en_gb'
      value: Record Source Ref
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_source_ref_label'
      language: '@language_en_gb'
      value: Record Source Ref
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_location'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_location_label'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_location'
      language: '@language_en_gb'
      value: Other Location
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_location_label'
      language: '@language_en_gb'
      value: Other Location
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_service'
      language: '@language_en_gb'
      value: Service
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_service_label'
      language: '@language_en_gb'
      value: Service
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_service'
      language: '@language_en_gb'
      value: Other Service
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_other_service_label'
      language: '@language_en_gb'
      value: Other Service
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_type'
      language: '@language_en_gb'
      value: Record Source Type
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_type_label'
      language: '@language_en_gb'
      value: Record Source Type
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_category'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_category_label'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_date'
      language: '@language_en_gb'
      value: Incident Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_date_label'
      language: '@language_en_gb'
      value: Incident Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_time'
      language: '@language_en_gb'
      value: Incident Time
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_time_label'
      language: '@language_en_gb'
      value: Incident Time
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_summary'
      language: '@language_en_gb'
      value: Incident Summary
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_incident_summary_label'
      language: '@language_en_gb'
      value: Incident Summary
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_reported_date'
      language: '@language_en_gb'
      value: Reported Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_reported_date_label'
      language: '@language_en_gb'
      value: Reported Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_commencement_date'
      language: '@language_en_gb'
      value: Commencement Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_commencement_date_label'
      language: '@language_en_gb'
      value: Commencement Date
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_event_synopsis_title'
      language: '@language_en_gb'
      value: Synopsis of the Event
  -
    fields:
      placeholder: '@reportable_incidents_main_form_field_event_synopsis_label'
      language: '@language_en_gb'
      value: Synopsis of the Event
  -
    fields:
      placeholder: '@reportable_incidents_main_form_section_source_details'
      language: '@language_en_gb'
      value: Source Details
  -
    fields:
      placeholder: '@reportable_incidents_status_draft'
      language: '@language_en_gb'
      value: Draft
  -
    fields:
      placeholder: '@reportable_incidents_status_in_progress'
      language: '@language_en_gb'
      value: In Progress
  -
    fields:
      placeholder: '@reportable_incidents_status_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@reportable_incidents_status_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@reportable_incidents_status_submitted'
      language: '@language_en_gb'
      value: Submitted
  -
    fields:
      placeholder: '@reportable_incidents_status_finalised'
      language: '@language_en_gb'
      value: Finalised
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_title'
      language: '@language_en_gb'
      value: Filter Reportable Incident Briefs
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_rib_id'
      language: '@language_en_gb'
      value: RIB ID
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_rib_id_label'
      language: '@language_en_gb'
      value: RIB ID
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_rib_number'
      language: '@language_en_gb'
      value: RIB Number
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_rib_number_label'
      language: '@language_en_gb'
      value: RIB Number
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_created_at'
      language: '@language_en_gb'
      value: Created Date
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_created_at_label'
      language: '@language_en_gb'
      value: Created Date
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_reporting_reason'
      language: '@language_en_gb'
      value: Reason For Reporting
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_reporting_reason_label'
      language: '@language_en_gb'
      value: Reason For Reporting
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_status_label'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_owner'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_owner_label'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_location'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_location_label'
      language: '@language_en_gb'
      value: Location
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_source_type'
      language: '@language_en_gb'
      value: Source Record Type
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_source_type_label'
      language: '@language_en_gb'
      value: Source Record Type
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_source_ref'
      language: '@language_en_gb'
      value: Source Record Ref
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_source_ref_label'
      language: '@language_en_gb'
      value: Source Record Ref
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_category'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_category_label'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_sac_score'
      language: '@language_en_gb'
      value: SAC Score
  -
    fields:
      placeholder: '@reportable_incidents_filter_form_field_sac_score_label'
      language: '@language_en_gb'
      value: Sac Score
  -
    fields:
      placeholder: '@reportable_incidents_action_form_type_title'
      language: '@language_en_gb'
      value: Reportable Incidents Form Type
  -
    fields:
      placeholder: '@reportable_incidents_action_form_title'
      language: '@language_en_gb'
      value: Reportable Incidents Action
  -
    fields:
      placeholder: '@reportable_incidents_form_type_filter'
      language: '@language_en_gb'
      value: Reportable Incidents Filter
  -
    fields:
      placeholder: '@reportable_incidents_form_main_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief Form
  -
    fields:
      placeholder: '@reportable_incidents_success_attachment_save'
      language: '@language_en_gb'
      value: Attachment saved successfully
  -
    fields:
      placeholder: '@reportable_incidents_attachments_no_attachments'
      language: '@language_en_gb'
      value: This record has no attachments
  -
    fields:
      placeholder: '@reportable_incident_contact_role_title'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@reportable_incident_contact_role_label'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@reportable_incident_contact_role_default'
      language: '@language_en_gb'
      value: Select a Role
  -
    fields:
      placeholder: '@reportable_incident_contact_role_rib_contact'
      language: '@language_en_gb'
      value: RIB Contact
  -
    fields:
      placeholder: '@reportable_incident_contact_role_investigation_lead'
      language: '@language_en_gb'
      value: Investigation Lead
  -
    fields:
      placeholder: '@reportable_incident_contact_role_person_involved'
      language: '@language_en_gb'
      value: Person Involved
  -
    fields:
      placeholder: '@reportable_incidents_rejected_notice'
      language: '@language_en_gb'
      value: 'This Reportable Incident was rejected by {{user}} on {{date}}'
  -
    fields:
      placeholder: '@reportable_incidents_errors_cannot_remove_owner'
      language: '@language_en_gb'
      value: 'A Reportable Incident owner cannot be removed'
  -
    fields:
      placeholder: '@reportable_incidents_main_form_type_main'
      language: '@language_en_gb'
      value: Reportable Incident Brief Form
  - fields:
      placeholder: '@reportable_incident_success_template_document_attached'
      language: '@language_en_gb'
      value: Successfully attached document from template
  - fields:
      placeholder: '@reportable_incident_document_reportable_incident_brief'
      language: '@language_en_gb'
      value: Reportable Incident Brief
  - fields:
      placeholder: '@reportable_incident_document_source_record_id'
      language: '@language_en_gb'
      value: Source Record Id
  - fields:
      placeholder: '@reportable_incident_document_rib_record_id'
      language: '@language_en_gb'
      value: RIB Record id
  - fields:
      placeholder: '@reportable_incident_document_moh_rib_no'
      language: '@language_en_gb'
      value: MoH RIB No
  - fields:
      placeholder: '@reportable_incident_document_severity_assessment_code'
      language: '@language_en_gb'
      value:  Incident harm score
  - fields:
      placeholder: '@reportable_incident_document_this_incident_is'
      language: '@language_en_gb'
      value: This incident is about
  - fields:
      placeholder: '@reportable_incident_document_if_not_sac_reasons'
      language: '@language_en_gb'
      value: IF NOT SAC what are the reasons this incident is being reported
  - fields:
      placeholder: '@reportable_incident_document_date_of_incident_ioms'
      language: '@language_en_gb'
      value: Date of incident notification
  - fields:
      placeholder: '@reportable_incident_document_principal_incident_type'
      language: '@language_en_gb'
      value: Principal incident type
  - fields:
      placeholder: '@reportable_incident_document_date_of_incident'
      language: '@language_en_gb'
      value: Date of actual incident
  - fields:
      placeholder: '@reportable_incident_document_time_of_incident'
      language: '@language_en_gb'
      value: Time of incident
  - fields:
      placeholder: '@reportable_incident_document_location'
      language: '@language_en_gb'
      value: Location
  - fields:
      placeholder: '@reportable_incident_document_specific_service'
      language: '@language_en_gb'
      value: Specific Service
  - fields:
      placeholder: '@reportable_incident_document_reason_for_reporting'
      language: '@language_en_gb'
      value: Reason for reporting
  - fields:
      placeholder: '@reportable_incident_document_synopsis_of_the_event'
      language: '@language_en_gb'
      value: Synopsis of the Event
  - fields:
      placeholder: '@reportable_incident_document_action_taken'
      language: '@language_en_gb'
      value: Action Taken
  - fields:
      placeholder: '@reportable_incident_document_has_open_disclose_process_commenced'
      language: '@language_en_gb'
      value: Has the open disclosure process commenced?
  - fields:
      placeholder: '@reportable_incident_document_if_no_please_comment'
      language: '@language_en_gb'
      value: If no, please comment
  - fields:
      placeholder: '@reportable_incident_document_date_initial_open_disclosure_commenced'
      language: '@language_en_gb'
      value: Date initial open disclosure commenced
  - fields:
      placeholder: '@reportable_incident_document_further_planned_action'
      language: '@language_en_gb'
      value: Further Planned Action
  - fields:
      placeholder: '@reportable_incident_document_type_of_investigation'
      language: '@language_en_gb'
      value: Type of investigation
  - fields:
      placeholder: '@reportable_incident_document_referral_other_agencies'
      language: '@language_en_gb'
      value: 'If other, please specify:'
  - fields:
      placeholder: '@reportable_incident_document_contact_name'
      language: '@language_en_gb'
      value: Contact Name
  - fields:
      placeholder: '@reportable_incident_document_position'
      language: '@language_en_gb'
      value: Position
  - fields:
      placeholder: '@reportable_incident_document_contact_number'
      language: '@language_en_gb'
      value: Contact Number
  - fields:
      placeholder: '@reportable_incident_document_email_address'
      language: '@language_en_gb'
      value: Email Address
  - fields:
      placeholder: '@reportable_incident_document_clinical_incidents_statement_1'
      language: '@language_en_gb'
      value: All clinical incidents that are rated as SAC1 require an approved investigation. The investigation findings report is to be provided to the Ministry of Health within 35 days of the incident being notified in ims+.
  - fields:
      placeholder: '@reportable_incident_document_clinical_incidents_statement_2'
      language: '@language_en_gb'
      value: Clinical incident RIBs and any other documents created for or by the CRAG are created for the purpose of authorised investigation and research and are privileged under the NSW Health Administration Act 1982. The copying or distribution of the document or any information it may contain without express permission of the Minister is prohibited. For authorised use and disclosure see PD 2019_034 and CRAG protocol.
  - fields:
      placeholder: '@reportable_incident_document_person_to_be_responsible_for_investigation'
      language: '@language_en_gb'
      value: Person to be responsible for Investigation
  - fields:
      placeholder: '@reportable_incident_document_contact_no'
      language: '@language_en_gb'
      value: Contact no
  - fields:
      placeholder: '@reportable_incident_document_reportable_incident_has_been_approved'
      language: '@language_en_gb'
      value: This Reportable Incident Brief has been approved for transmission by <span class="bold">{{ approvedByName }}</span> on <span class="bold">{{ approvedOnDate }}</span>
  - fields:
      placeholder: '@reportable_incident_document_the_accuracy_and_content_is_endorsed'
      language: '@language_en_gb'
      value: The accuracy and content of the document is endorsed. See Reportable Incident Brief Procedure.
  - fields:
      placeholder: '@reportable_incident_template_reportable_incident_brief_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief.
  - fields:
      placeholder: '@reportable_incident_template_reportable_incident_brief_description'
      language: '@language_en_gb'
      value: Use this template to create a new Reportable Incident Brief.
  - fields:
      placeholder: '@reportable_incident_template_reportable_incident_brief_filename'
      language: '@language_en_gb'
      value: reportable-incident-brief
  - fields:
      placeholder: '@reportable_incident_document_rib_part_a_questions_1__10_to'
      language: '@language_en_gb'
      value: 'RIB Part A (Questions 1 - 10) to be sent to Chief Executive (CE) endorsement and submission to the Ministry of Health (MoH) within 24 hours of incident notification.'
  - fields:
      placeholder: '@reportable_incident_document_if_not_harm_score_1_what_are_the'
      language: '@language_en_gb'
      value: 'If not harm score 1, what are the reasons this is being reported'
  - fields:
      placeholder: '@reportable_incident_document_where_and_when_did_the_incident_happen'
      language: '@language_en_gb'
      value: 'Where and when did the incident happen?'
  - fields:
      placeholder: '@reportable_incident_document_what_happened_what_actions_were_taken_in_response'
      language: '@language_en_gb'
      value: 'What happened? What actions were taken in response?'
  - fields:
      placeholder: '@reportable_incident_document_has_bclinical_disclosureb_been_initiated'
      language: '@language_en_gb'
      value: 'Has <b><u>Clinician Disclosure</u></b> been initiated:'
  - fields:
      placeholder: '@reportable_incident_document_any_other_immediate_concernsrisks_addressed'
      language: '@language_en_gb'
      value: 'Any other immediate concerns/risks addressed'
  - fields:
      placeholder: '@reportable_incident_document_external_notifications'
      language: '@language_en_gb'
      value: 'External Notifications'
  - fields:
      placeholder: '@reportable_incident_document_media_interest'
      language: '@language_en_gb'
      value: 'Media Interest'
  - fields:
      placeholder: '@reportable_incident_document_the_accuracy_and_content_of_the_document_is'
      language: '@language_en_gb'
      value: 'The accuracy and content of the document is endorsed. See Reportable Incident Brief Procedure.'
  - fields:
      placeholder: '@reportable_incident_document_rib_part_b_questions_11__16_to'
      language: '@language_en_gb'
      value: 'RIB Part B (Questions 11 - 16) to be completed as and when requested by the Ministry of Health or the Clinical Excellence Commission.'
  - fields:
      placeholder: '@reportable_incident_document_patient_carer_and_family'
      language: '@language_en_gb'
      value: 'Patient, Carer and Family'
  - fields:
      placeholder: '@reportable_incident_document_confirm_a_dedicated_family_contact_person_has_been'
      language: '@language_en_gb'
      value: 'Confirm a dedicated family contact person has been appointed'
  - fields:
      placeholder: '@reportable_incident_document_staff'
      language: '@language_en_gb'
      value: 'Staff'
  - fields:
      placeholder: '@reportable_incident_document_type_of_investigation_planned'
      language: '@language_en_gb'
      value: 'Type of investigation planned'
  - fields:
      placeholder: '@reportable_incident_document_organisation'
      language: '@language_en_gb'
      value: 'Organisation'
  - fields:
      placeholder: '@reportable_incident_document_other_comments'
      language: '@language_en_gb'
      value: 'Other comments'
  - fields:
      placeholder: '@reportable_incident_document_date'
      language: '@language_en_gb'
      value: 'Date'
  - fields:
      placeholder: '@reportable_incidents_errors_user_cannot_reject'
      language: '@language_en_gb'
      value: 'Only the Reportable Incident owner can set the status to Rejected'
  - fields:
      placeholder: '@reportable_incidents_errors_invalid_status'
      language: '@language_en_gb'
      value: 'Invalid Status Transition'
  - fields:
      placeholder: '@reportable_incidents_banners_locked'
      language: '@language_en_gb'
      value: 'This record is locked by {{name}} since {{date}}'
  - fields:
      placeholder: '@reportable_incident_template_pra_template_title'
      language: '@language_en_gb'
      value: 'Preliminary Risk Assessment Report'
  - fields:
      placeholder: '@reportable_incident_template_pra_template_description'
      language: '@language_en_gb'
      value: 'Use this template to create a new PRA Report.'
  - fields:
      placeholder: '@reportable_incident_template_pra_template_filename'
      language: '@language_en_gb'
      value: 'pra-template'
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_required_yes'
      language: '@language_en_gb'
      value: Yes
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_required_no'
      language: '@language_en_gb'
      value: No
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_required_other'
      language: '@language_en_gb'
      value: Other
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_status_in_progress'
      language: '@language_en_gb'
      value: PRA In Progress
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_status_sent_to_ce'
      language: '@language_en_gb'
      value: PRA sent to CE
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_required_title'
      language: '@language_en_gb'
      value: PRA or Safety Check?
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_required_label'
      language: '@language_en_gb'
      value: PRA or Safety Check?
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_status_title'
      language: '@language_en_gb'
      value: PRA Status
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_status_label'
      language: '@language_en_gb'
      value: PRA Status
  - fields:
      placeholder: '@placeholder_reportable_incidents_date_report_sent_title'
      language: '@language_en_gb'
      value: Date PRA Sent to CE
  - fields:
      placeholder: '@placeholder_reportable_incidents_date_report_sent_label'
      language: '@language_en_gb'
      value: Date PRA Sent to CE
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_meeting_date_title'
      language: '@language_en_gb'
      value: PRA Meeting Date
  - fields:
      placeholder: '@placeholder_reportable_incidents_pra_meeting_date_label'
      language: '@language_en_gb'
      value: PRA Meeting Date
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_name_title'
      language: '@language_en_gb'
      value: Contact Name
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_name_label'
      language: '@language_en_gb'
      value: Contact Name
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_number_title'
      language: '@language_en_gb'
      value: Contact Number
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_number_label'
      language: '@language_en_gb'
      value: Contact Number
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_email_title'
      language: '@language_en_gb'
      value: Contact Email
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_contact_email_label'
      language: '@language_en_gb'
      value: Contact Email
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_date_approved_part_a_title'
      language: '@language_en_gb'
      value: Date Approved Part A
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_date_approved_part_a_label'
      language: '@language_en_gb'
      value: Date Approved Part A
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_date_approved_part_ab_title'
      language: '@language_en_gb'
      value: Date Approved Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_date_approved_part_ab_label'
      language: '@language_en_gb'
      value: Date Approved Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_a_submission_type_title'
      language: '@language_en_gb'
      value: RIB Part A Submission Type
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_a_submission_type_label'
      language: '@language_en_gb'
      value: RIB Part A Submission Type
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_reporting_lower_harm_title'
      language: '@language_en_gb'
      value: Reason for reporting lower harm
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_reporting_lower_harm_label'
      language: '@language_en_gb'
      value: Reason for reporting lower harm
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_ready_for_rib_part_b_title'
      language: '@language_en_gb'
      value: Ready for RIB Part B?
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_ready_for_rib_part_b_label'
      language: '@language_en_gb'
      value: Ready for RIB Part B?
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_type_of_review_planned_title'
      language: '@language_en_gb'
      value: Type of review planned
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_type_of_review_planned_label'
      language: '@language_en_gb'
      value: Type of review planned
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_b_finalised_title'
      language: '@language_en_gb'
      value: RIB Part B Finalised?
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_b_finalised_label'
      language: '@language_en_gb'
      value: RIB Part B Finalised?
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_ab_submission_type_title'
      language: '@language_en_gb'
      value: RIB Part AB Submission Type
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_rib_part_ab_submission_type_label'
      language: '@language_en_gb'
      value: RIB Part AB Submission Type
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_rib_title'
      language: '@language_en_gb'
      value: Reason for RIB Part A Update
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_rib_label'
      language: '@language_en_gb'
      value: Reason for RIB Part A Update
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_delegate_approver_part_a_title'
      language: '@language_en_gb'
      value: CE/Delegate Approver Part A
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_delegate_approver_part_a_label'
      language: '@language_en_gb'
      value: CE/Delegate Approver Part A
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_type_of_review_planned_other_or_nr_title'
      language: '@language_en_gb'
      value: Type of review planned Other or NR
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_type_of_review_planned_other_or_nr_label'
      language: '@language_en_gb'
      value: Type of review planned Other or NR
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_rib_part_ab_update_title'
      language: '@language_en_gb'
      value: Reason for RIB Part AB Update
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_reason_for_rib_part_ab_update_label'
      language: '@language_en_gb'
      value: Reason for RIB Part AB Update
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_delegate_approver_part_ab_title'
      language: '@language_en_gb'
      value: CE/Delegate Approver Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_delegate_approver_part_ab_label'
      language: '@language_en_gb'
      value: CE/Delegate Approver Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_time_pra_sent_to_ce_title'
      language: '@language_en_gb'
      value: Time PRA Sent to CE
  - fields:
      placeholder: '@placeholder_reportable_incident_main_form_time_pra_sent_to_ce_label'
      language: '@language_en_gb'
      value: Time PRA Sent to CE
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_description'
      language: '@language_en_gb'
      value: RIB Part B (mandatory) - Send to the Chief Executive or delegate for endorsement and submission to the Ministry of Health within 72 hours of incident notification. The Chief Executive or Ministry of Health may direct RIB Part B be submitted sooner for specific incidents.
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_submission_or_update'
      language: '@language_en_gb'
      value: Is this the original submission of RIB Part A+B or an update?
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_reason_for_update'
      language: '@language_en_gb'
      value: If update, provide reason, approver and date
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_is_preliminary_risk_required'
      language: '@language_en_gb'
      value: Is a preliminary risk assessment (PRA) or safety check required to be completed for the Chief Executive?
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_responses_can_be_na'
      language: '@language_en_gb'
      value: Responses to Part B can be <b>‘Not applicable’</b> if the Health Service is not undertaking a PRA or safety check.
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_date_and_time_of_pra_or_safety_check'
      language: '@language_en_gb'
      value: Date and time of completed preliminary risk assessment or safety check submission to Chief Executive
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_patient_carer_family'
      language: '@language_en_gb'
      value: Patient, Carer and Family
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_confirm_family_contact'
      language: '@language_en_gb'
      value: Confirm a dedicated family contact person has been assigned
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_statement'
      language: '@language_en_gb'
      value: Clinical incident RIBs and any other documents created for or by the Clinical Risk Action Group (CRAG) are created for the purpose of authorised investigation and research and are privileged under the NSW Health Administration Act 1982. The copying or distribution of the document or any information it may contain without express permission of the Minister is prohibited. For authorised use and disclosure see PD2020_047 and CRAG protocol.
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_statement_accuracy'
      language: '@language_en_gb'
      value: The accuracy and content of the document is endorsed. This Reportable Incident Brief has been approved for transmission by
  - fields:
      placeholder: '@placeholder_reportable_incident_template_rib_ab_title'
      language: '@language_en_gb'
      value: Reportable Incident Brief Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_template_rib_ab_description'
      language: '@language_en_gb'
      value: Use this template to create a new Reportable Incident Brief Part AB
  - fields:
      placeholder: '@placeholder_reportable_incident_rib_ab_template_filename'
      language: '@language_en_gb'
      value: reportable-incident-part-ab
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_type_of_review_planned'
      language: '@language_en_gb'
      value: Type of review planned
  - fields:
      placeholder: '@placeholder_reportable_incident_document_rib_part_ab_title'
      language: '@language_en_gb'
      value: Part B
  - fields:
      placeholder: '@placeholder_reportable_incident_organisational_risks'
      language: '@language_en_gb'
      value: Organisational risks
  - fields:
      placeholder: '@placeholder_reportable_incident_document_time'
      language: '@language_en_gb'
      value: Time
  - fields:
      placeholder: '@reportable_incident_template_part_a_template_title'
      language: '@language_en_gb'
      value: 'Reportable Incident Brief Part A.'
  - fields:
      placeholder: '@reportable_incident_template_part_a_template_description'
      language: '@language_en_gb'
      value: 'Use this template to create a new Reportable Incident Brief Part A.'
  - fields:
      placeholder: '@reportable_incident_template_part_a_template_filename'
      language: '@language_en_gb'
      value: 'reportable-incident-brief-part-a'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_title'
      language: '@language_en_gb'
      value: 'PART A'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_description'
      language: '@language_en_gb'
      value: 'Send RIB Part A to the Chief Executive or delegate for endorsement and submission to the Ministry of Health within 24 hours of incident notification.'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_incident_number'
      language: '@language_en_gb'
      value: 'Incident Number'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_severity_assessment_code'
      language: '@language_en_gb'
      value:  'Harm Score'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_if_not_harm_score_1_what_are_the'
      language: '@language_en_gb'
      value: 'If not Harm Score 1, indicate the reason this is being reported'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_original_submission'
      language: '@language_en_gb'
      value:  'Is this the original submission of RIB Part A or an update?'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_original_submission_update'
      language: '@language_en_gb'
      value:  'If update, provide reason, approver and date'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_clinical_incidents_statement'
      language: '@language_en_gb'
      value:  'Clinical incident RIBs and any other documents created for or by the Clinical Risk Action Group (CRAG) are created for the purpose of authorised investigation and research and are privileged under the NSW Health Administration Act 1982. The copying or distribution of the document or any information it may contain without express permission of the Minister is prohibited. For authorised use and disclosure see PD2020_047 and CRAG protocol.'
  - fields:
      placeholder: '@reportable_incident_document_rib_new_part_a_the_accuracy_and_content_is_endorsed'
      language: '@language_en_gb'
      value: 'The accuracy and content of the document is endorsed.'
