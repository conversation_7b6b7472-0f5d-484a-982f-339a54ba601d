entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@reportable_incidents_module_title', language: '@language_fr_ca', value: 'Dossiers d''incident à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_back_to_dashboard', language: '@language_fr_ca', value: 'Retour au tableau de bord' } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_all_ribs', language: '@language_fr_ca', value: 'Tous les RIB' } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_submitted_to_moh', language: '@language_fr_ca', value: '<PERSON><PERSON>s au MoH' } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_finalised', language: '@language_fr_ca', value: Finalisé } }
  - { fields: { placeholder: '@reportable_incidents_sidebar_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@reportable_incidents_label_singular', language: '@language_fr_ca', value: 'Résumé d''incident à signaler (RIB)' } }
  - { fields: { placeholder: '@reportable_incidents_label_plural', language: '@language_fr_ca', value: 'Dossiers d''incident à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_columns_moh_rib_number', language: '@language_fr_ca', value: 'Numéro de RIB du MoH' } }
  - { fields: { placeholder: '@reportable_incidents_columns_source_record_ref', language: '@language_fr_ca', value: 'Réf. de source enregistrée' } }
  - { fields: { placeholder: '@reportable_incidents_columns_columns_pra_required', language: '@language_fr_ca', value: 'PRA ou contrôle de sécurité?' } }
  - { fields: { placeholder: '@reportable_incidents_columns_columns_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@reportable_incidents_columns_columns_sac_score', language: '@language_fr_ca', value: 'Note SAC' } }
  - { fields: { placeholder: '@reportable_incidents_columns_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_draft', language: '@language_fr_ca', value: Ébauche } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_finalised', language: '@language_fr_ca', value: Finalisé } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@reportable_incidents_columns_status_submitted', language: '@language_fr_ca', value: Soumis } }
  - { fields: { placeholder: '@reportable_incidents_columns_owner', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@reportable_incidents_columns_created_at', language: '@language_fr_ca', value: 'Créé à' } }
  - { fields: { placeholder: '@reportable_incidents_columns_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incidents_columns_reason_for_reporting', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_loading_reportable_incidents', language: '@language_fr_ca', value: 'Chargement des incidents à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_heading', language: '@language_fr_ca', value: 'Nº de RIB {{id}}' } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@reportable_incidents_edit_sidebar_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@reportable_incidents_option_maternal_death', language: '@language_fr_ca', value: 'Mort maternelle' } }
  - { fields: { placeholder: '@reportable_incidents_option_safe_work', language: '@language_fr_ca', value: 'SafeWork NSW' } }
  - { fields: { placeholder: '@reportable_incidents_option_chasm', language: '@language_fr_ca', value: CHASM } }
  - { fields: { placeholder: '@reportable_incidents_option_scidua', language: '@language_fr_ca', value: SCIDUA } }
  - { fields: { placeholder: '@reportable_incidents_option_media_team', language: '@language_fr_ca', value: 'Avis à l''équipe des médias' } }
  - { fields: { placeholder: '@reportable_incidents_option_perinatal', language: '@language_fr_ca', value: Périnatale } }
  - { fields: { placeholder: '@reportable_incidents_option_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@reportable_incidents_option_state_wide', language: '@language_fr_ca', value: 'Implication possible à l''échelle nationale' } }
  - { fields: { placeholder: '@reportable_incidents_option_legal', language: '@language_fr_ca', value: 'Signification juridique' } }
  - { fields: { placeholder: '@reportable_incidents_option_sac2_4', language: '@language_fr_ca', value: 'Enquête privilégiée SAC2-4' } }
  - { fields: { placeholder: '@reportable_incidents_option_required', language: '@language_fr_ca', value: 'Requis par la politique' } }
  - { fields: { placeholder: '@reportable_incidents_section_main', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_rib_number', language: '@language_fr_ca', value: 'Nº de RIB du MoH' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_rib_number_label', language: '@language_fr_ca', value: 'Nº de RIB du MoH' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_owner', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_owner_label', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_commenced', language: '@language_fr_ca', value: 'Procédure de divulgation commencée?' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_commenced_label', language: '@language_fr_ca', value: 'Procédure de divulgation commencée?' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_created_date', language: '@language_fr_ca', value: 'Date de création' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_created_date_label', language: '@language_fr_ca', value: 'Date de création' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_reporting_reason', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_reporting_reason_label', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_reason', language: '@language_fr_ca', value: 'Motif pour ne pas commencer la divulgation ouverte' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_reason_label', language: '@language_fr_ca', value: 'Motif pour ne pas commencer la divulgation ouverte' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_action_taken', language: '@language_fr_ca', value: 'Action entreprise (en résumé)' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_action_taken_label', language: '@language_fr_ca', value: 'Action entreprise (en résumé)' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_further_action', language: '@language_fr_ca', value: 'Action supplémentaire prévue' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_further_action_label', language: '@language_fr_ca', value: 'Action supplémentaire prévue' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_agency_referral', language: '@language_fr_ca', value: 'Renvoi vers d''autres agences' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_agency_referral_label', language: '@language_fr_ca', value: 'Renvoi vers d''autres agences' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_agency_referral', language: '@language_fr_ca', value: 'Autres agences référées' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_agency_referral_label', language: '@language_fr_ca', value: 'Autres agences référées' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_reported_reason', language: '@language_fr_ca', value: 'Quelles sont les raisons pour lesquelles cet incident est signalé?' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_reported_reason_label', language: '@language_fr_ca', value: 'Quelles sont les raisons pour lesquelles cet incident est signalé?' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_sac_score', language: '@language_fr_ca', value: 'Note SAC' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_sac_score_label', language: '@language_fr_ca', value: 'Note SAC' } }
  - { fields: { placeholder: '@reportable_incidents_message_reportable_incident_saved', language: '@language_fr_ca', value: 'Résumé d''incident à signaler enregistré avec succès' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_rejection_reason', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_rejection_reason_label', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_type_main', language: '@language_fr_ca', value: 'Formulaire de résumé d''incident à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_type', language: '@language_fr_ca', value: 'Type de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_type_label', language: '@language_fr_ca', value: 'Type de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_id', language: '@language_fr_ca', value: 'ID de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_id_label', language: '@language_fr_ca', value: 'ID de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_ref', language: '@language_fr_ca', value: 'Réf. de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_source_ref_label', language: '@language_fr_ca', value: 'Réf. de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_location_label', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_location', language: '@language_fr_ca', value: 'Autre emplacement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_location_label', language: '@language_fr_ca', value: 'Autre emplacement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_service', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_service_label', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_service', language: '@language_fr_ca', value: 'Autre service' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_other_service_label', language: '@language_fr_ca', value: 'Autre service' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_type', language: '@language_fr_ca', value: 'Type de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_type_label', language: '@language_fr_ca', value: 'Type de source du dossier' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_date', language: '@language_fr_ca', value: 'Date de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_date_label', language: '@language_fr_ca', value: 'Date de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_time', language: '@language_fr_ca', value: 'Heure de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_time_label', language: '@language_fr_ca', value: 'Heure de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_summary', language: '@language_fr_ca', value: 'Sommaire de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_summary_label', language: '@language_fr_ca', value: 'Sommaire de l''incident' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_reported_date', language: '@language_fr_ca', value: 'Date du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_reported_date_label', language: '@language_fr_ca', value: 'Date du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_commencement_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_commencement_date_label', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_event_synopsis_title', language: '@language_fr_ca', value: 'Synoptique de l''événement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_field_event_synopsis_label', language: '@language_fr_ca', value: 'Synoptique de l''événement' } }
  - { fields: { placeholder: '@reportable_incidents_main_form_section_source_details', language: '@language_fr_ca', value: 'Détails de la source' } }
  - { fields: { placeholder: '@reportable_incidents_status_draft', language: '@language_fr_ca', value: Ébauche } }
  - { fields: { placeholder: '@reportable_incidents_status_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@reportable_incidents_status_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@reportable_incidents_status_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@reportable_incidents_status_submitted', language: '@language_fr_ca', value: Soumis } }
  - { fields: { placeholder: '@reportable_incidents_status_finalised', language: '@language_fr_ca', value: Finalisé } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_title', language: '@language_fr_ca', value: 'Filtrer les résumés d''incident à signaler (RIB)' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_id', language: '@language_fr_ca', value: 'ID de RIB' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_id_label', language: '@language_fr_ca', value: 'ID de RIB' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_number', language: '@language_fr_ca', value: 'Numéro de RIB' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_number_label', language: '@language_fr_ca', value: 'Numéro de RIB' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_created_at', language: '@language_fr_ca', value: 'Date de création' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_created_at_label', language: '@language_fr_ca', value: 'Date de création' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_reporting_reason', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_reporting_reason_label', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_owner', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_owner_label', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_location_label', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_type', language: '@language_fr_ca', value: 'Type de dossier source' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_type_label', language: '@language_fr_ca', value: 'Type de dossier source' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_ref', language: '@language_fr_ca', value: 'Réf. de dossier source' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_ref_label', language: '@language_fr_ca', value: 'Réf. de dossier source' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_sac_score', language: '@language_fr_ca', value: 'Note SAC' } }
  - { fields: { placeholder: '@reportable_incidents_filter_form_field_sac_score_label', language: '@language_fr_ca', value: 'Score SAC' } }
  - { fields: { placeholder: '@reportable_incidents_action_form_type_title', language: '@language_fr_ca', value: 'Type de formulaire d''incidents à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_action_form_title', language: '@language_fr_ca', value: 'Action d''incidents à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_form_main_title', language: '@language_fr_ca', value: 'Formulaire de résumé d''incident à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_form_type_filter', language: '@language_fr_ca', value: 'Filtre d''incidents à signaler' } }
  - { fields: { placeholder: '@reportable_incidents_success_attachment_save', language: '@language_fr_ca', value: 'Pièce jointe enregistrée avec succès' } }
  - { fields: { placeholder: '@reportable_incidents_attachments_no_attachments', language: '@language_fr_ca', value: 'Ce dossier n''a pas de pièce jointe' } }
  - { fields: { placeholder: '@reportable_incident_contact_role_title', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@reportable_incident_contact_role_label', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@reportable_incident_contact_role_default', language: '@language_fr_ca', value: 'Sélectionnez au rôle' } }
  - { fields: { placeholder: '@reportable_incident_contact_role_rib_contact', language: '@language_fr_ca', value: 'Contact associé au RIB' } }
  - { fields: { placeholder: '@reportable_incident_contact_role_investigation_lead', language: '@language_fr_ca', value: 'Responsable de l''enquête' } }
  - { fields: { placeholder: '@reportable_incident_contact_role_person_involved', language: '@language_fr_ca', value: 'Personne impliquée' } }
  - { fields: { placeholder: '@reportable_incidents_rejected_notice', language: '@language_fr_ca', value: 'Cet incident à signaler a été rejeté par {{user}} le {{date}}' } }
  - { fields: { placeholder: '@reportable_incidents_errors_cannot_remove_owner', language: '@language_fr_ca', value: 'Le titulaire d''incident à signaler ne peut pas être supprimé' } }
  - { fields: { placeholder: '@reportable_incident_success_template_document_attached', language: '@language_fr_ca', value: 'Document joint avec succès à partir du modèle' } }
  - { fields: { placeholder: '@reportable_incident_document_reportable_incident_brief', language: '@language_fr_ca', value: 'Résumé d''incident à signaler (RIB)' } }
  - { fields: { placeholder: '@reportable_incident_document_source_record_id', language: '@language_fr_ca', value: 'Id de dossier source' } }
  - { fields: { placeholder: '@reportable_incident_document_rib_record_id', language: '@language_fr_ca', value: 'Id de dossier de RIB' } }
  - { fields: { placeholder: '@reportable_incident_document_moh_rib_no', language: '@language_fr_ca', value: 'Nº RIB MoH' } }
  - { fields: { placeholder: '@reportable_incident_document_severity_assessment_code', language: '@language_fr_ca', value: 'Score des dommages liés à l''incident' } }
  - { fields: { placeholder: '@reportable_incident_document_this_incident_is', language: '@language_fr_ca', value: 'Cet incident concerne' } }
  - { fields: { placeholder: '@reportable_incident_document_if_not_sac_reasons', language: '@language_fr_ca', value: 'SI PAS SAC, quelles sont les motifs pour lesquels cet incident est signalé' } }
  - { fields: { placeholder: '@reportable_incident_document_date_of_incident_ioms', language: '@language_fr_ca', value: 'Date de l''avis d''incident' } }
  - { fields: { placeholder: '@reportable_incident_document_principal_incident_type', language: '@language_fr_ca', value: 'Type d''incident principal' } }
  - { fields: { placeholder: '@reportable_incident_document_time_of_incident', language: '@language_fr_ca', value: 'Heure de l''incident' } }
  - { fields: { placeholder: '@reportable_incident_document_date_of_incident', language: '@language_fr_ca', value: 'Date de l''incident réel' } }
  - { fields: { placeholder: '@reportable_incident_document_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@reportable_incident_document_specific_service', language: '@language_fr_ca', value: 'Service spécifique' } }
  - { fields: { placeholder: '@reportable_incident_document_reason_for_reporting', language: '@language_fr_ca', value: 'Motif du signalement' } }
  - { fields: { placeholder: '@reportable_incident_document_synopsis_of_the_event', language: '@language_fr_ca', value: 'Synoptique de l''événement' } }
  - { fields: { placeholder: '@reportable_incident_document_action_taken', language: '@language_fr_ca', value: 'Action prise' } }
  - { fields: { placeholder: '@reportable_incident_document_has_open_disclose_process_commenced', language: '@language_fr_ca', value: 'Le processus de divulgation ouvert a-t-il commencé?' } }
  - { fields: { placeholder: '@reportable_incident_document_if_no_please_comment', language: '@language_fr_ca', value: 'Si non, veuillez commenter' } }
  - { fields: { placeholder: '@reportable_incident_document_date_initial_open_disclosure_commenced', language: '@language_fr_ca', value: 'Date de début de la divulgation ouverte initiale' } }
  - { fields: { placeholder: '@reportable_incident_document_further_planned_action', language: '@language_fr_ca', value: 'Action envisagée supplémentaire' } }
  - { fields: { placeholder: '@reportable_incident_document_type_of_investigation', language: '@language_fr_ca', value: 'Type d''enquête' } }
  - { fields: { placeholder: '@reportable_incident_document_referral_other_agencies', language: '@language_fr_ca', value: 'Si autre, veuillez préciser:' } }
  - { fields: { placeholder: '@reportable_incident_document_contact_name', language: '@language_fr_ca', value: 'Nom du contact' } }
  - { fields: { placeholder: '@reportable_incident_document_position', language: '@language_fr_ca', value: Position } }
  - { fields: { placeholder: '@reportable_incident_document_contact_number', language: '@language_fr_ca', value: 'Numéro de contact' } }
  - { fields: { placeholder: '@reportable_incident_document_email_address', language: '@language_fr_ca', value: 'Adresse de courriel' } }
  - { fields: { placeholder: '@reportable_incident_document_clinical_incidents_statement_1', language: '@language_fr_ca', value: 'Tous les incidents cliniques classés SAC1 nécessitent une enquête approuvée. Le rapport sur les résultats de l''enquête doit être transmis au ministère de la Santé dans les 35 jours suivant l''avis de l''incident dans ims +.' } }
  - { fields: { placeholder: '@reportable_incident_document_clinical_incidents_statement_2', language: '@language_fr_ca', value: 'Les RIB d''incident clinique et tout autre document créé pour ou par le CRAG sont créés à des fins d''enquête et de recherche autorisées et sont privilégiés en vertu du NSW Health Administration Act 1982. La copie ou la distribution du document ou de toute information qu''il peut contenir sans autorisation expresse du ministre est interdite. Pour une utilisation et une divulgation autorisées, voir PD 2019_034 et le protocole du CRAG.' } }
  - { fields: { placeholder: '@reportable_incident_document_person_to_be_responsible_for_investigation', language: '@language_fr_ca', value: 'Personne chargée de l''enquête' } }
  - { fields: { placeholder: '@reportable_incident_document_contact_no', language: '@language_fr_ca', value: 'Nº du Contact' } }
  - { fields: { placeholder: '@reportable_incident_document_reportable_incident_has_been_approved', language: '@language_fr_ca', value: 'Ce résumé d''incident à signaler a été approuvé pour transmission par <span class="bold">{{ approvedByName }}</span> le <span class="bold">{{ approvedOnDate }}</span>' } }
  - { fields: { placeholder: '@reportable_incident_document_the_accuracy_and_content_is_endorsed', language: '@language_fr_ca', value: 'L''exactitude et le contenu du document sont approuvés. Voir la procédure de résumé d''incident à signaler.' } }
  - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_title', language: '@language_fr_ca', value: 'Résumé d''incident à signaler.' } }
  - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_description', language: '@language_fr_ca', value: 'Utilisez ce modèle pour créer un nouveau résumé d''incident à signaler.' } }
  - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_filename', language: '@language_fr_ca', value: résumé-incident-à-signaler } }
  - { fields: { placeholder: '@reportable_incident_document_rib_part_a_questions_1__10_to', language: '@language_fr_ca', value: 'La partie A du RIB (questions 1 à 10) doit être envoyée à l''approbation du chef de la direction (CE) et soumise au ministère de la Santé (MoH) dans les 24 heures suivant l''avis de l''incident.' } }
  - { fields: { placeholder: '@reportable_incident_document_if_not_harm_score_1_what_are_the', language: '@language_fr_ca', value: 'Si ce n''est pas un score de préjudice 1, quels sont les motifs pour lesquels cela est signalé' } }
  - { fields: { placeholder: '@reportable_incident_document_where_and_when_did_the_incident_happen', language: '@language_fr_ca', value: 'Où et quand l''incident s''est-il produit?' } }
  - { fields: { placeholder: '@reportable_incident_document_what_happened_what_actions_were_taken_in_response', language: '@language_fr_ca', value: 'Qu''est-il arrivé? Quelles mesures ont été prises en réaction?' } }
  - { fields: { placeholder: '@reportable_incident_document_has_bclinical_disclosureb_been_initiated', language: '@language_fr_ca', value: "La <b><u>divulgation clinique</u></b> a-t-elle été initiée\_:" } }
  - { fields: { placeholder: '@reportable_incident_document_any_other_immediate_concernsrisks_addressed', language: '@language_fr_ca', value: 'Tout autre problème ou risque immédiat traité' } }
  - { fields: { placeholder: '@reportable_incident_document_external_notifications', language: '@language_fr_ca', value: 'Avis externes' } }
  - { fields: { placeholder: '@reportable_incident_document_media_interest', language: '@language_fr_ca', value: 'Intérêt médiatique' } }
  - { fields: { placeholder: '@reportable_incident_document_the_accuracy_and_content_of_the_document_is', language: '@language_fr_ca', value: 'L''exactitude et le contenu du document sont approuvés. Voir la procédure de résumé d''incident à signaler.' } }
  - { fields: { placeholder: '@reportable_incident_document_rib_part_b_questions_11__16_to', language: '@language_fr_ca', value: 'RIB Partie B (questions 11 à 16) à remplir à la demande du Ministère de la Santé ou de la Commission d''excellence clinique.' } }
  - { fields: { placeholder: '@reportable_incident_document_patient_carer_and_family', language: '@language_fr_ca', value: 'Patient, soignant et famille' } }
  - { fields: { placeholder: '@reportable_incident_document_confirm_a_dedicated_family_contact_person_has_been', language: '@language_fr_ca', value: 'Confirmer qu''un contact familial dédié a été nommé' } }
  - { fields: { placeholder: '@reportable_incident_document_staff', language: '@language_fr_ca', value: Personnel } }
  - { fields: { placeholder: '@reportable_incident_document_type_of_investigation_planned', language: '@language_fr_ca', value: 'Type d''enquête planifiée' } }
  - { fields: { placeholder: '@reportable_incident_document_organisation', language: '@language_fr_ca', value: Organisation } }
  - { fields: { placeholder: '@reportable_incident_document_other_comments', language: '@language_fr_ca', value: 'Autres commentaires' } }
  - { fields: { placeholder: '@reportable_incident_document_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@reportable_incidents_errors_user_cannot_reject', language: '@language_fr_ca', value: 'Seul le titulaire de l''incident à signaler peut définir l''état à rejeté' } }
  - { fields: { placeholder: '@reportable_incidents_errors_invalid_status', language: '@language_fr_ca', value: 'Changement d''état non valide' } }
  - { fields: { placeholder: '@reportable_incidents_banners_locked', language: '@language_fr_ca', value: 'Ce dossier est verrouillé par {{name}} depuis le {{date}}' } }
  - { fields: { placeholder: '@placeholder_reportable_incidents_pra_required_label', language: '@language_fr_ca', value: 'PRA ou contrôle de sécurité?' } }
  - { fields: { placeholder: '@placeholder_reportable_incidents_pra_required_yes', language: '@language_fr_ca', value: 'Oui' } }
  - { fields: { placeholder: '@placeholder_reportable_incidents_pra_required_no', language: '@language_fr_ca', value: 'Non' } }
  - { fields: { placeholder: '@placeholder_reportable_incidents_pra_required_other', language: '@language_fr_ca', value: 'Autre' } }
