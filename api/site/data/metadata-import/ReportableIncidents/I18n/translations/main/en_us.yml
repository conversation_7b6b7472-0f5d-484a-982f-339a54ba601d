entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@reportable_incident_contact_role_default', language: '@language_en_us', value: Select a Role } }
    - { fields: { placeholder: '@reportable_incident_contact_role_investigation_lead', language: '@language_en_us', value: Investigation Lead } }
    - { fields: { placeholder: '@reportable_incident_contact_role_label', language: '@language_en_us', value: Role } }
    - { fields: { placeholder: '@reportable_incident_contact_role_person_involved', language: '@language_en_us', value: Person Involved } }
    - { fields: { placeholder: '@reportable_incident_contact_role_rib_contact', language: '@language_en_us', value: RIB Contact } }
    - { fields: { placeholder: '@reportable_incident_contact_role_title', language: '@language_en_us', value: Role } }
    - { fields: { placeholder: '@reportable_incident_document_action_taken', language: '@language_en_us', value: Action Taken } }
    - { fields: { placeholder: '@reportable_incident_document_any_other_immediate_concernsrisks_addressed', language: '@language_en_us', value: Any other immediate concerns/risks addressed } }
    - { fields: { placeholder: '@reportable_incident_document_clinical_incidents_statement_1', language: '@language_en_us', value: All clinical incidents that are rated as SAC1 require an approved investigation. The investigation findings report is to be provided to the Ministry of Health within 35 days of the incident being notified in ims+. } }
    - { fields: { placeholder: '@reportable_incident_document_clinical_incidents_statement_2', language: '@language_en_us', value: Clinical incident RIBs and any other documents created for or by the CRAG are created for the purpose of authorized investigation and research and are privileged under the NSW Health Administration Act 1982. The copying or distribution of the document or any information it may contain without express permission of the Minister is prohibited. For authorized use and disclosure see PD 2014_004 and CRAG protocol. } }
    - { fields: { placeholder: '@reportable_incident_document_confirm_a_dedicated_family_contact_person_has_been', language: '@language_en_us', value: Confirm a dedicated family contact person has been appointed } }
    - { fields: { placeholder: '@reportable_incident_document_contact_name', language: '@language_en_us', value: Contact Name } }
    - { fields: { placeholder: '@reportable_incident_document_contact_no', language: '@language_en_us', value: Contact no } }
    - { fields: { placeholder: '@reportable_incident_document_contact_number', language: '@language_en_us', value: Contact Number } }
    - { fields: { placeholder: '@reportable_incident_document_date', language: '@language_en_us', value: Date } }
    - { fields: { placeholder: '@reportable_incident_document_date_initial_open_disclosure_commenced', language: '@language_en_us', value: Date initial open disclosure commenced } }
    - { fields: { placeholder: '@reportable_incident_document_date_of_incident', language: '@language_en_us', value: Date of actual incident } }
    - { fields: { placeholder: '@reportable_incident_document_date_of_incident_ioms', language: '@language_en_us', value: Date of incident notification } }
    - { fields: { placeholder: '@reportable_incident_document_email_address', language: '@language_en_us', value: Email Address } }
    - { fields: { placeholder: '@reportable_incident_document_external_notifications', language: '@language_en_us', value: External Notifications } }
    - { fields: { placeholder: '@reportable_incident_document_further_planned_action', language: '@language_en_us', value: Further Planned Action } }
    - { fields: { placeholder: '@reportable_incident_document_has_bclinical_disclosureb_been_initiated', language: '@language_en_us', value: 'Has <b><u>Clinician Disclosure</u></b> been initiated:' } }
    - { fields: { placeholder: '@reportable_incident_document_has_open_disclose_process_commenced', language: '@language_en_us', value: 'Has the open disclosure process commenced?' } }
    - { fields: { placeholder: '@reportable_incident_document_if_no_please_comment', language: '@language_en_us', value: "If no, please comment" } }
    - { fields: { placeholder: '@reportable_incident_document_if_not_harm_score_1_what_are_the', language: '@language_en_us', value: "If not harm score 1, what are the reasons this is being reported" } }
    - { fields: { placeholder: '@reportable_incident_document_if_not_sac_reasons', language: '@language_en_us', value: IF NOT SAC what are the reasons this incident is being reported } }
    - { fields: { placeholder: '@reportable_incident_document_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incident_document_media_interest', language: '@language_en_us', value: Media Interest } }
    - { fields: { placeholder: '@reportable_incident_document_moh_rib_no', language: '@language_en_us', value: MoH RIB No } }
    - { fields: { placeholder: '@reportable_incident_document_organisation', language: '@language_en_us', value: Organization } }
    - { fields: { placeholder: '@reportable_incident_document_other_comments', language: '@language_en_us', value: Other comments } }
    - { fields: { placeholder: '@reportable_incident_document_patient_carer_and_family', language: '@language_en_us', value: "Patient, Carer and Family" } }
    - { fields: { placeholder: '@reportable_incident_document_person_to_be_responsible_for_investigation', language: '@language_en_us', value: Person to be responsible for Investigation } }
    - { fields: { placeholder: '@reportable_incident_document_position', language: '@language_en_us', value: Position } }
    - { fields: { placeholder: '@reportable_incident_document_principal_incident_type', language: '@language_en_us', value: Principal incident type } }
    - { fields: { placeholder: '@reportable_incident_document_reason_for_reporting', language: '@language_en_us', value: Reason for reporting } }
    - { fields: { placeholder: '@reportable_incident_document_referral_other_agencies', language: '@language_en_us', value: "If other, please specify:" } }
    - { fields: { placeholder: '@reportable_incident_document_reportable_incident_brief', language: '@language_en_us', value: Reportable Incident Brief } }
    - { fields: { placeholder: '@reportable_incident_document_reportable_incident_has_been_approved', language: '@language_en_us', value: 'This Reportable Incident Brief has been approved for transmission by <span class=bold">{{ approvedByName }}</span> on <span class="bold">{{ approvedOnDate }}</span>"' } }
    - { fields: { placeholder: '@reportable_incident_document_rib_part_a_questions_1__10_to', language: '@language_en_us', value: RIB Part A (Questions 1 - 10) to be sent to Chief Executive (CE) endorsement and submission to the Ministry of Health (MoH) within 24 hours of incident notification. } }
    - { fields: { placeholder: '@reportable_incident_document_rib_part_b_questions_11__16_to', language: '@language_en_us', value: RIB Part B (Questions 11 - 16) to be completed with Response to Serious Incident Report and submitted for CE endorsement and MoH submission within 48 to 72 hours. (Please add details below) } }
    - { fields: { placeholder: '@reportable_incident_document_rib_record_id', language: '@language_en_us', value: RIB Record id } }
    - { fields: { placeholder: '@reportable_incident_document_severity_assessment_code', language: '@language_en_us', value: Incident harm score } }
    - { fields: { placeholder: '@reportable_incident_document_source_record_id', language: '@language_en_us', value: Source Record Id } }
    - { fields: { placeholder: '@reportable_incident_document_specific_service', language: '@language_en_us', value: Specific Service } }
    - { fields: { placeholder: '@reportable_incident_document_staff', language: '@language_en_us', value: Staff } }
    - { fields: { placeholder: '@reportable_incident_document_synopsis_of_the_event', language: '@language_en_us', value: Synopsis of the Event } }
    - { fields: { placeholder: '@reportable_incident_document_the_accuracy_and_content_is_endorsed', language: '@language_en_us', value: The accuracy and content of the document is endorsed. See Reportable Incident Brief Procedure. } }
    - { fields: { placeholder: '@reportable_incident_document_the_accuracy_and_content_of_the_document_is', language: '@language_en_us', value: The accuracy and content of the document is endorsed. See Reportable Incident Brief Procedure. } }
    - { fields: { placeholder: '@reportable_incident_document_this_incident_is', language: '@language_en_us', value: This incident is about } }
    - { fields: { placeholder: '@reportable_incident_document_time_of_incident', language: '@language_en_us', value: Time of incident } }
    - { fields: { placeholder: '@reportable_incident_document_type_of_investigation', language: '@language_en_us', value: Type of investigation } }
    - { fields: { placeholder: '@reportable_incident_document_type_of_investigation_planned', language: '@language_en_us', value: Type of investigation planned } }
    - { fields: { placeholder: '@reportable_incident_document_what_happened_what_actions_were_taken_in_response', language: '@language_en_us', value: 'What happened? What actions were taken in response?' } }
    - { fields: { placeholder: '@reportable_incident_document_where_and_when_did_the_incident_happen', language: '@language_en_us', value: 'Where and when did the incident happen?' } }
    - { fields: { placeholder: '@reportable_incidents_status_approved', language: '@language_en_us', value: Approved } }
    - { fields: { placeholder: '@reportable_incidents_status_draft', language: '@language_en_us', value: Draft } }
    - { fields: { placeholder: '@reportable_incidents_status_finalised', language: '@language_en_us', value: Finalized } }
    - { fields: { placeholder: '@reportable_incidents_status_in_progress', language: '@language_en_us', value: In Progress } }
    - { fields: { placeholder: '@reportable_incidents_status_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@reportable_incidents_status_submitted', language: '@language_en_us', value: Submitted } }
    - { fields: { placeholder: '@reportable_incident_success_template_document_attached', language: '@language_en_us', value: Successfully attached document from template } }
    - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_description', language: '@language_en_us', value: Use this template to create a new Reportable Incident Brief. } }
    - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_filename', language: '@language_en_us', value: reportable-incident-brief } }
    - { fields: { placeholder: '@reportable_incident_template_reportable_incident_brief_title', language: '@language_en_us', value: Reportable Incident Brief. } }
    - { fields: { placeholder: '@reportable_incidents_action_form_type_title', language: '@language_en_us', value: Reportable Incidents Form Type } }
    - { fields: { placeholder: '@reportable_incidents_action_form_title', language: '@language_en_us', value: Reportable Incidents Action } }
    - { fields: { placeholder: '@reportable_incidents_attachments_no_attachments', language: '@language_en_us', value: This record has no attachments } }
    - { fields: { placeholder: '@reportable_incidents_columns_created_at', language: '@language_en_us', value: Created At } }
    - { fields: { placeholder: '@reportable_incidents_columns_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incidents_columns_rib_id', language: '@language_en_us', value: 'RIB #' } }
    - { fields: { placeholder: '@reportable_incidents_columns_moh_rib_number', language: '@language_en_us', value: MoH RIB Number } }
    - { fields: { placeholder: '@reportable_incidents_columns_owner', language: '@language_en_us', value: Owner } }
    - { fields: { placeholder: '@reportable_incidents_columns_reason_for_reporting', language: '@language_en_us', value: Reason for Reporting } }
    - { fields: { placeholder: '@reportable_incidents_columns_columns_sac_score', language: '@language_en_us', value: Harm Score } }
    - { fields: { placeholder: '@reportable_incidents_columns_source_record_ref', language: '@language_en_us', value: Source Record Ref } }
    - { fields: { placeholder: '@reportable_incidents_columns_columns_pra_required', language: '@language_en_us', value: 'PRA or Safety Check' } }
    - { fields: { placeholder: '@reportable_incidents_columns_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_approved', language: '@language_en_us', value: Approved } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_draft', language: '@language_en_us', value: Draft } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_finalised', language: '@language_en_us', value: Finalized } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_in_progress', language: '@language_en_us', value: In Progress } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@reportable_incidents_columns_status_submitted', language: '@language_en_us', value: Submitted } }
    - { fields: { placeholder: '@reportable_incidents_columns_columns_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_access_control', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_all_actions', language: '@language_en_us', value: All Actions } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_attachments', language: '@language_en_us', value: Attachments } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_details', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_heading', language: '@language_en_us', value: "RIB #{{id}}" } }
    - { fields: { placeholder: '@reportable_incidents_edit_sidebar_my_actions', language: '@language_en_us', value: My Actions } }
    - { fields: { placeholder: '@reportable_incidents_errors_cannot_remove_owner', language: '@language_en_us', value: A Reportable Incident owner cannot be removed } }
    - { fields: { placeholder: '@reportable_incidents_errors_invalid_status', language: '@language_en_us', value: Invalid Status Transition } }
    - { fields: { placeholder: '@reportable_incidents_errors_user_cannot_reject', language: '@language_en_us', value: Only the Reportable Incident owner can set the status to Rejected } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_category', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_category_label', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_created_at', language: '@language_en_us', value: Created Date } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_created_at_label', language: '@language_en_us', value: Created Date } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_location_label', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_owner', language: '@language_en_us', value: Owner } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_owner_label', language: '@language_en_us', value: Owner } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_reporting_reason', language: '@language_en_us', value: Reason For Reporting } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_reporting_reason_label', language: '@language_en_us', value: Reason For Reporting } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_id', language: '@language_en_us', value: RIB ID } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_id_label', language: '@language_en_us', value: RIB ID } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_number', language: '@language_en_us', value: RIB Number } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_rib_number_label', language: '@language_en_us', value: RIB Number } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_sac_score', language: '@language_en_us', value: SAC Score } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_sac_score_label', language: '@language_en_us', value: Sac Score } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_ref', language: '@language_en_us', value: Source Record Ref } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_ref_label', language: '@language_en_us', value: Source Record Ref } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_type', language: '@language_en_us', value: Source Record Type } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_source_type_label', language: '@language_en_us', value: Source Record Type } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_field_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@reportable_incidents_filter_form_title', language: '@language_en_us', value: Filter Reportable Incident Briefs } }
    - { fields: { placeholder: '@reportable_incidents_form_type_filter', language: '@language_en_us', value: Reportable Incidents Filter } }
    - { fields: { placeholder: '@reportable_incidents_main_form_type_main', language: '@language_en_us', value: Reportable Incident Brief Form } }
    - { fields: { placeholder: '@reportable_incidents_form_main_title', language: '@language_en_us', value: Reportable Incident Briefs } }
    - { fields: { placeholder: '@reportable_incidents_label_plural', language: '@language_en_us', value: Reportable Incident Briefs } }
    - { fields: { placeholder: '@reportable_incidents_label_singular', language: '@language_en_us', value: Reportable Incident Brief } }
    - { fields: { placeholder: '@reportable_incidents_loading_reportable_incidents', language: '@language_en_us', value: Loading Reportable Incidents } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_action_taken', language: '@language_en_us', value: Action Taken (summary) } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_action_taken_label', language: '@language_en_us', value: Action Taken (summary) } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_agency_referral', language: '@language_en_us', value: Referral to other agencies } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_agency_referral_label', language: '@language_en_us', value: Referral to other agencies } }
    - { fields: { placeholder: '@reportable_incidents_option_chasm', language: '@language_en_us', value: CHASM } }
    - { fields: { placeholder: '@reportable_incidents_option_maternal_death', language: '@language_en_us', value: Maternal Death } }
    - { fields: { placeholder: '@reportable_incidents_option_media_team', language: '@language_en_us', value: Media Team notification } }
    - { fields: { placeholder: '@reportable_incidents_option_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@reportable_incidents_option_perinatal', language: '@language_en_us', value: Perinatal } }
    - { fields: { placeholder: '@reportable_incidents_option_safe_work', language: '@language_en_us', value: SafeWork NSW } }
    - { fields: { placeholder: '@reportable_incidents_option_scidua', language: '@language_en_us', value: SCIDUA } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_category', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_category_label', language: '@language_en_us', value: Category } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_commencement_date', language: '@language_en_us', value: Commencement Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_commencement_date_label', language: '@language_en_us', value: Commencement Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_created_date', language: '@language_en_us', value: Created Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_created_date_label', language: '@language_en_us', value: Created Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_commenced', language: '@language_en_us', value: 'Disclosure Process Commenced' } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_commenced_label', language: '@language_en_us', value: 'Disclosure Process Commenced?' } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_reason', language: '@language_en_us', value: Reason for not commencing open disclosure } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_disclosure_reason_label', language: '@language_en_us', value: Reason for not commencing open disclosure } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_event_synopsis_label', language: '@language_en_us', value: Synopsis of the Event } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_event_synopsis_title', language: '@language_en_us', value: Synopsis of the Event } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_further_action', language: '@language_en_us', value: Further planned action } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_further_action_label', language: '@language_en_us', value: Further planned action } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_date', language: '@language_en_us', value: Incident Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_date_label', language: '@language_en_us', value: Incident Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_summary', language: '@language_en_us', value: Incident Summary } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_summary_label', language: '@language_en_us', value: Incident Summary } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_time', language: '@language_en_us', value: Incident Time } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_incident_time_label', language: '@language_en_us', value: Incident Time } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_location_label', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_agency_referral', language: '@language_en_us', value: Other referred agencies } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_agency_referral_label', language: '@language_en_us', value: Other referred agencies } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_location', language: '@language_en_us', value: Other Location } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_location_label', language: '@language_en_us', value: Other Location } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_service', language: '@language_en_us', value: Other Service } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_service_label', language: '@language_en_us', value: Other Service } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_owner', language: '@language_en_us', value: Owner } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_owner_label', language: '@language_en_us', value: Owner } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_rejection_reason', language: '@language_en_us', value: Reason For Rejection } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_rejection_reason_label', language: '@language_en_us', value: Reason For Rejection } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_reported_date', language: '@language_en_us', value: Reported Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_reported_date_label', language: '@language_en_us', value: Reported Date } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_reported_reason', language: '@language_en_us', value: 'What are the reasons this incident is being reported?' } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_reported_reason_label', language: '@language_en_us', value: 'What are the reasons this incident is being reported?' } }
    - { fields: { placeholder: '@reportable_incidents_option_legal', language: '@language_en_us', value: Legal Significance } }
    - { fields: { placeholder: '@reportable_incidents_option_required', language: '@language_en_us', value: Required by policy } }
    - { fields: { placeholder: '@reportable_incidents_option_sac2_4', language: '@language_en_us', value: SAC2-4 Privileged Investigation } }
    - { fields: { placeholder: '@reportable_incidents_option_state_wide', language: '@language_en_us', value: Possible Statewide Implication } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_reporting_reason', language: '@language_en_us', value: Reason for Reporting } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_reporting_reason_label', language: '@language_en_us', value: Reason for Reporting } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_rib_number', language: '@language_en_us', value: MoH RIB No. } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_rib_number_label', language: '@language_en_us', value: MoH RIB No. } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_sac_score', language: '@language_en_us', value: SAC Score } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_other_sac_score_label', language: '@language_en_us', value: SAC Score } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_service', language: '@language_en_us', value: Service } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_service_label', language: '@language_en_us', value: Service } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_id', language: '@language_en_us', value: Record Source ID } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_id_label', language: '@language_en_us', value: Record Source ID } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_ref', language: '@language_en_us', value: Record Source Ref } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_ref_label', language: '@language_en_us', value: Record Source Ref } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_type', language: '@language_en_us', value: Record Source Type } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_source_type_label', language: '@language_en_us', value: Record Source Type } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_type', language: '@language_en_us', value: Record Source Type } }
    - { fields: { placeholder: '@reportable_incidents_main_form_field_type_label', language: '@language_en_us', value: Record Source Type } }
    - { fields: { placeholder: '@reportable_incidents_message_reportable_incident_saved', language: '@language_en_us', value: Reportable Incident Brief Saved Successfully } }
    - { fields: { placeholder: '@reportable_incidents_module_title', language: '@language_en_us', value: Reportable Incident Briefs } }
    - { fields: { placeholder: '@reportable_incidents_rejected_notice', language: '@language_en_us', value: "This Reportable Incident was rejected by {{user}} on {{date}}" } }
    - { fields: { placeholder: '@reportable_incidents_section_main', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@reportable_incidents_main_form_section_source_details', language: '@language_en_us', value: Source Details } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_all_ribs', language: '@language_en_us', value: All RIBs } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_approved', language: '@language_en_us', value: Approved } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_back_to_dashboard', language: '@language_en_us', value: Back to Dashboard } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_finalised', language: '@language_en_us', value: Finalized } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_in_progress', language: '@language_en_us', value: In Progress } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@reportable_incidents_sidebar_submitted_to_moh', language: '@language_en_us', value: Submitted to MoH } }
    - { fields: { placeholder: '@reportable_incidents_success_attachment_save', language: '@language_en_us', value: Attachment saved successfully } }
    - { fields: { placeholder: '@reportable_incidents_banners_locked', language: '@language_en_us', value: 'This record is locked by {{name}} since {{date}}' } }
    - { fields: { placeholder: '@reportable_incident_template_pra_template_title', language: '@language_en_us', value: 'Preliminary Risk Assessment Report' } }
    - { fields: { placeholder: '@reportable_incident_template_pra_template_description', language: '@language_en_us', value: 'Use this template to create a new PRA Report.' } }
    - { fields: { placeholder: '@reportable_incident_template_pra_template_filename', language: '@language_en_us', value: 'pra-template' } }
