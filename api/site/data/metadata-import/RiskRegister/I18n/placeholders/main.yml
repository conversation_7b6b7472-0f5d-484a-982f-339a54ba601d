entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: ERM.RISK.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: erm_risk_singular
  -
    fields:
      placeholder: ERM.RISK.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: erm_risk_plural
  -
    fields:
      placeholder: ERM.RISK.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: erm_risk_search
  -
    fields:
      placeholder: ERM.MODULE_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_module_title
  -
    fields:
      placeholder: ERM.NAV.MY_RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_my_risks
  -
    fields:
      placeholder: ERM.NAV.ADMIN_RISK_LISTING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_risk_listing
  -
    fields:
      placeholder: ERM.NAV.ADMIN_RISK_REGISTER_LISTING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_risk_register_listing
  -
    fields:
      placeholder: ERM.NAV.RISK_REGISTERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_risk_registers
  -
    fields:
      placeholder: ERM.NAV.RISK_TRACKERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_risk_trackers
  # @todo: Investigate this ridiculous bug
  # For some reason, the 8th placeholder in this file does not get attached to the domain. This unused placeholder
  # simply sits in that position so the UI is unaffected.
  -
    fields:
      placeholder: DEBUG_PLACEHOLDER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: debug_placeholder
  -
    fields:
      placeholder: ERM.NAV.REPORT_A_RISK
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_report_a_risk
  -
    fields:
      placeholder: ERM.NAV.ADMIN
      pointer: 'COMMON.ADMIN'
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin
  -
    fields:
      placeholder: ERM.NAV.ADMIN_OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_objectives
  -
    fields:
      placeholder: ERM.NAV.ADMIN_RISK_REGISTERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_risk_registers
  -
    fields:
      placeholder: ERM.NAV.ADMIN_PERMISSIONS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_permissions
  -
    fields:
      placeholder: ERM.NAV.ADMIN_RISK_MATRIX
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_risk_matrix
  -
    fields:
      placeholder: ERM.NAV.ADMIN_CONFIGURATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_nav_admin_configuration
  -
    fields:
      placeholder: ERM.LOADING.RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risks
  -
    fields:
      placeholder: ERM.LOADING.RISK_FORM
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risk_form
  -
    fields:
      placeholder: ERM.LOADING.RISK_REGISTER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risk_register
  -
    fields:
      placeholder: ERM.LOADING.RISK_REGISTERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risk_registers
  -
    fields:
      placeholder: ERM.LOADING.RISK_TRACKERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risk_trackers
  -
    fields:
      placeholder: ERM.LOADING.RISK_WORKFLOW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_risk_workflow
  -
    fields:
      placeholder: ERM.LOADING.OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_objectives
  -
    fields:
      placeholder: ERM.LOADING.HEATMAP
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_loading_heatmap
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_title
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.ADD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_add
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.FORM.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_form_title
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.FORM.SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_form_summary
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.DELETE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_delete
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.DELETE_CONFIRM
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_delete_confirm
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_save
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_saved_successfully
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_removed_successfully
  -
    fields:
      placeholder: ERM.ADMIN.ORGANISATIONAL_OBJECTIVES.UNTITLED_NODE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_organisational_objectives_untitled_node
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ESCALATION_MODEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_escalation_model
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ESCALATION_MODEL_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_escalation_model_updated_successfully
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.CONFIGURATION_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_configuration_updated_successfully
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.INCLUDE_ESCALATIONS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_include_escalations
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.FORCE_RISK_REVIEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_force_review
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.NO_CLOSED_MY_RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_no_closed_my_risks
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ESCALATION_MODEL.OPTIONS.IMMEDIATE_PARENT_ONLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_escalation_model_options_immediate_parent_only
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ESCALATION_MODEL.OPTIONS.ANY_PARENT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_escalation_model_options_any_parent
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.INCLUDE_ESCALATION_MODEL.OPTIONS.ENABLED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_include_escalation_model_options_enabled
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.INCLUDE_ESCALATION_MODEL.OPTIONS.DISABLED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_include_escalation_model_options_disabled
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.IS_ACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_is_active
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ACTIVATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_activate
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.DEACTIVATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_deactivate
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.STATUS.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_status_label
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.STATUS.ACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_status_active
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.STATUS.INACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_status_inactive
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.TABS.REGISTER_OWNERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_tabs_register_owners
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.TABS.RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_tabs_risks
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.DEACTIVATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_deactivated_successfully
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.ACTIVATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_activated_successfully

  # New Risk Register
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.NEW_REGISTER_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_new_register_title
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.FORM.TITLE
      pointer: COMMON.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
  -
    fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.FORM.CANCEL
      pointer: COMMON.CANCEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
  - fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.FORM.SAVE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_form_save
  - fields:
      placeholder: ERM.ADMIN.RISK_REGISTERS.EXCLUDE_CONTRIBUTORY_FACTORS_LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_admin_risk_registers_exclude_contributory_factors_label
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.ID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_accreditation'
    ref: erm_my_risks_columns_id
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_controls'
        -
          domain: '@domain_accreditation'
    ref: erm_my_risks_columns_title
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.TYPE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_type
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.SUBTYPE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_subtype
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.DATE_OPENED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_date_opened
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.DATE_CLOSED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_date_closed
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.TARGET_RATING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_target_rating
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.LOCATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_location
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.SERVICE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_service
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.TERTIARY_SUBTYPE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_tertiary_subtype
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.REVIEW_DATE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_review_date
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.INITIAL_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_initial_grading
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.CURRENT_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_current_grading
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.ESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_escalated
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.ESCALATED.YES
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_escalated_yes
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.ESCALATED.NO
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_escalated_no
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.REGISTER
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_register
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.STATUS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_status
  -
    fields:
      placeholder: ERM.MY_RISKS.COLUMNS.LAST_REVIEWED_BY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_last_reviewed_by
  -
    fields:
      placeholder: ERM.RISK.NAV.RISK.PAGE_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_nav_risk_page_title
  -
    fields:
      placeholder: ERM.RISK.NAV.RISK_DETAILS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_risk_details
  -
    fields:
      placeholder: ERM.RISK.NAV.SERVICES_AND_LOCATIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_services_and_locations
  -
    fields:
      placeholder: ERM.RISK.NAV.OBJECTIVES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_objectives
  -
    fields:
      placeholder: ERM.RISK.NAV.CONTROLS_AND_ASSURANCE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_controls_and_assurance
  -
    fields:
      placeholder: ERM.RISK.NAV.MEDICATIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_medications
  -
    fields:
      placeholder: ERM.RISK.NAV.EQUIPMENT
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_equipment
  -
    fields:
      placeholder: ERM.RISK.NAV.RISK_MONITORS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_risk_monitors
  -
    fields:
      placeholder: ERM.RISK.NAV.CONTACTS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_contacts
  -
    fields:
      placeholder: ERM.RISK.NAV.REVIEWS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_reviews
  -
    fields:
      placeholder: ERM.RISK.NAV.ESCALATION
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_escalation
  -
    fields:
      placeholder: ERM.RISK.NAV.ACTIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_actions
  -
    fields:
      placeholder: ERM.RISK.NAV.MY_ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_nav_my_actions
  -
    fields:
      placeholder: ERM.RISK.NAV.ALL_ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_nav_all_actions
  -
    fields:
      placeholder: ERM.RISK.NAV.ATTACHMENTS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_attachments
  -
    fields:
      placeholder: ERM.RISK.NAV.ACCESS_CONTROL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_access_control
  -
    fields:
      placeholder: ERM.RISK.NAV.LINKED_RECORDS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_linked_records
  -
    fields:
      placeholder: ERM.RISK.LABEL.RISK_DETAILS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_label_risk_details
  -
    fields:
      placeholder: ERM.RISK.LABEL.SERVICES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_label_services
  -
    fields:
      placeholder: ERM.RISK.LABEL.LOCATIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_label_locations
  -
    fields:
      placeholder: ERM.RISK.LABEL.CONTROLS_AND_ASSURANCE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_label_controls_and_assurance
  -
    fields:
      placeholder: ERM.RISK.LABEL.MEDICATIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_label_medications
  -
    fields:
      placeholder: ERM.RISK.BANNERS.IS_NEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_is_new
  -
    fields:
      placeholder: ERM.RISK.BANNERS.IS_LOCKED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_is_locked
  -
    fields:
      placeholder: ERM.RISK.ERRORS.LOCKED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_locked
  -
    fields:
      placeholder: ERM.RISK.BANNERS.INACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_inactive
  -
    fields:
      placeholder: ERM.RISK.BANNERS.ESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_escalated
  -
    fields:
      placeholder: ERM.RISK.BANNERS.PENDING_INBOUND_ESCALATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_pending_inbound_escalation
  -
    fields:
      placeholder: ERM.RISK.BANNERS.PENDING_INBOUND_DEESCALATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_pending_inbound_deescalation
  -
    fields:
      placeholder: ERM.RISK.BANNERS.PENDING_OUTBOUND_ESCALATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_pending_outbound_escalation
  -
    fields:
      placeholder: ERM.RISK.BANNERS.PENDING_OUTBOUND_DEESCALATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_banners_pending_outbound_deescalation
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_title
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_summary
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.TITLE_AND_REFERENCE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_sections_title_and_reference_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.TITLE_AND_REFERENCE.HELP
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_sections_title_and_reference_help
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.RISK_TYPE.NO_TYPES
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_risk_type_no_types
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.RISK_TYPE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_sections_risk_type_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TITLE
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_title
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TITLE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_title_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_DESCRIPTION
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_description
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_DESCRIPTION.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_description_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.EMPTY_TEXT
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_risk_type_empty_text
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.OPTIONS.HAZARD
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type_options_hazard
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.OPTIONS.INFECTION
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type_options_infection
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.OPTIONS.OPERATIONAL_RISK
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type_options_operational_risk
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TERTIARY_SUBTYPE
      type: 1
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
      -
        domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_tertiary_subtype
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TERTIARY_SUBTYPE.LABEL
      type: 1
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
      -
        domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_tertiary_subtype_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.ORGANISATIONAL_OBJECTIVES
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_organisational_objectives
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.ORGANISATIONAL_OBJECTIVES.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_organisational_objectives_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_RATINGS
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_ratings
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_RATINGS.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_ratings_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RELATIONSHIPS
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_relationships
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RELATIONSHIPS.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_relationships_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.REPORTED_BY
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_reported_by
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.REPORTED_BY.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_reported_by_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COUNTRY
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_country
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COUNTRY.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_country_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.GENDER
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_gender
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.GENDER.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_gender_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COUNTRY_2
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_country_2
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COUNTRY_2.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_country_2_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COMMENTS
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_comments
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.COMMENTS.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_comments_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.EMPTY_TEXT
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_risk_subtype_empty_text
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.HEAD_HAZARD
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_head_hazard
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.SLIP_HAZARD
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_slip_hazard
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.RESPIRATORY_HAZARD
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_respiratory_hazard
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.AIRBORNE_INFECTION
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_airborne_infection
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TYPE.OPTIONS.OTHER
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
      - domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_type_options_other
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.CONTACT_INFECTION
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_contact_infection
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.FINANCIAL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_financial
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.CLINICAL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_clinical
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_SUBTYPE.OPTIONS.OTHER
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
      - domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_subtype_options_other
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_TERTIARY_SUBTYPE.EMPTY_TEXT
      type: 1
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_risk_tertiary_subtype_empty_text
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.RISK_DATES.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_sections_risk_dates_label
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_OPENED_DATE
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_opened_date
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.RISK_OPENED_DATE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_default_form_fields_risk_opened_date_label
  -
    fields:
      placeholder: ERM.RISK.FORM.CLOSE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_close
  -
    fields:
      placeholder: ERM.RISK.FORM.REOPEN
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_reopen
  -
    fields:
      placeholder: ERM.RISK.FORM.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_save
  -
    fields:
      placeholder: ERM.RISK.FORM.DELETE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_delete
  -
    fields:
      placeholder: ERM.RISK.FORM.DELETE.WARNING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_delete_warning
  -
    fields:
      placeholder: ERM.RISK.FORM.DELETE.SUCCESS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_delete_success
  -
    fields:
      placeholder: ERM.RISK.FORM.INVALID.MISSING_INITIAL_RISK_RATING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_form_invalid_missing_initial_risk_rating
  -
    fields:
      placeholder: ERM.RISK.CREATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_created_successfully
  -
    fields:
      placeholder: ERM.RISK.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_saved_successfully
  -
    fields:
      placeholder: ERM.RISK.ACCEPTED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_accepted_successfully
  -
    fields:
      placeholder: ERM.RISK.REJECTED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_rejected_successfully
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_title
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.TABS.INITIAL_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_tabs_initial_grading
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.TABS.CURRENT_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_tabs_current_grading
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.TABS.TARGET_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_tabs_target_grading
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.CURRENT_RATING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_current_rating
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.CURRENT_RISK_LEVEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_current_risk_level
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.CONSEQUENCE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_consequence
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.LIKELIHOOD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_likelihood
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.NEGLIGIBLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_negligible
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.MINOR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_minor
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.MODERATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_moderate
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.SIGNIFICANT
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_significant
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.MAJOR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_major
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.CATASTROPHIC
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_catastrophic
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.ALMOST_CERTAIN
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_almost_certain
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.LIKELY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_likely
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.POSSIBLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_possible
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.UNLIKELY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_unlikely
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.RARE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_rare
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.CONSEQUENCE.MINOR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_consequence_minor
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.CONSEQUENCE.MODERATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_consequence_moderate
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.CONSEQUENCE.SIGNIFICANT
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_consequence_significant
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.CONSEQUENCE.MAJOR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_consequence_major
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.CONSEQUENCE.CATASTROPHIC
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_consequence_catastrophic
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD.ALMOST_CERTAIN
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood_almost_certain
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD.LIKELY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood_likely
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD.POSSIBLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood_possible
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD.UNLIKELY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood_unlikely
  -
    fields:
      placeholder: ERM.COMPONENTS.HEATMAP.GRID.IMPACT.LIKELIHOOD.RARE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_components_heatmap_grid_impact_likelihood_rare
  -
    fields:
      placeholder: ERM.RISK.NAV.BACK_TO_MY_RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_nav_back_to_my_risks
  -
    fields:
      placeholder: ERM.RISK.SERVICES_AND_LOCATIONS.SAVE_SERVICE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_services_and_locations_save_service
  -
    fields:
      placeholder: ERM.RISK.OBJECTIVES.SELECTED_OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_objectives_selected_objectives
  -
    fields:
      placeholder: ERM.RISK.OBJECTIVES.NONE_ATTACHED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_objectives_none_attached
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_title
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.ADD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_add
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.NONE_ATTACHED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_none_attached
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_saved_successfully
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_removed_successfully
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.CONTROLS.ADD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_controls_add
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.CONTROLS.SELECT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_controls_select
  -
    fields:
      placeholder: ERM.RISK.CONTRIBUTORY_FACTORS.CONTROLS.NONE_AVAILABLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_contributory_factors_controls_none_available
  -
    fields:
      placeholder: ERM.RISK.CONTROLS.IN_PLACE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_controls_in_place
  -
    fields:
      placeholder: ERM.RISK.CONTROLS.GAPS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_controls_gaps
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_title
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.IN_PLACE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_in_place
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.GAPS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_gaps
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.ADD
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_add
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.PLACEHOLDER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_placeholder
  -
    type: 0
    fields:
      placeholder: ERM.RISK.ASSURANCE.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_save
  -
    fields:
      placeholder: ERM.RISK.ASSURANCE.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_assurance_saved_successfully
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_title
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_label
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.EMPTY_TEXT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_empty_text
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.OPTIONS.INCIDENTS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_options_incidents
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.OPTIONS.CLAIMS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_options_claims
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.OPTIONS.MORTALITY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_options_mortality
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.MODULE.OPTIONS.FEEDBACK
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_module_options_feedback
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.QUERY.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_query_label
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.QUERY.EMPTY_TEXT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_query_empty_text
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATE.FIELDS.NAME.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_create_fields_name_label
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.LIST.COLUMNS.MONITOR_NAME
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_list_columns_monitor_name
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.LIST.COLUMNS.QUERY_NAME
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_list_columns_query_name
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.LIST.COLUMNS.COUNT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_list_columns_count
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.LIST.NO_MONITORS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_list_no_monitors
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.LIST.CONFIRM_DELETE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_confirm_delete
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.CREATED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_created_successfully
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_removed_successfully
  -
    fields:
      placeholder: ERM.RISK.RISK_MONITORS.SHOW_LIST
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_monitors_show_list
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.NOT_CURRENTLY_ESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_not_currently_escalated
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_title
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.FIELDS.COMMENTS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_fields_comments
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.FIELDS.TARGET_SERVICE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_fields_target_service_label
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.FIELDS.TARGET_SERVICE.EMPTY_TEXT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_fields_target_service_empty_text
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_save
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATE.BUTTON
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalate_button
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_saved_successfully
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.BANNERS.PENDING_OUTBOUND
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_banners_pending_outbound
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.BANNERS.PENDING_INBOUND
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_banners_pending_inbound
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.PENDING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_pending
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ACCEPTED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_accepted
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.REJECTED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_rejected
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.DATE.AND.OWNER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_date_and_owner
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.ESCALATED.FROM
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_escalation_from
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.DEESCALATED.FROM
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_deescalated_from
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.COMMENTS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_comments
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.HISTORY.COMMENTS.OPTIONAL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_history_comments_optional
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.MANAGE_REQUEST.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_manage_request_title
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.MANAGE_REQUEST.FIELDS.COMMENTS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_manage_request_fields_comments
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.OR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_or
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.MANAGE_REQUEST.REJECT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_manage_request_reject
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.MANAGE_REQUEST.ACCEPT
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_manage_request_accept
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalated
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ESCALATED_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_escalated_title
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.DEESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_deescalated
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.DEESCALATED_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_deescalated_title
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.REJECT_SUCCESS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_reject_success
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.ACCEPT_SUCCESS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_accept_success
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.DEESCALATE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_deescalate_title
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.DEESCALATE.BUTTON
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_deescalate_button
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.DEESCALATE.SUCCESS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_deescalate_success
  -
    fields:
      placeholder: ERM.RISK.RISK_GRADING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_grading
  -
    fields:
      placeholder: ERM.RISK.ATTACHMENTS.NEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_attachments_new
  -
    fields:
      placeholder: ERM.RISK.REVIEWS.NEXT_REVIEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_next_review_date
  -
    fields:
      placeholder: ERM.RISK.REVIEWS.PERFORM_REVIEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_perform_review
  -
    fields:
      placeholder: ERM.RISK.REVIEWS.LIST.COLUMNS.REVIEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_list_columns_review_date
  -
    fields:
      placeholder: ERM.RISK.REVIEWS.LIST.COLUMNS.REVIEWER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_list_columns_reviewer
  -
    fields:
      placeholder: ERM.RISK.REVIEWS.LIST.COLUMNS.COMMENTS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_list_columns_comments
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TABS.RISK_DETAILS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_reviews_form_tabs_risk_details
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TABS.SERVICES_AND_LOCATIONS
      pointer: ERM.RISK.NAV.SERVICES_AND_LOCATIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_reviews_form_tabs_services_and_locations
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TABS.ACTIONS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_reviews_form_tabs_actions
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TABS.CONTROLS_AND_ASSURANCE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_reviews_form_tabs_controls_and_assurance
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TABS.COMPLETE_REVIEW
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_reviews_form_tabs_complete_review
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_title
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_summary
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.SECTIONS.REVIEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_sections_review
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.FIELDS.COMMENTS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_reviews_form_fields_comments
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.FIELDS.COMMENTS.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_reviews_form_fields_comments_label
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.FIELDS.REVIEW_DATE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_reviews_form_fields_review_date_label
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.FIELDS.REVIEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: erm_risk_reviews_form_fields_review_date
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_save
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_saved_successfully
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.FORM.CLOSE_RISK
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_form_close_risk
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.NAV.BACK_TO_REVIEW_LIST
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_nav_back_to_review_list
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.MODAL_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_modal_title
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.CONFIRM_DELETE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_confirm_delete
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_removed_successfully
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.HISTORY.MODAL_TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_history_modal_title
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.HISTORY.NOT_SET
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_history_not_set
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.VIEW_HISTORY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_view_history
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.HISTORY.USERNAME
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_history_username
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.HISTORY.NEW_DATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_history_new_date
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.HISTORY.REASON
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_history_reason
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.ID
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_accreditation'
    ref: erm_risk_registers_list_columns_id
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.TITLE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_accreditation'
        - domain: '@domain_users'
        - domain: '@domain_acl_groups'
    ref: erm_risk_registers_list_columns_title
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.PARENT_REGISTERS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_registers_list_columns_parent_registers
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.ACTIVE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_registers_list_columns_active
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.ACTIVE_RISKS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_registers_list_columns_active_risks
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.INACTIVE_RISKS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_registers_list_columns_inactive_risks
  -
    fields:
      placeholder: ERM.RISK_REGISTERS.LIST.COLUMNS.PENDING_RISKS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_registers_list_columns_pending_risks
  -
    fields:
      placeholder: ERM.RISK_REGISTER.NAV.DETAILS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_nav_details
  -
    fields:
      placeholder: ERM.RISK_REGISTER.NAV.ACCESS_CONTROL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_nav_access_control
  -
    fields:
      placeholder: ERM.RISK_REGISTER.NAV.BACK_TO_REGISTER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_nav_back_to_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.NAV.BACK_TO_REGISTERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_nav_back_to_registers
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.PENDING
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_pending
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.NEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_new
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.ESCALATED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_escalated
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.ACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_active
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.REVIEW_REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_review_required
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.REVIEW_OVERDUE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_review_overdue
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.INACTIVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_inactive
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.CLOSED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_closed
  -
    fields:
      placeholder: ERM.RISK_REGISTER.WORKFLOW.REJECTED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_workflow_rejected
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.NEW
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_new
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.LIST.COLUMNS.ID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_list_columns_id
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.LIST.COLUMNS.NAME
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_list_columns_name
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.NAV.DETAILS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_nav_details
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.NAV.ACCESS_CONTROL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_nav_access_control
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.NAV.BACK_TO_RISK_TRACKER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_nav_back_to_risk_tracker
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.NAV.BACK_TO_RISK_TRACKERS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_nav_back_to_risk_trackers
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.FORM.NAME
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_form_name
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.FORM.DESCRIPTION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_form_description
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_save
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.DELETE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_delete
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_saved_successfully
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.DELETED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_deleted_successfully
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.RISK_REMOVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_risk_removed_successfully
  -
    fields:
      placeholder: ERM.RISK_TRACKERS.RISKS
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_trackers_risks
  -
    fields:
      placeholder: ERM.RISK.FILTERS.NO_SERVICE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_no_service
  -
    fields:
      placeholder: ERM.RISK.FILTERS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_title
  -
    fields:
      placeholder: ERM.RISK.FILTERS.NO_SERVICE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_no_service_label
  -
    fields:
      placeholder: ERM.RISK.FILTERS.LOCATION
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_location
  -
    fields:
      placeholder: ERM.RISK.FILTERS.LOCATION.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_location_label
  -
    fields:
      placeholder: ERM.RISK.FILTERS.SERVICE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_service
  -
    fields:
      placeholder: ERM.RISK.FILTERS.SERVICE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_service_label
  -
    fields:
      placeholder: ERM.ESCALATION_NODE.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
        -
          domain: '@domain_acl_groups'
    ref: erm_escalation_node_singular
  -
    fields:
      placeholder: ERM.ESCALATION_NODE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
        -
          domain: '@domain_acl_groups'
    ref: erm_escalation_node_title
  -
    fields:
      placeholder: ERM.ESCALATION_NODE.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
        -
          domain: '@domain_acl_groups'
    ref: erm_escalation_node_plural
  -
    fields:
      placeholder: ERM.ESCALATION_NODE.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
    ref: erm_escalation_node_search
  -
    fields:
      placeholder: ERM.ESCALATION_NODE.ID
      type: 0
      pointer: 'COMMON.ID'
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
        -
          domain: '@domain_acl_groups'
    ref: erm_escalation_node_id
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.REVIEW_DATE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_review_date
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.REASON
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_reason
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.CONFIRM_CHANGE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_confirm_change
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.SUCCESS.UPDATED
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_success_updated
  -
    fields:
      placeholder: ERM.RISK_REVIEWS.EDIT_REVIEW_DATE.ERRORS.DATE_NOT_CHANGED
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_reviews_edit_review_date_errors_date_not_changed
  -
    fields:
      placeholder: ERM.DATASOURCE.COUNTRY_LIST
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_country_list
  -
    fields:
      placeholder: ERM.DATASOURCE.MULTI_LIST
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_multi_list
  -
    fields:
      placeholder: ERM.DATASOURCE.RISK_FILTER_NO_SERVICE
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_risk_filter_no_service
  -
    fields:
      placeholder: ERM.DATASOURCE.RISK_TYPES
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_risk_types
  -
    fields:
      placeholder: ERM.FORM_TYPE.RISK_FORM
      type: 1
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_form_types'
    ref: erm_form_type_risk_form
  -
    fields:
      placeholder: ERM.FORM_TYPE.RISK_FILTER_FORM
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_form_types'
    ref: erm_form_type_risk_filter_form
  -
    fields:
      placeholder: ERM.FORM_TYPE.RISK_REVIEW_FORM
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_form_types'
    ref: erm_form_type_risk_review_form
  -
    fields:
      placeholder: ERM.FORM_TYPE.RISK_REGISTER_FORM
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_form_types'
    ref: erm_form_type_risk_register_form
  -
    fields:
      placeholder: ERM.FORM_TYPE.CHECKLISTS_FORM
      type: 0
      domains:
        -
          domain: '@domain_checklists'
    ref: erm_form_type_checklists_form
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.UNITED_KINGDOM
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_united_kingdom
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.GERMANY
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_germany
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.FRANCE
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_france
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.SPAIN
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_spain
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.LABEL1
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_label1
  -
    fields:
      placeholder: ERM.DATASOURCE_ITEM.ONLY_RISKS_WITH_NO_SERVICE
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_datasource_item_only_risks_with_no_service
  -
    fields:
      placeholder: ERM.FORMS.FORM1
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_forms_form1
  -
    fields:
      placeholder: ERM.FORMS.FORM2
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_forms_form2
  -
    fields:
      placeholder: ERM.FORMS.TEST_FORM
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_forms_test_form
  -
    fields:
      placeholder: ERM.FORMS.REVIEW
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_forms_review
  -
    fields:
      placeholder: ERM.FIELDS.AGE.LABEL
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_custom_fields_age_label
  -
    fields:
      placeholder: ERM.FIELDS.AGE.TITLE
      type: 1
      domains:
      - domain: '@domain_enterprise_risk_manager'
    ref: erm_custom_fields_age_title
  -
    fields:
      placeholder: ERM.RISK.DEFAULT_FORM.SECTIONS.RISK_REGISTER.LABEL
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_sections_risk_register_label
  -
    fields:
      placeholder: ERM.RISK.RISK_TYPE.SELECT_TYPE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_type_select_type
  -
    fields:
      placeholder: ERM.RISK.RISK_SUBTYPE.SELECT_SUBTYPE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_subtype_select_subtype
  -
    fields:
      placeholder: ERM.RISK.RISK_TERTIARY_SUBTYPE.SELECT_TERTIARY_SUBTYPE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_tertiary_subtype_select_tertiary_subtype
  -
    fields:
      placeholder: ERM.RISK.RISK_TERTIARY_SUBTYPE.NO_TYPES
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_tertiary_subtype_no_types
  -
    fields:
      placeholder: ERM.RISK.RISK_TERTIARY_SUBTYPE.NO_SUBTYPES
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_tertiary_subtype_no_subtypes
  -
    fields:
      placeholder: ERM.RISK.RISK_TERTIARY_SUBTYPE.NO_TERTIARY_SUBTYPES
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_tertiary_subtype_no_tertiary_subtypes
  -
    fields:
      placeholder: ERM.RISK_REGISTER.ASSOCIATE_MEDICINE_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_associate_medicine_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.SURGERY_AND_CANCER_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_surgery_and_cancer_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.EAR_NOSE_AND_THROAT_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_ear_nose_and_throat_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.GENERAL_SURGERY_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_general_surgery_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.OPTHALMOLOGY_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_opthalmology_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.MEDICINE_AND_EMERGENCY_CARE_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_medicine_and_emergency_care_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.CARDIOLOGY_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_cardiology_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.CARE_OF_THE_ELDERLY_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_care_of_the_elderly_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.MINOR_INJURIES_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_minor_injuries_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.ESTATES_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_estates_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.GENERAL_MAINTENANCE_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_general_maintenance_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.LINEN_SERVICES_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_linen_services_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.SITE_SERVICES_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_site_services_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.CATERING_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_catering_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.CORPORATE_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_corporate_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.HR_AND_PAYROLL_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_hr_and_payroll_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.COMMUNICATIONS_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_communications_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER.SERVICES_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_services_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER_HIERARCHY_RISK_REGISTER
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_hierarchy_risk_register
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_PRINCIPAL_OBJECTIVE_ONE_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_principal_objective_one_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_PRINCIPAL_OBJECTIVE_ONE_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_principal_objective_one_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_ONE_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_one_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_ONE_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_one_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_TWO_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_two_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_TWO_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_two_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_PRINCIPAL_OBJECTIVE_TWO_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_principal_objective_two_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_PRINCIPAL_OBJECTIVE_TWO_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_principal_objective_two_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_THREE_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_three_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_THREE_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_three_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_FOUR_TITLE
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_four_title
  -
    fields:
      placeholder: ERM.RISK_REGISTER_OBJECTIVE_DETAILED_OBJECTIVE_FOUR_SUMMARY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_objective_detailed_objective_four_summary
  -
    fields:
      placeholder: ERM.RISK_REGISTER_UPDATED_SUCCESSFULLY
      type: 0
      domains:
      -
        domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER_CREATION_ERROR
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_creation_error
  - fields:
      placeholder: ERM.RISK_REGISTER_CREATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_created_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER_DELETED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_deleted_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER_DELETE_FAIL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_delete_fail
  - fields:
      placeholder: ERM.RISK_OPENED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_opened_successfully
  - fields:
      placeholder: ERM.NAVIGATE_TO_RISK_REVIEW_BEFORE_CLOSE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_navigate_to_risk_review_before_close
  - fields:
      placeholder: ERM.CLOSE_DATE_REQUIRED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_close_date_required
  - fields:
      placeholder: ERM.CLOSURE_REASON_REQUIRED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_closure_reason_required
  - fields:
      placeholder: ERM.RISK_CLOSED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_closed_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.CONTROL.ADDED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_control_added_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.CONTROL.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_control_removed_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.ATTACHMENT_SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_attachment_saved_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.SERVICE_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_service_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.SELECTED_MATRIX_LAYOUT_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_selected_matrix_layout_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_BOUNDARY_COLOURS_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_boundary_colours_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.BUTTON.EDIT_BOUNDARIES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_button_edit_boundaries
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.TITLE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_title
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.CODE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_code
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_label
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.MINIMUM
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_minimum
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.MAXIMUM
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_maximum
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.COLOUR
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_colour
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.COLUMN.DELETE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_column_delete
  - fields:
      placeholder: ERM.RISK_REGISTER.BOUNDARIES_MODAL.BUTTON.ADD_ANOTHER
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_boundaries_modal_button_add_another
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_BOUNDARY_DUPLICATES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_boundary_duplicates
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_BOUNDARY_BOUNDARIES_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_boundary_boundaries_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.BUTTON.SAVE_LABELS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_button_save_labels
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_LABELS_UPDATED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_labels_updated_successfully
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_LABELS_ID_MISSING
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_labels_id_missing
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_LABELS_NOT_PROVIDED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_labels_not_provided
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_LABELS_TRANSLATION_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_labels_translation_not_found
  - fields:
      placeholder: ERM.RISK_REGISTER.MATRIX_LABELS_NO_CHANGES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_register_matrix_labels_no_changes
  - fields:
      placeholder: ERM.RISK_TRACKERS.NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_tracker_not_found
  - fields:
      placeholder: ERM.RISK.NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_not_found
  - fields:
      placeholder: ERM.RISK_TRACKERS.RISK.NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_tracker_risk_not_found
  - fields:
      placeholder: ERM.NEW_RISK_TYPE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
    ref: erm_new_risk_type
  - fields:
      placeholder: ERM.RISK_REGISTER.ERRORS.CANT_FIND_PARENT
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
    ref: erm_risk_register_errors_cant_find_parent
  - fields:
      placeholder: ERM.RISK_REGISTER.ERRORS.PARENT_REQUIRED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
    ref: erm_risk_register_errors_parent_required
  - fields:
      placeholder: ERM.SUCCESS.NOTES.SAVED
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_success_notes_saved
  - fields:
      placeholder: ERM.SUCCESS.NOTES.DELETED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_success_notes_deleted
  - fields:
      placeholder: ERM.RISK.NAV.COMMUNICATION
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_form_panels'
    ref: erm_risk_nav_communication
  - fields:
      placeholder: ERM.RISK.COMMUNICATIONS.HEADER
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_communications_header
  - fields:
      placeholder: ERM.AUDIT.ENTITIES.RISK
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_audit_entities_risk
  - fields:
      placeholder: ERM.AUDIT.ENTITIES.RISK_RATING
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_audit_entities_risk_rating
  - fields:
      placeholder: ERM.AUDIT.ENTITIES.RISK_ACTION
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_audit_entities_risk_action
  - fields:
      placeholder: ERM.AUDIT.ENTITIES.RISK_LOCATION
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_audit_entities_risk_location
  - fields:
      placeholder: ERM.RISK_TRACKERS.SAVE_CHANGES.MESSAGE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_trackers_save_changes_message
  - fields:
      placeholder: ERM.REVIEW.SERVICES
      pointer: 'MODULE.TITLE.SERVICES'
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
  - fields:
      placeholder: ERM.REVIEW.NO_SERVICES
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_review_no_services
  -
    fields:
      placeholder: ERM.ESCALATION.PERMISSION_INVALID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
    ref: erm_escalation_permission_invalid
  -
    fields:
      placeholder: ERM.MEDICATIONS.INVALID_MEDICATION_UUID
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_users'
    ref: erm_medications_invalid_medication_uuid
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.RISK_ID
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_risk_id
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.RISK_ID.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_risk_id_label
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.STATUS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_status
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.STATUS.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_status_label
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.COLUMNS.STATUS
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_columns_status
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.RISK.LEVEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_risk_level
  - fields:
      placeholder: ERM.RISK.FILTER_FORM.FIELDS.RISK.LEVEL.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filter_form_fields_risk_level_label
  - fields:
      placeholder: ERM.RISK_MONITORS.NEW
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_monitors_new
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.CLOSURE_REASON
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_closure_reason
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.CLOSURE_REASON.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_closure_reason_label
  - fields:
      placeholder: ERM.RISK_REGISTER.ERRORS.USER_NOT_ACTIVE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_user_not_active
  - fields:
      placeholder: ERM.RISK.RISK_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_type_label
  - fields:
      placeholder: ERM.RISK.RISK_SUBTYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_subtype_label
  - fields:
      placeholder: ERM.RISK.RISK_TERTIARY_SUBTYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_risk_tertiary_subtype_label
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.OWNER.LABEL
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_owner_label
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.OWNER.TITLE
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_owner
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.ATTACHMENTS.TITLE
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_attachments_title
  - fields:
      placeholder: ERM.RISK.DEFAULT_FORM.FIELDS.ATTACHMENTS.LABEL
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_default_form_fields_attachments_label
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.SEARCH.MODULE.LABEL
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_search_module_label
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.SEARCH.RECORD_ID.LABEL
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_search_record_id_label
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.SEARCH.REASON.LABEL
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_search_reason_label
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.SEARCH.SAVE
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_search_save
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LINK_CREATE_SUCCESS
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_link_create_success
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LINK_UPDATE_SUCCESS
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_link_update_success
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LIST.MODULE
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_list_module
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LIST.RECORD_ID
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_list_record_id
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LIST.REASON
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_list_reason
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.RISK_LINK_TO_SELF
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_risk_link_to_self
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.RISK_NOT_FOUND
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_risk_not_found
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.NO_MODULE_PROVIDED
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_no_module_provided
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.MODULE_NOT_FOUND
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_module_not_found
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.NOT_LINKED_RISK_MODULE
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_not_linked_risk_module
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.NO_RECORD_ID
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_no_record_id
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.RECORD_NOT_FOUND
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_record_not_found
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.ERRORS.RECORD_ALREADY_LINKED
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_errors_record_already_linked
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LINK_DELETE_SUCCESS
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_link_delete_success
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LIST.STATUS
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_list_status
  - fields:
      placeholder: ERM.RISK.LINKED_RECORDS.LIST.DATE_OPENED
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_linked_records_list_date_opened
  - fields:
      placeholder: ERM.RISK.INVALID.DATE.REVIEW.OPENED
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_invalid_date_review_opened
  - fields:
      placeholder: ERM.RISK.INVALID.DATE.CLOSED.OPENED
      type: 1
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_erm_risk_invalid_date_closed_opened
  -
    fields:
      placeholder: ERM.RISK.ERRORS.CREATE_UPDATE
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_create_update
  -
    fields:
      placeholder: ERM.RISK.ERRORS.ESCALATIONS_NOT_IMPLEMENTED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_escalations_not_implemented
  -
    fields:
      placeholder: ERM.RISK.ERRORS.STATUS_MUST_PROVIDED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_status_must_provided
  -
    fields:
      placeholder: ERM.RISK.ERRORS.TECHNICAL_ERROR
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_technical_error
  -
    fields:
      placeholder: ERM.RISK.TARGET_REGISTER_NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_target_register_not_found
  -
    fields:
      placeholder: ERM.RISK.ESCALATION.TARGET_REGISTER_REQUIRED
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_escalation_target_register_required
  -
    fields:
      placeholder: ERM.RISK.ERRORS.CREATE_UPDATE_RISK
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_create_update_risk
  -
    fields:
      placeholder: ERM.RISK.ERRORS.TECHNICAL_ERROR_ERM16
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_technical_error_erm16
  - fields:
      placeholder: ERM.MY_RISKS.COLUMNS.OWNER
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_owner
  - fields:
      placeholder: ERM.MY_RISKS.COLUMNS.LAST_RISK_REVIEW_DATE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_last_risk_review_date
  - fields:
      placeholder: ERM.MY_RISKS.COLUMNS.LAST_RISK_UPDATED_DATE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
    ref: erm_my_risks_columns_last_risk_updated_date
  -
    fields:
      placeholder: ERM.RISK.FILTERS.OWNER.LABEL
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_owner_label
  -
    fields:
      placeholder: ERM.RISK.FILTERS.OWNER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_filters_owner
  -
    fields:
      placeholder: ERM.RISK.ERRORS.STATUS_MUST_BE_INTEGER
      type: 0
      domains:
        -
          domain: '@domain_enterprise_risk_manager'
    ref: erm_risk_errors_status_must_be_integer
