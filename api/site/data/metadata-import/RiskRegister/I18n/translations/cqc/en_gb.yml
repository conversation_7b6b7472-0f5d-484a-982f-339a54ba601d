entityClass: I18n\Entity\Translation
priority: 15
data:
  # FORM FIELDS
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_safe_title'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Safe"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_safe_label'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Safe"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_effective_title'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Effective"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_effective_label'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Effective"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_caring_title'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Caring"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_caring_label'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Caring"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_responsive_title'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Responsive"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_responsive_label'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Responsive"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_well_led_title'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Well-led"?'
  - fields:
      placeholder: '@erm_risk_default_form_fields_cqc_well_led_label'
      language: '@language_en_gb'
      value: 'How could this risk impact the CQC domain "Well-led"?'

  # DATASOURCE ITEMS
  # CQC Safe
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_1'
      language: '@language_en_gb'
      value: 'Safe - Could impact on protection from bullying, harassment, avoidable harm.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_2'
      language: '@language_en_gb'
      value: 'Safe - Could impact on protection and freedom being supported and respected.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_3'
      language: '@language_en_gb'
      value: 'Safe - Insufficient staff to keep people safe and meet their needs.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_4'
      language: '@language_en_gb'
      value: 'Safe - Could impact on medicines not being managed safely'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_5'
      language: '@language_en_gb'
      value: 'Safe - Could impact on protection, prevention or control of infection.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_6'
      language: '@language_en_gb'
      value: 'Safe - Could impact on ability to deal with incidents and accidents quickly and openly and learn from mistakes.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_safe_7'
      language: '@language_en_gb'
      value: 'Safe - Could impact on ensuring children, young people and adults who may be vulnerable are kept safe from harm.'

  # CQC Effective
  - fields:
      placeholder: '@erm_datasource_item_cqc_effective_1'
      language: '@language_en_gb'
      value: 'Effective - Lack of knowledge and skills leading to ineffective care'
  - fields:
      placeholder: '@erm_datasource_item_cqc_effective_2'
      language: '@language_en_gb'
      value: 'Effective - Consent to care and treatment is not in line with legislation'
  - fields:
      placeholder: '@erm_datasource_item_cqc_effective_3'
      language: '@language_en_gb'
      value: 'Effective - People are not supported to eat, drink and maintain a healthy diet.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_effective_4'
      language: '@language_en_gb'
      value: 'Effective - Poor access to health care services and ongoing support.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_effective_5'
      language: '@language_en_gb'
      value: 'Effective - Care is provided in accordance with your needs and choices.'

  # CQC Caring
  - fields:
      placeholder: '@erm_datasource_item_cqc_caring_1'
      language: '@language_en_gb'
      value: 'Caring - Lack of personalised approach, negative relationships with patients/public'
  - fields:
      placeholder: '@erm_datasource_item_cqc_caring_2'
      language: '@language_en_gb'
      value: "Caring - People's privacy and dignity is not respected."
  - fields:
      placeholder: '@erm_datasource_item_cqc_caring_3'
      language: '@language_en_gb'
      value: 'Caring - Poor support at end of life, lack of dignity & pain at death'
  - fields:
      placeholder: '@erm_datasource_item_cqc_caring_4'
      language: '@language_en_gb'
      value: 'Caring - Lack of access to advocates and/or encouragement to express views.'
  - fields:
      placeholder: '@erm_datasource_item_cqc_caring_5'
      language: '@language_en_gb'
      value: 'Caring - Family, and friends can’t contact service users.'

  # CQC Responsive
  - fields:
      placeholder: '@erm_datasource_item_cqc_responsive_1'
      language: '@language_en_gb'
      value: 'Responsive - Care is not personalised or supportive of individuals needs'
  - fields:
      placeholder: '@erm_datasource_item_cqc_responsive_2'
      language: '@language_en_gb'
      value: "Responsive - People's concerns and complaints are not listened to."
  - fields:
      placeholder: '@erm_datasource_item_cqc_responsive_3'
      language: '@language_en_gb'
      value: 'Responsive - Poor continuity of care between services'

  # CQC Well-led
  - fields:
      placeholder: '@erm_datasource_item_cqc_well_led_1'
      language: '@language_en_gb'
      value: 'Well led - Working culture is not positive, open, inclusive or empowering'
  - fields:
      placeholder: '@erm_datasource_item_cqc_well_led_2'
      language: '@language_en_gb'
      value: 'Well led - Good management and leadership is not demonstrated'
  - fields:
      placeholder: '@erm_datasource_item_cqc_well_led_3'
      language: '@language_en_gb'
      value: 'Well led - High quality care is not delivered'
  - fields:
      placeholder: '@erm_datasource_item_cqc_well_led_4'
      language: '@language_en_gb'
      value: 'Well led - Could impact on external working relationships'
  - fields:
      placeholder: '@erm_datasource_item_cqc_well_led_5'
      language: '@language_en_gb'
      value: 'Well led - Staff do not feel confident to report concerns and/or are not supported'
  - fields:
      placeholder: '@erm_risk_filters_title'
      language: '@language_en_gb'
      value: 'Filter Risks'
