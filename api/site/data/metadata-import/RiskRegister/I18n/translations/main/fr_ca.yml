entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@erm_risk_singular', language: '@language_fr_ca', value: Risque } }
  - { fields: { placeholder: '@erm_risk_plural', language: '@language_fr_ca', value: Risques } }
  - { fields: { placeholder: '@erm_risk_search', language: '@language_fr_ca', value: 'Recherche de risques' } }
  - { fields: { placeholder: '@erm_module_title', language: '@language_fr_ca', value: 'Gestionnaire du risque de l''entreprise' } }
  - { fields: { placeholder: '@erm_nav_my_risks', language: '@language_fr_ca', value: 'Mes risques' } }
  - { fields: { placeholder: '@erm_nav_risk_registers', language: '@language_fr_ca', value: 'Registres de risques' } }
  - { fields: { placeholder: '@erm_nav_risk_trackers', language: '@language_fr_ca', value: 'Suivis des risques' } }
  - { fields: { placeholder: '@erm_nav_report_a_risk', language: '@language_fr_ca', value: 'Signaler un risque' } }
  - { fields: { placeholder: '@erm_nav_admin_objectives', language: '@language_fr_ca', value: Objectifs } }
  - { fields: { placeholder: '@erm_nav_admin_risk_registers', language: '@language_fr_ca', value: 'Registres de risques' } }
  - { fields: { placeholder: '@erm_nav_admin_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@erm_nav_admin_risk_matrix', language: '@language_fr_ca', value: 'Matrice de risque' } }
  - { fields: { placeholder: '@erm_loading_risks', language: '@language_fr_ca', value: 'Chargement des risques' } }
  - { fields: { placeholder: '@erm_loading_risk_form', language: '@language_fr_ca', value: 'Chargement du formulaire de risque' } }
  - { fields: { placeholder: '@erm_loading_risk_register', language: '@language_fr_ca', value: 'Chargement du registre de risques' } }
  - { fields: { placeholder: '@erm_loading_risk_registers', language: '@language_fr_ca', value: 'Chargement des registres de risques' } }
  - { fields: { placeholder: '@erm_loading_risk_trackers', language: '@language_fr_ca', value: 'Chargement des suivis de risques' } }
  - { fields: { placeholder: '@erm_loading_risk_workflow', language: '@language_fr_ca', value: 'Chargement du flux de travail de risque' } }
  - { fields: { placeholder: '@erm_loading_objectives', language: '@language_fr_ca', value: 'Chargement des objectifs' } }
  - { fields: { placeholder: '@erm_loading_heatmap', language: '@language_fr_ca', value: 'Chargement de la carte de densité' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_title', language: '@language_fr_ca', value: 'Objectifs d''organisation' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_add', language: '@language_fr_ca', value: 'Ajouter un objectif racine' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_form_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_form_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_delete', language: '@language_fr_ca', value: 'Supprimer l''objectif' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_delete_confirm', language: '@language_fr_ca', value: 'Êtes-vous certain de vouloir supprimer cet objectif?' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_save', language: '@language_fr_ca', value: 'Enregistrer l''objectif' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_saved_successfully', language: '@language_fr_ca', value: 'Objectif enregistré avec succès' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_removed_successfully', language: '@language_fr_ca', value: 'Objectif supprimé avec succès' } }
  - { fields: { placeholder: '@erm_admin_organisational_objectives_untitled_node', language: '@language_fr_ca', value: 'Nœud sans titre' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model', language: '@language_fr_ca', value: 'Modèle d''escalade' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_updated_successfully', language: '@language_fr_ca', value: 'Modèle d''escalade mis à jour avec succès' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_options_immediate_parent_only', language: '@language_fr_ca', value: 'Parent immédiat seulement' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_options_any_parent', language: '@language_fr_ca', value: 'Tout parent' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_include_escalation_model_options_enabled', language: '@language_fr_ca', value: 'Activé' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_include_escalation_model_options_disabled', language: '@language_fr_ca', value: 'Désactivé' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_is_active', language: '@language_fr_ca', value: 'Actif?' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_activate', language: '@language_fr_ca', value: 'Activer le registre de risques' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_deactivate', language: '@language_fr_ca', value: 'Désactiver le registre de risques' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@erm_admin_risk_registers_status_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@erm_admin_risk_registers_status_inactive', language: '@language_fr_ca', value: Inactif } }
  - { fields: { placeholder: '@erm_admin_risk_registers_tabs_register_owners', language: '@language_fr_ca', value: 'Propriétaires du registre' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_tabs_risks', language: '@language_fr_ca', value: Risques } }
  - { fields: { placeholder: '@erm_admin_risk_registers_deactivated_successfully', language: '@language_fr_ca', value: 'Registre de risques désactivé avec succès' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_activated_successfully', language: '@language_fr_ca', value: 'Registre de risques activé avec succès' } }
  - { fields: { placeholder: '@erm_my_risks_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@erm_my_risks_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@erm_my_risks_columns_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@erm_my_risks_columns_subtype', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@erm_my_risks_columns_tertiary_subtype', language: '@language_fr_ca', value: Sous-sous-type } }
  - { fields: { placeholder: '@erm_my_risks_columns_review_date', language: '@language_fr_ca', value: 'Date de révision du risque' } }
  - { fields: { placeholder: '@erm_my_risks_columns_current_grading', language: '@language_fr_ca', value: 'Qualification actuelle du risque' } }
  - { fields: { placeholder: '@erm_my_risks_columns_escalated', language: '@language_fr_ca', value: 'Escaladé?' } }
  - { fields: { placeholder: '@erm_my_risks_columns_escalated_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@erm_my_risks_columns_escalated_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@erm_risk_nav_risk_details', language: '@language_fr_ca', value: 'Détails du risque' } }
  - { fields: { placeholder: '@erm_risk_nav_services_and_locations', language: '@language_fr_ca', value: 'Services et emplacements' } }
  - { fields: { placeholder: '@erm_risk_nav_objectives', language: '@language_fr_ca', value: Objectifs } }
  - { fields: { placeholder: '@erm_risk_nav_controls_and_assurance', language: '@language_fr_ca', value: 'Contrôles et assurance' } }
  - { fields: { placeholder: '@erm_risk_nav_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@erm_risk_nav_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@erm_risk_nav_risk_monitors', language: '@language_fr_ca', value: 'Moniteurs de risque' } }
  - { fields: { placeholder: '@erm_risk_nav_contacts', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@erm_risk_nav_reviews', language: '@language_fr_ca', value: 'Révisions de risque' } }
  - { fields: { placeholder: '@erm_risk_nav_escalation', language: '@language_fr_ca', value: Escalade } }
  - { fields: { placeholder: '@erm_risk_nav_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@erm_risk_nav_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@erm_risk_nav_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@erm_risk_nav_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@erm_risk_nav_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@erm_risk_banners_is_new', language: '@language_fr_ca', value: 'Ce risque est nouveau' } }
  - { fields: { placeholder: '@erm_risk_banners_inactive', language: '@language_fr_ca', value: 'Ce risque n''est pas actif en ce moment' } }
  - { fields: { placeholder: '@erm_risk_banners_escalated', language: '@language_fr_ca', value: 'Ce risque a été escaladé au registre {{riskRegisterName}}' } }
  - { fields: { placeholder: '@erm_risk_banners_pending_inbound_escalation', language: '@language_fr_ca', value: 'Ce risque est en attente d''escalade dans ce registre des risques' } }
  - { fields: { placeholder: '@erm_risk_banners_pending_inbound_deescalation', language: '@language_fr_ca', value: 'Ce risque est en attente de désescalade dans ce registre des risques' } }
  - { fields: { placeholder: '@erm_risk_banners_pending_outbound_escalation', language: '@language_fr_ca', value: 'Ce risque est en attente d''escalade au registre {{targetRegister}}' } }
  - { fields: { placeholder: '@erm_risk_banners_pending_outbound_deescalation', language: '@language_fr_ca', value: 'Ce risque est en attente de désescalade vers le registre {{targetRegister}}' } }
  - { fields: { placeholder: '@erm_risk_default_form_title', language: '@language_fr_ca', value: 'Formulaire de risque simple' } }
  - { fields: { placeholder: '@erm_risk_default_form_summary', language: '@language_fr_ca', value: 'Un formulaire de risque simple for interaction générale' } }
  - { fields: { placeholder: '@erm_risk_default_form_sections_title_and_reference_label', language: '@language_fr_ca', value: 'Titre et référence' } }
  - { fields: { placeholder: '@erm_risk_default_form_sections_title_and_reference_help', language: '@language_fr_ca', value: 'Remplissez tous les champs' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_no_types', language: '@language_fr_ca', value: 'Aucun type attribué à ce risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_sections_risk_type_label', language: '@language_fr_ca', value: 'Type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_title', language: '@language_fr_ca', value: 'Titre du risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_title_label', language: '@language_fr_ca', value: 'Titre du risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_description', language: '@language_fr_ca', value: 'Description du risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_description_label', language: '@language_fr_ca', value: 'Description du risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type', language: '@language_fr_ca', value: 'Type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_label', language: '@language_fr_ca', value: 'Type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_empty_text', language: '@language_fr_ca', value: 'Sélectionnez un type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_hazard', language: '@language_fr_ca', value: Danger } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_infection', language: '@language_fr_ca', value: Infection } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_operational_risk', language: '@language_fr_ca', value: 'Risque opérationnel' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype', language: '@language_fr_ca', value: 'Sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_label', language: '@language_fr_ca', value: 'Sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype', language: '@language_fr_ca', value: 'Sous-sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype_label', language: '@language_fr_ca', value: 'Sous-sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_organisational_objectives', language: '@language_fr_ca', value: 'Objectifs d''organisation' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_organisational_objectives_label', language: '@language_fr_ca', value: 'Objectifs d''organisation' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_ratings', language: '@language_fr_ca', value: 'Classes de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_ratings_label', language: '@language_fr_ca', value: 'Classes de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_relationships', language: '@language_fr_ca', value: Relations } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_relationships_label', language: '@language_fr_ca', value: Relations } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_reported_by', language: '@language_fr_ca', value: 'Signalé par' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_reported_by_label', language: '@language_fr_ca', value: 'Risque signalé par' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_country', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_country_label', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_gender', language: '@language_fr_ca', value: Sexe } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_gender_label', language: '@language_fr_ca', value: Sexe } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_country_2', language: '@language_fr_ca', value: 'Pays 2' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_country_2_label', language: '@language_fr_ca', value: 'Pays 2' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_comments', language: '@language_fr_ca', value: 'Commentaires de révision' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_comments_label', language: '@language_fr_ca', value: 'Commentaires de révision' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_empty_text', language: '@language_fr_ca', value: 'Sélectionnez un sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_head_hazard', language: '@language_fr_ca', value: 'Danger pour la tête' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_slip_hazard', language: '@language_fr_ca', value: 'Danger de glissade' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_respiratory_hazard', language: '@language_fr_ca', value: 'Danger respiratoire' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_airborne_infection', language: '@language_fr_ca', value: 'infection à transmission aérienne' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_contact_infection', language: '@language_fr_ca', value: 'Infection par contact' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_financial', language: '@language_fr_ca', value: Financier } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_clinical', language: '@language_fr_ca', value: Clinique } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype_empty_text', language: '@language_fr_ca', value: 'Sélectionnez un sous-sous-type de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_sections_risk_dates_label', language: '@language_fr_ca', value: 'Dates de risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_opened_date', language: '@language_fr_ca', value: 'Date d''ouverture du risque' } }
  - { fields: { placeholder: '@erm_risk_default_form_fields_risk_opened_date_label', language: '@language_fr_ca', value: 'Date d''ouverture du risque' } }
  - { fields: { placeholder: '@erm_risk_form_close', language: '@language_fr_ca', value: 'Clôturer le risque' } }
  - { fields: { placeholder: '@erm_risk_form_reopen', language: '@language_fr_ca', value: 'Rouvrir le risque' } }
  - { fields: { placeholder: '@erm_risk_form_save', language: '@language_fr_ca', value: 'Enregistrer le risque' } }
  - { fields: { placeholder: '@erm_risk_created_successfully', language: '@language_fr_ca', value: 'Risque créé avec succès' } }
  - { fields: { placeholder: '@erm_risk_saved_successfully', language: '@language_fr_ca', value: 'Risque enregistré avec succès' } }
  - { fields: { placeholder: '@erm_risk_accepted_successfully', language: '@language_fr_ca', value: 'Risque accepté avec succès' } }
  - { fields: { placeholder: '@erm_risk_rejected_successfully', language: '@language_fr_ca', value: 'Risque rejeté avec succès' } }
  - { fields: { placeholder: '@erm_components_heatmap_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@erm_components_heatmap_tabs_initial_grading', language: '@language_fr_ca', value: 'Qualification initiale' } }
  - { fields: { placeholder: '@erm_components_heatmap_tabs_current_grading', language: '@language_fr_ca', value: 'Qualification courante' } }
  - { fields: { placeholder: '@erm_components_heatmap_tabs_target_grading', language: '@language_fr_ca', value: 'Qualification cible' } }
  - { fields: { placeholder: '@erm_components_heatmap_current_rating', language: '@language_fr_ca', value: 'Classe actuelle' } }
  - { fields: { placeholder: '@erm_components_heatmap_current_risk_level', language: '@language_fr_ca', value: 'Niveau de risque actuel' } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_consequence', language: '@language_fr_ca', value: Conséquence } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_impact', language: '@language_fr_ca', value: Incidence } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_likelihood', language: '@language_fr_ca', value: Probabilité } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_negligible', language: '@language_fr_ca', value: Négligeable } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_minor', language: '@language_fr_ca', value: Mineur } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_moderate', language: '@language_fr_ca', value: Moyen } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_significant', language: '@language_fr_ca', value: Important } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_major', language: '@language_fr_ca', value: Majeur } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_catastrophic', language: '@language_fr_ca', value: Catastrophique } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_almost_certain', language: '@language_fr_ca', value: 'Presque certain' } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_likely', language: '@language_fr_ca', value: Probable } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_possible', language: '@language_fr_ca', value: Possible } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_unlikely', language: '@language_fr_ca', value: Improbable } }
  - { fields: { placeholder: '@erm_components_heatmap_grid_rare', language: '@language_fr_ca', value: Rare } }
  - { fields: { placeholder: '@erm_risk_nav_back_to_my_risks', language: '@language_fr_ca', value: 'Retour à Mes risques' } }
  - { fields: { placeholder: '@erm_risk_services_and_locations_save_service', language: '@language_fr_ca', value: 'Enregistrer le changement de service' } }
  - { fields: { placeholder: '@erm_risk_objectives_selected_objectives', language: '@language_fr_ca', value: 'Objectifs sélectionnés' } }
  - { fields: { placeholder: '@erm_risk_objectives_none_attached', language: '@language_fr_ca', value: 'Ce risque n''a pas d''objectif d''organisation associé' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_title', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_add', language: '@language_fr_ca', value: 'Ajouter un facteur contributif' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_none_attached', language: '@language_fr_ca', value: 'Ce dossier ne comporte pas de facteurs contributifs' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_saved_successfully', language: '@language_fr_ca', value: 'Facteur contributif enregistré avec succès' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_removed_successfully', language: '@language_fr_ca', value: 'Facteur contributif supprimé avec succès' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_controls_add', language: '@language_fr_ca', value: 'Ajouter un contrôle' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_controls_select', language: '@language_fr_ca', value: 'Sélectionnez un contrôle' } }
  - { fields: { placeholder: '@erm_risk_contributory_factors_controls_none_available', language: '@language_fr_ca', value: 'Aucun contrôle disponible à joindre' } }
  - { fields: { placeholder: '@erm_risk_controls_in_place', language: '@language_fr_ca', value: 'Contrôles en place' } }
  - { fields: { placeholder: '@erm_risk_controls_gaps', language: '@language_fr_ca', value: 'Écarts dans les contrôles' } }
  - { fields: { placeholder: '@erm_risk_assurance_title', language: '@language_fr_ca', value: Assurance } }
  - { fields: { placeholder: '@erm_risk_assurance_in_place', language: '@language_fr_ca', value: 'Assurance en place' } }
  - { fields: { placeholder: '@erm_risk_assurance_gaps', language: '@language_fr_ca', value: 'Écarts dans les assurances' } }
  - { fields: { placeholder: '@erm_risk_assurance_add', language: '@language_fr_ca', value: 'Ajouter une assurance' } }
  - { fields: { placeholder: '@erm_risk_assurance_placeholder', language: '@language_fr_ca', value: 'Entrez l''assurance' } }
  - { fields: { placeholder: '@erm_risk_assurance_save', language: '@language_fr_ca', value: 'Enregistrer l''assurance' } }
  - { fields: { placeholder: '@erm_risk_assurance_saved_successfully', language: '@language_fr_ca', value: 'Assurance enregistrée avec succès' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_title', language: '@language_fr_ca', value: 'Créer un moniteur de risque' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_label', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_empty_text', language: '@language_fr_ca', value: 'Veuillez sélectionner' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_incidents', language: '@language_fr_ca', value: Incidents } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_claims', language: '@language_fr_ca', value: Réclamations } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_mortality', language: '@language_fr_ca', value: Mortalité } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_feedback', language: '@language_fr_ca', value: Rétroaction } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_query_label', language: '@language_fr_ca', value: 'Requête enregistré' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_query_empty_text', language: '@language_fr_ca', value: 'Veuillez sélectionner' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_name_label', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_monitor_name', language: '@language_fr_ca', value: 'Nom du moniteur de risque' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_query_name', language: '@language_fr_ca', value: 'Nom de requête' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_count', language: '@language_fr_ca', value: Nombre } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_list_no_monitors', language: '@language_fr_ca', value: 'Aucun moniteur de risque défini dans ce module' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_confirm_delete', language: '@language_fr_ca', value: 'Supprimer ce moniteur de risque?' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_created_successfully', language: '@language_fr_ca', value: 'Moniteur de risque créé avec succès' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_removed_successfully', language: '@language_fr_ca', value: 'Moniteur de risque supprimé avec succès' } }
  - { fields: { placeholder: '@erm_risk_risk_monitors_show_list', language: '@language_fr_ca', value: 'Afficher la liste' } }
  - { fields: { placeholder: '@erm_risk_escalation_history', language: '@language_fr_ca', value: 'Historique d''escalade' } }
  - { fields: { placeholder: '@erm_risk_escalation_history_not_currently_escalated', language: '@language_fr_ca', value: 'Ce risque n''est pas dans un état transféré en ce moment' } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_title', language: '@language_fr_ca', value: 'Faire escalader le risque' } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_comments', language: '@language_fr_ca', value: Commentaires } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_target_service_label', language: '@language_fr_ca', value: 'Service cible' } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_target_service_empty_text', language: '@language_fr_ca', value: 'Sélectionnez un service cible' } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_save', language: '@language_fr_ca', value: 'Faire escalader le risque' } }
  - { fields: { placeholder: '@erm_risk_escalation_escalate_button', language: '@language_fr_ca', value: 'Faire escalader le risque vers {{targetRegister}}' } }
  - { fields: { placeholder: '@erm_risk_escalation_saved_successfully', language: '@language_fr_ca', value: 'Risque escaladé avec succès' } }
  - { fields: { placeholder: '@erm_risk_escalation_banners_pending_outbound', language: '@language_fr_ca', value: 'Ce risque est en attente d''escalade vers {{targetRegister}}' } }
  - { fields: { placeholder: '@erm_risk_escalation_banners_pending_inbound', language: '@language_fr_ca', value: 'Ce risque est en attente d''escalade dans ce registre des risques' } }
  - { fields: { placeholder: '@erm_risk_escalation_pending', language: '@language_fr_ca', value: 'En attente' } }
  - { fields: { placeholder: '@erm_risk_escalation_manage_request_title', language: '@language_fr_ca', value: 'Gérer une demande d''escalade' } }
  - { fields: { placeholder: '@erm_risk_escalation_manage_request_fields_comments', language: '@language_fr_ca', value: 'Commentaires de réponse' } }
  - { fields: { placeholder: '@erm_risk_escalation_or', language: '@language_fr_ca', value: ou } }
  - { fields: { placeholder: '@erm_risk_escalation_manage_request_reject', language: '@language_fr_ca', value: Rejeter } }
  - { fields: { placeholder: '@erm_risk_escalation_manage_request_accept', language: '@language_fr_ca', value: Accepter } }
  - { fields: { placeholder: '@erm_risk_escalation_escalated', language: '@language_fr_ca', value: Escaladé } }
  - { fields: { placeholder: '@erm_risk_escalation_escalated_title', language: '@language_fr_ca', value: 'Escalade en cours vers {{riskTarget}} depuis {{riskSource}}' } }
  - { fields: { placeholder: '@erm_risk_escalation_deescalated', language: '@language_fr_ca', value: Désescaladé } }
  - { fields: { placeholder: '@erm_risk_escalation_deescalated_title', language: '@language_fr_ca', value: 'Désescalade en cours vers {{riskTarget}} depuis {{riskSource}}' } }
  - { fields: { placeholder: '@erm_risk_escalation_reject_success', language: '@language_fr_ca', value: 'Escalade rejetée avec succès' } }
  - { fields: { placeholder: '@erm_risk_escalation_accept_success', language: '@language_fr_ca', value: 'Escalade acceptée avec succès' } }
  - { fields: { placeholder: '@erm_risk_escalation_deescalate_title', language: '@language_fr_ca', value: 'Désescalader le risque' } }
  - { fields: { placeholder: '@erm_risk_escalation_deescalate_button', language: '@language_fr_ca', value: 'Désescalader vers {{targetRegister}}' } }
  - { fields: { placeholder: '@erm_risk_escalation_deescalate_success', language: '@language_fr_ca', value: 'Risque désescaladé avec succès' } }
  - { fields: { placeholder: '@erm_risk_risk_grading', language: '@language_fr_ca', value: 'Qualification du risque' } }
  - { fields: { placeholder: '@erm_risk_attachments_new', language: '@language_fr_ca', value: 'Nouvelle pièce jointe' } }
  - { fields: { placeholder: '@erm_risk_reviews_next_review_date', language: '@language_fr_ca', value: 'Date de prochaine révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_perform_review', language: '@language_fr_ca', value: 'Effectuer la révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_list_columns_review_date', language: '@language_fr_ca', value: 'Date de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_list_columns_comments', language: '@language_fr_ca', value: Commentaires } }
  - { fields: { placeholder: '@erm_risk_reviews_form_tabs_risk_details', language: '@language_fr_ca', value: 'Détails du risque' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_tabs_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@erm_risk_reviews_form_tabs_controls_and_assurance', language: '@language_fr_ca', value: 'Contrôles et assurance' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_tabs_complete_review', language: '@language_fr_ca', value: 'Achever la révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_title', language: '@language_fr_ca', value: 'Titre de la révision du risque' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_summary', language: '@language_fr_ca', value: 'Ceci est le sommaire pour le formulaire de révision du risque' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_sections_review', language: '@language_fr_ca', value: 'Révision du risque' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_fields_comments', language: '@language_fr_ca', value: 'Commentaires de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_fields_comments_label', language: '@language_fr_ca', value: 'Commentaires de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_fields_review_date_label', language: '@language_fr_ca', value: 'Date de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_fields_review_date', language: '@language_fr_ca', value: 'Date de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_save', language: '@language_fr_ca', value: 'Enregistrer les modifications' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_saved_successfully', language: '@language_fr_ca', value: 'Révision du risque enregistrée avec succès' } }
  - { fields: { placeholder: '@erm_risk_reviews_form_close_risk', language: '@language_fr_ca', value: 'Clôturer le risque?' } }
  - { fields: { placeholder: '@erm_risk_reviews_nav_back_to_review_list', language: '@language_fr_ca', value: 'Retour à la liste des révisions' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date', language: '@language_fr_ca', value: 'Date de modification de la révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_modal_title', language: '@language_fr_ca', value: 'Date de modification de la révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_history_modal_title', language: '@language_fr_ca', value: 'Historique de modification de la date des révisions de risque' } }
  - { fields: { placeholder: '@erm_risk_reviews_history_not_set', language: '@language_fr_ca', value: 'La date de révision du risque n''a pas été définie' } }
  - { fields: { placeholder: '@erm_risk_reviews_view_history', language: '@language_fr_ca', value: 'Afficher l''historique' } }
  - { fields: { placeholder: '@erm_risk_reviews_history_username', language: '@language_fr_ca', value: 'Nom d''utilisateur' } }
  - { fields: { placeholder: '@erm_risk_reviews_history_new_date', language: '@language_fr_ca', value: 'Nouvelle date' } }
  - { fields: { placeholder: '@erm_risk_reviews_history_reason', language: '@language_fr_ca', value: Motif } }
  - { fields: { placeholder: '@erm_risk_registers_list_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@erm_risk_registers_list_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@erm_risk_register_nav_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@erm_risk_register_nav_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@erm_risk_register_nav_back_to_register', language: '@language_fr_ca', value: 'Retour au registre de risques' } }
  - { fields: { placeholder: '@erm_risk_register_nav_back_to_registers', language: '@language_fr_ca', value: 'Retour aux registres de risques' } }
  - { fields: { placeholder: '@erm_risk_register_workflow_pending', language: '@language_fr_ca', value: '(En attente)' } }
  - { fields: { placeholder: '@erm_risk_register_workflow_new', language: '@language_fr_ca', value: Nouveau } }
  - { fields: { placeholder: '@erm_risk_register_workflow_escalated', language: '@language_fr_ca', value: Escaladé } }
  - { fields: { placeholder: '@erm_risk_register_workflow_active', language: '@language_fr_ca', value: Actif } }
  - { fields: { placeholder: '@erm_risk_register_workflow_review_required', language: '@language_fr_ca', value: 'Révision obligatoire' } }
  - { fields: { placeholder: '@erm_risk_register_workflow_review_overdue', language: '@language_fr_ca', value: 'Révision en retard' } }
  - { fields: { placeholder: '@erm_risk_register_workflow_inactive', language: '@language_fr_ca', value: Inactif } }
  - { fields: { placeholder: '@erm_risk_register_workflow_closed', language: '@language_fr_ca', value: Fermé } }
  - { fields: { placeholder: '@erm_risk_register_workflow_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@erm_risk_trackers_new', language: '@language_fr_ca', value: 'Nouveau suivi de risque' } }
  - { fields: { placeholder: '@erm_risk_trackers_list_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@erm_risk_trackers_list_columns_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@erm_risk_trackers_nav_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@erm_risk_trackers_nav_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@erm_risk_trackers_nav_back_to_risk_tracker', language: '@language_fr_ca', value: 'Retour au suivi de risque' } }
  - { fields: { placeholder: '@erm_risk_trackers_nav_back_to_risk_trackers', language: '@language_fr_ca', value: 'Retour aux suivis de risques' } }
  - { fields: { placeholder: '@erm_risk_trackers_form_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@erm_risk_trackers_form_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@erm_risk_trackers_save', language: '@language_fr_ca', value: 'Enregistrer le suivi de risque' } }
  - { fields: { placeholder: '@erm_risk_trackers_saved_successfully', language: '@language_fr_ca', value: 'Suivi de risque enregistré avec succès' } }
  - { fields: { placeholder: '@erm_risk_trackers_risk_removed_successfully', language: '@language_fr_ca', value: 'Risque supprimé avec succès' } }
  - { fields: { placeholder: '@erm_risk_trackers_risks', language: '@language_fr_ca', value: Risques } }
  - { fields: { placeholder: '@erm_risk_filters_no_service_label', language: '@language_fr_ca', value: 'Aucun service' } }
  - { fields: { placeholder: '@erm_risk_filters_location', language: '@language_fr_ca', value: 'Filtre d''emplacement' } }
  - { fields: { placeholder: '@erm_risk_filters_location_label', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@erm_risk_filters_service', language: '@language_fr_ca', value: 'Filtre de service' } }
  - { fields: { placeholder: '@erm_risk_filters_service_label', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@erm_escalation_node_singular', language: '@language_fr_ca', value: 'Registre des risques' } }
  - { fields: { placeholder: '@erm_escalation_node_title', language: '@language_fr_ca', value: 'Registre des risques' } }
  - { fields: { placeholder: '@erm_escalation_node_plural', language: '@language_fr_ca', value: 'Registres de risques' } }
  - { fields: { placeholder: '@erm_escalation_node_search', language: '@language_fr_ca', value: 'Rechercher des registres' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_review_date', language: '@language_fr_ca', value: 'Date de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_reason', language: '@language_fr_ca', value: 'Motif de la modification de date de révision' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_confirm_change', language: '@language_fr_ca', value: 'Confirmer la modification' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_success_updated', language: '@language_fr_ca', value: 'Nouvelle date de révision mise à jour avec succès' } }
  - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_errors_date_not_changed', language: '@language_fr_ca', value: 'La date de révision n''a pas été modifiée' } }
  - { fields: { placeholder: '@erm_datasource_country_list', language: '@language_fr_ca', value: 'Liste des pays' } }
  - { fields: { placeholder: '@erm_datasource_multi_list', language: '@language_fr_ca', value: 'Liste multiple' } }
  - { fields: { placeholder: '@erm_datasource_risk_filter_no_service', language: '@language_fr_ca', value: 'Filtre de risque sans service' } }
  - { fields: { placeholder: '@erm_datasource_risk_types', language: '@language_fr_ca', value: 'Types de risques' } }
  - { fields: { placeholder: '@erm_form_type_risk_form', language: '@language_fr_ca', value: 'Formulaire de risque' } }
  - { fields: { placeholder: '@erm_form_type_risk_filter_form', language: '@language_fr_ca', value: 'Formulaire de filtre de risque' } }
  - { fields: { placeholder: '@erm_form_type_risk_review_form', language: '@language_fr_ca', value: 'Formulaire de révision de risque' } }
  - { fields: { placeholder: '@erm_form_type_risk_register_form', language: '@language_fr_ca', value: 'Formulaire de registre de risque' } }
  - { fields: { placeholder: '@erm_form_type_checklists_form', language: '@language_fr_ca', value: 'Formulaire de listes de vérification' } }
  - { fields: { placeholder: '@erm_datasource_item_united_kingdom', language: '@language_fr_ca', value: Royaume-Uni } }
  - { fields: { placeholder: '@erm_datasource_item_germany', language: '@language_fr_ca', value: Allemagne } }
  - { fields: { placeholder: '@erm_datasource_item_france', language: '@language_fr_ca', value: France } }
  - { fields: { placeholder: '@erm_datasource_item_spain', language: '@language_fr_ca', value: Espagne } }
  - { fields: { placeholder: '@erm_datasource_item_label1', language: '@language_fr_ca', value: Étiquette1 } }
  - { fields: { placeholder: '@erm_datasource_item_only_risks_with_no_service', language: '@language_fr_ca', value: 'Uniquement les risques sans service' } }
  - { fields: { placeholder: '@erm_forms_form1', language: '@language_fr_ca', value: 'Formulaire de risque' } }
  - { fields: { placeholder: '@erm_forms_form2', language: '@language_fr_ca', value: 'Formulaire de risque 2' } }
  - { fields: { placeholder: '@erm_forms_test_form', language: '@language_fr_ca', value: 'Tester le formulaire de risque' } }
  - { fields: { placeholder: '@erm_forms_review', language: '@language_fr_ca', value: 'Formulaire de révision de risque' } }
  - { fields: { placeholder: '@erm_custom_fields_age_label', language: '@language_fr_ca', value: Âge } }
  - { fields: { placeholder: '@erm_custom_fields_age_title', language: '@language_fr_ca', value: Âge } }
  - { fields: { placeholder: '@erm_risk_default_form_sections_risk_register_label', language: '@language_fr_ca', value: 'Registre des risques' } }
  - { fields: { placeholder: '@erm_risk_risk_type_select_type', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@erm_risk_risk_subtype_select_subtype', language: '@language_fr_ca', value: 'Sélectionnez un sous-type' } }
  - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_select_tertiary_subtype', language: '@language_fr_ca', value: 'Sélectionnez un sous-sous-type' } }
  - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_types', language: '@language_fr_ca', value: 'Aucun disponible' } }
  - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_subtypes', language: '@language_fr_ca', value: 'Aucun disponible' } }
  - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_tertiary_subtypes', language: '@language_fr_ca', value: 'Aucun disponible' } }
  - { fields: { placeholder: '@erm_risk_register_associate_medicine_risk_register', language: '@language_fr_ca', value: 'Registre des risques de médecine associée' } }
  - { fields: { placeholder: '@erm_risk_register_surgery_and_cancer_risk_register', language: '@language_fr_ca', value: 'Registre des risques de chirurgie et de cancer' } }
  - { fields: { placeholder: '@erm_risk_register_ear_nose_and_throat_risk_register', language: '@language_fr_ca', value: 'Registre des risques pour les oreilles, le nez et la gorge' } }
  - { fields: { placeholder: '@erm_risk_register_opthalmology_risk_register', language: '@language_fr_ca', value: 'Registre des risques ophtalmologiques' } }
  - { fields: { placeholder: '@erm_risk_register_medicine_and_emergency_care_risk_register', language: '@language_fr_ca', value: 'Registre des risques liés aux médicaments et aux soins d''urgence' } }
  - { fields: { placeholder: '@erm_risk_register_cardiology_risk_register', language: '@language_fr_ca', value: 'Registre des risques de cardiologie' } }
  - { fields: { placeholder: '@erm_risk_register_care_of_the_elderly_risk_register', language: '@language_fr_ca', value: 'Registre des risques pour les soins aux personnes âgées' } }
  - { fields: { placeholder: '@erm_risk_register_minor_injuries_risk_register', language: '@language_fr_ca', value: 'Registre des risques de blessures mineures' } }
  - { fields: { placeholder: '@erm_risk_register_estates_risk_register', language: '@language_fr_ca', value: 'Registre des risques des biens' } }
  - { fields: { placeholder: '@erm_risk_register_general_maintenance_risk_register', language: '@language_fr_ca', value: 'Registre des risques d''entretien général' } }
  - { fields: { placeholder: '@erm_risk_register_linen_services_risk_register', language: '@language_fr_ca', value: 'Registre des risques des services de linge' } }
  - { fields: { placeholder: '@erm_risk_register_site_services_risk_register', language: '@language_fr_ca', value: 'Registre des risques des services du site' } }
  - { fields: { placeholder: '@erm_risk_register_catering_risk_register', language: '@language_fr_ca', value: 'Registre des risques liés à la restauration' } }
  - { fields: { placeholder: '@erm_risk_register_corporate_risk_register', language: '@language_fr_ca', value: 'Registre des risques d''entreprise' } }
  - { fields: { placeholder: '@erm_risk_register_hr_and_payroll_risk_register', language: '@language_fr_ca', value: 'Registre des risques RH et paie' } }
  - { fields: { placeholder: '@erm_risk_register_communications_risk_register', language: '@language_fr_ca', value: 'Registre des risques liés aux communications' } }
  - { fields: { placeholder: '@erm_risk_register_services_risk_register', language: '@language_fr_ca', value: 'Registre des risques liés aux services' } }
  - { fields: { placeholder: '@erm_risk_register_hierarchy_risk_register', language: '@language_fr_ca', value: 'Hiérarchie des registres des risques' } }
  - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_one_title', language: '@language_fr_ca', value: 'Objectif principal 1' } }
  - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_one_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif principal 1' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_one_title', language: '@language_fr_ca', value: 'Objectif détaillé 1' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_one_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif détaillé 1' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_two_title', language: '@language_fr_ca', value: 'Objectif détaillé 2' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_two_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif détaillé 2' } }
  - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_two_title', language: '@language_fr_ca', value: 'Objectif principal 2' } }
  - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_two_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif principal 2' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_three_title', language: '@language_fr_ca', value: 'Objectif détaillé 3' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_three_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif détaillé 3' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_four_title', language: '@language_fr_ca', value: 'Objectif détaillé 4' } }
  - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_four_summary', language: '@language_fr_ca', value: 'Sommaire de l''objectif détaillé 4' } }
  - { fields: { placeholder: '@erm_risk_register_updated_successfully', language: '@language_fr_ca', value: 'Registre mis à jour avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_creation_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la création du registre' } }
  - { fields: { placeholder: '@erm_risk_register_created_successfully', language: '@language_fr_ca', value: 'Registre créé avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_deleted_successfully', language: '@language_fr_ca', value: 'Registre supprimé avec succès' } }
  - { fields: { placeholder: '@erm_risk_opened_successfully', language: '@language_fr_ca', value: 'Risque ouvert avec succès' } }
  - { fields: { placeholder: '@erm_risk_closed_successfully', language: '@language_fr_ca', value: 'Risque clôturé avec succès' } }
  - { fields: { placeholder: '@erm_navigate_to_risk_review_before_close', language: '@language_fr_ca', value: 'Il est requis de compléter une révision du risque avant de pouvoir le clôturé. Voulez-vous naviguer vers le formulaire de révision?' } }
  - { fields: { placeholder: '@erm_close_date_required', language: '@language_fr_ca', value: 'Une date de clôture doit être choisie' } }
  - { fields: { placeholder: '@erm_closure_reason_required', language: '@language_fr_ca', value: 'Une raison de clôture doit être fournie' } }
  - { fields: { placeholder: '@erm_risk_register_control_added_successfully', language: '@language_fr_ca', value: 'Contrôle ajouté avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_control_removed_successfully', language: '@language_fr_ca', value: 'Contrôle supprimé avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_attachment_saved_successfully', language: '@language_fr_ca', value: 'Pièce jointe enregistrée avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_service_updated_successfully', language: '@language_fr_ca', value: 'Service mis à jour avec succès' } }
  - { fields: { placeholder: '@erm_risk_register_selected_matrix_layout_updated_successfully', language: '@language_fr_ca', value: 'Disposition de matrice sélectionnée mise à jour avec succès' } }
  - { fields: { placeholder: '@erm_risk_tracker_not_found', language: '@language_fr_ca', value: 'Suivi de risque introuvable' } }
  - { fields: { placeholder: '@erm_risk_not_found', language: '@language_fr_ca', value: 'Risque introuvable' } }
  - { fields: { placeholder: '@erm_tracker_risk_not_found', language: '@language_fr_ca', value: 'Risque du suivi introuvable' } }
  - { fields: { placeholder: '@erm_new_risk_type', language: '@language_fr_ca', value: 'Nouveau type de risque' } }
  - { fields: { placeholder: '@erm_risk_register_errors_cant_find_parent', language: '@language_fr_ca', value: 'Le parent du registre nouvellement créé n''existe pas ou vous n''y avez pas accès' } }
  - { fields: { placeholder: '@erm_risk_register_errors_parent_required', language: '@language_fr_ca', value: 'Le registre parent doit être fourni lors de la création d''un nouveau registre de risques' } }
  - { fields: { placeholder: '@erm_success_notes_saved', language: '@language_fr_ca', value: 'Note enregistrée avec succès' } }
  - { fields: { placeholder: '@erm_success_notes_deleted', language: '@language_fr_ca', value: 'Note supprimée avec succès' } }
  - { fields: { placeholder: '@erm_risk_nav_communication', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
  - { fields: { placeholder: '@erm_risk_communications_header', language: '@language_fr_ca', value: 'Communication et rétroaction' } }
  - { fields: { placeholder: '@placeholder_erm_audit_entities_risk', language: '@language_fr_ca', value: Risque } }
  - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_rating', language: '@language_fr_ca', value: 'Taux de risque' } }
  - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_action', language: '@language_fr_ca', value: 'Action du risque' } }
  - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_location', language: '@language_fr_ca', value: 'Emplacement du risque' } }
  - { fields: { placeholder: '@placeholder_erm_risk_trackers_save_changes_message', language: '@language_fr_ca', value: 'Enregistrer les modifications apportées au dossier de suivi de risque?' } }
  - { fields: { placeholder: '@placeholder_erm_review_no_services', language: '@language_fr_ca', value: 'Ce risque n''avait pas de service associé' } }
  - { fields: { placeholder: '@erm_escalation_permission_invalid', language: '@language_fr_ca', value: 'Vous n''êtes pas autorisé à faire escalader ce risque' } }
  - { fields: { placeholder: '@erm_medications_invalid_medication_uuid', language: '@language_fr_ca', value: 'Un UUID de médicament valide doit être fourni' } }
  - { fields: { placeholder: '@erm_risk_filters_title', language: '@language_fr_ca', value: 'Filtrer les risques' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_id', language: '@language_fr_ca', value: 'ID de risque' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_id_label', language: '@language_fr_ca', value: 'ID de risque' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_status', language: '@language_fr_ca', value: 'Statut' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_status_label', language: '@language_fr_ca', value: 'Statut' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_columns_status', language: '@language_fr_ca', value: 'Statut' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_level', language: '@language_fr_ca', value: 'Niveau de risque' } }
  - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_level_label', language: '@language_fr_ca', value: 'Niveau de risque' } }
  - { fields: { placeholder: '@erm_risk_monitors_new', language: '@language_fr_ca', value: 'Nouveau' } }
  - { fields: { placeholder: '@erm_admin_risk_registers_exclude_contributory_factors_label', language: '@language_fr_ca', value: 'Exclure les facteurs contributifs' } }
  - { fields: { placeholder: '@erm_risk_label_risk_details', language: '@language_fr_ca', value: 'Formulaire de risque' } }
  - { fields: { placeholder: '@erm_risk_label_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@erm_risk_label_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@erm_risk_label_controls_and_assurance', language: '@language_fr_ca', value: 'Contrôles et assurance' } }
  - { fields: { placeholder: '@erm_risk_label_medications', language: '@language_fr_ca', value: Médicaments } }
