entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@erm_admin_organisational_objectives_add', language: '@language_en_us', value: Add Root Objective } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_delete', language: '@language_en_us', value: Delete Objective } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_delete_confirm', language: '@language_en_us', value: 'Are you sure you want to delete this Objective?' } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_form_summary', language: '@language_en_us', value: Summary } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_form_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_removed_successfully', language: '@language_en_us', value: Objective removed successfully } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_save', language: '@language_en_us', value: Save Objective } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_saved_successfully', language: '@language_en_us', value: Objective saved successfully } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_title', language: '@language_en_us', value: Organizational Objectives } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_untitled_node', language: '@language_en_us', value: Untitled Node } }
    - { fields: { placeholder: '@erm_admin_risk_registers_activate', language: '@language_en_us', value: Activate Risk Register } }
    - { fields: { placeholder: '@erm_admin_risk_registers_activated_successfully', language: '@language_en_us', value: Risk Register activated successfully } }
    - { fields: { placeholder: '@erm_admin_risk_registers_deactivate', language: '@language_en_us', value: Deactivate Risk Register } }
    - { fields: { placeholder: '@erm_admin_risk_registers_deactivated_successfully', language: '@language_en_us', value: Risk Register deactivated successfully } }
    - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model', language: '@language_en_us', value: Escalation Model } }
    - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_updated_successfully', language: '@language_en_us', value: Escalation model updated successfully } }
    - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_options_any_parent', language: '@language_en_us', value: Any Parent } }
    - { fields: { placeholder: '@erm_admin_risk_registers_include_escalation_model_options_enabled', language: '@language_en_us', value: Enabled } }
    - { fields: { placeholder: '@erm_admin_risk_registers_include_escalation_model_options_disabled', language: '@language_en_us', value: Disabled } }
    - { fields: { placeholder: '@erm_admin_risk_registers_escalation_model_options_immediate_parent_only', language: '@language_en_us', value: Immediate Parent Only } }
    - { fields: { placeholder: '@erm_admin_risk_registers_is_active', language: '@language_en_us', value: 'Active?' } }
    - { fields: { placeholder: '@erm_admin_risk_registers_status_active', language: '@language_en_us', value: Active } }
    - { fields: { placeholder: '@erm_admin_risk_registers_status_inactive', language: '@language_en_us', value: Inactive } }
    - { fields: { placeholder: '@erm_admin_risk_registers_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@erm_admin_risk_registers_tabs_register_owners', language: '@language_en_us', value: Register Owners } }
    - { fields: { placeholder: '@erm_admin_risk_registers_tabs_risks', language: '@language_en_us', value: Risks } }
    - { fields: { placeholder: '@placeholder_erm_audit_entities_risk', language: '@language_en_us', value: Risk } }
    - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_action', language: '@language_en_us', value: Risk Action } }
    - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_location', language: '@language_en_us', value: Risk Location } }
    - { fields: { placeholder: '@placeholder_erm_audit_entities_risk_rating', language: '@language_en_us', value: Risk Rating } }
    - { fields: { placeholder: '@erm_components_heatmap_current_rating', language: '@language_en_us', value: Current Rating } }
    - { fields: { placeholder: '@erm_components_heatmap_current_risk_level', language: '@language_en_us', value: Current Risk Level } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_almost_certain', language: '@language_en_us', value: Almost Certain } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_catastrophic', language: '@language_en_us', value: Catastrophic } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_consequence', language: '@language_en_us', value: Consequence } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_impact', language: '@language_en_us', value: Impact } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_likelihood', language: '@language_en_us', value: Likelihood } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_likely', language: '@language_en_us', value: Likely } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_major', language: '@language_en_us', value: Major } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_minor', language: '@language_en_us', value: Minor } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_moderate', language: '@language_en_us', value: Moderate } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_negligible', language: '@language_en_us', value: Negligible } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_possible', language: '@language_en_us', value: Possible } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_rare', language: '@language_en_us', value: Rare } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_significant', language: '@language_en_us', value: Significant } }
    - { fields: { placeholder: '@erm_components_heatmap_grid_unlikely', language: '@language_en_us', value: Unlikely } }
    - { fields: { placeholder: '@erm_components_heatmap_tabs_current_grading', language: '@language_en_us', value: Current Grading } }
    - { fields: { placeholder: '@erm_components_heatmap_tabs_initial_grading', language: '@language_en_us', value: Initial Grading } }
    - { fields: { placeholder: '@erm_components_heatmap_tabs_target_grading', language: '@language_en_us', value: Target Grading } }
    - { fields: { placeholder: '@erm_components_heatmap_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@erm_datasource_item_france', language: '@language_en_us', value: France } }
    - { fields: { placeholder: '@erm_datasource_item_germany', language: '@language_en_us', value: Germany } }
    - { fields: { placeholder: '@erm_datasource_item_label1', language: '@language_en_us', value: Label1 } }
    - { fields: { placeholder: '@erm_datasource_item_only_risks_with_no_service', language: '@language_en_us', value: Only Risks with no Service } }
    - { fields: { placeholder: '@erm_datasource_item_spain', language: '@language_en_us', value: Spain } }
    - { fields: { placeholder: '@erm_datasource_item_united_kingdom', language: '@language_en_us', value: United Kingdom } }
    - { fields: { placeholder: '@erm_datasource_country_list', language: '@language_en_us', value: Country List } }
    - { fields: { placeholder: '@erm_datasource_multi_list', language: '@language_en_us', value: Multi List } }
    - { fields: { placeholder: '@erm_datasource_risk_filter_no_service', language: '@language_en_us', value: Risk Filter No Service } }
    - { fields: { placeholder: '@erm_datasource_risk_types', language: '@language_en_us', value: Risk Types } }
    - { fields: { placeholder: '@erm_escalation_node_plural', language: '@language_en_us', value: Risk Registers } }
    - { fields: { placeholder: '@erm_escalation_node_search', language: '@language_en_us', value: Search Registers } }
    - { fields: { placeholder: '@erm_escalation_node_singular', language: '@language_en_us', value: Risk Register } }
    - { fields: { placeholder: '@erm_escalation_node_title', language: '@language_en_us', value: Risk Register } }
    - { fields: { placeholder: '@erm_escalation_permission_invalid', language: '@language_en_us', value: You do not have permission to escalate this Risk } }
    - { fields: { placeholder: '@erm_custom_fields_age_label', language: '@language_en_us', value: Age } }
    - { fields: { placeholder: '@erm_custom_fields_age_title', language: '@language_en_us', value: Age } }
    - { fields: { placeholder: '@erm_form_type_checklists_form', language: '@language_en_us', value: Checklists Form } }
    - { fields: { placeholder: '@erm_form_type_risk_filter_form', language: '@language_en_us', value: Risk Filter Form } }
    - { fields: { placeholder: '@erm_form_type_risk_form', language: '@language_en_us', value: Risk Form } }
    - { fields: { placeholder: '@erm_form_type_risk_register_form', language: '@language_en_us', value: Risk Register Form } }
    - { fields: { placeholder: '@erm_form_type_risk_review_form', language: '@language_en_us', value: Risk Review Form } }
    - { fields: { placeholder: '@erm_forms_form1', language: '@language_en_us', value: Risk Form } }
    - { fields: { placeholder: '@erm_forms_form2', language: '@language_en_us', value: Risk Form 2 } }
    - { fields: { placeholder: '@erm_forms_review', language: '@language_en_us', value: Risk Review Form } }
    - { fields: { placeholder: '@erm_forms_test_form', language: '@language_en_us', value: Test Risk Form } }
    - { fields: { placeholder: '@erm_loading_heatmap', language: '@language_en_us', value: Loading Heatmap } }
    - { fields: { placeholder: '@erm_loading_objectives', language: '@language_en_us', value: Loading Objectives } }
    - { fields: { placeholder: '@erm_loading_risk_form', language: '@language_en_us', value: Loading Risk Form } }
    - { fields: { placeholder: '@erm_loading_risk_register', language: '@language_en_us', value: Loading Risk Register } }
    - { fields: { placeholder: '@erm_loading_risk_registers', language: '@language_en_us', value: Loading Risk Registers } }
    - { fields: { placeholder: '@erm_loading_risk_trackers', language: '@language_en_us', value: Loading Risk Trackers } }
    - { fields: { placeholder: '@erm_loading_risk_workflow', language: '@language_en_us', value: Loading Risk Workflow } }
    - { fields: { placeholder: '@erm_loading_risks', language: '@language_en_us', value: Loading Risks } }
    - { fields: { placeholder: '@erm_module_title', language: '@language_en_us', value: Enterprise Risk Manager } }
    - { fields: { placeholder: '@erm_my_risks_columns_current_grading', language: '@language_en_us', value: Current Risk Grading } }
    - { fields: { placeholder: '@erm_my_risks_columns_escalated', language: '@language_en_us', value: 'Escalated?' } }
    - { fields: { placeholder: '@erm_my_risks_columns_escalated_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@erm_my_risks_columns_escalated_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@erm_my_risks_columns_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@erm_my_risks_columns_review_date', language: '@language_en_us', value: Risk Review Date } }
    - { fields: { placeholder: '@erm_my_risks_columns_subtype', language: '@language_en_us', value: Subtype } }
    - { fields: { placeholder: '@erm_my_risks_columns_tertiary_subtype', language: '@language_en_us', value: Sub-Subtype } }
    - { fields: { placeholder: '@erm_my_risks_columns_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@erm_my_risks_columns_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@erm_nav_admin_objectives', language: '@language_en_us', value: Objectives } }
    - { fields: { placeholder: '@erm_nav_admin_permissions', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@erm_nav_admin_risk_matrix', language: '@language_en_us', value: Risk Matrix } }
    - { fields: { placeholder: '@erm_nav_admin_risk_registers', language: '@language_en_us', value: Risk Registers } }
    - { fields: { placeholder: '@erm_nav_my_risks', language: '@language_en_us', value: My Risks } }
    - { fields: { placeholder: '@erm_nav_report_a_risk', language: '@language_en_us', value: Report a Risk } }
    - { fields: { placeholder: '@erm_nav_risk_registers', language: '@language_en_us', value: Risk Registers } }
    - { fields: { placeholder: '@erm_nav_risk_trackers', language: '@language_en_us', value: Risk Trackers } }
    - { fields: { placeholder: '@erm_new_risk_type', language: '@language_en_us', value: New Risk Type } }
    - { fields: { placeholder: '@erm_risk_closed_successfully', language: '@language_en_us', value: Risk closed successfully } }
    - { fields: { placeholder: '@erm_risk_opened_successfully', language: '@language_en_us', value: Risk opened successfully } }
    - { fields: { placeholder: '@erm_navigate_to_risk_review_before_close', language: '@language_en_us', value: 'Closing a Risk requires completing a Risk Review. Do you want to proceed to the Risk Review form?' } }
    - { fields: { placeholder: '@erm_close_date_required', language: '@language_en_us', value: A close date must be selected } }
    - { fields: { placeholder: '@erm_closure_reason_required', language: '@language_en_us', value: A closure reason must be provided } }
    - { fields: { placeholder: '@erm_risk_register_created_successfully', language: '@language_en_us', value: Register created successfully } }
    - { fields: { placeholder: '@erm_risk_register_creation_error', language: '@language_en_us', value: An error occurred whilst creating the register } }
    - { fields: { placeholder: '@erm_risk_register_deleted_successfully', language: '@language_en_us', value: Register deleted successfully } }
    - { fields: { placeholder: '@erm_risk_register_hierarchy_risk_register', language: '@language_en_us', value: Risk Register Hierarchy } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_four_summary', language: '@language_en_us', value: Detailed objective 4 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_four_title', language: '@language_en_us', value: Detailed Objective 4 } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_one_summary', language: '@language_en_us', value: Detailed objective 1 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_one_title', language: '@language_en_us', value: Detailed Objective 1 } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_three_summary', language: '@language_en_us', value: Detailed objective 3 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_three_title', language: '@language_en_us', value: Detailed Objective 3 } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_two_summary', language: '@language_en_us', value: Detailed objective 2 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_detailed_objective_two_title', language: '@language_en_us', value: Detailed Objective 2 } }
    - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_one_summary', language: '@language_en_us', value: Principal objective 1 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_one_title', language: '@language_en_us', value: Principal Objective 1 } }
    - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_two_summary', language: '@language_en_us', value: Principal objective 2 summary } }
    - { fields: { placeholder: '@erm_risk_register_objective_principal_objective_two_title', language: '@language_en_us', value: Principal Objective 2 } }
    - { fields: { placeholder: '@erm_risk_register_updated_successfully', language: '@language_en_us', value: Register updated successfully } }
    - { fields: { placeholder: '@erm_risk_register_associate_medicine_risk_register', language: '@language_en_us', value: Associate Medicine Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_attachment_saved_successfully', language: '@language_en_us', value: Attachment saved successfully } }
    - { fields: { placeholder: '@erm_risk_register_cardiology_risk_register', language: '@language_en_us', value: Cardiology Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_care_of_the_elderly_risk_register', language: '@language_en_us', value: Care of the elderly Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_catering_risk_register', language: '@language_en_us', value: Catering Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_communications_risk_register', language: '@language_en_us', value: Communications Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_control_added_successfully', language: '@language_en_us', value: Control added successfully } }
    - { fields: { placeholder: '@erm_risk_register_control_removed_successfully', language: '@language_en_us', value: Control removed successfully } }
    - { fields: { placeholder: '@erm_risk_register_corporate_risk_register', language: '@language_en_us', value: Corporate Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_ear_nose_and_throat_risk_register', language: '@language_en_us', value: "Ear, nose and throat Risk Register" } }
    - { fields: { placeholder: '@erm_risk_register_errors_cant_find_parent', language: '@language_en_us', value: "The parent of the newly created register either does not exist, or you do not have access to it" } }
    - { fields: { placeholder: '@erm_risk_register_errors_parent_required', language: '@language_en_us', value: Parent register must be provided when creating a new risk register } }
    - { fields: { placeholder: '@erm_risk_register_estates_risk_register', language: '@language_en_us', value: Estates Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_general_maintenance_risk_register', language: '@language_en_us', value: General Maintenance Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_hr_and_payroll_risk_register', language: '@language_en_us', value: HR and payroll Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_linen_services_risk_register', language: '@language_en_us', value: Linen Services Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_medicine_and_emergency_care_risk_register', language: '@language_en_us', value: Medicine and emergency care Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_minor_injuries_risk_register', language: '@language_en_us', value: Minor injuries Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_nav_access_control', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@erm_risk_register_nav_back_to_register', language: '@language_en_us', value: Back to Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_nav_back_to_registers', language: '@language_en_us', value: Back to Risk Registers } }
    - { fields: { placeholder: '@erm_risk_register_nav_details', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@erm_risk_register_opthalmology_risk_register', language: '@language_en_us', value: Ophthalmology Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_selected_matrix_layout_updated_successfully', language: '@language_en_us', value: Selected matrix layout updated successfully } }
    - { fields: { placeholder: '@erm_risk_register_service_updated_successfully', language: '@language_en_us', value: Service updated successfully } }
    - { fields: { placeholder: '@erm_risk_register_services_risk_register', language: '@language_en_us', value: Services Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_site_services_risk_register', language: '@language_en_us', value: Site services Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_surgery_and_cancer_risk_register', language: '@language_en_us', value: Surgery and Cancer Risk Register } }
    - { fields: { placeholder: '@erm_risk_register_workflow_active', language: '@language_en_us', value: Active } }
    - { fields: { placeholder: '@erm_risk_register_workflow_closed', language: '@language_en_us', value: Closed } }
    - { fields: { placeholder: '@erm_risk_register_workflow_escalated', language: '@language_en_us', value: Escalated } }
    - { fields: { placeholder: '@erm_risk_register_workflow_inactive', language: '@language_en_us', value: Inactive } }
    - { fields: { placeholder: '@erm_risk_register_workflow_new', language: '@language_en_us', value: New } }
    - { fields: { placeholder: '@erm_risk_register_workflow_pending', language: '@language_en_us', value: (Pending) } }
    - { fields: { placeholder: '@erm_risk_register_workflow_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@erm_risk_register_workflow_review_overdue', language: '@language_en_us', value: Review Overdue } }
    - { fields: { placeholder: '@erm_risk_register_workflow_review_required', language: '@language_en_us', value: Review due within 30 days } }
    - { fields: { placeholder: '@erm_risk_registers_list_columns_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@erm_risk_registers_list_columns_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date', language: '@language_en_us', value: Edit Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_confirm_change', language: '@language_en_us', value: Confirm Change } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_errors_date_not_changed', language: '@language_en_us', value: The review date has not changed } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_modal_title', language: '@language_en_us', value: Edit Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_reason', language: '@language_en_us', value: Reason for Review date change } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_review_date', language: '@language_en_us', value: Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_edit_review_date_success_updated', language: '@language_en_us', value: Next review date updated successfully } }
    - { fields: { placeholder: '@erm_risk_reviews_form_close_risk', language: '@language_en_us', value: 'Close Risk?' } }
    - { fields: { placeholder: '@erm_risk_reviews_form_fields_comments', language: '@language_en_us', value: Review Comments } }
    - { fields: { placeholder: '@erm_risk_reviews_form_fields_comments_label', language: '@language_en_us', value: Review Comments } }
    - { fields: { placeholder: '@erm_risk_reviews_form_fields_review_date', language: '@language_en_us', value: Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_form_fields_review_date_label', language: '@language_en_us', value: Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_form_save', language: '@language_en_us', value: Save Changes } }
    - { fields: { placeholder: '@erm_risk_reviews_form_saved_successfully', language: '@language_en_us', value: Risk Review saved successfully } }
    - { fields: { placeholder: '@erm_risk_reviews_form_sections_review', language: '@language_en_us', value: Risk Review } }
    - { fields: { placeholder: '@erm_risk_reviews_form_summary', language: '@language_en_us', value: This is the summary for the Risk Review form } }
    - { fields: { placeholder: '@erm_risk_reviews_form_tabs_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@erm_risk_reviews_form_tabs_complete_review', language: '@language_en_us', value: Complete Review } }
    - { fields: { placeholder: '@erm_risk_reviews_form_tabs_controls_and_assurance', language: '@language_en_us', value: Controls & Assurance } }
    - { fields: { placeholder: '@erm_risk_reviews_form_tabs_risk_details', language: '@language_en_us', value: Risk Details } }
    - { fields: { placeholder: '@erm_risk_reviews_form_title', language: '@language_en_us', value: Risk Review Title } }
    - { fields: { placeholder: '@erm_risk_reviews_history_modal_title', language: '@language_en_us', value: Risk Reviews Date Change History } }
    - { fields: { placeholder: '@erm_risk_reviews_history_new_date', language: '@language_en_us', value: New Date } }
    - { fields: { placeholder: '@erm_risk_reviews_history_not_set', language: '@language_en_us', value: This Risk's review date has not been set } }
    - { fields: { placeholder: '@erm_risk_reviews_history_reason', language: '@language_en_us', value: Reason } }
    - { fields: { placeholder: '@erm_risk_reviews_history_username', language: '@language_en_us', value: Username } }
    - { fields: { placeholder: '@erm_risk_reviews_nav_back_to_review_list', language: '@language_en_us', value: Back to Review List } }
    - { fields: { placeholder: '@erm_risk_reviews_view_history', language: '@language_en_us', value: View History } }
    - { fields: { placeholder: '@erm_risk_trackers_form_description', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@erm_risk_trackers_form_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@erm_risk_trackers_list_columns_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@erm_risk_trackers_list_columns_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@erm_risk_trackers_nav_access_control', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@erm_risk_trackers_nav_back_to_risk_tracker', language: '@language_en_us', value: Back to Risk Tracker } }
    - { fields: { placeholder: '@erm_risk_trackers_nav_back_to_risk_trackers', language: '@language_en_us', value: Back to Risk Trackers } }
    - { fields: { placeholder: '@erm_risk_trackers_nav_details', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@erm_risk_trackers_new', language: '@language_en_us', value: New Risk Tracker } }
    - { fields: { placeholder: '@erm_risk_tracker_not_found', language: '@language_en_us', value: Risk Tracker not found } }
    - { fields: { placeholder: '@erm_risk_trackers_risk_removed_successfully', language: '@language_en_us', value: Risk removed successfully } }
    - { fields: { placeholder: '@erm_tracker_risk_not_found', language: '@language_en_us', value: Tracker Risk not found } }
    - { fields: { placeholder: '@erm_risk_trackers_risks', language: '@language_en_us', value: Risks } }
    - { fields: { placeholder: '@erm_risk_trackers_save', language: '@language_en_us', value: Save Risk Tracker } }
    - { fields: { placeholder: '@placeholder_erm_risk_trackers_save_changes_message', language: '@language_en_us', value: 'Save changes made to the Risk Tracker record?' } }
    - { fields: { placeholder: '@erm_risk_trackers_saved_successfully', language: '@language_en_us', value: Risk Tracker saved Successfully } }
    - { fields: { placeholder: '@erm_risk_accepted_successfully', language: '@language_en_us', value: Risk accepted successfully } }
    - { fields: { placeholder: '@erm_risk_assurance_add', language: '@language_en_us', value: Add Assurance } }
    - { fields: { placeholder: '@erm_risk_assurance_gaps', language: '@language_en_us', value: Gaps In Assurance } }
    - { fields: { placeholder: '@erm_risk_assurance_in_place', language: '@language_en_us', value: Assurance In Place } }
    - { fields: { placeholder: '@erm_risk_assurance_placeholder', language: '@language_en_us', value: Enter Assurance } }
    - { fields: { placeholder: '@erm_risk_assurance_save', language: '@language_en_us', value: Save Assurance } }
    - { fields: { placeholder: '@erm_risk_assurance_saved_successfully', language: '@language_en_us', value: Assurance saved successfully } }
    - { fields: { placeholder: '@erm_risk_assurance_title', language: '@language_en_us', value: Assurance } }
    - { fields: { placeholder: '@erm_risk_attachments_new', language: '@language_en_us', value: New Attachment } }
    - { fields: { placeholder: '@erm_risk_banners_escalated', language: '@language_en_us', value: "This Risk has been escalated to the {{riskRegisterName}} Register" } }
    - { fields: { placeholder: '@erm_risk_banners_inactive', language: '@language_en_us', value: This Risk is not currently Active } }
    - { fields: { placeholder: '@erm_risk_banners_is_new', language: '@language_en_us', value: This Risk is New } }
    - { fields: { placeholder: '@erm_risk_banners_is_locked', language: '@language_en_us', value: 'This record is locked by {{user}} since {{date}}' } }
    - { fields: { placeholder: '@erm_risk_banners_pending_inbound_deescalation', language: '@language_en_us', value: This Risk is pending de-escalation into this Risk Register } }
    - { fields: { placeholder: '@erm_risk_banners_pending_inbound_escalation', language: '@language_en_us', value: This Risk is pending escalation into this Risk Register } }
    - { fields: { placeholder: '@erm_risk_banners_pending_outbound_deescalation', language: '@language_en_us', value: "This Risk is pending de-escalation to the {{targetRegister}} Register" } }
    - { fields: { placeholder: '@erm_risk_banners_pending_outbound_escalation', language: '@language_en_us', value: "This Risk is pending escalation to the {{targetRegister}} Register" } }
    - { fields: { placeholder: '@erm_risk_communications_header', language: '@language_en_us', value: Communication & Feedback } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_add', language: '@language_en_us', value: Add Contributory Factor } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_controls_add', language: '@language_en_us', value: Add Control } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_controls_none_available', language: '@language_en_us', value: No Controls available to Attach } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_controls_select', language: '@language_en_us', value: Select a Control } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_none_attached', language: '@language_en_us', value: This Record doesn't have any Contributory Factors } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_removed_successfully', language: '@language_en_us', value: Contributory Factor removed successfully } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_saved_successfully', language: '@language_en_us', value: Contributory Factor saved successfully } }
    - { fields: { placeholder: '@erm_risk_contributory_factors_title', language: '@language_en_us', value: Contributory Factors } }
    - { fields: { placeholder: '@erm_risk_controls_gaps', language: '@language_en_us', value: Gaps in Controls } }
    - { fields: { placeholder: '@erm_risk_controls_in_place', language: '@language_en_us', value: Controls In Place } }
    - { fields: { placeholder: '@erm_risk_created_successfully', language: '@language_en_us', value: Risk created successfully } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_comments', language: '@language_en_us', value: Review Comments } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_comments_label', language: '@language_en_us', value: Review Comments } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_country', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_country_2', language: '@language_en_us', value: Country 2 } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_country_2_label', language: '@language_en_us', value: Country 2 } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_country_label', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_gender', language: '@language_en_us', value: Gender } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_gender_label', language: '@language_en_us', value: Gender } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_organisational_objectives', language: '@language_en_us', value: Organisational Objectives } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_organisational_objectives_label', language: '@language_en_us', value: Organisational Objectives } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_relationships', language: '@language_en_us', value: Relationships } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_relationships_label', language: '@language_en_us', value: Relationships } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_reported_by', language: '@language_en_us', value: Reported By } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_reported_by_label', language: '@language_en_us', value: Risk Reported By } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_description', language: '@language_en_us', value: Risk Description } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_description_label', language: '@language_en_us', value: Risk Description } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_opened_date', language: '@language_en_us', value: Risk Opened Date } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_opened_date_label', language: '@language_en_us', value: Risk Opened Date } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_ratings', language: '@language_en_us', value: Risk Grading } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_ratings_label', language: '@language_en_us', value: Risk Grading } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype', language: '@language_en_us', value: Risk Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_empty_text', language: '@language_en_us', value: Select a Risk Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_label', language: '@language_en_us', value: Risk Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_airborne_infection', language: '@language_en_us', value: Airborne Infection } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_clinical', language: '@language_en_us', value: Clinical } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_contact_infection', language: '@language_en_us', value: Contact Infection } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_financial', language: '@language_en_us', value: Financial } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_head_hazard', language: '@language_en_us', value: Head Hazard } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_respiratory_hazard', language: '@language_en_us', value: Respiratory Hazard } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_subtype_options_slip_hazard', language: '@language_en_us', value: Slip Hazard } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype', language: '@language_en_us', value: Risk Sub-Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype_empty_text', language: '@language_en_us', value: Select a Risk Sub-Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_tertiary_subtype_label', language: '@language_en_us', value: Risk Sub-Subtype } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_title', language: '@language_en_us', value: Risk Title } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_title_label', language: '@language_en_us', value: Risk Title } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type', language: '@language_en_us', value: Risk Type } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_empty_text', language: '@language_en_us', value: Select a Risk Type } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_label', language: '@language_en_us', value: Risk Type } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_hazard', language: '@language_en_us', value: Hazard } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_infection', language: '@language_en_us', value: Infection } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_operational_risk', language: '@language_en_us', value: Operational Risk } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_options_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@erm_risk_default_form_sections_risk_dates_label', language: '@language_en_us', value: Risk Dates } }
    - { fields: { placeholder: '@erm_risk_default_form_sections_risk_type_label', language: '@language_en_us', value: Risk Type } }
    - { fields: { placeholder: '@erm_risk_default_form_sections_title_and_reference_help', language: '@language_en_us', value: Fill in all fields } }
    - { fields: { placeholder: '@erm_risk_default_form_sections_title_and_reference_label', language: '@language_en_us', value: Title and Reference } }
    - { fields: { placeholder: '@erm_risk_default_form_summary', language: '@language_en_us', value: A simple Risk form for general interaction } }
    - { fields: { placeholder: '@erm_risk_default_form_title', language: '@language_en_us', value: Simple Risk Form } }
    - { fields: { placeholder: '@erm_risk_escalation_accept_success', language: '@language_en_us', value: Escalation accepted successfully } }
    - { fields: { placeholder: '@erm_risk_escalation_banners_pending_inbound', language: '@language_en_us', value: This Risk is pending escalation into this Risk Register } }
    - { fields: { placeholder: '@erm_risk_escalation_banners_pending_outbound', language: '@language_en_us', value: "This Risk is pending escalation to {{targetRegister}}" } }
    - { fields: { placeholder: '@erm_risk_escalation_deescalate_button', language: '@language_en_us', value: "De-Escalate to {{targetRegister}}" } }
    - { fields: { placeholder: '@erm_risk_escalation_deescalate_success', language: '@language_en_us', value: Risk De-Escalated successfully } }
    - { fields: { placeholder: '@erm_risk_escalation_deescalate_title', language: '@language_en_us', value: De-Escalate Risk } }
    - { fields: { placeholder: '@erm_risk_escalation_deescalated', language: '@language_en_us', value: De-Escalated } }
    - { fields: { placeholder: '@erm_risk_escalation_deescalated_title', language: '@language_en_us', value: "De-Escalating to {{riskTarget}} from {{riskSource}}" } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_button', language: '@language_en_us', value: "Escalate Risk to {{targetRegister}}" } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_comments', language: '@language_en_us', value: Comments } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_target_service_empty_text', language: '@language_en_us', value: Select a Target Service } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_fields_target_service_label', language: '@language_en_us', value: Target Service } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_save', language: '@language_en_us', value: Escalate Risk } }
    - { fields: { placeholder: '@erm_risk_escalation_escalate_title', language: '@language_en_us', value: Escalate Risk } }
    - { fields: { placeholder: '@erm_risk_escalation_escalated', language: '@language_en_us', value: Escalated } }
    - { fields: { placeholder: '@erm_risk_escalation_escalated_title', language: '@language_en_us', value: "Escalating to {{riskTarget}} from {{riskSource}}" } }
    - { fields: { placeholder: '@erm_risk_escalation_history', language: '@language_en_us', value: Escalation History } }
    - { fields: { placeholder: '@erm_risk_escalation_history_not_currently_escalated', language: '@language_en_us', value: This Risk is not currently in an escalated state } }
    - { fields: { placeholder: '@erm_risk_escalation_manage_request_accept', language: '@language_en_us', value: Accept } }
    - { fields: { placeholder: '@erm_risk_escalation_manage_request_fields_comments', language: '@language_en_us', value: Response Comments } }
    - { fields: { placeholder: '@erm_risk_escalation_manage_request_reject', language: '@language_en_us', value: Reject } }
    - { fields: { placeholder: '@erm_risk_escalation_manage_request_title', language: '@language_en_us', value: Manage Escalation Request } }
    - { fields: { placeholder: '@erm_risk_escalation_or', language: '@language_en_us', value: or } }
    - { fields: { placeholder: '@erm_risk_escalation_pending', language: '@language_en_us', value: Pending } }
    - { fields: { placeholder: '@erm_risk_escalation_accepted', language: '@language_en_us', value: Accepted } }
    - { fields: { placeholder: '@erm_risk_escalation_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@erm_risk_escalation_reject_success', language: '@language_en_us', value: Escalation rejected successfully } }
    - { fields: { placeholder: '@erm_risk_escalation_saved_successfully', language: '@language_en_us', value: Risk Escalated Successfully } }
    - { fields: { placeholder: '@erm_risk_filters_no_service_label', language: '@language_en_us', value: No Service } }
    - { fields: { placeholder: '@erm_risk_form_close', language: '@language_en_us', value: Close Risk } }
    - { fields: { placeholder: '@erm_risk_form_reopen', language: '@language_en_us', value: Reopen Risk } }
    - { fields: { placeholder: '@erm_risk_form_save', language: '@language_en_us', value: Save Risk } }
    - { fields: { placeholder: '@erm_risk_nav_access_control', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@erm_risk_nav_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@erm_risk_nav_all_actions', language: '@language_en_us', value: All Actions } }
    - { fields: { placeholder: '@erm_risk_nav_attachments', language: '@language_en_us', value: Attachments } }
    - { fields: { placeholder: '@erm_risk_nav_back_to_my_risks', language: '@language_en_us', value: Back to My Risks } }
    - { fields: { placeholder: '@erm_risk_nav_communication', language: '@language_en_us', value: Communication & Feedback } }
    - { fields: { placeholder: '@erm_risk_nav_contacts', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@erm_risk_nav_controls_and_assurance', language: '@language_en_us', value: Controls & Assurance } }
    - { fields: { placeholder: '@erm_risk_nav_equipment', language: '@language_en_us', value: Equipment } }
    - { fields: { placeholder: '@erm_risk_nav_escalation', language: '@language_en_us', value: Escalation } }
    - { fields: { placeholder: '@erm_risk_nav_medications', language: '@language_en_us', value: Medications } }
    - { fields: { placeholder: '@erm_risk_nav_my_actions', language: '@language_en_us', value: My Actions } }
    - { fields: { placeholder: '@erm_risk_nav_objectives', language: '@language_en_us', value: Objectives } }
    - { fields: { placeholder: '@erm_risk_nav_reviews', language: '@language_en_us', value: Risk Reviews } }
    - { fields: { placeholder: '@erm_risk_nav_risk_details', language: '@language_en_us', value: Risk Details } }
    - { fields: { placeholder: '@erm_risk_nav_risk_monitors', language: '@language_en_us', value: Risk Monitors } }
    - { fields: { placeholder: '@erm_risk_nav_services_and_locations', language: '@language_en_us', value: Service & Locations } }
    - { fields: { placeholder: '@erm_risk_not_found', language: '@language_en_us', value: Risk not found } }
    - { fields: { placeholder: '@erm_risk_objectives_none_attached', language: '@language_en_us', value: This Risk has no attached Organizational Objectives } }
    - { fields: { placeholder: '@erm_risk_objectives_selected_objectives', language: '@language_en_us', value: Selected Objectives } }
    - { fields: { placeholder: '@erm_risk_plural', language: '@language_en_us', value: Risks } }
    - { fields: { placeholder: '@erm_risk_rejected_successfully', language: '@language_en_us', value: Risk rejected successfully } }
    - { fields: { placeholder: '@erm_risk_reviews_list_columns_comments', language: '@language_en_us', value: Comments } }
    - { fields: { placeholder: '@erm_risk_reviews_list_columns_review_date', language: '@language_en_us', value: Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_next_review_date', language: '@language_en_us', value: Next Review Date } }
    - { fields: { placeholder: '@erm_risk_reviews_perform_review', language: '@language_en_us', value: Perform Review } }
    - { fields: { placeholder: '@erm_risk_risk_grading', language: '@language_en_us', value: Risk Grading } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_empty_text', language: '@language_en_us', value: Please select } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_label', language: '@language_en_us', value: Module } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_claims', language: '@language_en_us', value: Claims } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_feedback', language: '@language_en_us', value: Feedback } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_incidents', language: '@language_en_us', value: Incidents } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_module_options_mortality', language: '@language_en_us', value: Mortality } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_name_label', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_query_empty_text', language: '@language_en_us', value: Please select } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_fields_query_label', language: '@language_en_us', value: Saved Query } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_create_title', language: '@language_en_us', value: Create Risk Monitor } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_created_successfully', language: '@language_en_us', value: New risk monitor created successfully } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_count', language: '@language_en_us', value: Count } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_monitor_name', language: '@language_en_us', value: Risk Monitor Name } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_list_columns_query_name', language: '@language_en_us', value: Query Name } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_confirm_delete', language: '@language_en_us', value: 'Remove this Risk Monitor?' } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_list_no_monitors', language: '@language_en_us', value: No risk monitors defined for this module } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_removed_successfully', language: '@language_en_us', value: Risk monitor removed successfully } }
    - { fields: { placeholder: '@erm_risk_risk_monitors_show_list', language: '@language_en_us', value: Show List } }
    - { fields: { placeholder: '@erm_risk_risk_subtype_select_subtype', language: '@language_en_us', value: Select Subtype } }
    - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_subtypes', language: '@language_en_us', value: None Available } }
    - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_tertiary_subtypes', language: '@language_en_us', value: None Available } }
    - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_select_tertiary_subtype', language: '@language_en_us', value: Select Sub-Subtype } }
    - { fields: { placeholder: '@erm_risk_risk_type_select_type', language: '@language_en_us', value: Select Type } }
    - { fields: { placeholder: '@erm_risk_saved_successfully', language: '@language_en_us', value: Risk saved successfully } }
    - { fields: { placeholder: '@erm_risk_errors_locked', language: '@language_en_us', value: The risk is locked and cannot be updated at this time } }
    - { fields: { placeholder: '@erm_risk_search', language: '@language_en_us', value: Search for Risks } }
    - { fields: { placeholder: '@erm_risk_services_and_locations_save_service', language: '@language_en_us', value: Save Service Change } }
    - { fields: { placeholder: '@erm_risk_singular', language: '@language_en_us', value: Risk } }
    - { fields: { placeholder: '@erm_success_notes_deleted', language: '@language_en_us', value: Note deleted successfully } }
    - { fields: { placeholder: '@erm_success_notes_saved', language: '@language_en_us', value: Note saved successfully } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_risk_type_no_types', language: '@language_en_us', value: 'No types assigned to this risk' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_safe_title', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Safe"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_safe_label', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Safe"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_effective_title', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Effective"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_effective_label', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Effective"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_caring_title', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Caring"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_caring_label', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Caring"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_responsive_title', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Responsive"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_responsive_label', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Responsive"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_well_led_title', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Well-led"?' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_cqc_well_led_label', language: '@language_en_us', value: 'How could this risk impact the CQC domain "Well-led"?' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_1', language: '@language_en_us', value: 'Safe - Could impact on protection from bullying, harassment, avoidable harm.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_2', language: '@language_en_us', value: 'Safe - Could impact on protection and freedom being supported and respected.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_3', language: '@language_en_us', value: 'Safe - Insufficient staff to keep people safe and meet their needs.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_4', language: '@language_en_us', value: 'Safe - Could impact on medicines not being managed safely' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_5', language: '@language_en_us', value: 'Safe - Could impact on protection, prevention or control of infection.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_6', language: '@language_en_us', value: 'Safe - Could impact on ability to deal with incidents and accidents quickly and openly and learn from mistakes.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_safe_7', language: '@language_en_us', value: 'Safe - Could impact on ensuring children, young people and adults who may be vulnerable are kept safe from harm.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_effective_1', language: '@language_en_us', value: 'Effective - Lack of knowledge and skills leading to ineffective care' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_effective_2', language: '@language_en_us', value: 'Effective - Consent to care and treatment is not in line with legislation' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_effective_3', language: '@language_en_us', value: 'Effective - People are not supported to eat, drink and maintain a healthy diet.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_effective_4', language: '@language_en_us', value: 'Effective - Poor access to health care services and ongoing support.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_effective_5', language: '@language_en_us', value: 'Effective - Care is provided in accordance with your needs and choices.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_caring_1', language: '@language_en_us', value: 'Caring - Lack of personalized approach, negative relationships with patients/public' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_caring_2', language: '@language_en_us', value: 'Caring - People''s privacy and dignity is not respected.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_caring_3', language: '@language_en_us', value: 'Caring - Poor support at end of life, lack of dignity & pain at death' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_caring_4', language: '@language_en_us', value: 'Caring - Lack of access to advocates and/or encouragement to express views.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_caring_5', language: '@language_en_us', value: 'Caring - Family, and friends can''t contact service users.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_responsive_1', language: '@language_en_us', value: 'Responsive - Care is not personalized or supportive of individuals needs' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_responsive_2', language: '@language_en_us', value: 'Responsive - People''s concerns and complaints are not listened to.' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_responsive_3', language: '@language_en_us', value: 'Responsive - Poor continuity of care between services' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_well_led_1', language: '@language_en_us', value: 'Well led - Working culture is not positive, open, inclusive or empowering' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_well_led_2', language: '@language_en_us', value: 'Well led - Good management and leadership is not demonstrated' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_well_led_3', language: '@language_en_us', value: 'Well led - High quality care is not delivered' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_well_led_4', language: '@language_en_us', value: 'Well led - Could impact on external working relationships' } }
    - { fields: { placeholder: '@erm_datasource_item_cqc_well_led_5', language: '@language_en_us', value: 'Well led - Staff do not feel confident to report concerns and/or are not supported' } }
    - { fields: { placeholder: '@erm_risk_filters_location', language: '@language_en_us', value: 'Location Filter' } }
    - { fields: { placeholder: '@erm_risk_filters_location_label', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@erm_risk_filters_service', language: '@language_en_us', value: 'Service Filter' } }
    - { fields: { placeholder: '@erm_risk_filters_service_label', language: '@language_en_us', value: Service } }
    - { fields: { placeholder: '@erm_risk_risk_tertiary_subtype_no_types', language: '@language_en_us', value: 'None Available' } }
    - { fields: { placeholder: '@placeholder_erm_review_no_services', language: '@language_en_us', value: 'This Risk had no attached Services' } }
    - { fields: { placeholder: '@erm_medications_invalid_medication_uuid', language: '@language_en_us', value: 'A valid Medication UUID must be provided' } }
    - { fields: { placeholder: '@erm_admin_organisational_objectives_new_objective', language: '@language_en_us', value: 'New Objective' } }
    - { fields: { placeholder: '@erm_risk_filters_risk_review_date', language: '@language_en_us', value: 'Risk Review Date' } }
    - { fields: { placeholder: '@erm_risk_filters_risk_review_date_label', language: '@language_en_us', value: 'Risk Review Date' } }
    - { fields: { placeholder: '@erm_risk_filters_risk_open_date', language: '@language_en_us', value: 'Risk Open Date' } }
    - { fields: { placeholder: '@erm_risk_filters_risk_closed_date', language: '@language_en_us', value: 'Risk Closed Date' } }
    - { fields: { placeholder: '@erm_risk_filters_risk_closed_date_label', language: '@language_en_us', value: 'Risk Closed Date' } }
    - { fields: { placeholder: '@erm_risk_filters_current_rating', language: '@language_en_us', value: 'Current Rating' } }
    - { fields: { placeholder: '@erm_risk_filters_title', language: '@language_en_us', value: 'Filter Risks' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_id', language: '@language_en_us', value: 'Risk Id' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_id_label', language: '@language_en_us', value: 'Risk Id' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_status', language: '@language_en_us', value: 'Status' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_status_label', language: '@language_en_us', value: 'Status' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_columns_status', language: '@language_en_us', value: 'Status' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_level', language: '@language_en_us', value: 'Risk Level' } }
    - { fields: { placeholder: '@erm_risk_filter_form_fields_risk_level_label', language: '@language_en_us', value: 'Risk Level' } }
    - { fields: { placeholder: '@erm_risk_monitors_new', language: '@language_en_us', value: 'New' } }
    - { fields: { placeholder: '@erm_risk_default_form_fields_closure_reason_label', language: '@language_en_us', value: 'Enforce risk review on risk closure' } }
    - { fields: { placeholder: '@erm_risk_label_risk_details', language: '@language_en_us', value: Risk Form } }
    - { fields: { placeholder: '@erm_risk_label_services', language: '@language_en_us', value: Services } }
    - { fields: { placeholder: '@erm_risk_label_locations', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@erm_risk_label_controls_and_assurance', language: '@language_en_us', value: Controls & Assurance } }
    - { fields: { placeholder: '@erm_risk_label_medications', language: '@language_en_us', value: Medications } }
