entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@acl_access_control_generated_roles', language: '@language_en_us', value: Generated Roles } }
    - { fields: { placeholder: '@acl_access_control_manual_roles', language: '@language_en_us', value: Manual Roles } }
    - { fields: { placeholder: '@acl_access_control_title', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@acl_error_cannot_edit_own_permissions', language: '@language_en_us', value: You cannot edit your own permissions } }
    - { fields: { placeholder: '@acl_error_select_role', language: '@language_en_us', value: You must set a Role } }
    - { fields: { placeholder: '@acl_field_creator_rule', language: '@language_en_us', value: 'Creator Rule?' } }
    - { fields: { placeholder: '@acl_field_description', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@acl_field_module', language: '@language_en_us', value: Module } }
    - { fields: { placeholder: '@acl_field_module_select_module', language: '@language_en_us', value: Select a Module } }
    - { fields: { placeholder: '@acl_field_role', language: '@language_en_us', value: Role } }
    - { fields: { placeholder: '@acl_field_role_select_role', language: '@language_en_us', value: Select a Role } }
    - { fields: { placeholder: '@acl_field_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@acl_groups', language: '@language_en_us', value: Groups } }
    - { fields: { placeholder: '@acl_groups_group_form_added', language: '@language_en_us', value: Group form successfully added } }
    - { fields: { placeholder: '@acl_groups_group_form_removed', language: '@language_en_us', value: Group form successfully removed } }
    - { fields: { placeholder: '@acl_module_permissions_list_columns_can_assign', language: '@language_en_us', value: Can Assign } }
    - { fields: { placeholder: '@acl_module_permissions_list_columns_can_create', language: '@language_en_us', value: Can Create } }
    - { fields: { placeholder: '@acl_module_permissions_list_columns_is_admin', language: '@language_en_us', value: Is Admin } }
    - { fields: { placeholder: '@acl_module_permissions_list_columns_module', language: '@language_en_us', value: Module } }
    - { fields: { placeholder: '@acl_module_permissions_saved_successfully', language: '@language_en_us', value: Permissions saved successfully } }
    - { fields: { placeholder: '@acl_module_permissions_title', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@acl_nav_groups', language: '@language_en_us', value: Groups } }
    - { fields: { placeholder: '@acl_nav_roles', language: '@language_en_us', value: Roles } }
    - { fields: { placeholder: '@acl_nav_rules', language: '@language_en_us', value: Rules } }
    - { fields: { placeholder: '@acl_roles_deny', language: '@language_en_us', value: Deny } }
    - { fields: { placeholder: '@acl_roles_read_and_write', language: '@language_en_us', value: Read/Write } }
    - { fields: { placeholder: '@acl_roles_read_only', language: '@language_en_us', value: Read Only } }
    - { fields: { placeholder: '@acl_roles_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@acl_roles_submit', language: '@language_en_us', value: Submit } }
    - { fields: { placeholder: '@acl_rule_form_title', language: '@language_en_us', value: Rule Details } }
    - { fields: { placeholder: '@acl_users', language: '@language_en_us', value: Users } }
    - { fields: { placeholder: '@acl_roles_role_title_register_owner', language: '@language_en_us', value: Register Owner } }
    - { fields: { placeholder: '@acl_roles_role_title_risk_reviewer', language: '@language_en_us', value: Risk Reviewer } }
    - { fields: { placeholder: '@acl_roles_role_title_erm_read_only', language: '@language_en_us', value: ERM Read Only } }
    - { fields: { placeholder: '@acl_roles_role_title_temporary_creator_rule', language: '@language_en_us', value: Temporary Creator Role } }
    - { fields: { placeholder: '@acl_roles_role_title_risk_viewer', language: '@language_en_us', value: Risk Viewer } }
    - { fields: { placeholder: '@acl_roles_role_title_risk_reporter', language: '@language_en_us', value: Risk Reporter } }
    - { fields: { placeholder: '@acl_roles_role_title_tracker_lead', language: '@language_en_us', value: Tracker Lead } }
    - { fields: { placeholder: '@acl_roles_role_title_risk_erm_deny', language: '@language_en_us', value: ERM Deny } }
