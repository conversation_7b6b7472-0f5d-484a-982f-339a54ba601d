entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@acl_access_control_title', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@acl_access_control_manual_roles', language: '@language_fr_ca', value: '<PERSON><PERSON><PERSON> manuels' } }
  - { fields: { placeholder: '@acl_access_control_generated_roles', language: '@language_fr_ca', value: 'Rôles générés' } }
  - { fields: { placeholder: '@acl_module_permissions_title', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@acl_module_permissions_list_columns_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@acl_module_permissions_list_columns_is_admin', language: '@language_fr_ca', value: 'Est administrateur' } }
  - { fields: { placeholder: '@acl_module_permissions_list_columns_can_create', language: '@language_fr_ca', value: '<PERSON><PERSON>t c<PERSON>er' } }
  - { fields: { placeholder: '@acl_module_permissions_list_columns_can_assign', language: '@language_fr_ca', value: 'Peut attribuer' } }
  - { fields: { placeholder: '@acl_module_permissions_saved_successfully', language: '@language_fr_ca', value: 'Permissions enregistrées avec succès' } }
  - { fields: { placeholder: '@acl_rule_form_title', language: '@language_fr_ca', value: 'Détails de la règle' } }
  - { fields: { placeholder: '@acl_field_creator_rule', language: '@language_fr_ca', value: 'Créateur de règle?' } }
  - { fields: { placeholder: '@acl_field_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@acl_field_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@acl_field_module', language: '@language_fr_ca', value: Module } }
  - { fields: { placeholder: '@acl_field_module_select_module', language: '@language_fr_ca', value: 'Sélectionnez un module' } }
  - { fields: { placeholder: '@acl_field_role', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@acl_field_role_select_role', language: '@language_fr_ca', value: 'Sélectionnez au rôle' } }
  - { fields: { placeholder: '@acl_roles_read_only', language: '@language_fr_ca', value: 'Lecture seule' } }
  - { fields: { placeholder: '@acl_roles_read_and_write', language: '@language_fr_ca', value: Lecture/écriture } }
  - { fields: { placeholder: '@acl_roles_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@acl_roles_submit', language: '@language_fr_ca', value: Soumettre } }
  - { fields: { placeholder: '@acl_roles_deny', language: '@language_fr_ca', value: Refuser } }
  - { fields: { placeholder: '@acl_groups', language: '@language_fr_ca', value: Groupes } }
  - { fields: { placeholder: '@acl_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@acl_error_cannot_edit_own_permissions', language: '@language_fr_ca', value: 'Vous ne pouvez pas modifier vos propres permissions' } }
  - { fields: { placeholder: '@acl_error_select_role', language: '@language_fr_ca', value: 'Vous devez définir un rôle' } }
  - { fields: { placeholder: '@acl_groups_group_form_added', language: '@language_fr_ca', value: 'Formulaire de groupe ajouté avec succès' } }
  - { fields: { placeholder: '@acl_groups_group_form_removed', language: '@language_fr_ca', value: 'Le formulaire de groupe a bien été supprimé' } }
  - { fields: { placeholder: '@acl_nav_groups', language: '@language_fr_ca', value: Groupes } }
  - { fields: { placeholder: '@acl_nav_roles', language: '@language_fr_ca', value: Rôles } }
  - { fields: { placeholder: '@acl_nav_rules', language: '@language_fr_ca', value: Règles } }
  - { fields: { placeholder: '@acl_nav_groups_details', language: '@language_fr_ca', value: 'Détails du groupe' } }
  - { fields: { placeholder: '@acl_nav_groups_permissions', language: '@language_fr_ca', value: 'Permissions du groupe' } }
  - { fields: { placeholder: '@acl_roles_role_title_register_owner', language: '@language_fr_ca', value: 'Enregistrer le propriétaire' } }
  - { fields: { placeholder: '@acl_roles_role_title_risk_reviewer', language: '@language_fr_ca', value: 'Réviseur de risque' } }
  - { fields: { placeholder: '@acl_roles_role_title_erm_read_only', language: '@language_fr_ca', value: 'ERM en lecture seule' } }
  - { fields: { placeholder: '@acl_roles_role_title_temporary_creator_rule', language: '@language_fr_ca', value: 'Rôle de créateur temporaire' } }
  - { fields: { placeholder: '@acl_roles_role_title_risk_viewer', language: '@language_fr_ca', value: 'Visualiseur de risque' } }
  - { fields: { placeholder: '@acl_roles_role_title_risk_reporter', language: '@language_fr_ca', value: 'Reporter de risque' } }
  - { fields: { placeholder: '@acl_roles_role_title_tracker_lead', language: '@language_fr_ca', value: 'Responsable du traqueur' } }
  - { fields: { placeholder: '@acl_roles_role_title_risk_erm_deny', language: '@language_fr_ca', value: 'Refus ERM' } }
