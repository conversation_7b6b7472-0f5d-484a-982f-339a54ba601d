entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: ACL.ACCESS_CONTROL.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_access_control_title
  -
    fields:
      placeholder: ACL.ACCESS_CONTROL.MANUAL_ROLES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_access_control_manual_roles
  -
    fields:
      placeholder: ACL.ACCESS_CONTROL.GENERATED_ROLES
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_access_control_generated_roles
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.TITLE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_title
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.LIST.COLUMNS.MODULE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_list_columns_module
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.LIST.COLUMNS.IS_ADMIN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_list_columns_is_admin
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.LIST.COLUMNS.CAN_CREATE
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_list_columns_can_create
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.LIST.COLUMNS.CAN_ASSIGN
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_list_columns_can_assign
  -
    fields:
      placeholder: ACL.MODULE_PERMISSIONS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_common'
    ref: acl_module_permissions_saved_successfully
  -
    fields:
      placeholder: ACL.RULE.FORM.TITLE
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_rule_form_title
  -
    fields:
      placeholder: ACL.FIELD.CREATOR_RULE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_creator_rule
  -
    fields:
      placeholder: ACL.FIELD.TITLE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_title
  -
    fields:
      placeholder: ACL.FIELD.DESCRIPTION
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_description
  -
    fields:
      placeholder: ACL.FIELD.MODULE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_module
  -
    fields:
      placeholder: ACL.FIELD.MODULE.SELECT_MODULE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_module_select_module
  -
    fields:
      placeholder: ACL.FIELD.ROLE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_role
  -
    fields:
      placeholder: ACL.FIELD.ROLE.SELECT_ROLE
      type: 0
      domains:
        -
          domain: '@domain_acl_rules'
    ref: acl_field_role_select_role
  -
    fields:
      placeholder: ACL.ROLES.READ_ONLY
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_roles_read_only
  -
    fields:
      placeholder: ACL.ROLES.READ_AND_WRITE
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_roles_read_and_write
  -
    fields:
      placeholder: ACL.ROLES.STATUS
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_roles_status
  -
    fields:
      placeholder: ACL.ROLES.SUBMIT
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_roles_submit
  -
    fields:
      placeholder: ACL.ROLES.DENY
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_roles_deny
  -
    fields:
      placeholder: ACL.GROUPS
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_groups
  -
    fields:
      placeholder: ACL.USERS
      type: 0
      domains:
        -
          domain: '@domain_acl'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_common'
    ref: acl_users
  - fields:
      placeholder: ACL.ERROR.CANNOT_EDIT_OWN_PERMISSIONS
      type: 0
      domains:
      - domain: '@domain_acl'
      - domain: '@domain_common'
    ref: acl_error_cannot_edit_own_permissions
  - fields:
      placeholder: ACL.ERROR.SELECT_ROLE
      type: 0
      domains:
      - domain: '@domain_acl'
      - domain: '@domain_common'
    ref: acl_error_select_role
  - fields:
      placeholder: ACL.GROUPS.GROUP_FORM_ADDED
      type: 0
      domains:
      - domain: '@domain_acl_groups'
    ref: acl_groups_group_form_added
  - fields:
      placeholder: ACL.GROUPS.GROUP_FORM_REMOVED
      type: 0
      domains:
        - domain: '@domain_acl_groups'
    ref: acl_groups_group_form_removed
  - fields:
      placeholder: ACL.NAV_GROUPS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_nav_groups
  - fields:
      placeholder: ACL.NAV_ROLES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_nav_roles
  - fields:
      placeholder: ACL.NAV_RULES
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_nav_rules
  -
    fields:
      placeholder: ACL.NAV_GROUPS.DETAILS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_nav_groups_details
  -
    fields:
      placeholder: ACL.NAV_GROUPS.PERMISSIONS
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_nav_groups_permissions
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.REGISTER_OWNER
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_register_owner
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.RISK_REVIEWER
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_risk_reviewer
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.ERM_READ_ONLY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_erm_read_only
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.TEMPORARY_CREATOR_RULE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_temporary_creator_rule
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.RISK_VIEWER
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_risk_viewer
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.RISK_REPORTER
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_risk_reporter
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.TRACKER_LEAD
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_tracker_lead
  -
    fields:
      placeholder: ACL.ROLES.ROLE.TITLE.ERM_DENY
      type: 0
      domains:
        - domain: '@domain_common'
    ref: acl_roles_role_title_risk_erm_deny
