entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@devices_singular'
      language: '@language_en_gb'
      value: Equipment
  -
    fields:
      placeholder: '@devices_plural'
      language: '@language_en_gb'
      value: Equipment
  -
    fields:
      placeholder: '@devices_search'
      language: '@language_en_gb'
      value: 'Search Equipment'
  -
    fields:
      placeholder: '@devices_create'
      language: '@language_en_gb'
      value: 'Create Equipment'
  -
    fields:
      placeholder: '@devices_edit'
      language: '@language_en_gb'
      value: 'Edit Equipment'
  -
    fields:
      placeholder: '@devices_columns_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@devices_columns_device'
      language: '@language_en_gb'
      value: Device
  -
    fields:
      placeholder: '@devices_form_name'
      language: '@language_en_gb'
      value: Name
  -
    fields:
      placeholder: '@devices_form_label'
      language: '@language_en_gb'
      value: Name
  -
    fields:
      placeholder: '@devices_form_approval_status'
      language: '@language_en_gb'
      value: 'Approval Status'
  -
    fields:
      placeholder: '@devices_form_approval_status_label'
      language: '@language_en_gb'
      value: 'Approval Status'
  -
    fields:
      placeholder: '@devices_form_model'
      language: '@language_en_gb'
      value: Model
  -
    fields:
      placeholder: '@devices_form_manufacturer'
      language: '@language_en_gb'
      value: Manufacturer
  -
    fields:
      placeholder: '@devices_form_manufacturer_label'
      language: '@language_en_gb'
      value: Manufacturer
  -
    fields:
      placeholder: '@devices_form_supplier'
      language: '@language_en_gb'
      value: Supplier
  -
    fields:
      placeholder: '@devices_form_supplier_label'
      language: '@language_en_gb'
      value: Supplier
  -
    fields:
      placeholder: '@devices_form_category'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@devices_form_category_label'
      language: '@language_en_gb'
      value: Category
  -
    fields:
      placeholder: '@devices_form_ref'
      language: '@language_en_gb'
      value: Ref
  -
    fields:
      placeholder: '@devices_form_ref_label'
      language: '@language_en_gb'
      value: Ref
  -
    fields:
      placeholder: '@devices_form_batch_no'
      language: '@language_en_gb'
      value: 'Batch No.'
  -
    fields:
      placeholder: '@devices_form_batch_no_label'
      language: '@language_en_gb'
      value: 'Batch No.'
  -
    fields:
      placeholder: '@devices_form_serial_no'
      language: '@language_en_gb'
      value: 'Serial No.'
  -
    fields:
      placeholder: '@devices_form_serial_no_label'
      language: '@language_en_gb'
      value: 'Serial No.'
  -
    fields:
      placeholder: '@devices_form_ce_marking'
      language: '@language_en_gb'
      value: 'CE Marking'
  -
    fields:
      placeholder: '@devices_form_ce_marking_label'
      language: '@language_en_gb'
      value: 'CE Marking'
  -
    fields:
      placeholder: '@devices_form_date_of_manufacture'
      language: '@language_en_gb'
      value: 'Date of Manufacture'
  -
    fields:
      placeholder: '@devices_form_date_of_manufacture_label'
      language: '@language_en_gb'
      value: 'Date of Manufacture'
  -
    fields:
      placeholder: '@devices_form_date_put_in_use'
      language: '@language_en_gb'
      value: 'Date put in Use'
  -
    fields:
      placeholder: '@devices_form_date_put_in_use_label'
      language: '@language_en_gb'
      value: 'Date put in Use'
  -
    fields:
      placeholder: '@devices_form_date_of_last_service'
      language: '@language_en_gb'
      value: 'Date of Last Service'
  -
    fields:
      placeholder: '@devices_form_date_of_last_service_label'
      language: '@language_en_gb'
      value: 'Date of Last Service'
  -
    fields:
      placeholder: '@devices_form_date_of_next_service'
      language: '@language_en_gb'
      value: 'Date of Next Service'
  -
    fields:
      placeholder: '@devices_form_date_of_next_service_label'
      language: '@language_en_gb'
      value: 'Date of Next Service'
  -
    fields:
      placeholder: '@devices_form_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@devices_form_description_label'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@devices_form_catalogue_number'
      language: '@language_en_gb'
      value: 'Catalogue Number'
  -
    fields:
      placeholder: '@devices_form_catalogue_number_label'
      language: '@language_en_gb'
      value: 'Catalogue Number'
  -
    fields:
      placeholder: '@devices_form_quantity'
      language: '@language_en_gb'
      value: Quantity
  -
    fields:
      placeholder: '@devices_form_quantity_label'
      language: '@language_en_gb'
      value: Quantity
  -
    fields:
      placeholder: '@devices_select_status'
      language: '@language_en_gb'
      value: 'Select Status'
  -
    fields:
      placeholder: '@devices_select_type'
      language: '@language_en_gb'
      value: 'Select Type'
  -
    fields:
      placeholder: '@devices_select_model'
      language: '@language_en_gb'
      value: 'Select Model'
  -
    fields:
      placeholder: '@devices_select_manufacturer'
      language: '@language_en_gb'
      value: 'Select Manufacturer'
  -
    fields:
      placeholder: '@devices_select_supplier'
      language: '@language_en_gb'
      value: 'Select Supplier'
  -
    fields:
      placeholder: '@devices_select_category'
      language: '@language_en_gb'
      value: 'Select Category'
  -
    fields:
      placeholder: '@devices_status_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@devices_status_unapproved'
      language: '@language_en_gb'
      value: Unapproved
  -
    fields:
      placeholder: '@devices_status_rejected'
      language: '@language_en_gb'
      value: Rejected
# Form Types
  -
    fields:
      placeholder: '@devices_type_form'
      language: '@language_en_gb'
      value: 'Equipment Form'
# Data Sources
  -
    fields:
      placeholder: '@devices_data_source_categories'
      language: '@language_en_gb'
      value: 'Device Categories'
  -
    fields:
      placeholder: '@devices_data_source_manufacturers'
      language: '@language_en_gb'
      value: 'Device Manufacturers'
  -
    fields:
      placeholder: '@devices_data_source_models'
      language: '@language_en_gb'
      value: 'Device Models'
  -
    fields:
      placeholder: '@devices_data_source_statuses'
      language: '@language_en_gb'
      value: 'Device Statuses'
  -
    fields:
      placeholder: '@devices_data_source_suppliers'
      language: '@language_en_gb'
      value: 'Device Suppliers'
  -
    fields:
      placeholder: '@devices_data_source_types'
      language: '@language_en_gb'
      value: 'Device Types'
# Data Source Items (Categories)
  -
    fields:
      placeholder: '@devices_data_source_categories_items_item1'
      language: '@language_en_gb'
      value: 'Category 1'
  -
    fields:
      placeholder: '@devices_data_source_categories_items_item2'
      language: '@language_en_gb'
      value: 'Category 2'
  -
    fields:
      placeholder: '@devices_data_source_categories_items_item3'
      language: '@language_en_gb'
      value: 'Category 3'
# Data Source Items (Manufactures)
  -
    fields:
      placeholder: '@devices_data_source_manufacturers_items_item1'
      language: '@language_en_gb'
      value: 'Manufacturer 1'
  -
    fields:
      placeholder: '@devices_data_source_manufacturers_items_item2'
      language: '@language_en_gb'
      value: 'Manufacturer 2'
  -
    fields:
      placeholder: '@devices_data_source_manufacturers_items_item3'
      language: '@language_en_gb'
      value: 'Manufacturer 3'
# Data Source Items (Models)
  -
    fields:
      placeholder: '@devices_data_source_models_items_item1'
      language: '@language_en_gb'
      value: 'Models 1'
  -
    fields:
      placeholder: '@devices_data_source_models_items_item2'
      language: '@language_en_gb'
      value: 'Models 2'
  -
    fields:
      placeholder: '@devices_data_source_models_items_item3'
      language: '@language_en_gb'
      value: 'Models 3'
# Data Source Items (Suppliers)
  -
    fields:
      placeholder: '@devices_data_source_suppliers_items_item1'
      language: '@language_en_gb'
      value: 'Suppliers 1'
  -
    fields:
      placeholder: '@devices_data_source_suppliers_items_item2'
      language: '@language_en_gb'
      value: 'Suppliers 2'
  -
    fields:
      placeholder: '@devices_data_source_suppliers_items_item3'
      language: '@language_en_gb'
      value: 'Suppliers 3'
# Data Source Items (Types)
  -
    fields:
      placeholder: '@devices_data_source_types_items_item1'
      language: '@language_en_gb'
      value: 'Types 1'
  -
    fields:
      placeholder: '@devices_data_source_types_items_item2'
      language: '@language_en_gb'
      value: 'Types 2'
  -
    fields:
      placeholder: '@devices_datasource_device_categories'
      language: '@language_en_gb'
      value: 'Device Categories'
  -
    fields:
      placeholder: '@devices_datasource_device_manufacturers'
      language: '@language_en_gb'
      value: 'Device Manufacturers'
  -
    fields:
      placeholder: '@devices_datasource_device_models'
      language: '@language_en_gb'
      value: 'Device Models'
  -
    fields:
      placeholder: '@devices_datasource_device_statuses'
      language: '@language_en_gb'
      value: 'Device Statuses'
  -
    fields:
      placeholder: '@devices_datasource_device_suppliers'
      language: '@language_en_gb'
      value: 'Device Suppliers'
  -
    fields:
      placeholder: '@devices_datasource_device_types'
      language: '@language_en_gb'
      value: 'Device Types'
  -
    fields:
      placeholder: '@devices_suppliers_1'
      language: '@language_en_gb'
      value: 'Supplier 1'
  -
    fields:
      placeholder: '@devices_suppliers_2'
      language: '@language_en_gb'
      value: 'Supplier 2'
  -
    fields:
      placeholder: '@devices_suppliers_3'
      language: '@language_en_gb'
      value: 'Supplier 3'
  -
    fields:
      placeholder: '@devices_models_1'
      language: '@language_en_gb'
      value: 'Model 1'
  -
    fields:
      placeholder: '@devices_models_2'
      language: '@language_en_gb'
      value: 'Model 2'
  -
    fields:
      placeholder: '@devices_models_3'
      language: '@language_en_gb'
      value: 'Model 3'
  -
    fields:
      placeholder: '@devices_manufacturer_1'
      language: '@language_en_gb'
      value: 'Manufacturer 1'
  -
    fields:
      placeholder: '@devices_manufacturer_2'
      language: '@language_en_gb'
      value: 'Manufacturer 2'
  -
    fields:
      placeholder: '@devices_manufacturer_3'
      language: '@language_en_gb'
      value: 'Manufacturer 3'
  -
    fields:
      placeholder: '@devices_categories_1'
      language: '@language_en_gb'
      value: 'Category 1'
  -
    fields:
      placeholder: '@devices_categories_2'
      language: '@language_en_gb'
      value: 'Category 2'
  -
    fields:
      placeholder: '@devices_categories_3'
      language: '@language_en_gb'
      value: 'Category 3'
  -
    fields:
      placeholder: '@devices_types_1'
      language: '@language_en_gb'
      value: 'Type 1'
  -
    fields:
      placeholder: '@devices_types_2'
      language: '@language_en_gb'
      value: 'Type 2'
  -
    fields:
      placeholder: '@devices_v2_form_label_search'
      language: '@language_en_gb'
      value: 'Search'
  -
    fields:
      placeholder: '@devices_v2_form_label_expiry_date'
      language: '@language_en_gb'
      value: 'Expiry Date'
  -
    fields:
      placeholder: '@devices_v2_form_placeholder_expiry_date_format'
      language: '@language_en_gb'
      value: 'dd/mm/yyyy'
  -
    fields:
      placeholder: '@devices_v2_form_label_date_manufactured'
      language: '@language_en_gb'
      value: 'Date Manufactured'
  -
    fields:
      placeholder: '@devices_v2_form_placeholder_date_manufactured_format'
      language: '@language_en_gb'
      value: 'dd/mm/yyyy'
  -
    fields:
      placeholder: '@devices_v2_form_label_brand_name'
      language: '@language_en_gb'
      value: 'Brand Name'
  -
    fields:
      placeholder: '@devices_v2_form_placeholder_brand_name'
      language: '@language_en_gb'
      value: 'Start typing brand name of equipment'
  -
    fields:
      placeholder: '@devices_v2_form_message_brand_name_no_matches_found'
      language: '@language_en_gb'
      value: 'No matches found'
  -
    fields:
      placeholder: '@devices_v2_form_button_clear'
      language: '@language_en_gb'
      value: 'Clear'
  -
    fields:
      placeholder: '@devices_v2_form_subheading_equipment'
      language: '@language_en_gb'
      value: 'Equipment'
  -
    fields:
      placeholder: '@devices_v2_form_label_type'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@devices_v2_form_button_sub_type'
      language: '@language_en_gb'
      value: 'Sub-Type'
  -
    fields:
      placeholder: '@devices_v2_form_label_product_name'
      language: '@language_en_gb'
      value: 'Product Name'
  -
    fields:
      placeholder: '@devices_v2_form_label_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer'
  -
    fields:
      placeholder: '@devices_v2_form_label_model'
      language: '@language_en_gb'
      value: 'Model'
  -
    fields:
      placeholder: '@devices_v2_form_label_catalogue_number'
      language: '@language_en_gb'
      value: 'Catalogue Number'
  -
    fields:
      placeholder: '@devices_v2_form_label_batch_number'
      language: '@language_en_gb'
      value: 'Batch Number'
  -
    fields:
      placeholder: '@devices_v2_form_label_quantity_used'
      language: '@language_en_gb'
      value: 'Quantity Used'
  -
    fields:
      placeholder: '@devices_v2_form_label_serial_number'
      language: '@language_en_gb'
      value: 'Serial Number'
  -
    fields:
      placeholder: '@devices_v2_form_label_supplier'
      language: '@language_en_gb'
      value: 'Supplier'
  -
    fields:
      placeholder: '@devices_v2_form_label_current_device_location'
      language: '@language_en_gb'
      value: 'Current Location of Device'
  -
    fields:
      placeholder: '@devices_v2_form_label_device_type'
      language: '@language_en_gb'
      value: 'Type of Device'
  -
    fields:
      placeholder: '@devices_v2_form_label_device_operator'
      language: '@language_en_gb'
      value: 'Operator'
  -
    fields:
      placeholder: '@devices_v2_form_label_device_usage'
      language: '@language_en_gb'
      value: 'Usage of Device'
  -
    fields:
      placeholder: '@devices_v2_form_label_supply_category'
      language: '@language_en_gb'
      value: 'Category'
  -
    fields:
      placeholder: '@devices_v2_form_label_supply_classification'
      language: '@language_en_gb'
      value: 'Classification'
  -
    fields:
      placeholder: '@devices_v2_form_placeholder_select_one'
      language: '@language_en_gb'
      value: 'Select One'
  -
    fields:
      placeholder: '@devices_v2_form_placeholder_device_id'
      language: '@language_en_gb'
      value: 'Device Identifier'
  -
    fields:
      placeholder: '@devices_v2_display_placeholder_device_id'
      language: '@language_en_gb'
      value: 'Device Identifier'
  -
    fields:
      placeholder: '@devices_v2_display_heading_equipment'
      language: '@language_en_gb'
      value: 'Equipment'
  -
    fields:
      placeholder: '@devices_v2_display_button_edit_equipment'
      language: '@language_en_gb'
      value: 'Edit Equipment'
  -
    fields:
      placeholder: '@devices_v2_display_button_delete'
      language: '@language_en_gb'
      value: 'Delete'
  -
    fields:
      placeholder: '@devices_v2_display_label_brand_name'
      language: '@language_en_gb'
      value: 'Brand Name'
  -
    fields:
      placeholder: '@devices_v2_display_label_type'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@devices_v2_display_label_sub_type'
      language: '@language_en_gb'
      value: 'Sub-Type'
  -
    fields:
      placeholder: '@devices_v2_display_label_product_name'
      language: '@language_en_gb'
      value: 'Product Name'
  -
    fields:
      placeholder: '@devices_v2_display_label_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer'
  -
    fields:
      placeholder: '@devices_v2_display_label_model'
      language: '@language_en_gb'
      value: 'Model'
  -
    fields:
      placeholder: '@devices_v2_display_label_catalogue_number'
      language: '@language_en_gb'
      value: 'Catalogue Number'
  -
    fields:
      placeholder: '@devices_v2_display_label_catalogue_number'
      language: '@language_en_gb'
      value: 'Catalogue Number'
  -
    fields:
      placeholder: '@devices_v2_display_label_batch_number'
      language: '@language_en_gb'
      value: 'Batch Number'
  -
    fields:
      placeholder: '@devices_v2_display_label_quantity_used'
      language: '@language_en_gb'
      value: 'Quantity Used'
  -
    fields:
      placeholder: '@devices_v2_display_label_expiry_date'
      language: '@language_en_gb'
      value: 'Expiry Date'
  -
    fields:
      placeholder: '@devices_v2_display_label_date_manufactured'
      language: '@language_en_gb'
      value: 'Date Manufactured'
  -
    fields:
      placeholder: '@devices_v2_display_label_serial_number'
      language: '@language_en_gb'
      value: 'Serial Number'
  -
    fields:
      placeholder: '@devices_v2_display_label_supplier'
      language: '@language_en_gb'
      value: 'Supplier'
  -
    fields:
      placeholder: '@devices_v2_display_label_current_device_location'
      language: '@language_en_gb'
      value: 'Current Location of Device'
  -
    fields:
      placeholder: '@devices_v2_display_label_device_type'
      language: '@language_en_gb'
      value: 'Type of Device'
  -
    fields:
      placeholder: '@devices_v2_display_label_device_operator'
      language: '@language_en_gb'
      value: 'Operator of device at the time of the event'
  -
    fields:
      placeholder: '@devices_v2_display_label_device_usage'
      language: '@language_en_gb'
      value: 'Usage of Device'
  -
    fields:
      placeholder: '@devices_v2_display_label_supply_category'
      language: '@language_en_gb'
      value: 'Category'
  -
    fields:
      placeholder: '@devices_v2_display_label_supply_classification'
      language: '@language_en_gb'
      value: 'Classification'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_administration_and_giving_sets'
      language: '@language_en_gb'
      value: 'Administration and giving sets'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_anaesthetic_machines_and_monitors'
      language: '@language_en_gb'
      value: 'Anaesthetic machines and monitors'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_anaesthetic_and_breathing_masks'
      language: '@language_en_gb'
      value: 'Anaesthetic and breathing masks'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_autoclaves'
      language: '@language_en_gb'
      value: 'Autoclaves'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_bath_aids'
      language: '@language_en_gb'
      value: 'Bath aids'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_beds_and_mattresses'
      language: '@language_en_gb'
      value: 'Beds and mattresses'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_blood_pressure_measurement'
      language: '@language_en_gb'
      value: 'Blood pressure measurement'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_commodes'
      language: '@language_en_gb'
      value: 'Commodes'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_contact_lenses_and_care_products'
      language: '@language_en_gb'
      value: 'Contact lenses and care products'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_ct_systems'
      language: '@language_en_gb'
      value: 'CT systems'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_dental_appliances'
      language: '@language_en_gb'
      value: 'Dental appliances'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_dental_materials'
      language: '@language_en_gb'
      value: 'Dental materials'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_dialysis_equipment'
      language: '@language_en_gb'
      value: 'Dialysis equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_diathermy_equipment_and_accessories'
      language: '@language_en_gb'
      value: 'Diathermy equipment and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_dressings'
      language: '@language_en_gb'
      value: 'Dressings'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_endoscopes_and_accessories'
      language: '@language_en_gb'
      value: 'Endoscopes and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_endotracheal_tubes_and_airways'
      language: '@language_en_gb'
      value: 'Endotracheal tubes and airways'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_external_defibrillators'
      language: '@language_en_gb'
      value: 'External defibrillators'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_external_pacemakers'
      language: '@language_en_gb'
      value: 'External pacemakers'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_feeding_systems_enteral'
      language: '@language_en_gb'
      value: 'Feeding systems - enteral'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_feeding_tubes'
      language: '@language_en_gb'
      value: 'Feeding tubes'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_gloves'
      language: '@language_en_gb'
      value: 'Gloves'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_guidewires'
      language: '@language_en_gb'
      value: 'Guidewires'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_hearing_aids'
      language: '@language_en_gb'
      value: 'Hearing aids'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_heart_lung_bypass_machine'
      language: '@language_en_gb'
      value: 'Heart lung bypass machine'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_hypodermic_syringes_and_needles'
      language: '@language_en_gb'
      value: 'Hypodermic syringes and needles'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_active_general'
      language: '@language_en_gb'
      value: 'Implants  –  active (general)'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_breast'
      language: '@language_en_gb'
      value: 'Implants  –  breast'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_cardiovascular'
      language: '@language_en_gb'
      value: 'Implants  –  cardiovascular'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_hip_and_knee'
      language: '@language_en_gb'
      value: 'Implants  –  hip and knee'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_non_active'
      language: '@language_en_gb'
      value: 'Implants  –  non-active'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implants_pacemakers_defibrillators_and_leads'
      language: '@language_en_gb'
      value: 'Implants  –  pacemakers, defibrillators and leads'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_implant_materials'
      language: '@language_en_gb'
      value: 'Implant materials'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_in_vitro_medical_devices'
      language: '@language_en_gb'
      value: 'In vitro medical devices'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_infant_incubators'
      language: '@language_en_gb'
      value: 'Infant incubators'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_infusion_pumps_syringe_drivers'
      language: '@language_en_gb'
      value: 'Infusion pumps, syringe drivers'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_insulin_syringes'
      language: '@language_en_gb'
      value: 'Insulin syringes'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_intravenous_catheters_and_cannulae'
      language: '@language_en_gb'
      value: 'Intravenous catheters and cannulae'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_laryngoscopes'
      language: '@language_en_gb'
      value: 'Laryngoscopes'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_lasers_and_accessories'
      language: '@language_en_gb'
      value: 'Lasers and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_magnetic_resonance_equipment_and_accessories'
      language: '@language_en_gb'
      value: 'Magnetic resonance equipment and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_mobile_x_ray_systems'
      language: '@language_en_gb'
      value: 'Mobile X-ray systems'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_mobility_devices_wheeled_seating_aids_and_accessories'
      language: '@language_en_gb'
      value: 'Mobility devices- wheeled, seating aids and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_mobility_devices_non_wheeled'
      language: '@language_en_gb'
      value: 'Mobility devices - non-wheeled'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_monitors_and_electrodes'
      language: '@language_en_gb'
      value: 'Monitors and electrodes'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_ophthalmic_equipment'
      language: '@language_en_gb'
      value: 'Ophthalmic equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_orthotics'
      language: '@language_en_gb'
      value: 'Orthotics'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_patient_hoists'
      language: '@language_en_gb'
      value: 'Patient hoists'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_patient_monitoring_equipment'
      language: '@language_en_gb'
      value: 'Patient monitoring equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_physiotherapy_equipment'
      language: '@language_en_gb'
      value: 'Physiotherapy equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_prostheses_external_limb'
      language: '@language_en_gb'
      value: 'Prostheses  –  external limb'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_radiotherapy_equipment'
      language: '@language_en_gb'
      value: 'Radiotherapy equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_radionuclide_equipment'
      language: '@language_en_gb'
      value: 'Radionuclide equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_resuscitators'
      language: '@language_en_gb'
      value: 'Resuscitators'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_staples_and_staple_guns'
      language: '@language_en_gb'
      value: 'Staples and staple guns'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_stretchers'
      language: '@language_en_gb'
      value: 'Stretchers'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_surgical_instruments'
      language: '@language_en_gb'
      value: 'Surgical instruments'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_surgical_power_tools'
      language: '@language_en_gb'
      value: 'Surgical power tools'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_sutures'
      language: '@language_en_gb'
      value: 'Sutures'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_thermometers'
      language: '@language_en_gb'
      value: 'Thermometers'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_ultrasound_equipment'
      language: '@language_en_gb'
      value: 'Ultrasound equipment'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_urinary_catheters'
      language: '@language_en_gb'
      value: 'Urinary catheters'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_ventilators'
      language: '@language_en_gb'
      value: 'Ventilators'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_walking_sticks_frames'
      language: '@language_en_gb'
      value: 'Walking sticks / frames'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_wound_drains'
      language: '@language_en_gb'
      value: 'Wound drains'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_x_ray_equipment_systems_and_accessories'
      language: '@language_en_gb'
      value: 'X-ray equipment, systems and accessories'
  -
    fields:
      placeholder: '@devices_v2_form_option_device_type_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_operator_healthcare_professional'
      language: '@language_en_gb'
      value: 'Healthcare professional'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_operator_patient'
      language: '@language_en_gb'
      value: 'Patient'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_operator_other_caregiver'
      language: '@language_en_gb'
      value: 'Other Caregiver'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_operator_none'
      language: '@language_en_gb'
      value: 'None'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_usage_initial_use'
      language: '@language_en_gb'
      value: 'Initial Use'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_single_use_device'
      language: '@language_en_gb'
      value: 'Reuse of Single Use Device'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_reusable_device'
      language: '@language_en_gb'
      value: 'Reuse of Reusable Device'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_usage_re_serviced_refurbished'
      language: '@language_en_gb'
      value: 'Re-serviced/Refurbished'
  -
    fields:
      placeholder: '@devices_v2_form_option_equipment_usage_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@devices_v2_form_message_delete_title'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@devices_v2_form_message_delete_content'
      language: '@language_en_gb'
      value: 'Do you want to delete this equipment from the incident?'
  -
    fields:
      placeholder: '@devices_v2_form_button_delete_cancel'
      language: '@language_en_gb'
      value: 'Cancel'
  -
    fields:
      placeholder: '@devices_v2_form_button_delete_ok'
      language: '@language_en_gb'
      value: 'OK'
  -
    fields:
      placeholder: '@devices_v2_form_message_cancel_title'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@devices_v2_form_message_cancel_content'
      language: '@language_en_gb'
      value: 'Do you want to discard your changes?'
  -
    fields:
      placeholder: '@devices_v2_form_button_cancel_cancel'
      language: '@language_en_gb'
      value: 'Cancel'
  -
    fields:
      placeholder: '@devices_v2_form_button_cancel_ok'
      language: '@language_en_gb'
      value: 'OK'
  -
    fields:
      placeholder: '@devices_v2_form_message_successful_save'
      language: '@language_en_gb'
      value: 'has been added'
  -
    fields:
      placeholder: '@devices_v2_form_message_successful_add'
      language: '@language_en_gb'
      value: 'has been added'
  -
    fields:
      placeholder: '@devices_v2_form_message_successful_update'
      language: '@language_en_gb'
      value: 'has been updated'
  -
    fields:
      placeholder: '@devices_v2_form_message_device_search_exact_matches_only'
      language: '@language_en_gb'
      value: 'Exact matches only. Please enter two or more characters to broaden your search.'
  -
    fields:
      placeholder: '@devices_v2_form_message_device_search_no_matches'
      language: '@language_en_gb'
      value: 'No matching equipment found'
  -
    fields:
      placeholder: '@devices_v2_form_message_device_search_matching_equipment'
      language: '@language_en_gb'
      value: 'Matching Equipment'
  -
    fields:
      placeholder: '@devices_v2_message_search_table_generic_name'
      language: '@language_en_gb'
      value: 'Name'
  -
    fields:
      placeholder: '@devices_v2_message_search_table_type'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@devices_v2_message_search_table_sub_type'
      language: '@language_en_gb'
      value: 'Sub Type'
  -
    fields:
      placeholder: '@devices_v2_message_search_table_brand'
      language: '@language_en_gb'
      value: 'Brand'
  -
    fields:
      placeholder: '@devices_v2_message_search_table_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer'
  -
    fields:
      placeholder: '@devices_v2_button_search_table_choose'
      language: '@language_en_gb'
      value: 'Choose'
  -
    fields:
      placeholder: '@devices_successfully_saved'
      language: '@language_en_gb'
      value: 'Equipment Successfully Saved'
  -
    fields:
      placeholder: '@devices_successfully_deleted'
      language: '@language_en_gb'
      value: 'Equipment Successfully Deleted'
  -
    fields:
      placeholder: '@devices_device_added_successfully'
      language: '@language_en_gb'
      value: 'Equipment added successfully'
  -
    fields:
      placeholder: '@devices_device_removed_successfully'
      language: '@language_en_gb'
      value: 'Equipment removed successfully'
  -
    fields:
      placeholder: '@devices_risk_added'
      language: '@language_en_gb'
      value: 'Equipment added to Risk'
  -
    fields:
      placeholder: '@devices_risk_remove'
      language: '@language_en_gb'
      value: 'Equipment removed from Risk'
  -
    fields:
      placeholder: '@devices_new_equipment'
      language: '@language_en_gb'
      value: New Equipment
