entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@devices_singular', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@devices_plural', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@devices_search', language: '@language_fr_ca', value: 'Rechercher un équipement' } }
  - { fields: { placeholder: '@devices_create', language: '@language_fr_ca', value: 'Créer un équipement' } }
  - { fields: { placeholder: '@devices_edit', language: '@language_fr_ca', value: 'Modifier un équipement' } }
  - { fields: { placeholder: '@devices_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@devices_columns_device', language: '@language_fr_ca', value: Appareil } }
  - { fields: { placeholder: '@devices_form_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@devices_form_label', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@devices_form_approval_status', language: '@language_fr_ca', value: 'État d''approbation' } }
  - { fields: { placeholder: '@devices_form_approval_status_label', language: '@language_fr_ca', value: 'État d''approbation' } }
  - { fields: { placeholder: '@devices_form_model', language: '@language_fr_ca', value: Modèle } }
  - { fields: { placeholder: '@devices_form_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@devices_form_manufacturer_label', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@devices_form_supplier', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@devices_form_supplier_label', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@devices_form_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@devices_form_category_label', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@devices_form_ref', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@devices_form_ref_label', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@devices_form_batch_no', language: '@language_fr_ca', value: 'Lot Nº' } }
  - { fields: { placeholder: '@devices_form_batch_no_label', language: '@language_fr_ca', value: 'Lot Nº' } }
  - { fields: { placeholder: '@devices_form_serial_no', language: '@language_fr_ca', value: 'Nº de série' } }
  - { fields: { placeholder: '@devices_form_serial_no_label', language: '@language_fr_ca', value: 'Nº de série' } }
  - { fields: { placeholder: '@devices_form_ce_marking', language: '@language_fr_ca', value: 'Marquage CE' } }
  - { fields: { placeholder: '@devices_form_ce_marking_label', language: '@language_fr_ca', value: 'Marquage CE' } }
  - { fields: { placeholder: '@devices_form_date_of_manufacture', language: '@language_fr_ca', value: 'Date de fabrication' } }
  - { fields: { placeholder: '@devices_form_date_of_manufacture_label', language: '@language_fr_ca', value: 'Date de fabrication' } }
  - { fields: { placeholder: '@devices_form_date_put_in_use', language: '@language_fr_ca', value: 'Date de mise en service' } }
  - { fields: { placeholder: '@devices_form_date_put_in_use_label', language: '@language_fr_ca', value: 'Date de mise en service' } }
  - { fields: { placeholder: '@devices_form_date_of_last_service', language: '@language_fr_ca', value: 'Date du dernier entretien' } }
  - { fields: { placeholder: '@devices_form_date_of_last_service_label', language: '@language_fr_ca', value: 'Date du dernier entretien' } }
  - { fields: { placeholder: '@devices_form_date_of_next_service', language: '@language_fr_ca', value: 'Date du prochain entretien' } }
  - { fields: { placeholder: '@devices_form_date_of_next_service_label', language: '@language_fr_ca', value: 'Date du prochain entretien' } }
  - { fields: { placeholder: '@devices_form_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@devices_form_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@devices_form_catalogue_number', language: '@language_fr_ca', value: 'Numéro de catalogue' } }
  - { fields: { placeholder: '@devices_form_catalogue_number_label', language: '@language_fr_ca', value: 'Numéro de catalogue' } }
  - { fields: { placeholder: '@devices_form_quantity', language: '@language_fr_ca', value: Quantité } }
  - { fields: { placeholder: '@devices_form_quantity_label', language: '@language_fr_ca', value: Quantité } }
  - { fields: { placeholder: '@devices_select_status', language: '@language_fr_ca', value: 'Sélectionner l''état' } }
  - { fields: { placeholder: '@devices_select_type', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@devices_select_model', language: '@language_fr_ca', value: 'Sélectionner le modèle' } }
  - { fields: { placeholder: '@devices_select_manufacturer', language: '@language_fr_ca', value: 'Sélectionner un fabricant' } }
  - { fields: { placeholder: '@devices_select_supplier', language: '@language_fr_ca', value: 'Sélectionner le fournisseur' } }
  - { fields: { placeholder: '@devices_select_category', language: '@language_fr_ca', value: 'Sélectionner la catégorie' } }
  - { fields: { placeholder: '@devices_status_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@devices_status_unapproved', language: '@language_fr_ca', value: 'Non approuvé' } }
  - { fields: { placeholder: '@devices_status_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@devices_type_form', language: '@language_fr_ca', value: 'Formulaire d''équipement' } }
  - { fields: { placeholder: '@devices_data_source_categories', language: '@language_fr_ca', value: 'Catégories d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_manufacturers', language: '@language_fr_ca', value: 'Fabricants d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_models', language: '@language_fr_ca', value: 'Modèles d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_statuses', language: '@language_fr_ca', value: 'États d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_suppliers', language: '@language_fr_ca', value: 'Fournisseurs d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_types', language: '@language_fr_ca', value: 'Types d''appareils' } }
  - { fields: { placeholder: '@devices_data_source_categories_items_item1', language: '@language_fr_ca', value: "Catégorie\_1" } }
  - { fields: { placeholder: '@devices_data_source_categories_items_item2', language: '@language_fr_ca', value: "Catégorie\_2" } }
  - { fields: { placeholder: '@devices_data_source_categories_items_item3', language: '@language_fr_ca', value: "Catégorie\_3" } }
  - { fields: { placeholder: '@devices_data_source_manufacturers_items_item1', language: '@language_fr_ca', value: "Fabricant\_1" } }
  - { fields: { placeholder: '@devices_data_source_manufacturers_items_item2', language: '@language_fr_ca', value: "Fabricant\_2" } }
  - { fields: { placeholder: '@devices_data_source_manufacturers_items_item3', language: '@language_fr_ca', value: "Fabricant\_3" } }
  - { fields: { placeholder: '@devices_data_source_models_items_item1', language: '@language_fr_ca', value: "Modèles\_1" } }
  - { fields: { placeholder: '@devices_data_source_models_items_item2', language: '@language_fr_ca', value: "Modèles\_2" } }
  - { fields: { placeholder: '@devices_data_source_models_items_item3', language: '@language_fr_ca', value: "Modèles\_3" } }
  - { fields: { placeholder: '@devices_data_source_suppliers_items_item1', language: '@language_fr_ca', value: "Fournisseurs\_1" } }
  - { fields: { placeholder: '@devices_data_source_suppliers_items_item2', language: '@language_fr_ca', value: "Fournisseurs\_2" } }
  - { fields: { placeholder: '@devices_data_source_suppliers_items_item3', language: '@language_fr_ca', value: "Fournisseurs\_3" } }
  - { fields: { placeholder: '@devices_data_source_types_items_item1', language: '@language_fr_ca', value: "Types\_1" } }
  - { fields: { placeholder: '@devices_data_source_types_items_item2', language: '@language_fr_ca', value: "Types\_2" } }
  - { fields: { placeholder: '@devices_datasource_device_categories', language: '@language_fr_ca', value: 'Catégories d''appareils' } }
  - { fields: { placeholder: '@devices_datasource_device_manufacturers', language: '@language_fr_ca', value: 'Fabricants d''appareils' } }
  - { fields: { placeholder: '@devices_datasource_device_models', language: '@language_fr_ca', value: 'Modèles d''appareils' } }
  - { fields: { placeholder: '@devices_datasource_device_statuses', language: '@language_fr_ca', value: 'États d''appareils' } }
  - { fields: { placeholder: '@devices_datasource_device_suppliers', language: '@language_fr_ca', value: 'Fournisseurs d''appareils' } }
  - { fields: { placeholder: '@devices_datasource_device_types', language: '@language_fr_ca', value: 'Types d''appareils' } }
  - { fields: { placeholder: '@devices_suppliers_1', language: '@language_fr_ca', value: "Fournisseur\_1" } }
  - { fields: { placeholder: '@devices_suppliers_2', language: '@language_fr_ca', value: "Fournisseur\_2" } }
  - { fields: { placeholder: '@devices_suppliers_3', language: '@language_fr_ca', value: "Fournisseur\_3" } }
  - { fields: { placeholder: '@devices_models_1', language: '@language_fr_ca', value: "Modèle\_1" } }
  - { fields: { placeholder: '@devices_models_2', language: '@language_fr_ca', value: "Modèle\_2" } }
  - { fields: { placeholder: '@devices_models_3', language: '@language_fr_ca', value: "Modèle\_3" } }
  - { fields: { placeholder: '@devices_manufacturer_1', language: '@language_fr_ca', value: "Fabricant\_1" } }
  - { fields: { placeholder: '@devices_manufacturer_2', language: '@language_fr_ca', value: "Fabricant\_2" } }
  - { fields: { placeholder: '@devices_manufacturer_3', language: '@language_fr_ca', value: "Fabricant\_3" } }
  - { fields: { placeholder: '@devices_categories_1', language: '@language_fr_ca', value: "Catégorie\_1" } }
  - { fields: { placeholder: '@devices_categories_2', language: '@language_fr_ca', value: "Catégorie\_2" } }
  - { fields: { placeholder: '@devices_categories_3', language: '@language_fr_ca', value: "Catégorie\_3" } }
  - { fields: { placeholder: '@devices_types_1', language: '@language_fr_ca', value: "Type\_1" } }
  - { fields: { placeholder: '@devices_types_2', language: '@language_fr_ca', value: "Type\_2" } }
  - { fields: { placeholder: '@devices_v2_form_label_search', language: '@language_fr_ca', value: Rechercher } }
  - { fields: { placeholder: '@devices_v2_form_label_expiry_date', language: '@language_fr_ca', value: 'Date d''expiration' } }
  - { fields: { placeholder: '@devices_v2_form_placeholder_expiry_date_format', language: '@language_fr_ca', value: jj/mm/aaaa } }
  - { fields: { placeholder: '@devices_v2_form_label_date_manufactured', language: '@language_fr_ca', value: 'Date de fabrication' } }
  - { fields: { placeholder: '@devices_v2_form_placeholder_date_manufactured_format', language: '@language_fr_ca', value: jj/mm/aaaa } }
  - { fields: { placeholder: '@devices_v2_form_label_brand_name', language: '@language_fr_ca', value: 'Nom de marque' } }
  - { fields: { placeholder: '@devices_v2_form_placeholder_brand_name', language: '@language_fr_ca', value: 'Commencez à taper le nom de marque de l''équipement' } }
  - { fields: { placeholder: '@devices_v2_form_message_brand_name_no_matches_found', language: '@language_fr_ca', value: 'Aucune correspondance trouvée' } }
  - { fields: { placeholder: '@devices_v2_form_button_clear', language: '@language_fr_ca', value: Effacer } }
  - { fields: { placeholder: '@devices_v2_form_subheading_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@devices_v2_form_label_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@devices_v2_form_button_sub_type', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@devices_v2_form_label_product_name', language: '@language_fr_ca', value: 'Nom du produit' } }
  - { fields: { placeholder: '@devices_v2_form_label_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@devices_v2_form_label_model', language: '@language_fr_ca', value: Modèle } }
  - { fields: { placeholder: '@devices_v2_form_label_catalogue_number', language: '@language_fr_ca', value: 'Numéro de catalogue' } }
  - { fields: { placeholder: '@devices_v2_form_label_batch_number', language: '@language_fr_ca', value: 'Numéro de lot' } }
  - { fields: { placeholder: '@devices_v2_form_label_quantity_used', language: '@language_fr_ca', value: 'Quantité utilisée' } }
  - { fields: { placeholder: '@devices_v2_form_label_serial_number', language: '@language_fr_ca', value: 'Numéro de série' } }
  - { fields: { placeholder: '@devices_v2_form_label_supplier', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@devices_v2_form_label_current_device_location', language: '@language_fr_ca', value: 'Emplacement courant de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_form_label_device_type', language: '@language_fr_ca', value: 'Type de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_form_label_device_operator', language: '@language_fr_ca', value: Opérateur } }
  - { fields: { placeholder: '@devices_v2_form_label_device_usage', language: '@language_fr_ca', value: 'Utilisation de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_form_label_supply_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@devices_v2_form_label_supply_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@devices_v2_form_placeholder_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@devices_v2_display_heading_equipment', language: '@language_fr_ca', value: Équipement } }
  - { fields: { placeholder: '@devices_v2_display_button_edit_equipment', language: '@language_fr_ca', value: 'Modifier un équipement' } }
  - { fields: { placeholder: '@devices_v2_display_button_delete', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@devices_v2_display_label_brand_name', language: '@language_fr_ca', value: 'Nom de marque' } }
  - { fields: { placeholder: '@devices_v2_display_label_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@devices_v2_display_label_sub_type', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@devices_v2_display_label_product_name', language: '@language_fr_ca', value: 'Nom du produit' } }
  - { fields: { placeholder: '@devices_v2_display_label_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@devices_v2_display_label_model', language: '@language_fr_ca', value: Modèle } }
  - { fields: { placeholder: '@devices_v2_display_label_catalogue_number', language: '@language_fr_ca', value: 'Numéro de catalogue' } }
  - { fields: { placeholder: '@devices_v2_display_label_batch_number', language: '@language_fr_ca', value: 'Numéro de lot' } }
  - { fields: { placeholder: '@devices_v2_display_label_quantity_used', language: '@language_fr_ca', value: 'Quantité utilisée' } }
  - { fields: { placeholder: '@devices_v2_display_label_expiry_date', language: '@language_fr_ca', value: 'Date d''expiration' } }
  - { fields: { placeholder: '@devices_v2_display_label_date_manufactured', language: '@language_fr_ca', value: 'Date de fabrication' } }
  - { fields: { placeholder: '@devices_v2_display_label_serial_number', language: '@language_fr_ca', value: 'Numéro de série' } }
  - { fields: { placeholder: '@devices_v2_display_label_supplier', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@devices_v2_display_label_current_device_location', language: '@language_fr_ca', value: 'Emplacement courant de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_display_label_device_type', language: '@language_fr_ca', value: 'Type de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_display_label_device_operator', language: '@language_fr_ca', value: 'Opérateur de l''appareil au moment de l''événement' } }
  - { fields: { placeholder: '@devices_v2_display_label_device_usage', language: '@language_fr_ca', value: 'Utilisation de l''appareil' } }
  - { fields: { placeholder: '@devices_v2_display_label_supply_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@devices_v2_display_label_supply_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_administration_and_giving_sets', language: '@language_fr_ca', value: 'Administration et remise des trousses' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_anaesthetic_machines_and_monitors', language: '@language_fr_ca', value: 'Machines et moniteurs d''anesthésie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_anaesthetic_and_breathing_masks', language: '@language_fr_ca', value: 'Masques d''anesthésie et respiratoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_autoclaves', language: '@language_fr_ca', value: Autoclaves } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_bath_aids', language: '@language_fr_ca', value: 'Accessoires de bain' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_beds_and_mattresses', language: '@language_fr_ca', value: 'Lits et matelas' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_blood_pressure_measurement', language: '@language_fr_ca', value: 'Mesure de la pression artérielle' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_commodes', language: '@language_fr_ca', value: 'Chaises percées' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_contact_lenses_and_care_products', language: '@language_fr_ca', value: 'Lentilles de contact et produits de soin' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_ct_systems', language: '@language_fr_ca', value: 'Systèmes de tomodensitomètrie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_dental_appliances', language: '@language_fr_ca', value: 'Appareils dentaires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_dental_materials', language: '@language_fr_ca', value: 'Matériels dentaires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_dialysis_equipment', language: '@language_fr_ca', value: 'Équipement de dialyse' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_diathermy_equipment_and_accessories', language: '@language_fr_ca', value: 'Appareils de diathermie et accessoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_dressings', language: '@language_fr_ca', value: Vêtements } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_endoscopes_and_accessories', language: '@language_fr_ca', value: 'Endoscopes et accessoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_endotracheal_tubes_and_airways', language: '@language_fr_ca', value: 'Tubes endotrachéaux et voies respiratoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_external_defibrillators', language: '@language_fr_ca', value: 'Défibrillateurs externes' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_external_pacemakers', language: '@language_fr_ca', value: 'Stimulateurs cardiaques externes' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_feeding_systems_enteral', language: '@language_fr_ca', value: 'Systèmes d''alimentation entérale' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_feeding_tubes', language: '@language_fr_ca', value: 'tubes d''alimentation' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_gloves', language: '@language_fr_ca', value: Gants } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_guidewires', language: '@language_fr_ca', value: 'Fils guides' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_hearing_aids', language: '@language_fr_ca', value: 'Prothèses auditives' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_heart_lung_bypass_machine', language: '@language_fr_ca', value: 'Machine de dérivation pulmonaire cardiaque' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_hypodermic_syringes_and_needles', language: '@language_fr_ca', value: 'Seringues hypodermiques et aiguilles' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_active_general', language: '@language_fr_ca', value: 'Implants - actifs (général)' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_breast', language: '@language_fr_ca', value: 'Implants - du sein' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_cardiovascular', language: '@language_fr_ca', value: 'Implants - cardiovasculaires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_hip_and_knee', language: '@language_fr_ca', value: 'Implants - de hanche et genou' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_non_active', language: '@language_fr_ca', value: 'Implants - non actifs' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_pacemakers_defibrillators_and_leads', language: '@language_fr_ca', value: 'Implants - stimulateurs cardiaques, défibrillateurs et sondes' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_implant_materials', language: '@language_fr_ca', value: 'Matériaux d''implants' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_in_vitro_medical_devices', language: '@language_fr_ca', value: 'Dispositifs médicaux in vitro' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_infant_incubators', language: '@language_fr_ca', value: 'Couveuses pour bébés' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_infusion_pumps_syringe_drivers', language: '@language_fr_ca', value: 'Pompes à perfusion, guide-seringues' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_insulin_syringes', language: '@language_fr_ca', value: 'Seringues à insuline' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_intravenous_catheters_and_cannulae', language: '@language_fr_ca', value: 'Cathéters et canules intraveineux' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_laryngoscopes', language: '@language_fr_ca', value: Laryngoscopes } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_lasers_and_accessories', language: '@language_fr_ca', value: 'Lasers et accessoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_magnetic_resonance_equipment_and_accessories', language: '@language_fr_ca', value: 'Équipement à résonance magnétique et accessoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_mobile_x_ray_systems', language: '@language_fr_ca', value: 'Systèmes de radiographie mobiles' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_mobility_devices_wheeled_seating_aids_and_accessories', language: '@language_fr_ca', value: 'Dispositifs de mobilité - roues, aides à l''assise et accessoires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_mobility_devices_non_wheeled', language: '@language_fr_ca', value: 'Dispositifs de mobilité - sans roue' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_monitors_and_electrodes', language: '@language_fr_ca', value: 'Moniteurs et électrodes' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_ophthalmic_equipment', language: '@language_fr_ca', value: 'Équipement ophthalmique' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_orthotics', language: '@language_fr_ca', value: Orthèses } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_patient_hoists', language: '@language_fr_ca', value: Lève-patients } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_patient_monitoring_equipment', language: '@language_fr_ca', value: 'Équipement de surveillance des patients' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_physiotherapy_equipment', language: '@language_fr_ca', value: 'Matériel de physiothérapie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_prostheses_external_limb', language: '@language_fr_ca', value: 'Prothèses - de membre externe' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_radiotherapy_equipment', language: '@language_fr_ca', value: 'Matériel de radiothérapie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_radionuclide_equipment', language: '@language_fr_ca', value: 'Équipement radionucléide' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_resuscitators', language: '@language_fr_ca', value: Réanimateurs } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_staples_and_staple_guns', language: '@language_fr_ca', value: 'Agrafes et pistolets à agrafes' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_stretchers', language: '@language_fr_ca', value: Civières } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_surgical_instruments', language: '@language_fr_ca', value: 'Instruments chirurgicaux' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_surgical_power_tools', language: '@language_fr_ca', value: 'Outils électriques chirurgicaux' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_sutures', language: '@language_fr_ca', value: Sutures } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_thermometers', language: '@language_fr_ca', value: Thermomètres } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_ultrasound_equipment', language: '@language_fr_ca', value: 'Matériel d''échographie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_urinary_catheters', language: '@language_fr_ca', value: 'Cathéters urinaires' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_ventilators', language: '@language_fr_ca', value: Ventilateurs } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_walking_sticks_frames', language: '@language_fr_ca', value: 'Béquilles et déambulateurs' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_wound_drains', language: '@language_fr_ca', value: 'Drains de plaie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_x_ray_equipment_systems_and_accessories', language: '@language_fr_ca', value: 'Matériel, systèmes et accessoires de radiographie' } }
  - { fields: { placeholder: '@devices_v2_form_option_device_type_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_healthcare_professional', language: '@language_fr_ca', value: 'Professionnel de santé' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_patient', language: '@language_fr_ca', value: Patient } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_other_caregiver', language: '@language_fr_ca', value: 'Autre soignant' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_none', language: '@language_fr_ca', value: Aucun } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_initial_use', language: '@language_fr_ca', value: 'Utilisation initiale' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_single_use_device', language: '@language_fr_ca', value: 'Réutilisation d''un dispositif à usage unique' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_reusable_device', language: '@language_fr_ca', value: 'Réutilisation d''un dispositif réutilisable' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_re_serviced_refurbished', language: '@language_fr_ca', value: 'Révisé ou remis à neuf' } }
  - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@devices_v2_form_message_delete_title', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@devices_v2_form_message_delete_content', language: '@language_fr_ca', value: 'Voulez-vous supprimer cet équipement de l''incident?' } }
  - { fields: { placeholder: '@devices_v2_form_button_delete_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@devices_v2_form_button_delete_ok', language: '@language_fr_ca', value: OK } }
  - { fields: { placeholder: '@devices_v2_form_message_cancel_title', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@devices_v2_form_message_cancel_content', language: '@language_fr_ca', value: 'Voulez-vous annuler vos modifications?' } }
  - { fields: { placeholder: '@devices_v2_form_button_cancel_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@devices_v2_form_button_cancel_ok', language: '@language_fr_ca', value: OK } }
  - { fields: { placeholder: '@devices_v2_form_message_successful_save', language: '@language_fr_ca', value: 'a été ajouté(e)' } }
  - { fields: { placeholder: '@devices_v2_form_message_successful_add', language: '@language_fr_ca', value: 'a été ajouté(e)' } }
  - { fields: { placeholder: '@devices_v2_form_message_successful_update', language: '@language_fr_ca', value: 'a été mis(e) à jour' } }
  - { fields: { placeholder: '@devices_v2_form_message_device_search_exact_matches_only', language: '@language_fr_ca', value: 'Correspondances exactes uniquement. Veuillez entrer deux caractères ou plus pour élargir votre recherche.' } }
  - { fields: { placeholder: '@devices_v2_form_message_device_search_no_matches', language: '@language_fr_ca', value: 'Aucun équipement correspondant trouvé' } }
  - { fields: { placeholder: '@devices_v2_form_message_device_search_matching_equipment', language: '@language_fr_ca', value: 'Équipement correspondant' } }
  - { fields: { placeholder: '@devices_v2_message_search_table_generic_name', language: '@language_fr_ca', value: 'Nom' } }
  - { fields: { placeholder: '@devices_v2_message_search_table_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@devices_v2_message_search_table_sub_type', language: '@language_fr_ca', value: Sous-type } }
  - { fields: { placeholder: '@devices_v2_message_search_table_brand', language: '@language_fr_ca', value: Marque } }
  - { fields: { placeholder: '@devices_v2_message_search_table_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@devices_v2_button_search_table_choose', language: '@language_fr_ca', value: Choisir } }
  - { fields: { placeholder: '@devices_successfully_saved', language: '@language_fr_ca', value: 'Équipement enregistré avec succès' } }
  - { fields: { placeholder: '@devices_successfully_deleted', language: '@language_fr_ca', value: 'Équipement supprimé avec succès' } }
  - { fields: { placeholder: '@devices_device_added_successfully', language: '@language_fr_ca', value: 'Équipement ajouté avec succès' } }
  - { fields: { placeholder: '@devices_device_removed_successfully', language: '@language_fr_ca', value: 'Équipement retiré avec succès' } }
  - { fields: { placeholder: '@devices_risk_added', language: '@language_fr_ca', value: 'Équipement ajouté au risque' } }
  - { fields: { placeholder: '@devices_risk_remove', language: '@language_fr_ca', value: 'Équipement supprimé du risque' } }
  - { fields: { placeholder: '@devices_new_equipment', language: '@language_fr_ca', value: 'Nouvel équipement' } }
