entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@devices_columns_device', language: '@language_ar', value: 'جهاز' } }
    - { fields: { placeholder: '@devices_columns_id', language: '@language_ar', value: 'هوية شخصية' } }
    - { fields: { placeholder: '@devices_create', language: '@language_ar', value: 'إنشاء المعدات' } }
    - { fields: { placeholder: '@devices_edit', language: '@language_ar', value: 'تحرير المعدات' } }
    - { fields: { placeholder: '@devices_form_approval_status', language: '@language_ar', value: 'حالة القبول' } }
    - { fields: { placeholder: '@devices_form_batch_no', language: '@language_ar', value: 'رقم الحزمة.' } }
    - { fields: { placeholder: '@devices_form_catalogue_number', language: '@language_ar', value: 'رقم الكتالوج' } }
    - { fields: { placeholder: '@devices_form_category', language: '@language_ar', value: 'الفئة' } }
    - { fields: { placeholder: '@devices_form_ce_marking', language: '@language_ar', value: 'علامة CE' } }
    - { fields: { placeholder: '@devices_form_date_of_last_service', language: '@language_ar', value: 'تاريخ آخر خدمة' } }
    - { fields: { placeholder: '@devices_form_date_of_manufacture', language: '@language_ar', value: 'تاريخ التصنيع' } }
    - { fields: { placeholder: '@devices_form_date_of_next_service', language: '@language_ar', value: 'تاريخ الخدمة القادمة' } }
    - { fields: { placeholder: '@devices_form_date_put_in_use', language: '@language_ar', value: 'تاريخ وضع في الاستخدام' } }
    - { fields: { placeholder: '@devices_form_description', language: '@language_ar', value: 'وصف' } }
    - { fields: { placeholder: '@devices_form_manufacturer', language: '@language_ar', value: 'الصانع' } }
    - { fields: { placeholder: '@devices_form_model', language: '@language_ar', value: 'نموذج' } }
    - { fields: { placeholder: '@devices_form_name', language: '@language_ar', value: 'الإسم' } }
    - { fields: { placeholder: '@devices_form_quantity', language: '@language_ar', value: 'كمية' } }
    - { fields: { placeholder: '@devices_form_ref', language: '@language_ar', value: 'المرجع' } }
    - { fields: { placeholder: '@devices_form_serial_no', language: '@language_ar', value: 'الرقم التسلسلي.' } }
    - { fields: { placeholder: '@devices_form_supplier', language: '@language_ar', value: 'المورد' } }
    - { fields: { placeholder: '@devices_plural', language: '@language_ar', value: 'الرجعية' } }
    - { fields: { placeholder: '@devices_search', language: '@language_ar', value: 'معدات البحث' } }
    - { fields: { placeholder: '@devices_select_category', language: '@language_ar', value: 'اختر الفئة' } }
    - { fields: { placeholder: '@devices_select_manufacturer', language: '@language_ar', value: 'حدد الشركة المصنعة' } }
    - { fields: { placeholder: '@devices_select_model', language: '@language_ar', value: 'اختر الموديل' } }
    - { fields: { placeholder: '@devices_select_status', language: '@language_ar', value: 'اختر الحالة' } }
    - { fields: { placeholder: '@devices_select_supplier', language: '@language_ar', value: 'اختر المورد' } }
    - { fields: { placeholder: '@devices_select_type', language: '@language_ar', value: 'اختر صنف' } }
    - { fields: { placeholder: '@devices_singular', language: '@language_ar', value: 'الرجعية' } }
    - { fields: { placeholder: '@devices_status_approved', language: '@language_ar', value: 'موافق عليه' } }
    - { fields: { placeholder: '@devices_status_unapproved', language: '@language_ar', value: 'غير مموافق عليه عليها' } }
    - { fields: { placeholder: '@devices_v2_form_message_successful_update', language: '@language_ar', value: 'تم تحديث المعدات' } }
    - { fields: { placeholder: '@devices_v2_form_message_successful_save', language: '@language_ar', value: 'تمت الإضافة' } }
    - { fields: { placeholder: '@devices_v2_form_message_successful_add', language: '@language_ar', value: 'تمت إضافة' } }
    - { fields: { placeholder: '@devices_categories_1', language: '@language_ar', value: 'الفئة 1' } }
    - { fields: { placeholder: '@devices_categories_2', language: '@language_ar', value: 'الفئة 2' } }
    - { fields: { placeholder: '@devices_categories_3', language: '@language_ar', value: 'الفئة 3' } }
    - { fields: { placeholder: '@devices_data_source_categories', language: '@language_ar', value: 'فئات الجهاز' } }
    - { fields: { placeholder: '@devices_data_source_categories_items_item1', language: '@language_ar', value: 'الفئة 1' } }
    - { fields: { placeholder: '@devices_data_source_categories_items_item2', language: '@language_ar', value: 'الفئة 2' } }
    - { fields: { placeholder: '@devices_data_source_categories_items_item3', language: '@language_ar', value: 'الفئة 3' } }
    - { fields: { placeholder: '@devices_data_source_manufacturers', language: '@language_ar', value: 'مصنعي الأجهزة' } }
    - { fields: { placeholder: '@devices_data_source_manufacturers_items_item1', language: '@language_ar', value: 'الصانع 1' } }
    - { fields: { placeholder: '@devices_data_source_manufacturers_items_item2', language: '@language_ar', value: 'الصانع 2' } }
    - { fields: { placeholder: '@devices_data_source_manufacturers_items_item3', language: '@language_ar', value: 'الصانع 3' } }
    - { fields: { placeholder: '@devices_data_source_models', language: '@language_ar', value: 'نماذج الجهاز' } }
    - { fields: { placeholder: '@devices_data_source_models_items_item1', language: '@language_ar', value: 'نماذج 1' } }
    - { fields: { placeholder: '@devices_data_source_models_items_item2', language: '@language_ar', value: 'نماذج 2' } }
    - { fields: { placeholder: '@devices_data_source_models_items_item3', language: '@language_ar', value: 'نماذج 3' } }
    - { fields: { placeholder: '@devices_data_source_statuses', language: '@language_ar', value: 'حالات الجهاز' } }
    - { fields: { placeholder: '@devices_data_source_suppliers', language: '@language_ar', value: 'موردو الأجهزة' } }
    - { fields: { placeholder: '@devices_data_source_suppliers_items_item1', language: '@language_ar', value: 'الموردون 1' } }
    - { fields: { placeholder: '@devices_data_source_suppliers_items_item2', language: '@language_ar', value: 'الموردين 2' } }
    - { fields: { placeholder: '@devices_data_source_suppliers_items_item3', language: '@language_ar', value: 'الموردين 3' } }
    - { fields: { placeholder: '@devices_data_source_types', language: '@language_ar', value: 'أنواع الأجهزة' } }
    - { fields: { placeholder: '@devices_data_source_types_items_item1', language: '@language_ar', value: 'أنواع 1' } }
    - { fields: { placeholder: '@devices_data_source_types_items_item2', language: '@language_ar', value: 'أنواع 2' } }
    - { fields: { placeholder: '@devices_datasource_device_categories', language: '@language_ar', value: 'فئات الجهاز' } }
    - { fields: { placeholder: '@devices_datasource_device_manufacturers', language: '@language_ar', value: 'مصنعي الأجهزة' } }
    - { fields: { placeholder: '@devices_datasource_device_models', language: '@language_ar', value: 'نماذج الجهاز' } }
    - { fields: { placeholder: '@devices_datasource_device_statuses', language: '@language_ar', value: 'حالات الجهاز' } }
    - { fields: { placeholder: '@devices_datasource_device_suppliers', language: '@language_ar', value: 'موردو الأجهزة' } }
    - { fields: { placeholder: '@devices_datasource_device_types', language: '@language_ar', value: 'أنواع الأجهزة' } }
    - { fields: { placeholder: '@devices_form_approval_status_label', language: '@language_ar', value: 'حالة الموافقة' } }
    - { fields: { placeholder: '@devices_form_batch_no_label', language: '@language_ar', value: 'رقم الدفعة' } }
    - { fields: { placeholder: '@devices_form_catalogue_number_label', language: '@language_ar', value: 'رقم الكتالوج' } }
    - { fields: { placeholder: '@devices_form_category_label', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@devices_form_ce_marking_label', language: '@language_ar', value: 'علامة CE' } }
    - { fields: { placeholder: '@devices_form_date_of_last_service_label', language: '@language_ar', value: 'تاريخ آخر خدمة' } }
    - { fields: { placeholder: '@devices_form_date_of_manufacture_label', language: '@language_ar', value: 'تاريخ الصنع' } }
    - { fields: { placeholder: '@devices_form_date_of_next_service_label', language: '@language_ar', value: 'تاريخ الخدمة التالية' } }
    - { fields: { placeholder: '@devices_form_date_put_in_use_label', language: '@language_ar', value: 'تاريخ وضع في الاستخدام' } }
    - { fields: { placeholder: '@devices_form_description_label', language: '@language_ar', value: الوصف } }
    - { fields: { placeholder: '@devices_form_label', language: '@language_ar', value: الإسم } }
    - { fields: { placeholder: '@devices_form_manufacturer_label', language: '@language_ar', value: 'الشركة المصنعة' } }
    - { fields: { placeholder: '@devices_form_quantity_label', language: '@language_ar', value: كمية } }
    - { fields: { placeholder: '@devices_form_ref_label', language: '@language_ar', value: المرجع } }
    - { fields: { placeholder: '@devices_form_serial_no_label', language: '@language_ar', value: 'الرقم التسلسلي' } }
    - { fields: { placeholder: '@devices_form_supplier_label', language: '@language_ar', value: المورد } }
    - { fields: { placeholder: '@devices_manufacturer_1', language: '@language_ar', value: 'الصانع 1' } }
    - { fields: { placeholder: '@devices_manufacturer_2', language: '@language_ar', value: 'الصانع 2' } }
    - { fields: { placeholder: '@devices_manufacturer_3', language: '@language_ar', value: 'الصانع 3' } }
    - { fields: { placeholder: '@devices_models_1', language: '@language_ar', value: 'نموذج 1' } }
    - { fields: { placeholder: '@devices_models_2', language: '@language_ar', value: 'نموذج 2' } }
    - { fields: { placeholder: '@devices_models_3', language: '@language_ar', value: 'نموذج 3' } }
    - { fields: { placeholder: '@devices_risk_added', language: '@language_ar', value: 'المعدات المضافة إلى المخاطر' } }
    - { fields: { placeholder: '@devices_risk_remove', language: '@language_ar', value: 'المعدات التي تم إزالتها من المخاطر' } }
    - { fields: { placeholder: '@devices_status_rejected', language: '@language_ar', value: مرفوض } }
    - { fields: { placeholder: '@devices_successfully_deleted', language: '@language_ar', value: 'تم حذف المعدات بنجاح' } }
    - { fields: { placeholder: '@devices_successfully_saved', language: '@language_ar', value: 'تم حفظ المعدات بنجاح' } }
    - { fields: { placeholder: '@devices_suppliers_1', language: '@language_ar', value: 'المورد 1' } }
    - { fields: { placeholder: '@devices_suppliers_2', language: '@language_ar', value: 'المورد 2' } }
    - { fields: { placeholder: '@devices_suppliers_3', language: '@language_ar', value: 'المورد 3' } }
    - { fields: { placeholder: '@devices_type_form', language: '@language_ar', value: 'نموذج المعدات' } }
    - { fields: { placeholder: '@devices_types_1', language: '@language_ar', value: 'اكتب 1' } }
    - { fields: { placeholder: '@devices_types_2', language: '@language_ar', value: 'اكتب 2' } }
    - { fields: { placeholder: '@devices_v2_button_search_table_choose', language: '@language_ar', value: إختيار } }
    - { fields: { placeholder: '@devices_v2_display_button_delete', language: '@language_ar', value: حذف } }
    - { fields: { placeholder: '@devices_v2_display_button_edit_equipment', language: '@language_ar', value: 'تحرير المعدات' } }
    - { fields: { placeholder: '@devices_v2_display_heading_equipment', language: '@language_ar', value: معدات } }
    - { fields: { placeholder: '@devices_v2_display_label_batch_number', language: '@language_ar', value: 'رقم الدفعة' } }
    - { fields: { placeholder: '@devices_v2_display_label_brand_name', language: '@language_ar', value: 'إسم العلامة التجارية' } }
    - { fields: { placeholder: '@devices_v2_display_label_catalogue_number', language: '@language_ar', value: 'رقم الكتالوج' } }
    - { fields: { placeholder: '@devices_v2_display_label_current_device_location', language: '@language_ar', value: 'الموقع الحالي للجهاز' } }
    - { fields: { placeholder: '@devices_v2_display_label_date_manufactured', language: '@language_ar', value: 'تاريخ الصنع' } }
    - { fields: { placeholder: '@devices_v2_display_label_device_operator', language: '@language_ar', value: 'مشغل الجهاز في وقت الحدث' } }
    - { fields: { placeholder: '@devices_v2_display_label_device_type', language: '@language_ar', value: 'نوع الجهاز' } }
    - { fields: { placeholder: '@devices_v2_display_label_device_usage', language: '@language_ar', value: 'إستخدام الجهاز' } }
    - { fields: { placeholder: '@devices_v2_display_label_expiry_date', language: '@language_ar', value: 'تاريخ إنتهاء الصلاحية' } }
    - { fields: { placeholder: '@devices_v2_display_label_manufacturer', language: '@language_ar', value: 'الشركة المصنعة' } }
    - { fields: { placeholder: '@devices_v2_display_label_model', language: '@language_ar', value: نموذج } }
    - { fields: { placeholder: '@devices_v2_display_label_product_name', language: '@language_ar', value: 'إسم المنتج' } }
    - { fields: { placeholder: '@devices_v2_display_label_quantity_used', language: '@language_ar', value: 'الكمية المستخدمة' } }
    - { fields: { placeholder: '@devices_v2_display_label_serial_number', language: '@language_ar', value: 'الرقم التسلسلي' } }
    - { fields: { placeholder: '@devices_v2_display_label_sub_type', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@devices_v2_display_label_supplier', language: '@language_ar', value: 'المورد ' } }
    - { fields: { placeholder: '@devices_v2_display_label_supply_category', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@devices_v2_display_label_supply_classification', language: '@language_ar', value: تصنيف } }
    - { fields: { placeholder: '@devices_v2_display_label_type', language: '@language_ar', value: النوع } }
    - { fields: { placeholder: '@devices_v2_form_button_cancel_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@devices_v2_form_button_cancel_ok', language: '@language_ar', value: حسنا } }
    - { fields: { placeholder: '@devices_v2_form_button_clear', language: '@language_ar', value: واضح } }
    - { fields: { placeholder: '@devices_v2_form_button_delete_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@devices_v2_form_button_delete_ok', language: '@language_ar', value: حسنا } }
    - { fields: { placeholder: '@devices_v2_form_button_sub_type', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@devices_v2_form_label_batch_number', language: '@language_ar', value: 'رقم الدفعة' } }
    - { fields: { placeholder: '@devices_v2_form_label_brand_name', language: '@language_ar', value: 'إسم العلامة التجارية' } }
    - { fields: { placeholder: '@devices_v2_form_label_catalogue_number', language: '@language_ar', value: 'رقم الكتالوج' } }
    - { fields: { placeholder: '@devices_v2_form_label_current_device_location', language: '@language_ar', value: 'الموقع الحالي للجهاز' } }
    - { fields: { placeholder: '@devices_v2_form_label_date_manufactured', language: '@language_ar', value: 'تاريخ الصنع' } }
    - { fields: { placeholder: '@devices_v2_form_label_device_operator', language: '@language_ar', value: 'المشغل أو العامل' } }
    - { fields: { placeholder: '@devices_v2_form_label_device_type', language: '@language_ar', value: 'نوع الجهاز' } }
    - { fields: { placeholder: '@devices_v2_form_label_device_usage', language: '@language_ar', value: 'إستخدام الجهاز' } }
    - { fields: { placeholder: '@devices_v2_form_label_expiry_date', language: '@language_ar', value: 'تاريخ الإنتهاء' } }
    - { fields: { placeholder: '@devices_v2_form_label_manufacturer', language: '@language_ar', value: الصانع } }
    - { fields: { placeholder: '@devices_v2_form_label_model', language: '@language_ar', value: نموذج } }
    - { fields: { placeholder: '@devices_v2_form_label_product_name', language: '@language_ar', value: 'إسم المنتج' } }
    - { fields: { placeholder: '@devices_v2_form_label_quantity_used', language: '@language_ar', value: 'الكمية المستخدمة' } }
    - { fields: { placeholder: '@devices_v2_form_label_search', language: '@language_ar', value: بحث } }
    - { fields: { placeholder: '@devices_v2_form_label_serial_number', language: '@language_ar', value: 'الرقم التسلسلي' } }
    - { fields: { placeholder: '@devices_v2_form_label_supplier', language: '@language_ar', value: المورد } }
    - { fields: { placeholder: '@devices_v2_form_label_supply_category', language: '@language_ar', value: الفئة } }
    - { fields: { placeholder: '@devices_v2_form_label_supply_classification', language: '@language_ar', value: تصنيف } }
    - { fields: { placeholder: '@devices_v2_form_label_type', language: '@language_ar', value: النوع } }
    - { fields: { placeholder: '@devices_v2_form_message_brand_name_no_matches_found', language: '@language_ar', value: 'لم يتم العثور على تطابق' } }
    - { fields: { placeholder: '@devices_v2_form_message_cancel_content', language: '@language_ar', value: 'هل تريد تجاهل تغييراتك؟' } }
    - { fields: { placeholder: '@devices_v2_form_message_cancel_title', language: '@language_ar', value: 'هل أنت متأكد؟' } }
    - { fields: { placeholder: '@devices_v2_form_message_delete_content', language: '@language_ar', value: 'هل تريد حذف هذا الجهاز من الحدث؟' } }
    - { fields: { placeholder: '@devices_v2_form_message_delete_title', language: '@language_ar', value: 'هل أنت متأكد؟' } }
    - { fields: { placeholder: '@devices_v2_form_message_device_search_exact_matches_only', language: '@language_ar', value: 'تطابق تام فقط. يرجى إدخال حرفين أو أكثر لتوسيع نطاق بحثك.' } }
    - { fields: { placeholder: '@devices_v2_form_message_device_search_matching_equipment', language: '@language_ar', value: 'مطابقة المعدات' } }
    - { fields: { placeholder: '@devices_v2_form_message_device_search_no_matches', language: '@language_ar', value: 'لم يتم العثور على معدات مطابقة' } }
    - { fields: { placeholder: '@devices_v2_form_message_successful_save', language: '@language_ar', value: 'تمت الإضافة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_administration_and_giving_sets', language: '@language_ar', value: 'الإدارة وإعطاء مجموعات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_anaesthetic_and_breathing_masks', language: '@language_ar', value: 'أقنعة التخدير والتنفس' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_anaesthetic_machines_and_monitors', language: '@language_ar', value: 'آلات التخدير والشاشات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_autoclaves', language: '@language_ar', value: 'أجهزة التعقيم بالحرارة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_bath_aids', language: '@language_ar', value: 'مساعدات الحمام' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_beds_and_mattresses', language: '@language_ar', value: 'سرير وفرشات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_blood_pressure_measurement', language: '@language_ar', value: 'قياس ضغط الدم' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_commodes', language: '@language_ar', value: القصرية } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_contact_lenses_and_care_products', language: '@language_ar', value: 'العدسات اللاصقة ومنتجات العناية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_ct_systems', language: '@language_ar', value: 'أنظمة التصوير المقطعي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_dental_appliances', language: '@language_ar', value: 'أجهزة طب الأسنان' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_dental_materials', language: '@language_ar', value: 'مواد طب الأسنان' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_dialysis_equipment', language: '@language_ar', value: 'معدات غسيل الكلى' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_diathermy_equipment_and_accessories', language: '@language_ar', value: 'معدات العلاج بالإنفاذ الحراري وملحقاته' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_dressings', language: '@language_ar', value: الضمادات } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_endoscopes_and_accessories', language: '@language_ar', value: 'المناظير والاكسسوارات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_endotracheal_tubes_and_airways', language: '@language_ar', value: 'أنابيب القصبة الهوائية والممرات الهوائية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_external_defibrillators', language: '@language_ar', value: 'الرجفان الخارجي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_external_pacemakers', language: '@language_ar', value: 'أجهزة تنظيم ضربات القلب الخارجية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_feeding_systems_enteral', language: '@language_ar', value: 'أنظمة التغذية - معوية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_feeding_tubes', language: '@language_ar', value: 'أنابيب التغذية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_gloves', language: '@language_ar', value: قفازات } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_guidewires', language: '@language_ar', value: 'دليل الأسلاك' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_hearing_aids', language: '@language_ar', value: 'مساعدات للسمع' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_heart_lung_bypass_machine', language: '@language_ar', value: ' جهاز القلبية الرئوية الإلتفافي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_hypodermic_syringes_and_needles', language: '@language_ar', value: 'المحاقن تحت الجلد والإبر' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implant_materials', language: '@language_ar', value: 'مواد الزرع' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_active_general', language: '@language_ar', value: 'زرع - فعال (عام)' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_breast', language: '@language_ar', value: 'زرع - الثدي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_cardiovascular', language: '@language_ar', value: 'زرع - القلب والأوعية الدموية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_hip_and_knee', language: '@language_ar', value: 'زرع - الورك والركبة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_non_active', language: '@language_ar', value: 'زرع - غير نشط' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_implants_pacemakers_defibrillators_and_leads', language: '@language_ar', value: 'زرع - أجهزة تنظيم ضربات القلب ، أجهزة تنظيم ضربات القلب ويؤدي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_in_vitro_medical_devices', language: '@language_ar', value: 'في الأجهزة الطبية المختبرية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_infant_incubators', language: '@language_ar', value: 'حاضنات الرضع' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_infusion_pumps_syringe_drivers', language: '@language_ar', value: 'مضخات التسريب ، و نواقل الحقن' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_insulin_syringes', language: '@language_ar', value: 'محاقن الأنسولين' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_intravenous_catheters_and_cannulae', language: '@language_ar', value: 'القسطرة الوريدية والقنية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_laryngoscopes', language: '@language_ar', value: 'مناظير الحنجرة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_lasers_and_accessories', language: '@language_ar', value: 'الليزر والاكسسوارات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_magnetic_resonance_equipment_and_accessories', language: '@language_ar', value: 'معدات الرنين المغناطيسي والاكسسوارات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_mobile_x_ray_systems', language: '@language_ar', value: 'أنظمة الأشعة السينية المتنقلة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_mobility_devices_non_wheeled', language: '@language_ar', value: 'أجهزة التنقل - بدون عجلات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_mobility_devices_wheeled_seating_aids_and_accessories', language: '@language_ar', value: 'أجهزة التنقل - ذات العجلات ، وأدوات الجلوس والملحقات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_monitors_and_electrodes', language: '@language_ar', value: 'الشاشات والأقطاب الكهربائية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_ophthalmic_equipment', language: '@language_ar', value: 'معدات طب العيون' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_orthotics', language: '@language_ar', value: 'تقويم العظام' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_patient_hoists', language: '@language_ar', value: 'رافعات المريض' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_patient_monitoring_equipment', language: '@language_ar', value: 'معدات مراقبة المريض' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_physiotherapy_equipment', language: '@language_ar', value: 'معدات العلاج الطبيعي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_prostheses_external_limb', language: '@language_ar', value: 'الأطراف الاصطناعية - الطرف الخارجي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_radionuclide_equipment', language: '@language_ar', value: 'معدات النويدات المشعة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_radiotherapy_equipment', language: '@language_ar', value: 'معدات العلاج الإشعاعي' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_resuscitators', language: '@language_ar', value: 'أجهزة تنفس' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_staples_and_staple_guns', language: '@language_ar', value: 'المواد الأساسية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_stretchers', language: '@language_ar', value: نقالات } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_surgical_instruments', language: '@language_ar', value: 'الأدوات الجراحية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_surgical_power_tools', language: '@language_ar', value: 'أدوات القوة الجراحية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_sutures', language: '@language_ar', value: الغرز } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_thermometers', language: '@language_ar', value: 'ميزان الحرارة' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_ultrasound_equipment', language: '@language_ar', value: 'معدات الموجات فوق الصوتية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_urinary_catheters', language: '@language_ar', value: 'القسطرة البولية' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_ventilators', language: '@language_ar', value: مُنَفِّسَة } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_walking_sticks_frames', language: '@language_ar', value: 'المشي العصي / إطارات' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_wound_drains', language: '@language_ar', value: 'مَنْزَح الجرح' } }
    - { fields: { placeholder: '@devices_v2_form_option_device_type_x_ray_equipment_systems_and_accessories', language: '@language_ar', value: 'أجهزة وأنظمة الأشعة السينية' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_healthcare_professional', language: '@language_ar', value: 'الرعاية الصحية المهنية' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_none', language: '@language_ar', value: 'لا شيء' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_other_caregiver', language: '@language_ar', value: 'مقدمي الرعاية الآخرين' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_operator_patient', language: '@language_ar', value: المريض } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_initial_use', language: '@language_ar', value: 'الاستخدام الأولي' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_other', language: '@language_ar', value: آخر } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_re_serviced_refurbished', language: '@language_ar', value: 'إعادة الخدمات / تجديد' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_reusable_device', language: '@language_ar', value: 'إعادة استخدام الجهاز القابل لإعادة الاستخدام' } }
    - { fields: { placeholder: '@devices_v2_form_option_equipment_usage_reuse_of_single_use_device', language: '@language_ar', value: 'إعادة استخدام الجهاز للاستخدام مرة واحدة' } }
    - { fields: { placeholder: '@devices_v2_form_placeholder_brand_name', language: '@language_ar', value: 'ابدأ في كتابة الاسم التجاري للمعدات' } }
    - { fields: { placeholder: '@devices_v2_form_placeholder_date_manufactured_format', language: '@language_ar', value: 'يوم / شهر / سنة' } }
    - { fields: { placeholder: '@devices_v2_form_placeholder_expiry_date_format', language: '@language_ar', value: 'يوم / شهر / سنة' } }
    - { fields: { placeholder: '@devices_v2_form_placeholder_select_one', language: '@language_ar', value: 'إختيار واحد' } }
    - { fields: { placeholder: '@devices_v2_form_subheading_equipment', language: '@language_ar', value: المعدات } }
    - { fields: { placeholder: '@devices_v2_message_search_table_brand', language: '@language_ar', value: 'العلامة التجارية' } }
    - { fields: { placeholder: '@devices_v2_message_search_table_generic_name', language: '@language_ar', value: 'الإسم' } }
    - { fields: { placeholder: '@devices_v2_message_search_table_manufacturer', language: '@language_ar', value: الصانع } }
    - { fields: { placeholder: '@devices_v2_message_search_table_sub_type', language: '@language_ar', value: 'النوع الفرعي' } }
    - { fields: { placeholder: '@devices_new_equipment', language: '@language_ar', value: 'إضافة معدات جديدة' } }
    - { fields: { placeholder: '@devices_v2_message_search_table_type', language: '@language_ar', value: 'النوع' } }
