entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: DEVICES.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_singular
  -
    fields:
      placeholder: DEVICES.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_plural
  -
    fields:
      placeholder: DEVICES.SEARCH
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_search
  -
    fields:
      placeholder: DEVICES.CREATE
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_create
  -
    fields:
      placeholder: DEVICES.EDIT
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_edit
  -
    fields:
      placeholder: DEVICES.COLUMNS.ID
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_columns_id
  -
    fields:
      placeholder: DEVICES.COLUMNS.DEVICE
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_columns_device
  -
    fields:
      placeholder: DEVICES.FORM.NAME
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
        -
          domain: '@domain_safety_alerts'
    ref: devices_form_name
  -
    fields:
      placeholder: DEVICES.FORM.LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_label
  -
    fields:
      placeholder: DEVICES.FORM.APPROVAL_STATUS
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
        -
          domain: '@domain_safety_alerts'
    ref: devices_form_approval_status
  -
    fields:
      placeholder: DEVICES.FORM.APPROVAL_STATUS_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_approval_status_label
  -
    fields:
      placeholder: DEVICES.FORM.MODEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_model
  -
    fields:
      placeholder: DEVICES.FORM.MANUFACTURER
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
        -
          domain: '@domain_safety_alerts'
    ref: devices_form_manufacturer
  -
    fields:
      placeholder: DEVICES.FORM.MANUFACTURER_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_manufacturer_label
  -
    fields:
      placeholder: DEVICES.FORM.SUPPLIER
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_supplier
  -
    fields:
      placeholder: DEVICES.FORM.SUPPLIER_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_supplier_label
  -
    fields:
      placeholder: DEVICES.FORM.CATEGORY
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_category
  -
    fields:
      placeholder: DEVICES.FORM.CATEGORY_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_category_label
  -
    fields:
      placeholder: DEVICES.FORM.REF
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_ref
  -
    fields:
      placeholder: DEVICES.FORM.REF_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_ref_label
  -
    fields:
      placeholder: DEVICES.FORM.BATCH_NO
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_batch_no
  -
    fields:
      placeholder: DEVICES.FORM.BATCH_NO_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_batch_no_label
  -
    fields:
      placeholder: DEVICES.FORM.SERIAL_NO
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_serial_no
  -
    fields:
      placeholder: DEVICES.FORM.SERIAL_NO_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_serial_no_label
  -
    fields:
      placeholder: DEVICES.FORM.CE_MARKING
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_ce_marking
  -
    fields:
      placeholder: DEVICES.FORM.CE_MARKING_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_ce_marking_label
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_MANUFACTURE
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_manufacture
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_MANUFACTURE_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_manufacture_label
  -
    fields:
      placeholder: DEVICES.FORM.DATE_PUT_IN_USE
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_put_in_use
  -
    fields:
      placeholder: DEVICES.FORM.DATE_PUT_IN_USE_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_put_in_use_label
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_LAST_SERVICE
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_last_service
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_LAST_SERVICE_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_last_service_label
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_NEXT_SERVICE
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_next_service
  -
    fields:
      placeholder: DEVICES.FORM.DATE_OF_NEXT_SERVICE_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_date_of_next_service_label
  -
    fields:
      placeholder: DEVICES.FORM.DESCRIPTION
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_description
  -
    fields:
      placeholder: DEVICES.FORM.DESCRIPTION_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_description_label
  -
    fields:
      placeholder: DEVICES.FORM.CATALOGUE_NUMBER
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_catalogue_number

  -
    fields:
      placeholder: DEVICES.FORM.CATALOGUE_NUMBER_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_catalogue_number_label
  -
    fields:
      placeholder: DEVICES.FORM.QUANTITY
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_quantity
  -
    fields:
      placeholder: DEVICES.FORM.QUANTITY_LABEL
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_field_maintenance'
    ref: devices_form_quantity_label
  -
    fields:
      placeholder: DEVICES.SELECT.STATUS
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_status
  -
    fields:
      placeholder: DEVICES.SELECT.TYPE
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_type
  -
    fields:
      placeholder: DEVICES.SELECT.MODEL
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_model
  -
    fields:
      placeholder: DEVICES.SELECT.MANUFACTURER
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_manufacturer
  -
    fields:
      placeholder: DEVICES.SELECT.SUPPLIER
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_supplier
  -
    fields:
      placeholder: DEVICES.SELECT.CATEGORY
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_select_category
  -
    fields:
      placeholder: DEVICES.STATUS.APPROVED
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_status_approved
  -
    fields:
      placeholder: DEVICES.STATUS.UNAPPROVED
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_status_unapproved
  -
    fields:
      placeholder: DEVICES.STATUS.REJECTED
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_status_rejected
  # From Types
  -
    fields:
      placeholder: DEVICES.TYPES.FORM
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
        -
          domain: '@domain_safety_alerts'
    ref: devices_type_form
  # Data Sources
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.CATEGORIES
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_categories
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MANUFACTURERS
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_manufacturers
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MODELS
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_models
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.STATUSES
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_statuses
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.SUPPLIERS
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_suppliers
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.TYPES
      type: 0
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_types
  # Data Source Items (Categories)
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.CATEGORIES.ITEMS.ITEM1
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_categories_items_item1
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.CATEGORIES.ITEMS.ITEM2
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_categories_items_item2
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.CATEGORIES.ITEMS.ITEM3
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_categories_items_item3
  # data Source Items (Manufacturers)
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MANUFACTURERS.ITEMS.ITEM1
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_manufacturers_items_item1
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MANUFACTURERS.ITEMS.ITEM2
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_manufacturers_items_item2
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MANUFACTURERS.ITEMS.ITEM3
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_manufacturers_items_item3
  # data Source Items (Models)
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MODELS.ITEMS.ITEM1
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_models_items_item1
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MODELS.ITEMS.ITEM2
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_models_items_item2
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.MODELS.ITEMS.ITEM3
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_models_items_item3
  # data Source Items (Suppliers)
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.SUPPLIERS.ITEMS.ITEM1
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_suppliers_items_item1
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.SUPPLIERS.ITEMS.ITEM2
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_suppliers_items_item2
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.SUPPLIERS.ITEMS.ITEM3
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_suppliers_items_item3
  # data Source Items (Types)
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.TYPES.ITEMS.ITEM1
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_types_items_item1
  -
    fields:
      placeholder: DEVICES.DATA_SOURCE.TYPES.ITEMS.ITEM2
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_data_source_types_items_item2

  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_CATEGORIES
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_categories
  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_MANUFACTURERS
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_manufacturers
  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_MODELS
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_models
  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_STATUSES
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_statuses
  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_SUPPLIERS
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_suppliers
  -
    fields:
      placeholder: DEVICES.DATASOURCE.DEVICE_TYPES
      type: 1
      domains:
        -
          domain: '@domain_devices'
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_enterprise_risk_manager'
    ref: devices_datasource_device_types
  -
    fields:
      placeholder: DEVICES.SUPPLIERS.SUPPLIER_1
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_suppliers_1
  -
    fields:
      placeholder: DEVICES.SUPPLIERS.SUPPLIER_2
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_suppliers_2
  -
    fields:
      placeholder: DEVICES.SUPPLIERS.SUPPLIER_3
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_suppliers_3
  -
    fields:
      placeholder: DEVICES.MODELS.MODEL_1
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_models_1
  -
    fields:
      placeholder: DEVICES.MODELS.MODEL_2
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_models_2
  -
    fields:
      placeholder: DEVICES.MODELS.MODEL_3
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_models_3
  -
    fields:
      placeholder: DEVICES.MANUFACTURERS.MANUFACTURER_1
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_manufacturer_1
  -
    fields:
      placeholder: DEVICES.MANUFACTURERS.MANUFACTURER_2
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_manufacturer_2
  -
    fields:
      placeholder: DEVICES.MANUFACTURERS.MANUFACTURER_3
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_manufacturer_3
  -
    fields:
      placeholder: DEVICES.CATEGORIES.CATEGORY_1
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_categories_1
  -
    fields:
      placeholder: DEVICES.CATEGORIES.CATEGORY_2
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_categories_2
  -
    fields:
      placeholder: DEVICES.CATEGORIES.CATEGORY_3
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_categories_3
  -
    fields:
      placeholder: DEVICES.TYPES.TYPE_1
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_types_1
  -
    fields:
      placeholder: DEVICES.TYPES.TYPE_2
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: devices_types_2
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.SEARCH
      type: 1
      domains:
      - domain: '@domain_devices'
    ref: devices_v2_form_label_search
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.EXPIRY_DATE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_expiry_date
  -
    fields:
      placeholder: DEVICES.V2.FORM.PLACEHOLDER.EXPIRY_DATE_FORMAT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_placeholder_expiry_date_format
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.DATE_MANUFACTURED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_date_manufactured
  -
    fields:
      placeholder: DEVICES.V2.FORM.PLACEHOLDER.DATE_MANUFACTURED_FORMAT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_placeholder_date_manufactured_format
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.BRAND_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_brand_name
  -
    fields:
      placeholder: DEVICES.V2.FORM.PLACEHOLDER.BRAND_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_placeholder_brand_name
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.BRAND_NAME_NO_MATCHES_FOUND
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_brand_name_no_matches_found
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.CLEAR
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_clear
  -
    fields:
      placeholder: DEVICES.V2.FORM.SUBHEADING.DEVICES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_subheading_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_type
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.SUB_TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_sub_type
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.PRODUCT_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_product_name
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.MANUFACTURER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_manufacturer
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.MODEL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_model
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.CATALOGUE_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_catalogue_number
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.BATCH_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_batch_number
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.QUANTITY_USED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_quantity_used
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.SERIAL_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_serial_number
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.SUPPLIER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_supplier
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.CURRENT_DEVICE_LOCATION
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_current_device_location
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.DEVICE_TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_device_type
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.DEVICE_OPERATOR
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_device_operator
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.DEVICE_USAGE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_device_usage
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.SUPPLY_CATEGORY
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_supply_category
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.SUPPLY_CLASSIFICATION
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_label_supply_classification
  -
    fields:
      placeholder: DEVICES.V2.FORM.PLACEHOLDER.SELECT_ONE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_placeholder_select_one
  -
    fields:
      placeholder: DEVICES.V2.FORM.LABEL.DEVICE_ID
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_placeholder_device_id
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.DEVICE_ID
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_placeholder_device_id
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.HEADING.DEVICES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_heading_equipment
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.BUTTON.EDIT_DEVICES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_button_edit_equipment
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.BUTTON.DELETE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_button_delete
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.BRAND_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_brand_name
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_type
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.SUB_TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_sub_type
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.PRODUCT_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_product_name
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.MANUFACTURER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_manufacturer
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.MODEL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_model
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.CATALOGUE_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_catalogue_number
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.BATCH_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_batch_number
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.QUANTITY_USED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_quantity_used
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.EXPIRY_DATE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_expiry_date
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.DATE_MANUFACTURED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_date_manufactured
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.SERIAL_NUMBER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_serial_number
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.SUPPLIER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_supplier
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.CURRENT_DEVICE_LOCATION
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_current_device_location
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.DEVICE_TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_device_type
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.DEVICE_OPERATOR
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_device_operator
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.DEVICE_USAGE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_device_usage
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.SUPPLY_CATEGORY
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_supply_category
  -
    fields:
      placeholder: DEVICES.V2.DISPLAY.LABEL.SUPPLY_CLASSIFICATION
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_display_label_supply_classification
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ADMINISTRATION_AND_GIVING_SETS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_administration_and_giving_sets
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ANAESTHETIC_MACHINES_AND_MONITORS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_anaesthetic_machines_and_monitors
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ANAESTHETIC_AND_BREATHING_MASKS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_anaesthetic_and_breathing_masks
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.AUTOCLAVES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_autoclaves
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BATH_AIDS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_bath_aids
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BEDS_AND_MATTRESSES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_beds_and_mattresses
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.BLOOD_PRESSURE_MEASUREMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_blood_pressure_measurement
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.COMMODES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_commodes
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.CONTACT_LENSES_AND_CARE_PRODUCTS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_contact_lenses_and_care_products
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.CT_SYSTEMS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_ct_systems
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DENTAL_APPLIANCES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_dental_appliances
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DENTAL_MATERIALS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_dental_materials
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DIALYSIS_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_dialysis_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DIATHERMY_EQUIPMENT_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_diathermy_equipment_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.DRESSINGS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_dressings
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ENDOSCOPES_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_endoscopes_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ENDOTRACHEAL_TUBES_AND_AIRWAYS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_endotracheal_tubes_and_airways
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.EXTERNAL_DEFIBRILLATORS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_external_defibrillators
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.EXTERNAL_PACEMAKERS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_external_pacemakers
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.FEEDING_SYSTEMS_ENTERAL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_feeding_systems_enteral
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.FEEDING_TUBES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_feeding_tubes
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.GLOVES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_gloves
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.GUIDEWIRES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_guidewires
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HEARING_AIDS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_hearing_aids
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HEART_LUNG_BYPASS_MACHINE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_heart_lung_bypass_machine
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.HYPODERMIC_SYRINGES_AND_NEEDLES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_hypodermic_syringes_and_needles
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_ACTIVE_GENERAL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_active_general
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_BREAST
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_breast
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_CARDIOVASCULAR
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_cardiovascular
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_HIP_AND_KNEE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_hip_and_knee
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_NON_ACTIVE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_non_active
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANTS_PACEMAKERS_DEFIBRILLATORS_AND_LEADS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implants_pacemakers_defibrillators_and_leads
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IMPLANT_MATERIALS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_implant_materials
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.IN_VITRO_MEDICAL_DEVICES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_in_vitro_medical_devices
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INFANT_INCUBATORS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_infant_incubators
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INFUSION_PUMPS_SYRINGE_DRIVERS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_infusion_pumps_syringe_drivers
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INSULIN_SYRINGES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_insulin_syringes
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.INTRAVENOUS_CATHETERS_AND_CANNULAE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_intravenous_catheters_and_cannulae
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.LARYNGOSCOPES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_laryngoscopes
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.LASERS_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_lasers_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MAGNETIC_RESONANCE_EQUIPMENT_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_magnetic_resonance_equipment_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILE_X_RAY_SYSTEMS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_mobile_x_ray_systems
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILITY_DEVICES_WHEELED_SEATING_AIDS_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_mobility_devices_wheeled_seating_aids_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MOBILITY_DEVICES_NON_WHEELED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_mobility_devices_non_wheeled
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.MONITORS_AND_ELECTRODES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_monitors_and_electrodes
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.OPHTHALMIC_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_ophthalmic_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ORTHOTICS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_orthotics
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PATIENT_HOISTS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_patient_hoists
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PATIENT_MONITORING_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_patient_monitoring_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PHYSIOTHERAPY_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_physiotherapy_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.PROSTHESES_EXTERNAL_LIMB
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_prostheses_external_limb
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RADIOTHERAPY_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_radiotherapy_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RADIONUCLIDE_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_radionuclide_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.RESUSCITATORS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_resuscitators
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.STAPLES_AND_STAPLE_GUNS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_staples_and_staple_guns
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.STRETCHERS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_stretchers
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SURGICAL_INSTRUMENTS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_surgical_instruments
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SURGICAL_POWER_TOOLS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_surgical_power_tools
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.SUTURES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_sutures
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.THERMOMETERS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_thermometers
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.ULTRASOUND_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_ultrasound_equipment
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.URINARY_CATHETERS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_urinary_catheters
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.VENTILATORS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_ventilators
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.WALKING_STICKS_FRAMES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_walking_sticks_frames
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.WOUND_DRAINS
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_wound_drains
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.X_RAY_EQUIPMENT_SYSTEMS_AND_ACCESSORIES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_x_ray_equipment_systems_and_accessories
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.DEVICE_TYPE.OTHER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_device_type_other
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.HEALTHCARE_PROFESSIONAL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_operator_healthcare_professional
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.PATIENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_operator_patient
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.OTHER_CAREGIVER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_operator_other_caregiver
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_OPERATOR.NONE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_operator_none
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.INITIAL_USE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_usage_initial_use
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.REUSE_OF_SINGLE_USE_DEVICE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_usage_reuse_of_single_use_device
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.REUSE_OF_REUSABLE_DEVICE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_usage_reuse_of_reusable_device
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.RE_SERVICED_REFURBISHED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_usage_re_serviced_refurbished
  -
    fields:
      placeholder: DEVICES.V2.FORM.OPTION.EQUIPMENT_USAGE.OTHER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_option_equipment_usage_other
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.DELETE.TITLE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_delete_title
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.DELETE.CONTENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_delete_content
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.DELETE.CANCEL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_delete_cancel
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.DELETE.OK
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_delete_ok
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.CANCEL.TITLE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_cancel_title
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.CANCEL.CONTENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_cancel_content
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.CANCEL.CANCEL
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_cancel_cancel
  -
    fields:
      placeholder: DEVICES.V2.FORM.BUTTON.CANCEL.OK
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_button_cancel_ok
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.SUCCESSFUL_SAVE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_successful_save
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.SUCCESSFUL_ADD
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_successful_add
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.SUCCESSFUL_UPDATE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_successful_update
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.DEVICE_SEARCH.EXACT_MATCHES_ONLY
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_device_search_exact_matches_only
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.DEVICE_SEARCH.NO_MATCHES
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_device_search_no_matches
  -
    fields:
      placeholder: DEVICES.V2.FORM.MESSAGE.DEVICE_SEARCH.MATCHING_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_form_message_device_search_matching_equipment
  -
    fields:
      placeholder: DEVICES.V2.MESSAGE.SEARCH_TABLE.GENERIC_NAME
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_message_search_table_generic_name
  -
    fields:
      placeholder: DEVICES.V2.MESSAGE.SEARCH_TABLE.TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_safety_alerts'
    ref: devices_v2_message_search_table_type
  -
    fields:
      placeholder: DEVICES.V2.MESSAGE.SEARCH_TABLE.SUB_TYPE
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_safety_alerts'
    ref: devices_v2_message_search_table_sub_type
  -
    fields:
      placeholder: DEVICES.V2.MESSAGE.SEARCH_TABLE.BRAND
      type: 1
      domains:
        - domain: '@domain_devices'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_safety_alerts'
    ref: devices_v2_message_search_table_brand
  -
    fields:
      placeholder: DEVICES.V2.MESSAGE.SEARCH_TABLE.MANUFACTURER
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_message_search_table_manufacturer
  -
    fields:
      placeholder: DEVICES.V2.BUTTON.SEARCH_TABLE.CHOOSE
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_v2_button_search_table_choose
  -
    fields:
      placeholder: DEVICES.SUCCESSFULLY_SAVED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_successfully_saved
  -
    fields:
      placeholder: DEVICES.SUCCESSFULLY_DELETED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_successfully_deleted
  -
    fields:
      placeholder: DEVICES.DEVICE_ADDED_SUCCESSFULLY
      type: 1
      domains:
        - domain: '@domain_common'
    ref: devices_device_added_successfully
  -
    fields:
      placeholder: DEVICES.DEVICE_REMOVED_SUCCESSFULLY
      type: 1
      domains:
        - domain: '@domain_common'
    ref: devices_device_removed_successfully
  - fields:
      placeholder: DEVICES.RISK_ADDED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_risk_added
  - fields:
      placeholder: DEVICES.RISK_REMOVED
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_risk_remove
  - fields:
      placeholder: DEVICES.NEW_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_devices'
    ref: devices_new_equipment
