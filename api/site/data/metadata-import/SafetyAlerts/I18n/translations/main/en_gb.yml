entityClass: I18n\Entity\Translation
priority: 15
data:
  - fields:
      placeholder: '@safety_alerts_singular'
      language: '@language_en_gb'
      value: Safety Alert
  # Dashboard
  - fields:
      placeholder: '@safety_alerts_dashboard_open'
      language: '@language_en_gb'
      value: Open
  - fields:
      placeholder: '@safety_alerts_dashboard_closed'
      language: '@language_en_gb'
      value: Closed
  - fields:
      placeholder: '@safety_alerts_dashboard_requires_response'
      language: '@language_en_gb'
      value: Requires Response
  # Filters
  - fields:
      placeholder: '@safety_alerts_filters_all'
      language: '@language_en_gb'
      value: 'All'
  - fields:
      placeholder: '@safety_alerts_filters_sent'
      language: '@language_en_gb'
      value: 'Sent'
  - fields:
      placeholder: '@safety_alerts_filters_not_sent'
      language: '@language_en_gb'
      value: 'Not Sent'
  - fields:
      placeholder: '@safety_alerts_filters_not_read'
      language: '@language_en_gb'
      value: 'Sent But Not Read'
  - fields:
      placeholder: '@safety_alerts_filters_no_response'
      language: '@language_en_gb'
      value: 'Read But Not Responded'
  - fields:
      placeholder: '@acl_safety_alerts_distribute'
      language: '@language_en_gb'
      value: Distribute
  - fields:
      placeholder: '@safety_alerts_nav'
      language: '@language_en_gb'
      value: Safety Alerts
  - fields:
      placeholder: '@safety_alerts'
      language: '@language_en_gb'
      value: Safety Alerts
  # Navigation
  - fields:
      placeholder: '@safety_alerts_nav_new'
      language: '@language_en_gb'
      value: Create New Alert
  - fields:
      placeholder: '@safety_alerts_nav_details'
      language: '@language_en_gb'
      value: Details
  - fields:
      placeholder: '@safety_alerts_nav_distribution'
      language: '@language_en_gb'
      value: Distribution
  - fields:
      placeholder: '@safety_alerts_nav_for_action'
      language: '@language_en_gb'
      value: For Action By
  - fields:
      placeholder: '@safety_alerts_nav_for_information'
      language: '@language_en_gb'
      value: For Information Only
  - fields:
      placeholder: '@safety_alerts_nav_responses'
      language: '@language_en_gb'
      value: Responses
  - fields:
      placeholder: '@safety_alerts_nav_actions'
      language: '@language_en_gb'
      value: Actions
  - fields:
      placeholder: '@safety_alerts_nav_my_actions'
      language: '@language_en_gb'
      value: 'My Actions'
  - fields:
      placeholder: '@safety_alerts_nav_all_actions'
      language: '@language_en_gb'
      value: 'All Actions'
  - fields:
      placeholder: '@safety_alerts_nav_safety_alert_number'
      language: '@language_en_gb'
      value: 'Safety Alert #{{id}}'
  # Distribution
  - fields:
      placeholder: '@safety_alerts_distribution_singular'
      language: '@language_en_gb'
      value: Distribution
  - fields:
      placeholder: '@safety_alerts_distribution_plural'
      language: '@language_en_gb'
      value: Distribution
  - fields:
      placeholder: '@safety_alerts_distribution_search'
      language: '@language_en_gb'
      value: Search Distribution
  - fields:
      placeholder: '@safety_alerts_distribution_create'
      language: '@language_en_gb'
      value: Create Distribution
  - fields:
      placeholder: '@safety_alerts_distribution_edit'
      language: '@language_en_gb'
      value: Edit Distribution
  # List Schema
  - fields:
      placeholder: '@safety_alerts_column_date_issued'
      language: '@language_en_gb'
      value: Date Issued
  - fields:
      placeholder: '@safety_alerts_column_deadline_action_underway'
      language: '@language_en_gb'
      value: 'Deadline (action underway)'
  - fields:
      placeholder: '@safety_alerts_column_distributed_date'
      language: '@language_en_gb'
      value: 'Date Distributed'
  - fields:
      placeholder: '@safety_alerts_column_read_date'
      language: '@language_en_gb'
      value: 'Date Read'
  - fields:
      placeholder: '@safety_alerts_column_date_responded'
      language: '@language_en_gb'
      value: 'Date Responded'
  - fields:
      placeholder: '@safety_alerts_column_response_type'
      language: '@language_en_gb'
      value: 'Response Type'
  - fields:
      placeholder: '@safety_alerts_column_response'
      language: '@language_en_gb'
      value: 'Response'
  - fields:
      placeholder: '@placeholder_safety_alerts_column_response_has_attachments'
      language: '@language_en_gb'
      value: 'Has Attachments?'
  - fields:
      placeholder: '@safety_alerts_column_deadline_action_complete'
      language: '@language_en_gb'
      value: 'Deadline (action complete)'
  - fields:
      placeholder: '@safety_alerts_distribution_columns_created'
      language: '@language_en_gb'
      value: Created Date
  # Notification and Errors
  - fields:
      placeholder: '@safety_alerts_notify_success'
      language: '@language_en_gb'
      value: 'Safety Alert has been created successfully'
  - fields:
      placeholder: '@safety_alerts_notify_failed'
      language: '@language_en_gb'
      value: 'Failed to create Safety Alert'
  - fields:
      placeholder: '@safety_alerts_notify_success_update'
      language: '@language_en_gb'
      value: 'Safety Alert has been updated'
  - fields:
      placeholder: '@safety_alerts_notify_failed_update'
      language: '@language_en_gb'
      value: 'Failed to update Safety Alert'
  - fields:
      placeholder: '@safety_alerts_notify_distributed'
      language: '@language_en_gb'
      value: 'Safety alert has been distributed successfully'
  - fields:
      placeholder: '@safety_alerts_notify_response_save'
      language: '@language_en_gb'
      value: 'Response has been saved'
  - fields:
      placeholder: '@safety_alerts_notify_needs_recipients'
      language: '@language_en_gb'
      value: 'Please select recipients for distribution'
  - fields:
      placeholder: '@safety_alerts_error_distribution_user_save'
      language: '@language_en_gb'
      value: 'Failed to save user, user may already been in the list'
  - fields:
      placeholder: '@safety_alerts_error_distribution_contact_save'
      language: '@language_en_gb'
      value: 'Failed to save contact, user may already been in the list'
  - fields:
      placeholder: '@safety_alerts_error_failed_to_distribute'
      language: '@language_en_gb'
      value: 'Failed to distribute Safety Alert'
  - fields:
      placeholder: '@safety_alerts_notify_users_added_only'
      language: '@language_en_gb'
      value: 'Only Datix users can be selected "for action by". Contacts in the selected distribution list have not been added.'
  # Forms
  - fields:
      placeholder: '@safety_alerts_form_type_safety_alerts'
      language: '@language_en_gb'
      value: Safety Alert Form
  - fields:
      placeholder: '@safety_alerts_form_1'
      language: '@language_en_gb'
      value: Main Form
  # Sections
  - fields:
      placeholder: '@safety_alerts_section_title_and_reference'
      language: '@language_en_gb'
      value: 'Title and Reference'
  - fields:
      placeholder: '@safety_alerts_section_alert_details'
      language: '@language_en_gb'
      value: 'Alert Details'
  - fields:
      placeholder: '@safety_alerts_section_action_details'
      language: '@language_en_gb'
      value: 'Action Details'
  - fields:
      placeholder: '@safety_alerts_section_deadlines'
      language: '@language_en_gb'
      value: 'Deadlines'
  - fields:
      placeholder: '@safety_alerts_section_locations_and_services'
      language: '@language_en_gb'
      value: 'Locations and Services'
  - fields:
      placeholder: '@safety_alerts_section_attachments'
      language: '@language_en_gb'
      value: 'Attachments'
  # Fields
  - fields:
      placeholder: '@safety_alerts_default_form_fields_title'
      language: '@language_en_gb'
      value: Title
  - fields:
      placeholder: '@safety_alerts_default_form_fields_title_label'
      language: '@language_en_gb'
      value: Title
  - fields:
      placeholder: '@safety_alerts_default_form_fields_reference'
      language: '@language_en_gb'
      value: Reference
  - fields:
      placeholder: '@safety_alerts_default_form_fields_reference_label'
      language: '@language_en_gb'
      value: Reference
  - fields:
      placeholder: '@safety_alerts_default_form_fields_handler'
      language: '@language_en_gb'
      value: Handler
  - fields:
      placeholder: '@safety_alerts_default_form_fields_handler_label'
      language: '@language_en_gb'
      value: Handler
  - fields:
      placeholder: '@safety_alerts_default_form_fields_source'
      language: '@language_en_gb'
      value: Source
  - fields:
      placeholder: '@safety_alerts_default_form_fields_source_label'
      language: '@language_en_gb'
      value: Source
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_issued'
      language: '@language_en_gb'
      value: Date Issued
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_issued_label'
      language: '@language_en_gb'
      value: Date Issued
  - fields:
      placeholder: '@safety_alerts_default_form_fields_opened_date'
      language: '@language_en_gb'
      value: Opened Date
  - fields:
      placeholder: '@safety_alerts_default_form_fields_opened_date_label'
      language: '@language_en_gb'
      value: Opened Date
  - fields:
      placeholder: '@safety_alerts_default_form_fields_closed_date'
      language: '@language_en_gb'
      value: Closed Date
  - fields:
      placeholder: '@safety_alerts_default_form_fields_closed_date_label'
      language: '@language_en_gb'
      value: Closed Date
  - fields:
      placeholder: '@safety_alerts_default_form_fields_type'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@safety_alerts_default_form_fields_type_label'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@safety_alerts_default_form_fields_description'
      language: '@language_en_gb'
      value: Description
  - fields:
      placeholder: '@safety_alerts_default_form_fields_description_label'
      language: '@language_en_gb'
      value: Description
  - fields:
      placeholder: '@safety_alerts_default_form_fields_action_type'
      language: '@language_en_gb'
      value: Action Type
  - fields:
      placeholder: '@safety_alerts_default_form_fields_action_type_label'
      language: '@language_en_gb'
      value: Action Type
  - fields:
      placeholder: '@safety_alerts_default_form_fields_action_description'
      language: '@language_en_gb'
      value: Action Description
  - fields:
      placeholder: '@safety_alerts_default_form_fields_action_description_label'
      language: '@language_en_gb'
      value: Action Description
  - fields:
      placeholder: '@safety_alerts_default_form_fields_deadline_action_underway'
      language: '@language_en_gb'
      value: 'Deadline (action underway)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_deadline_action_underway_label'
      language: '@language_en_gb'
      value: 'Deadline (action underway)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_deadline_action_complete'
      language: '@language_en_gb'
      value: 'Deadline (action complete)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_deadline_action_complete_label'
      language: '@language_en_gb'
      value: 'Deadline (action complete)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_action_underway'
      language: '@language_en_gb'
      value: 'Date (action underway)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_action_underway_label'
      language: '@language_en_gb'
      value: 'Date (action underway)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_action_complete'
      language: '@language_en_gb'
      value: 'Date (action complete)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_date_action_complete_label'
      language: '@language_en_gb'
      value: 'Date (action complete)'
  - fields:
      placeholder: '@safety_alerts_default_form_fields_locations'
      language: '@language_en_gb'
      value: Locations
  - fields:
      placeholder: '@safety_alerts_default_form_fields_locations_label'
      language: '@language_en_gb'
      value: Locations
  - fields:
      placeholder: '@safety_alerts_default_form_fields_services'
      language: '@language_en_gb'
      value: Services
  - fields:
      placeholder: '@safety_alerts_default_form_fields_services_label'
      language: '@language_en_gb'
      value: Services
  - fields:
      placeholder: '@safety_alerts_default_form_fields_attachments'
      language: '@language_en_gb'
      value: Safety Alert Attachments
  - fields:
      placeholder: '@safety_alerts_default_form_fields_attachments_label'
      language: '@language_en_gb'
      value: '' # Leave blank
  # Filter Form
  - fields:
      placeholder: '@safety_alerts_form_filter'
      language: '@language_en_gb'
      value: Filter Alerts
  - fields:
      placeholder: '@safety_alerts_form_type_filter'
      language: '@language_en_gb'
      value: Filter Form
  # Response required message
  - fields:
      placeholder: '@safety_alerts_message_response_required'
      language: '@language_en_gb'
      value: This Safety Alert requires your response

  # Response Form
  - fields:
      placeholder: '@safety_alerts_form_response'
      language: '@language_en_gb'
      value: Response
  - fields:
      placeholder: '@safety_alerts_form_type_response'
      language: '@language_en_gb'
      value: Safety Alert Response Form
  - fields:
      placeholder: '@safety_alerts_section_response'
      language: '@language_en_gb'
      value: Response
  - fields:
      placeholder: '@safety_alerts_response_form_fields_response_type'
      language: '@language_en_gb'
      value: Response Type
  - fields:
      placeholder: '@safety_alerts_response_form_fields_response_type_label'
      language: '@language_en_gb'
      value: Response Type
  - fields:
      placeholder: '@safety_alerts_response_form_fields_response'
      language: '@language_en_gb'
      value: Response
  - fields:
      placeholder: '@safety_alerts_response_form_fields_response_label'
      language: '@language_en_gb'
      value: Response
  - fields:
      placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_title'
      language: '@language_en_gb'
      value: Safety Alert Response Attachments
  - fields:
      placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_label'
      language: '@language_en_gb'
      value: Attachments

  # Datasource Item
  # Type
  - fields:
      placeholder: '@safety_alerts_form_field_type_value_medical_devices_alert'
      language: '@language_en_gb'
      value: Medical devices alert
  - fields:
      placeholder: '@safety_alerts_form_field_type_value_medication_alert'
      language: '@language_en_gb'
      value: Medication alert
  - fields:
      placeholder: '@safety_alerts_form_field_type_value_patient_safety_alert'
      language: '@language_en_gb'
      value: Patient safety alert
  - fields:
      placeholder: '@safety_alerts_form_field_type_value_estates_alert'
      language: '@language_en_gb'
      value: Estates alert
  - fields:
      placeholder: '@safety_alerts_form_field_type_value_supply_disruption_alert'
      language: '@language_en_gb'
      value: Supply disruption alert
  # Action Type
  - fields:
      placeholder: '@safety_alerts_form_field_action_type_value_action_required'
      language: '@language_en_gb'
      value: Action required
  - fields:
      placeholder: '@safety_alerts_form_field_action_type_value_immediate_action_required'
      language: '@language_en_gb'
      value: Immediate action required
  - fields:
      placeholder: '@safety_alerts_form_field_action_type_value_no_action_required'
      language: '@language_en_gb'
      value: No action required
  # Source
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_other'
      language: '@language_en_gb'
      value: Other
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_nhs_england'
      language: '@language_en_gb'
      value: NHS England
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_nhs_digital'
      language: '@language_en_gb'
      value: NHS Digital
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_nhs_improvement_estates_and_facilities'
      language: '@language_en_gb'
      value: NHS Improvement Estates and Facilities
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_nhs_improvement'
      language: '@language_en_gb'
      value: NHS Improvement
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_mhra_medical_device_alerts'
      language: '@language_en_gb'
      value: MHRA Medical Device Alerts
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_mhra_drug_alerts'
      language: '@language_en_gb'
      value: MHRA Drug Alerts
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_cmo_messaging'
      language: '@language_en_gb'
      value: CMO Messaging
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_mhra_dear_doctor_letter'
      language: '@language_en_gb'
      value: MHRA Dear Doctor Letter
  - fields:
      placeholder: '@safety_alerts_form_field_source_value_dhsc_supply_disruption_alert'
      language: '@language_en_gb'
      value: DHSC Supply Disruption Alert
  # Response Type
  - fields:
      placeholder: '@safety_alerts_form_field_response_type_value_no_action_required'
      language: '@language_en_gb'
      value: No action required
  - fields:
      placeholder: '@safety_alerts_form_field_response_type_value_action_underway'
      language: '@language_en_gb'
      value: Action underway
  - fields:
      placeholder: '@safety_alerts_form_field_response_type_value_action_complete'
      language: '@language_en_gb'
      value: Action complete
  # Distribution tabs
  - fields:
      placeholder: '@safety_alerts_alert_sent_status'
      language: '@language_en_gb'
      value: Status
  - fields:
      placeholder: '@safety_alerts_sent_safety_alert'
      language: '@language_en_gb'
      value: Sent
  - fields:
      placeholder: '@safety_alerts_distribution_users'
      language: '@language_en_gb'
      value: Users
  - fields:
      placeholder: '@safety_alerts_distribution_contacts'
      language: '@language_en_gb'
      value: Distribution Contacts
  - fields:
      placeholder: '@safety_alerts_distribution_distribute_alert'
      language: '@language_en_gb'
      value: 'E-mail Selected Recipients ({{selectedRecipients}})'
  - fields:
      placeholder: '@safety_alerts_distribution_distribute_alert_all'
      language: '@language_en_gb'
      value: E-mail All Recipients
  - fields:
      placeholder: '@safety_alerts_add_distribution'
      language: '@language_en_gb'
      value: Add Distribution Lists
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_message_meta'
      language: '@language_en_gb'
      value: '{{author}} on {{date}}'
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_new_message_placeholder'
      language: '@language_en_gb'
      value: If you have a query for the recipient, you can enter a message here.
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_recipient_message_placeholder'
      language: '@language_en_gb'
      value: If you have a query regarding the safety alert, you can enter a message for the distribution team here.
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_no_messages'
      language: '@language_en_gb'
      value: There are currently no queries
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_new_message_label'
      language: '@language_en_gb'
      value: Message body
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_new_save'
      language: '@language_en_gb'
      value: Submit Query
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_save_success'
      language: '@language_en_gb'
      value: Query saved successfully
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_save_error'
      language: '@language_en_gb'
      value: An error occurred whilst saving the query
  - fields:
      placeholder: '@placeholder_safety_alerts_messages_message_not_provided'
      language: '@language_en_gb'
      value: A query must be provided
  - fields:
      placeholder: '@placeholder_safety_alerts_response_distribution_path_title'
      language: '@language_en_gb'
      value: Distribution Path
  - fields:
      placeholder: '@placeholder_safety_alerts_response_response_title'
      language: '@language_en_gb'
      value: Response
  - fields:
      placeholder: '@placeholder_safety_alerts_response_no_response'
      language: '@language_en_gb'
      value: User has not responded
  - fields:
      placeholder: '@placeholder_safety_alerts_response_messages_title'
      language: '@language_en_gb'
      value: Queries
  - fields:
      placeholder: '@safety_alerts_response_form_fields_queries_title'
      language: '@language_en_gb'
      value: Queries
  - fields:
      placeholder: '@safety_alerts_response_form_fields_queries_label'
      language: '@language_en_gb'
      value: Queries
  - fields:
      placeholder: '@safety_alerts_equipments_uuid'
      language: '@language_en_gb'
      value: 'A valid Equipment UUID must be provided'
  - fields:
      placeholder: '@safety_alerts_incidents_link_note'
      language: '@language_en_gb'
      value: 'Link Note'
  - fields:
      placeholder: '@safety_alerts_incidents_link_success'
      language: '@language_en_gb'
      value: Incident linked successfully
  - fields:
      placeholder: '@safety_alerts_incidents_unlink_success'
      language: '@language_en_gb'
      value: Incident removed successfully
  - fields:
      placeholder: '@safety_alerts_incidents_link_error'
      language: '@language_en_gb'
      value: An error occurred whilst linking the Incident
  - fields:
      placeholder: '@safety_alerts_incidents_invalid_incident_id'
      language: '@language_en_gb'
      value: An integer Incident ID must be provided
  - fields:
      placeholder: '@safety_alerts_incidents_already_linked'
      language: '@language_en_gb'
      value: The selected Incident is already linked to this Safety Alert
  - fields:
      placeholder: '@safety_alerts_source_no_options'
      language: '@language_en_gb'
      value: No Source options available
  - fields:
      placeholder: '@safety_alerts_source_label'
      language: '@language_en_gb'
      value: Source
  - fields:
      placeholder: '@safety_alerts_type_no_options'
      language: '@language_en_gb'
      value: No Type options available
  - fields:
      placeholder: '@safety_alerts_type_label'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@safety_alerts_form_delete_warning'
      language: '@language_en_gb'
      value: Deleting a Safety Alert is irreversible. Please ensure that no linked data will be impacted by this deletion. If you are happy to proceed, please confirm.
  - fields:
      placeholder: '@safety_alerts_form_delete_success'
      language: '@language_en_gb'
      value: Safety Alert deleted successfully
  - fields:
      placeholder: '@safety_alerts_form_delete_error'
      language: '@language_en_gb'
      value: Unable to delete Safety Alert
