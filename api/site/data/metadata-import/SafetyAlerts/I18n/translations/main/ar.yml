entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@safety_alerts_singular', language: '@language_ar', value: 'تنبيه السلامة' } }
  - { fields: { placeholder: '@acl_safety_alerts_distribute', language: '@language_ar', value: التوزيع } }
  # Dashboard
  - { fields: { placeholder: '@safety_alerts_dashboard_closed', language: '@language_ar', value: مغلق } }
  - { fields: { placeholder: '@safety_alerts_dashboard_open', language: '@language_ar', value: فتح } }
  - { fields: { placeholder: '@safety_alerts_dashboard_requires_response', language: '@language_ar', value: 'يتطلب الرد' } }
  # Filters
  - { fields: { placeholder: '@safety_alerts_filters_all', language: '@language_ar', value: الكل } }
  - { fields: { placeholder: '@safety_alerts_filters_no_response', language: '@language_ar', value: 'تمت القراءة ولكن لم يتم الرد' } }
  - { fields: { placeholder: '@safety_alerts_filters_not_read', language: '@language_ar', value: 'أرسلت ولكن لم تتم القراءة' } }
  - { fields: { placeholder: '@safety_alerts_filters_not_sent', language: '@language_ar', value: 'لم ترسل' } }
  - { fields: { placeholder: '@safety_alerts_filters_sent', language: '@language_ar', value: أرسلت } }
  # Navigation
  - { fields: { placeholder: '@safety_alerts', language: '@language_ar', value: 'تنبيهات السلامة' } }
  - { fields: { placeholder: '@safety_alerts_nav', language: '@language_ar', value: 'تنبيهات السلامة' } }
  - { fields: { placeholder: '@safety_alerts_nav_details', language: '@language_ar', value: التفاصيل } }
  - { fields: { placeholder: '@safety_alerts_nav_distribution', language: '@language_ar', value: التوزيع } }
  - { fields: { placeholder: '@safety_alerts_nav_for_action', language: '@language_ar', value: 'للإجراء من قبل' } }
  - { fields: { placeholder: '@safety_alerts_nav_for_information', language: '@language_ar', value: 'للعلم فقط' } }
  - { fields: { placeholder: '@safety_alerts_nav_new', language: '@language_ar', value: 'إنشاء تنبيه جديد' } }
  - { fields: { placeholder: '@safety_alerts_nav_responses', language: '@language_ar', value: الإستجابات } }
  - { fields: { placeholder: '@safety_alerts_nav_actions', language: '@language_ar', value: أفعال } }
  - { fields: { placeholder: '@safety_alerts_nav_my_actions', language: '@language_ar', value: أفعالي } }
  - { fields: { placeholder: '@safety_alerts_nav_all_actions', language: '@language_ar', value: 'جميع الإجراءات' } }
  # Distribution
  - { fields: { placeholder: '@safety_alerts_distribution_create', language: '@language_ar', value: 'إنشاء التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_distribution_edit', language: '@language_ar', value: 'تعديل التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_distribution_plural', language: '@language_ar', value: التوزيع } }
  - { fields: { placeholder: '@safety_alerts_distribution_search', language: '@language_ar', value: ' البحث في التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_distribution_singular', language: '@language_ar', value: التوزيع } }
  - { fields: { placeholder: '@safety_alerts_distribution_contacts', language: '@language_ar', value: 'جهات إتصالات التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_distribution_distribute_alert', language: '@language_ar', value: 'المستلمون المختارون عبر البريد الإلكتروني ({{selectRecipients}})' } }
  - { fields: { placeholder: '@safety_alerts_distribution_distribute_alert_all', language: '@language_ar', value: 'البريد الإلكتروني لجميع المستلمين' } }
  - { fields: { placeholder: '@safety_alerts_distribution_users', language: '@language_ar', value: المستخدمين } }
  - { fields: { placeholder: '@safety_alerts_distribution_columns_created', language: '@language_ar', value: 'تاريخ الإنشاء' } }
  - { fields: { placeholder: '@safety_alerts_add_distribution', language: '@language_ar', value: 'إضافة قوائم التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_alert_sent_status', language: '@language_ar', value: الحالة } }
  - { fields: { placeholder: '@safety_alerts_sent_safety_alert', language: '@language_ar', value: أرسلت } }
  # List Schema
  - { fields: { placeholder: '@safety_alerts_column_date_issued', language: '@language_ar', value: 'تاريخ الإصدار' } }
  - { fields: { placeholder: '@safety_alerts_column_date_responded', language: '@language_ar', value: 'تاريخ الرد' } }
  - { fields: { placeholder: '@safety_alerts_column_deadline_action_complete', language: '@language_ar', value: 'الموعد النهائي (إكتمال الإجراء)' } }
  - { fields: { placeholder: '@safety_alerts_column_deadline_action_underway', language: '@language_ar', value: 'الموعد النهائي (الإجراء قيد العمل)' } }
  - { fields: { placeholder: '@safety_alerts_column_distributed_date', language: '@language_ar', value: 'تاريخ التوزيع' } }
  - { fields: { placeholder: '@safety_alerts_column_read_date', language: '@language_ar', value: 'تاريخ القراءة' } }
  - { fields: { placeholder: '@safety_alerts_column_response', language: '@language_ar', value: الإستجابة } }
  - { fields: { placeholder: '@placeholder_safety_alerts_column_response_has_attachments', language: '@language_ar', value: 'لديه مرفقات؟' } }
  - { fields: { placeholder: '@safety_alerts_column_response_type', language: '@language_ar', value: 'نوع الإستجابة' } }
  # Notification and Errors
  - { fields: { placeholder: '@safety_alerts_notify_distributed', language: '@language_ar', value: 'تم توزيع تنبيه السلامة بنجاح' } }
  - { fields: { placeholder: '@safety_alerts_notify_failed', language: '@language_ar', value: 'فشل في إنشاء تنبيه الأمان' } }
  - { fields: { placeholder: '@safety_alerts_notify_failed_update', language: '@language_ar', value: 'إخفاق تحديث تنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_notify_needs_recipients', language: '@language_ar', value: 'يرجى إختيار المستلمين للتوزيع' } }
  - { fields: { placeholder: '@safety_alerts_notify_response_save', language: '@language_ar', value: 'تم حفظ الرد' } }
  - { fields: { placeholder: '@safety_alerts_notify_success', language: '@language_ar', value: 'تم إنشاء تنبيه السلامة بنجاح' } }
  - { fields: { placeholder: '@safety_alerts_notify_success_update', language: '@language_ar', value: 'تم تحديث تنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_notify_users_added_only', language: '@language_ar', value: 'يمكن تحديد مستخدمي Datix فقط \ "لإتخاذ الإجراء \". لم تتم إضافة جهات الإتصال في قائمة التوزيع المحددة.' } }
  - { fields: { placeholder: '@safety_alerts_error_distribution_contact_save', language: '@language_ar', value: 'إخفاق حفظ جهة الإتصال ، فقد يكون المستخدم بالفعل في القائمة' } }
  - { fields: { placeholder: '@safety_alerts_error_distribution_user_save', language: '@language_ar', value: 'إخفاق حفظ المستخدم ، فقد يكون المستخدم بالفعل في القائمة' } }
  - { fields: { placeholder: '@safety_alerts_error_failed_to_distribute', language: '@language_ar', value: 'إخفاق  توزيع تنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_message_response_required', language: '@language_ar', value: 'يتطلب تنبيه السلامة هذا ردكم' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_message_meta', language: '@language_ar', value: '{{author}} في {{date}}' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_message_not_provided', language: '@language_ar', value: 'يجب تقديم الإستعلام' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_message_label', language: '@language_ar', value: 'نص الرسالة' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_message_placeholder', language: '@language_ar', value: 'الرجاء إدخال رسالتك هنا' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_save', language: '@language_ar', value: 'إرسال الإستعلام' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_no_messages', language: '@language_ar', value: 'لا يوجد حاليا أي استفسارات' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_save_error', language: '@language_ar', value: 'حدث خطأ أثناء حفظ الإستعلام' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_save_success', language: '@language_ar', value: 'تم حفظ الإستعلام بنجاح' } }
  # Form Types
  - { fields: { placeholder: '@safety_alerts_form_type_filter', language: '@language_ar', value: 'نموذج التصفية' } }
  - { fields: { placeholder: '@safety_alerts_form_type_response', language: '@language_ar', value: 'نموذج إستجابة تنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_form_type_safety_alerts', language: '@language_ar', value: 'نموذج تنبيه السلامة' } }
  # Forms
  - { fields: { placeholder: '@safety_alerts_form_filter', language: '@language_ar', value: 'تنبيهات التصفية' } }
  - { fields: { placeholder: '@safety_alerts_form_1', language: '@language_ar', value: 'النموذج الرئيسي' } }
  - { fields: { placeholder: '@safety_alerts_form_response', language: '@language_ar', value: الإستجابة } }
  # Sections
  - { fields: { placeholder: '@safety_alerts_section_action_details', language: '@language_ar', value: 'تفاصيل الإجراء' } }
  - { fields: { placeholder: '@safety_alerts_section_alert_details', language: '@language_ar', value: 'تفاصيل التنبيه' } }
  - { fields: { placeholder: '@safety_alerts_section_attachments', language: '@language_ar', value: المرفقات } }
  - { fields: { placeholder: '@safety_alerts_section_deadlines', language: '@language_ar', value: 'المواعيد النهائية' } }
  - { fields: { placeholder: '@safety_alerts_section_locations_and_services', language: '@language_ar', value: 'المواقع والخدمات' } }
  - { fields: { placeholder: '@safety_alerts_section_response', language: '@language_ar', value: الإستجابة } }
  - { fields: { placeholder: '@safety_alerts_section_title_and_reference', language: '@language_ar', value: 'العنوان والمرجع' } }
  # Fields
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_description_label', language: '@language_ar', value: وصف الإجراء } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_description', language: '@language_ar', value: 'وصف الإجراء' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_type_label', language: '@language_ar', value: نوع الإجراء } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_type', language: '@language_ar', value: 'نوع الإجراء' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_attachments_label', language: '@language_ar', value: '' } } # Leave blank
  - { fields: { placeholder: '@safety_alerts_default_form_fields_attachments', language: '@language_ar', value: 'مرفقات تنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_closed_date_label', language: '@language_ar', value: تاريخ الإغلاق } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_closed_date', language: '@language_ar', value: 'تاريخ الإغلاق' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_complete_label', language: '@language_ar', value: التاريخ (إكتمل الإجراء) } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_complete', language: '@language_ar', value: 'التاريخ (اكتمال الإجراء)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_underway_label', language: '@language_ar', value: التاريخ (الإجراء قيد العمل) } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_underway', language: '@language_ar', value: 'التاريخ (الإجراء قيد العمل)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_issued_label', language: '@language_ar', value: تاريخ الإصدار } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_issued', language: '@language_ar', value: 'تاريخ الإصدار' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_complete_label', language: '@language_ar', value: الموعد النهائي (إكتمل الإجراء) } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_complete', language: '@language_ar', value: 'الموعد النهائي (اكتمل الإجراء)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_underway_label', language: '@language_ar', value: الموعد النهائي (الإجراء جار) } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_underway', language: '@language_ar', value: 'الموعد النهائي (الإجراء قيد العمل)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_description_label', language: '@language_ar', value: الوصف } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_description', language: '@language_ar', value: الوصف } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_handler_label', language: '@language_ar', value: المسؤول } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_handler', language: '@language_ar', value: المسؤول } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_locations_label', language: '@language_ar', value: المواقع } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_locations', language: '@language_ar', value: المواقع } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_opened_date_label', language: '@language_ar', value: تاريخ الفتح } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_opened_date', language: '@language_ar', value: 'تاريخ الفتح' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_reference_label', language: '@language_ar', value: المرجع } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_reference', language: '@language_ar', value: المرجع } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_services_label', language: '@language_ar', value: الخدمات } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_services', language: '@language_ar', value: الخدمات } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_source_label', language: '@language_ar', value: المصدر } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_source', language: '@language_ar', value: المصدر } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_title_label', language: '@language_ar', value: العنوان } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_title', language: '@language_ar', value: العنوان } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_type_label', language: '@language_ar', value: أكتب } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_type', language: '@language_ar', value: النوع } }
  # Response Form
  - { fields: { placeholder: '@module_title_safety_alerts_response', language: '@language_ar', value: 'الإستجابة لتنبيهات السلامة ' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_label', language: '@language_ar', value: المرفقات } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_title', language: '@language_ar', value: 'مرفقات الإستجابة لتنبيه السلامة' } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_type_label', language: '@language_ar', value: 'نوع الإستجابة' } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_type', language: '@language_ar', value: 'نوع الإستجابة' } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_label', language: '@language_ar', value: الإستجابة } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response', language: '@language_ar', value: الإستجابة } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_distribution_path_title', language: '@language_ar', value: 'مسار التوزيع' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_messages_title', language: '@language_ar', value: الإستفسارات } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_no_response', language: '@language_ar', value: 'المستخدم لم يستجب' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_response_title', language: '@language_ar', value: الإستجابة } }
  # Datasource Item
  # Type
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_estates_alert', language: '@language_ar', value: 'تنبيه الممتلكات' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_medical_devices_alert', language: '@language_ar', value: 'تنبيه الأجهزة الطبية' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_medication_alert', language: '@language_ar', value: 'تنبيه الدواء' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_patient_safety_alert', language: '@language_ar', value: 'تنبيه سلامة المرضى' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_supply_disruption_alert', language: '@language_ar', value: ' تنبيه إنقطاع الإمداد' } }
  # Action Type
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_action_required', language: '@language_ar', value: 'الإجراء مطلوب' } }
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_immediate_action_required', language: '@language_ar', value: 'إجراء فوري مطلوب' } }
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_no_action_required', language: '@language_ar', value: 'لا يوجد إجراء مطلوب' } }
  # Source
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_cmo_messaging', language: '@language_ar', value: 'CMO المراسلة' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_dhsc_supply_disruption_alert', language: '@language_ar', value: 'DHSC تنبيه إنقطاع الإمداد' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_dear_doctor_letter', language: '@language_ar', value: 'MHRA رسالة عزيزي الطبيب' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_drug_alerts', language: '@language_ar', value: 'MHRA تنبيهات الأدوية' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_medical_device_alerts', language: '@language_ar', value: 'MHRA تنبيهات الأجهزة الطبية' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_digital', language: '@language_ar', value: 'NHS الرقمية' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_england', language: '@language_ar', value: 'NHS إنجلترا' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_other', language: '@language_ar', value: 'آخر' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_improvement', language: '@language_ar', value: 'تحسين NHS' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_improvement_estates_and_facilities', language: '@language_ar', value: 'NHS تحسين الممتلكات والمرافق' } }
  # Response Type
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_action_complete', language: '@language_ar', value: 'إكتمال الإجراء' } }
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_action_underway', language: '@language_ar', value: 'الإجراء قيد العمل ' } }
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_no_action_required', language: '@language_ar', value: 'لا يوجد إجراء مطلوب' } }
