entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@safety_alerts_singular', language: '@language_fr_ca', value: 'Alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_dashboard_open', language: '@language_fr_ca', value: Ouvert } }
  - { fields: { placeholder: '@safety_alerts_dashboard_closed', language: '@language_fr_ca', value: <PERSON><PERSON><PERSON> } }
  - { fields: { placeholder: '@safety_alerts_dashboard_requires_response', language: '@language_fr_ca', value: 'Requiert une réponse' } }
  - { fields: { placeholder: '@acl_safety_alerts_distribute', language: '@language_fr_ca', value: Distribuer } }
  - { fields: { placeholder: '@safety_alerts_filters_all', language: '@language_fr_ca', value: Tous } }
  - { fields: { placeholder: '@safety_alerts_filters_sent', language: '@language_fr_ca', value: Envoyé } }
  - { fields: { placeholder: '@safety_alerts_filters_not_sent', language: '@language_fr_ca', value: 'Non envoyé' } }
  - { fields: { placeholder: '@safety_alerts_filters_not_read', language: '@language_fr_ca', value: 'Envoyé mais non lu' } }
  - { fields: { placeholder: '@safety_alerts_filters_no_response', language: '@language_fr_ca', value: 'Lu mais pas répondu' } }
  - { fields: { placeholder: '@safety_alerts_column_date_issued', language: '@language_fr_ca', value: 'Date de publication' } }
  - { fields: { placeholder: '@safety_alerts_column_deadline_action_underway', language: '@language_fr_ca', value: 'Date limite (action en cours)' } }
  - { fields: { placeholder: '@safety_alerts_column_deadline_action_complete', language: '@language_fr_ca', value: 'Date limite (achèvement de l''action)' } }
  - { fields: { placeholder: '@safety_alerts_column_distributed_date', language: '@language_fr_ca', value: 'Date de distribution' } }
  - { fields: { placeholder: '@safety_alerts_column_read_date', language: '@language_fr_ca', value: 'Date de lecture' } }
  - { fields: { placeholder: '@safety_alerts_column_date_responded', language: '@language_fr_ca', value: 'Date de réponse' } }
  - { fields: { placeholder: '@safety_alerts_column_response_type', language: '@language_fr_ca', value: 'Type de réponse' } }
  - { fields: { placeholder: '@safety_alerts_column_response', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@placeholder_safety_alerts_column_response_has_attachments', language: '@language_fr_ca', value: 'Contient des pièces jointes?' } }
  - { fields: { placeholder: '@safety_alerts_nav', language: '@language_fr_ca', value: 'Alertes de sécurité' } }
  - { fields: { placeholder: '@safety_alerts', language: '@language_fr_ca', value: 'Alertes de sécurité' } }
  - { fields: { placeholder: '@safety_alerts_nav_new', language: '@language_fr_ca', value: 'Créer une nouvelle alerte' } }
  - { fields: { placeholder: '@safety_alerts_nav_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@safety_alerts_nav_distribution', language: '@language_fr_ca', value: Distribution } }
  - { fields: { placeholder: '@safety_alerts_nav_for_action', language: '@language_fr_ca', value: 'Pour action par' } }
  - { fields: { placeholder: '@safety_alerts_nav_for_information', language: '@language_fr_ca', value: 'Pour information seulement' } }
  - { fields: { placeholder: '@safety_alerts_nav_responses', language: '@language_fr_ca', value: Réponses } }
  - { fields: { placeholder: '@safety_alerts_nav_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@safety_alerts_nav_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@safety_alerts_nav_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@safety_alerts_distribution_singular', language: '@language_fr_ca', value: Distribution } }
  - { fields: { placeholder: '@safety_alerts_distribution_plural', language: '@language_fr_ca', value: Distribution } }
  - { fields: { placeholder: '@safety_alerts_distribution_search', language: '@language_fr_ca', value: 'Recherche de distribution' } }
  - { fields: { placeholder: '@safety_alerts_distribution_create', language: '@language_fr_ca', value: 'Créer une distribution' } }
  - { fields: { placeholder: '@safety_alerts_distribution_edit', language: '@language_fr_ca', value: 'Modifier la distribution' } }
  - { fields: { placeholder: '@safety_alerts_form_1', language: '@language_fr_ca', value: 'Formulaire principal' } }
  - { fields: { placeholder: '@safety_alerts_form_type_safety_alerts', language: '@language_fr_ca', value: 'Formulaire d''alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_section_title_and_reference', language: '@language_fr_ca', value: 'Titre et référence' } }
  - { fields: { placeholder: '@safety_alerts_section_alert_details', language: '@language_fr_ca', value: 'Détails de l''alerte' } }
  - { fields: { placeholder: '@safety_alerts_section_action_details', language: '@language_fr_ca', value: 'Détails de l''action' } }
  - { fields: { placeholder: '@safety_alerts_section_deadlines', language: '@language_fr_ca', value: 'Dates limites' } }
  - { fields: { placeholder: '@safety_alerts_section_locations_and_services', language: '@language_fr_ca', value: 'Emplacements et services' } }
  - { fields: { placeholder: '@safety_alerts_section_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_reference', language: '@language_fr_ca', value: Référence } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_reference_label', language: '@language_fr_ca', value: Référence } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_handler', language: '@language_fr_ca', value: Responsable } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_handler_label', language: '@language_fr_ca', value: Responsable } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_source', language: '@language_fr_ca', value: Source } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_source_label', language: '@language_fr_ca', value: Source } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_issued', language: '@language_fr_ca', value: 'Date de publication' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_issued_label', language: '@language_fr_ca', value: 'Date de publication' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_opened_date', language: '@language_fr_ca', value: 'Date d''ouverture' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_opened_date_label', language: '@language_fr_ca', value: 'Date d''ouverture' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_closed_date', language: '@language_fr_ca', value: 'Date de fermeture' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_closed_date_label', language: '@language_fr_ca', value: 'Date de fermeture' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_type', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_type_label', language: '@language_fr_ca', value: 'Type d''action' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_description', language: '@language_fr_ca', value: 'Description de l''action' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_action_description_label', language: '@language_fr_ca', value: 'Description de l''action' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_underway', language: '@language_fr_ca', value: 'Date limite (action en cours)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_underway_label', language: '@language_fr_ca', value: 'Date limite (action en cours)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_complete', language: '@language_fr_ca', value: 'Date limite (achèvement de l''action)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_deadline_action_complete_label', language: '@language_fr_ca', value: 'Date limite (achèvement de l''action)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_underway', language: '@language_fr_ca', value: 'Date (action en cours)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_underway_label', language: '@language_fr_ca', value: 'Date (action en cours)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_complete', language: '@language_fr_ca', value: 'Date (action achevée)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_date_action_complete_label', language: '@language_fr_ca', value: 'Date (action achevée)' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_locations', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_locations_label', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_services_label', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_attachments', language: '@language_fr_ca', value: 'Pièces jointes de l''alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_default_form_fields_attachments_label', language: '@language_fr_ca', value: '' } }
  - { fields: { placeholder: '@safety_alerts_form_filter', language: '@language_fr_ca', value: 'Filtrer les alertes' } }
  - { fields: { placeholder: '@safety_alerts_form_type_filter', language: '@language_fr_ca', value: 'Formulaire de filtrage' } }
  - { fields: { placeholder: '@safety_alerts_message_response_required', language: '@language_fr_ca', value: 'Cette alerte à la sécurité nécessite votre réponse' } }
  - { fields: { placeholder: '@safety_alerts_form_response', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@safety_alerts_form_type_response', language: '@language_fr_ca', value: 'Formulaire de réponse à une alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_section_response', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_type', language: '@language_fr_ca', value: 'Type de réponse' } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_type_label', language: '@language_fr_ca', value: 'Type de réponse' } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_response_label', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_title', language: '@language_fr_ca', value: 'Pièces jointes de réponse à l''alerte à la sécurité' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_form_fields_attachments_label', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@safety_alerts_distribution_columns_created', language: '@language_fr_ca', value: 'Date de création' } }
  - { fields: { placeholder: '@safety_alerts_notify_success', language: '@language_fr_ca', value: 'L''alerte à la sécurité a été créée avec succès' } }
  - { fields: { placeholder: '@safety_alerts_notify_failed', language: '@language_fr_ca', value: 'Échec de création de l''alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_notify_success_update', language: '@language_fr_ca', value: 'L''alerte à la sécurité a été mise à jour' } }
  - { fields: { placeholder: '@safety_alerts_notify_failed_update', language: '@language_fr_ca', value: 'Échec de modification de l''alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_notify_distributed', language: '@language_fr_ca', value: 'L''alerte à la sécurité a été distribuée avec succès' } }
  - { fields: { placeholder: '@safety_alerts_notify_response_save', language: '@language_fr_ca', value: 'La réponse a été enregistrée' } }
  - { fields: { placeholder: '@safety_alerts_notify_needs_recipients', language: '@language_fr_ca', value: 'Veuillez sélectionner les destinataires pour la distribution' } }
  - { fields: { placeholder: '@safety_alerts_error_distribution_user_save', language: '@language_fr_ca', value: 'Échec d''enregistrement de l''utilisateur. Il figure peut-être déjà dans la liste' } }
  - { fields: { placeholder: '@safety_alerts_error_distribution_contact_save', language: '@language_fr_ca', value: 'Échec d''enregistrement du contact. L''utilisateur figurait peut-être déjà dans la liste' } }
  - { fields: { placeholder: '@safety_alerts_error_failed_to_distribute', language: '@language_fr_ca', value: 'Échec de distribution de l''alerte à la sécurité' } }
  - { fields: { placeholder: '@safety_alerts_notify_users_added_only', language: '@language_fr_ca', value: "Seuls les utilisateurs de Datix peuvent être sélectionnés «\_pour action par\_». Les personnes-ressources de la liste de distribution sélectionnée n'ont pas été ajoutées." } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_medical_devices_alert', language: '@language_fr_ca', value: 'Alerte relative aux dispositifs médicaux' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_medication_alert', language: '@language_fr_ca', value: 'Alerte relative à un médicament' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_patient_safety_alert', language: '@language_fr_ca', value: 'Alerte à la sécurité relative à un patient' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_estates_alert', language: '@language_fr_ca', value: 'Alerte relative aux biens' } }
  - { fields: { placeholder: '@safety_alerts_form_field_type_value_supply_disruption_alert', language: '@language_fr_ca', value: 'Alerte de rupture de fourniture' } }
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_action_required', language: '@language_fr_ca', value: 'Action requise' } }
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_immediate_action_required', language: '@language_fr_ca', value: 'Action immédiate requise' } }
  - { fields: { placeholder: '@safety_alerts_form_field_action_type_value_no_action_required', language: '@language_fr_ca', value: 'Aucune action requise' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_other', language: '@language_fr_ca', value: 'Autre' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_england', language: '@language_fr_ca', value: 'NHS Angleterre' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_digital', language: '@language_fr_ca', value: 'NHS numérique' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_improvement_estates_and_facilities', language: '@language_fr_ca', value: 'Amélioration NHS aux biens et établissements' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_nhs_improvement', language: '@language_fr_ca', value: 'Amélioration NHS' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_medical_device_alerts', language: '@language_fr_ca', value: 'Alertes du MHRA relatives à des dispositifs médicaux' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_drug_alerts', language: '@language_fr_ca', value: 'Alertes du MHRA relatives à des médicaments' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_cmo_messaging', language: '@language_fr_ca', value: 'Messagerie CMO' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_mhra_dear_doctor_letter', language: '@language_fr_ca', value: 'Lettre au distingué docteur du MHRA' } }
  - { fields: { placeholder: '@safety_alerts_form_field_source_value_dhsc_supply_disruption_alert', language: '@language_fr_ca', value: 'Alerte de rupture de fourniture de DHSC' } }
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_no_action_required', language: '@language_fr_ca', value: 'Aucune action requise' } }
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_action_underway', language: '@language_fr_ca', value: 'Action en cours' } }
  - { fields: { placeholder: '@safety_alerts_form_field_response_type_value_action_complete', language: '@language_fr_ca', value: 'Action achevée' } }
  - { fields: { placeholder: '@safety_alerts_alert_sent_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@safety_alerts_sent_safety_alert', language: '@language_fr_ca', value: Envoyé } }
  - { fields: { placeholder: '@safety_alerts_distribution_users', language: '@language_fr_ca', value: Utilisateurs } }
  - { fields: { placeholder: '@safety_alerts_distribution_contacts', language: '@language_fr_ca', value: 'Contacts de distribution' } }
  - { fields: { placeholder: '@safety_alerts_distribution_distribute_alert', language: '@language_fr_ca', value: 'Destinataires sélectionnés pour le courriel ({{selectedRecipients}})' } }
  - { fields: { placeholder: '@safety_alerts_distribution_distribute_alert_all', language: '@language_fr_ca', value: 'Courriel à tous les destinataires' } }
  - { fields: { placeholder: '@safety_alerts_add_distribution', language: '@language_fr_ca', value: 'Ajouter des listes de distribution' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_message_meta', language: '@language_fr_ca', value: '{{author}} le {{date}}' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_message_placeholder', language: '@language_fr_ca', value: 'Si vous avez une requête pour le destinataire, vous pouvez entrer un message ici.' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_recipient_message_placeholder', language: '@language_fr_ca', value: 'Si vous avez une requête à propos de l''alerte à la sécurité, vous pouvez entrer ici un message pour l''équipe de distribution.' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_no_messages', language: '@language_fr_ca', value: 'Il n''y a actuellement aucune requête' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_message_label', language: '@language_fr_ca', value: 'Corps du message' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_new_save', language: '@language_fr_ca', value: 'Envoyer la requête' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_save_success', language: '@language_fr_ca', value: 'Requête enregistrée avec succès' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la requête' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_messages_message_not_provided', language: '@language_fr_ca', value: 'Une requête doit être fournie' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_distribution_path_title', language: '@language_fr_ca', value: 'Chemin de distribution' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_response_title', language: '@language_fr_ca', value: Réponse } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_no_response', language: '@language_fr_ca', value: 'L''utilisateur n''a pas répondu' } }
  - { fields: { placeholder: '@placeholder_safety_alerts_response_messages_title', language: '@language_fr_ca', value: Requêtes } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_queries_title', language: '@language_fr_ca', value: Requêtes } }
  - { fields: { placeholder: '@safety_alerts_response_form_fields_queries_label', language: '@language_fr_ca', value: Requêtes } }
  - { fields: { placeholder: '@safety_alerts_equipments_uuid', language: '@language_fr_ca', value: 'Un UUID d''équipement valide doit être fourni' } }
