entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: SAFETY_ALERTS.SINGULAR
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_singular
  # Dashboard
  - fields:
      placeholder: SAFETY_ALERTS.DASHBOARD.OPEN
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_dashboard_open
  - fields:
      placeholder: SAFETY_ALERTS.DASHBOARD.CLOSED
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_dashboard_closed
  - fields:
      placeholder: SAFETY_ALERTS.DASHBOARD.REQUIRES_RESPONSE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_dashboard_requires_response
  - fields:
      placeholder: ACL.SAFETY_ALERTS.DISTRIBUTE
      domains:
        - domain: '@domain_acl'
        - domain: '@domain_common'
    ref: acl_safety_alerts_distribute
  # Filters
  - fields:
      placeholder: SAFETY_ALERTS.FILTERS.ALL
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_filters_all
  - fields:
      placeholder: SAFETY_ALERTS.FILTERS.SENT
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_filters_sent
  - fields:
      placeholder: SAFETY_ALERTS.FILTERS.NOT_SENT
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_filters_not_sent
  - fields:
      placeholder: SAFETY_ALERTS.FILTERS.NOT_READ
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_filters_not_read
  - fields:
      placeholder: SAFETY_ALERTS.FILTERS.NO_RESPONSE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_filters_no_response
  # Columns
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.ID
      pointer: COMMON.ID
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.REF
      pointer: COMMON.REF
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.TITLE
      pointer: COMMON.TITLE
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.DATE_ISSUED
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_date_issued
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.DEADLINE_ACTION_UNDERWAY
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_deadline_action_underway
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.DEADLINE_ACTION_COMPLETE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_deadline_action_complete
  # Columns - distributed to list
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.NAME
      type: 0
      pointer: USERS.COLUMNS.NAME
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.DISTRIBUTED_DATE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_distributed_date
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.READ_DATE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_read_date
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.DATE_RESPONDED
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_date_responded
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.RESPONSE_TYPE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_response_type
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.RESPONSE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_column_response
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.RESPONSE_HAS_ATTACHMENTS
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_column_response_has_attachments
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.RESPONSE_HAS_ATTACHMENTS.YES
      pointer: COMMON.YES
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_column_response_yes
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.RESPONSE_HAS_ATTACHMENTS.NO
      pointer: COMMON.NO
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_column_response_no
  # Navigation
  - fields:
      placeholder: NAV.SAFETY_ALERTS
      domains:
        - domain: '@domain_common'
    ref: safety_alerts_nav
  - fields:
      placeholder: SAFETY_ALERTS.MODULE_TITLE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts
  - fields:
      placeholder: SAFETY_ALERTS.DASHBOARD
      type: 0
      pointer: dashboard.singular
      domains:
        - domain: '@domain_safety_alerts'
  -
    fields:
      placeholder: SAFETY_ALERTS.NAV.SAFETY_ALERT_NUMBER
      type: 0
      domains:
      -
        domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_safety_alert_number
  - fields:
      placeholder: SAFETY_ALERTS.NAV.BACK_TO_DASHBOARD
      pointer: NAV.BACK_TO_DASHBOARD
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.NAV.NEW
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_new
  - fields:
      placeholder: SAFETY_ALERTS.NAV.DASHBOARD
      type: 0
      pointer: dashboard.singular
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.NAV.DETAILS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_details
  - fields:
      placeholder: SAFETY_ALERTS.NAV.DISTRIBUTION
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_distribution
  - fields:
      placeholder: SAFETY_ALERTS.NAV.FOR_ACTION
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_for_action
  - fields:
      placeholder: SAFETY_ALERTS.NAV.FOR_INFORMATION
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_for_information
  - fields:
      placeholder: SAFETY_ALERTS.NAV.RESPONSES
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_responses
  - fields:
      placeholder: SAFETY_ALERTS.NAV.ACTIONS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_actions
  - fields:
      placeholder: SAFETY_ALERTS.NAV.MY_ACTIONS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_my_actions
  - fields:
      placeholder: SAFETY_ALERTS.NAV.ALL_ACTIONS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_nav_all_actions
  - fields:
      placeholder: SAFETY_ALERTS.NAV.MEDICATIONS
      type: 0
      pointer: 'MEDICATIONS.PLURAL'
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.NAV.DEVICES
      type: 0
      pointer: 'DEVICES.PLURAL'
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.NAV.ADMIN.PERMISSIONS
      type: 0
      pointer: COMMON.ADMINISTRATION.PERMISSIONS
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS_DISTRIBUTION.SINGULAR
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_singular
  - fields:
      placeholder: SAFETY_ALERTS_DISTRIBUTION.PLURAL
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_plural
  - fields:
      placeholder: SAFETY_ALERTS_DISTRIBUTION.SEARCH
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_search
  - fields:
      placeholder: SAFETY_ALERTS_DISTRIBUTION.CREATE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_create
  - fields:
      placeholder: SAFETY_ALERTS_DISTRIBUTION.EDIT
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_edit
  # Main Form
  - fields:
      placeholder: SAFETY_ALERTS.FORMS.FORM1
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_1
  # Main Form Type
  - fields:
      placeholder: SAFETY_ALERTS.FORM_TYPE.SAFETY_ALERTS
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_form_types'
    ref: safety_alerts_form_type_safety_alerts
  # Sections
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.TITLE_AND_REFERENCE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_title_and_reference
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.ALERT_DETAILS
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_alert_details
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.ACTION_DETAILS
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_action_details
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.DEADLINES
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_deadlines
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.LOCATIONS_AND_SERVICES
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_locations_and_services
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.ATTACHMENTS
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_attachments
  # Fields
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.TITLE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_title
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.TITLE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_title_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.REFERENCE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_reference
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.REFERENCE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_reference_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.HANDLER.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_handler
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.HANDLER.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_handler_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SOURCE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_source
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SOURCE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_source_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ISSUED.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_date_issued
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ISSUED.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_date_issued_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.OPENED_DATE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_opened_date
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.OPENED_DATE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_opened_date_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.CLOSED_DATE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_closed_date
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.CLOSED_DATE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_closed_date_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.TYPE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_type
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.TYPE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_type_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SOURCE.NO_SOURCE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_type_no_source
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SOURCE.NO_TYPE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_type_no_type
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DESCRIPTION.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_description
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DESCRIPTION.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_description_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ACTION_TYPE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_action_type
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ACTION_TYPE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_action_type_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ACTION_DESCRIPTION.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_action_description
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ACTION_DESCRIPTION.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_action_description_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DEADLINE_ACTION_UNDERWAY.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_deadline_action_underway
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DEADLINE_ACTION_UNDERWAY.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_deadline_action_underway_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DEADLINE_ACTION_COMPLETE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_deadline_action_complete
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DEADLINE_ACTION_COMPLETE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_deadline_action_complete_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ACTION_UNDERWAY.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_date_action_underway
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ACTION_UNDERWAY.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_date_action_underway_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ACTION_COMPLETE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_date_action_complete
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.DATE_ACTION_COMPLETE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
        - domain: '@domain_notification_centre'
    ref: safety_alerts_default_form_fields_date_action_complete_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.LOCATIONS.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_locations
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.LOCATIONS.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_locations_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SERVICES.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_services
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.SERVICES.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_services_label
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ATTACHMENTS.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_attachments
  - fields:
      placeholder: SAFETY_ALERTS.DEFAULT_FORM.FIELDS.ATTACHMENTS.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_default_form_fields_attachments_label
  # Filter Form
  - fields:
      placeholder: SAFETY_ALERTS.FORMS.FILTER
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_filter
  # Filter Form form type
  - fields:
      placeholder: SAFETY_ALERTS.FORM_TYPE.FILTER
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_form_types'
    ref: safety_alerts_form_type_filter
  # Response required message
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGE.RESPONSE_REQUIRED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_message_response_required
  # Response Form
  - fields:
      placeholder: SAFETY_ALERTS.FORMS.RESPONSE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_response
  # Response Form form type
  - fields:
      placeholder: SAFETY_ALERTS.FORM_TYPE.RESPONSE
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_form_types'
    ref: safety_alerts_form_type_response
  # Response Form section
  - fields:
      placeholder: SAFETY_ALERTS.SECTION.RESPONSE
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_section_response
  # Response Form fields
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.RESPONSE_TYPE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_response_type
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.RESPONSE_TYPE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_response_type_label
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.RESPONSE.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_response
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.RESPONSE.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_response_label
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.ATTACHMENTS.TITLE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: placeholder_safety_alerts_response_form_fields_attachments_title
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.ATTACHMENTS.LABEL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: placeholder_safety_alerts_response_form_fields_attachments_label

  # Columns
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.SURNAME
      type: 0
      pointer: USERS.USER.SURNAME
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.JOB_TITLE
      type: 0
      pointer: USERS.USER.JOB_TITLE
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.CONTACT_SUBTYPE
      type: 0
      pointer: COMMON.SUBTYPE
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.COLUMNS.EMAIL
      type: 0
      pointer: CONTACTS.FORM.EMAIL.SINGULAR
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.DISTRIBUTIONS.COLUMNS.CREATED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_columns_created
  # Notifications and Errors
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.SUCCESS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_success
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.FAILED
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_failed
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.SUCCESS_UPDATE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_success_update
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.FAILED_UPDATE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_failed_update
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.DISTRIBUTED
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_distributed
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.RESPONSE_SAVE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_response_save
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.NEEDS_RECIPIENTS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_needs_recipients
  - fields:
      placeholder: SAFETY_ALERTS.ERROR.DISTRIBUTION_USER_SAVE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_error_distribution_user_save
  - fields:
      placeholder: SAFETY_ALERTS.ERROR.DISTRIBUTION_CONTACT_SAVE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_error_distribution_contact_save
  - fields:
      placeholder: SAFETY_ALERTS.ERROR.FAILED_TO_DISTRIBUTE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_error_failed_to_distribute
  - fields:
      placeholder: SAFETY_ALERTS.NOTIFY.USERS_ADDED_ONLY
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_notify_users_added_only
  # Datasource Item
  # Type
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.TYPE.VALUE.MEDICAL_DEVICES_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_type_value_medical_devices_alert
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.TYPE.VALUE.MEDICATION_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_type_value_medication_alert
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.TYPE.VALUE.PATIENT_SAFETY_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_type_value_patient_safety_alert
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.TYPE.VALUE.ESTATES_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_type_value_estates_alert
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.TYPE.VALUE.SUPPLY_DISRUPTION_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_type_value_supply_disruption_alert
  # Action Type
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.ACTION_TYPE.VALUE.ACTION_REQUIRED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_action_type_value_action_required
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.ACTION_TYPE.VALUE.IMMEDIATE_ACTION_REQUIRED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_action_type_value_immediate_action_required
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.ACTION_TYPE.VALUE.NO_ACTION_REQUIRED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_action_type_value_no_action_required
  # Source
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.OTHER
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_other
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.NHS_ENGLAND
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_nhs_england
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.NHS_DIGITAL
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_nhs_digital
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.NHS_IMPROVEMENT_ESTATES_AND_FACILITIES
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_nhs_improvement_estates_and_facilities
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.NHS_IMPROVEMENT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_nhs_improvement
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.MHRA_MEDICAL_DEVICE_ALERTS
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_mhra_medical_device_alerts
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.MHRA_DRUG_ALERTS
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_mhra_drug_alerts
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.CMO_MESSAGING
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_cmo_messaging
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.MHRA_DEAR_DOCTOR_LETTER
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_mhra_dear_doctor_letter
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.SOURCE.VALUE.DHSC_SUPPLY_DISRUPTION_ALERT
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_source_value_dhsc_supply_disruption_alert
  # Response Type
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.RESPONSE_TYPE.VALUE.NO_ACTION_REQUIRED
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_response_type_value_no_action_required
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.RESPONSE_TYPE.VALUE.ACTION_UNDERWAY
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_response_type_value_action_underway
  - fields:
      placeholder: SAFETY_ALERTS.FORM.FIELD.RESPONSE_TYPE.VALUE.ACTION_COMPLETE
      type: 1
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_form_field_response_type_value_action_complete
  # Distribution tabs
  - fields:
      placeholder: SAFETY_ALERTS.ALERT_SENT_STATUS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_alert_sent_status
  - fields:
      placeholder: SAFETY_ALERTS.SENT_SAFETY_ALERT
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_sent_safety_alert
  - fields:
      placeholder: SAFETY_ALERTS.DISTRIBUTION.USERS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_users
  - fields:
      placeholder: SAFETY_ALERTS.DISTRIBUTION.CONTACTS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_contacts
  - fields:
      placeholder: SAFETY_ALERTS.DISTRIBUTION.DISTRIBUTE_ALERT
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_distribute_alert
  - fields:
      placeholder: SAFETY_ALERTS.DISTRIBUTION.DISTRIBUTE_ALERT_ALL
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_distribution_distribute_alert_all
  - fields:
      placeholder: SAFETY_ALERTS.ADD_DISTRIBUTION
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_add_distribution
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.MESSAGE_META
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_message_meta
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.NEW.MESSAGE.PLACEHOLDER
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_new_message_placeholder
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.RECIPIENT.MESSAGE.PLACEHOLDER
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_recipient_message_placeholder
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.NO_MESSAGES
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_no_messages
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.NEW.MESSAGE.LABEL
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_new_message_label
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.NEW.SAVE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_new_save
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.SAVE_SUCCESS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_save_success
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.SAVE_ERROR
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_save_error
  - fields:
      placeholder: SAFETY_ALERTS.MESSAGES.MESSAGE_NOT_PROVIDED
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_messages_message_not_provided
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE.DISTRIBUTION_PATH.TITLE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_response_distribution_path_title
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE.RESPONSE.TITLE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_response_response_title
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE.NO_RESPONSE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_response_no_response
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE.MESSAGES.TITLE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: placeholder_safety_alerts_response_messages_title
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.QUERIES.TITLE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_queries_title
  - fields:
      placeholder: SAFETY_ALERTS.RESPONSE_FORM.FIELDS.QUERIES.LABEL
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
        - domain: '@domain_field_maintenance'
    ref: safety_alerts_response_form_fields_queries_label
  - fields:
      placeholder: ERM.EQUIPMENT.INVALID_EQUIPMENT_UUID
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_equipments_uuid

  # Safety Alerts - Incidents
  - fields:
      placeholder: SAFETY_ALERTS.NAV.INCIDENTS
      pointer: INCIDENTS.INCIDENT.INCIDENTS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.INCIDENT
      pointer: INCIDENTS.INCIDENT.INCIDENT
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.ID
      pointer: INCIDENTS.INCIDENT.ID
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.REFERENCE
      pointer: INCIDENTS.INCIDENT.REFERENCE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.TITLE
      pointer: INCIDENTS.INCIDENT.TITLE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.INCIDENT_DATE
      pointer: INCIDENTS.INCIDENT.INCIDENT_DATE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.LINK_NOTE
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_link_note
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.LINK_SUCCESS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_link_success
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.UNLINK_SUCCESS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_unlink_success
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.LINK_ERROR
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_link_error
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.INVALID_INCIDENT_ID
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_invalid_incident_id
  - fields:
      placeholder: SAFETY_ALERTS.INCIDENTS.ALREADY_LINKED
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_incidents_already_linked
  - fields:
      placeholder: SAFETY_ALERTS.SOURCE.NO_OPTIONS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_source_no_options
  - fields:
      placeholder: SAFETY_ALERTS.SOURCE.LABEL
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_source_label
  - fields:
      placeholder: SAFETY_ALERTS.TYPE.NO_OPTIONS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_type_no_options
  - fields:
      placeholder: SAFETY_ALERTS.TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_type_label
  - fields:
      placeholder: SAFETY_ALERTS.FORM.DELETE.WARNING
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_delete_warning
  - fields:
      placeholder: SAFETY_ALERTS.FORM.DELETE.SUCCESS
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_delete_success
  - fields:
      placeholder: SAFETY_ALERTS.FORM.DELETE.ERROR
      type: 0
      domains:
        - domain: '@domain_safety_alerts'
    ref: safety_alerts_form_delete_error
