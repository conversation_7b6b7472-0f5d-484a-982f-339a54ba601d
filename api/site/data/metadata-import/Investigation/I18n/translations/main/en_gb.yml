entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@investigations_user_roles_none'
      language: '@language_en_gb'
      value: None
  -
    fields:
      placeholder: '@investigations_user_roles_subject'
      language: '@language_en_gb'
      value: Subject
  -
    fields:
      placeholder: '@investigations_user_roles_recipient'
      language: '@language_en_gb'
      value: Recipient
  -
    fields:
      placeholder: '@investigations_user_roles_investigator'
      language: '@language_en_gb'
      value: Investigator
  -
    fields:
      placeholder: '@investigations_user_roles_lead_investigator'
      language: '@language_en_gb'
      value: 'Lead Investigator'
  -
    fields:
      placeholder: '@investigations_user_role'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@investigations_user_roles_patient'
      language: '@language_en_gb'
      value: Patient
  -
    fields:
      placeholder: '@investigations_user_roles_witness'
      language: '@language_en_gb'
      value: Witness
  -
    fields:
      placeholder: '@investigations_user_roles_nurse'
      language: '@language_en_gb'
      value: Nurse
  -
    fields:
      placeholder: '@investigations_user_roles_doctor'
      language: '@language_en_gb'
      value: Doctor
  -
    fields:
      placeholder: '@investigations_components_fivewhys'
      language: '@language_en_gb'
      value: 'Five Whys'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_addnew'
      language: '@language_en_gb'
      value: 'Add New Five Whys Entry'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_addwhy'
      language: '@language_en_gb'
      value: 'Add Why'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_why'
      language: '@language_en_gb'
      value: 'Why?'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_problem'
      language: '@language_en_gb'
      value: Problem
  -
    fields:
      placeholder: '@investigations_components_fivewhys_contributoryfactors'
      language: '@language_en_gb'
      value: 'Contributory Factors'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_addcontributoryfactor'
      language: '@language_en_gb'
      value: 'Add Contributory Factor'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_contributoryfactortitle'
      language: '@language_en_gb'
      value: 'Contributory Factor Title'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_save'
      language: '@language_en_gb'
      value: Save
  -
    fields:
      placeholder: '@investigations_components_fivewhys_delete'
      language: '@language_en_gb'
      value: Delete
  -
    fields:
      placeholder: '@investigations_components_fivewhys_newproblem'
      language: '@language_en_gb'
      value: 'New Problem'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_areyousure'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@investigations_components_fivewhys_why_placeholder'
      language: '@language_en_gb'
      value: 'Why?'
  -
    fields:
      placeholder: '@investigations_components_participantform_participant'
      language: '@language_en_gb'
      value: Participant
  -
    fields:
      placeholder: '@investigations_components_participantform_participantname'
      language: '@language_en_gb'
      value: 'Participant Name'
  -
    fields:
      placeholder: '@investigations_components_participantform_usecontact'
      language: '@language_en_gb'
      value: 'Use Contact'
  -
    fields:
      placeholder: '@investigations_components_participantform_closecontactform'
      language: '@language_en_gb'
      value: 'Close Contact Form'
  -
    fields:
      placeholder: '@investigations_components_participantform_newcontact'
      language: '@language_en_gb'
      value: 'New Contact'
  -
    fields:
      placeholder: '@investigations_components_participantform_view_contact'
      language: '@language_en_gb'
      value: 'View Contact'
  -
    fields:
      placeholder: '@investigations_components_participantform_searchexistingcontacts'
      language: '@language_en_gb'
      value: 'Search Existing Contacts'
  -
    fields:
      placeholder: '@investigations_components_participantform_saveparticipant'
      language: '@language_en_gb'
      value: 'Save Participant'
  -
    fields:
      placeholder: '@investigations_components_participantform_role'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@investigations_components_participantform_newparticipant'
      language: '@language_en_gb'
      value: 'New Participant'
  -
    fields:
      placeholder: '@investigations_components_participantform_newparticipantfromcontact'
      language: '@language_en_gb'
      value: 'New Participant (from Contact Record)'
  -
    fields:
      placeholder: '@investigations_components_participantform_existingparticipant'
      language: '@language_en_gb'
      value: 'Existing Participant'
  -
    fields:
      placeholder: '@investigations_components_participantform_participant_name'
      language: '@language_en_gb'
      value: 'Participant Name'
  -
    fields:
      placeholder: '@investigations_components_participantform_reset_participant'
      language: '@language_en_gb'
      value: 'Reset Participant'
  -
    fields:
      placeholder: '@investigations_components_eventform_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@investigations_components_eventform_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@investigations_components_eventform_startdate'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@investigations_components_eventform_end_date'
      language: '@language_en_gb'
      value: 'End Date'
  -
    fields:
      placeholder: '@investigations_components_eventform_source'
      language: '@language_en_gb'
      value: Source
  -
    fields:
      placeholder: '@investigations_components_eventform_deleteevent'
      language: '@language_en_gb'
      value: 'Delete Event'
  -
    fields:
      placeholder: '@investigations_components_eventform_cancel'
      language: '@language_en_gb'
      value: Cancel
  -
    fields:
      placeholder: '@investigations_components_eventform_addanotherevent'
      language: '@language_en_gb'
      value: 'Add Another Event?'
  -
    fields:
      placeholder: '@investigations_components_eventform_saveevent'
      language: '@language_en_gb'
      value: 'Save Event'
  -
    fields:
      placeholder: '@investigations_components_eventform_eventdetails'
      language: '@language_en_gb'
      value: 'Event Details'
  -
    fields:
      placeholder: '@investigations_components_eventform_errors_invalid_end_date'
      language: '@language_en_gb'
      value: 'End Date must not be before Start Date'
  -
    fields:
      placeholder: '@investigations_components_eventform_participant'
      language: '@language_en_gb'
      value: Participant
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram'
      language: '@language_en_gb'
      value: 'Fishbone Diagram'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_fishbone_diagram_title'
      language: '@language_en_gb'
      value: 'Fishbone Diagram Title'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_add_root_node'
      language: '@language_en_gb'
      value: 'Add Root Node'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_save'
      language: '@language_en_gb'
      value: 'Save and Update Fishbone Diagram'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_saved_successfully'
      language: '@language_en_gb'
      value: 'Fishbone Diagram saved successfully'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_contributory_factor'
      language: '@language_en_gb'
      value: 'Contributory Factor?'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_nodes_confirm_delete'
      language: '@language_en_gb'
      value: 'Are you sure you want to delete this node?'
  -
    fields:
      placeholder: '@investigations_components_fishbone_diagram_nodes_delete_successful'
      language: '@language_en_gb'
      value: 'Fishbone Diagram node deleted successfully'
  -
    fields:
      placeholder: '@investigations_process_maps_form_form_title'
      language: '@language_en_gb'
      value: 'Process Map'
  -
    fields:
      placeholder: '@investigations_process_maps_form_save'
      language: '@language_en_gb'
      value: 'Save Changes'
  -
    fields:
      placeholder: '@investigations_process_maps_form_save_and_view'
      language: '@language_en_gb'
      value: 'Save and View Process Map'
  -
    fields:
      placeholder: '@investigations_objectives_objective_list'
      language: '@language_en_gb'
      value: 'Objectives List'
  -
    fields:
      placeholder: '@investigations_objectives_add_objective'
      language: '@language_en_gb'
      value: 'Add Objective'
  -
    fields:
      placeholder: '@investigations_objectives_new_objective'
      language: '@language_en_gb'
      value: 'New Objective'
  -
    fields:
      placeholder: '@investigations_objectives_summary'
      language: '@language_en_gb'
      value: Summary
  -
    fields:
      placeholder: '@investigations_objectives_details'
      language: '@language_en_gb'
      value: Details
  -
    fields:
      placeholder: '@investigations_objectives_delete_objective'
      language: '@language_en_gb'
      value: 'Delete Objective'
  -
    fields:
      placeholder: '@investigations_objectives_save_objective'
      language: '@language_en_gb'
      value: 'Save Objective'
  -
    fields:
      placeholder: '@investigations_objectives_no_objectives'
      language: '@language_en_gb'
      value: 'This Investigation has no Objectives'
  -
    fields:
      placeholder: '@investigations_components_eventchronology'
      language: '@language_en_gb'
      value: 'Event Chronology'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_startdate'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_enddate'
      language: '@language_en_gb'
      value: 'End Date'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_person'
      language: '@language_en_gb'
      value: Person
  -
    fields:
      placeholder: '@investigations_components_eventchronology_role'
      language: '@language_en_gb'
      value: Role
  -
    fields:
      placeholder: '@investigations_components_eventchronology_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@investigations_components_eventchronology_description'
      language: '@language_en_gb'
      value: Description
  -
    fields:
      placeholder: '@investigations_components_eventchronology_source'
      language: '@language_en_gb'
      value: Source
  -
    fields:
      placeholder: '@investigations_components_eventchronology_source_label'
      language: '@language_en_gb'
      value: Source
  -
    fields:
      placeholder: '@investigations_components_eventchronology_addevent'
      language: '@language_en_gb'
      value: 'Add Event'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@investigations_components_eventchronology_editevent'
      language: '@language_en_gb'
      value: 'Edit Event'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_viewevent'
      language: '@language_en_gb'
      value: 'View Event'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_processmaps'
      language: '@language_en_gb'
      value: 'Process maps'
  -
    fields:
      placeholder: '@investigations_components_eventchronology_delete'
      language: '@language_en_gb'
      value: Delete
  -
    fields:
      placeholder: '@investigations_components_eventchronology_addfirstmessage'
      language: '@language_en_gb'
      value: 'Click the button above to add the first Event'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_millimeters_of_mercury'
      language: '@language_en_gb'
      value: 'Millimeters of Mercury'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_beats_per_minute'
      language: '@language_en_gb'
      value: 'Beats per Minute'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_oxygen_saturation'
      language: '@language_en_gb'
      value: 'Oxygen Saturation'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_breaths_per_minute'
      language: '@language_en_gb'
      value: 'Breaths Per Minute'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_score'
      language: '@language_en_gb'
      value: Score
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_degrees_celcius'
      language: '@language_en_gb'
      value: 'Degrees Celcius'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_systolic'
      language: '@language_en_gb'
      value: Systolic
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_unit_diastolic'
      language: '@language_en_gb'
      value: Diastolic
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_singular'
      language: '@language_en_gb'
      value: 'Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_plural'
      language: '@language_en_gb'
      value: 'Clinical Measurements'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_save_error'
      language: '@language_en_gb'
      value: 'Error saving Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_new'
      language: '@language_en_gb'
      value: 'New Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_add'
      language: '@language_en_gb'
      value: 'Add Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_no_measurements'
      language: '@language_en_gb'
      value: 'This Investigation currently has no Clinical Measurements'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_occurrence_date'
      language: '@language_en_gb'
      value: 'Occurrence Date'
  - fields:
      placeholder: '@investigations_components_clinical_measurements_occurrence_time'
      language: '@language_en_gb'
      value: Occurrence Time
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_details'
      language: '@language_en_gb'
      value: 'Clinical Measurement Details'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_loading_clinical_measurement'
      language: '@language_en_gb'
      value: 'Loading Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_loading_grid'
      language: '@language_en_gb'
      value: 'Loading Grid'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_loading_chart'
      language: '@language_en_gb'
      value: 'Loading Chart'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_loading_error'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Clinical Measurements'
  -
    fields:
      placeholder: '@investigations_attachments_no_attachments'
      language: '@language_en_gb'
      value: 'This Investigation has no Attachments'
  -
    fields:
      placeholder: '@investigations_admin_is_default'
      language: '@language_en_gb'
      value: 'Default?'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_blood_pressure'
      language: '@language_en_gb'
      value: 'Blood Pressure'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_heart_rate'
      language: '@language_en_gb'
      value: 'Heart Rate'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_oxygen_saturation'
      language: '@language_en_gb'
      value: 'Oxygen Saturation'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_respiratory_rate'
      language: '@language_en_gb'
      value: 'Respiratory Rate'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_pain_score'
      language: '@language_en_gb'
      value: 'Pain Score'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_temperature'
      language: '@language_en_gb'
      value: Temperature
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_type_select'
      language: '@language_en_gb'
      value: 'Select Type'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_please_add_values'
      language: '@language_en_gb'
      value: 'At least one measurement value is required'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_form_invalid'
      language: '@language_en_gb'
      value: 'Please fill all required fields in the Measurement form'
  -
    fields:
      placeholder: '@investigations_process_maps_list_add_new'
      language: '@language_en_gb'
      value: 'Add New Process Map'
  -
    fields:
      placeholder: '@investigations_process_maps_list_view'
      language: '@language_en_gb'
      value: 'View Process Map'
  -
    fields:
      placeholder: '@investigations_process_maps_tool_details'
      language: '@language_en_gb'
      value: Details
  -
    fields:
      placeholder: '@investigations_process_maps_tool_appearance'
      language: '@language_en_gb'
      value: Appearance
  -
    fields:
      placeholder: '@investigations_process_maps_tool_body_colour'
      language: '@language_en_gb'
      value: 'Body Colour'
  -
    fields:
      placeholder: '@investigations_process_maps_tool_process_model_shapes'
      language: '@language_en_gb'
      value: 'Process Model Shapes'
  -
    fields:
      placeholder: '@investigations_process_maps_tool_fault_tree_shapes'
      language: '@language_en_gb'
      value: 'Fault Tree Shapes'
  -
    fields:
      placeholder: '@investigations_process_maps_tool_investigation_events'
      language: '@language_en_gb'
      value: 'Investigation Events'
  -
    fields:
      placeholder: '@investigations_process_maps_tool_form_start'
      language: '@language_en_gb'
      value: Start
  -
    fields:
      placeholder: '@investigations_process_maps_tool_form_end'
      language: '@language_en_gb'
      value: End
  -
    fields:
      placeholder: '@investigations_process_maps_tool_form_date_not_set'
      language: '@language_en_gb'
      value: 'Date Not Set'
  -
    fields:
      placeholder: '@investigations_loading_investigations'
      language: '@language_en_gb'
      value: 'Loading Investigations...'
  -
    fields:
      placeholder: '@investigations_loading_investigation_form'
      language: '@language_en_gb'
      value: 'Loading Investigation Form...'
  -
    fields:
      placeholder: '@investigations_loading_tools'
      language: '@language_en_gb'
      value: 'Loading Tools...'
  -
    fields:
      placeholder: '@investigations_loading_events'
      language: '@language_en_gb'
      value: 'Loading Events...'
  -
    fields:
      placeholder: '@investigations_investigation_levels_level_1'
      language: '@language_en_gb'
      value: 1
  -
    fields:
      placeholder: '@investigations_investigation_levels_level_2'
      language: '@language_en_gb'
      value: 2
  -
    fields:
      placeholder: '@investigations_investigation_levels_level_3'
      language: '@language_en_gb'
      value: 3
  -
    fields:
      placeholder: '@investigations_investigation_status_not_started'
      language: '@language_en_gb'
      value: 'Not Started'
  -
    fields:
      placeholder: '@investigations_investigation_status_in_progress'
      language: '@language_en_gb'
      value: 'In Progress'
  -
    fields:
      placeholder: '@investigations_investigation_status_submitted_for_approval'
      language: '@language_en_gb'
      value: 'Submitted For Approval'
  -
    fields:
      placeholder: '@investigations_investigation_status_completed'
      language: '@language_en_gb'
      value: Finalised
  -
    fields:
      placeholder: '@investigations_investigation_status_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@investigations_investigation_status_decommissioned'
      language: '@language_en_gb'
      value: Decommissioned
  -
    fields:
      placeholder: '@investigations_investigation_started'
      language: '@language_en_gb'
      value: Started
  -
    fields:
      placeholder: '@investigations_investigation_priority'
      language: '@language_en_gb'
      value: Priority
  -
    fields:
      placeholder: '@investigations_investigation_priority_low'
      language: '@language_en_gb'
      value: Low
  -
    fields:
      placeholder: '@investigations_investigation_priority_medium'
      language: '@language_en_gb'
      value: Medium
  -
    fields:
      placeholder: '@investigations_investigation_priority_high'
      language: '@language_en_gb'
      value: High
  -
    fields:
      placeholder: '@investigations_investigation_level'
      language: '@language_en_gb'
      value: Level
  -
    fields:
      placeholder: '@investigations_investigation_lead_investigator'
      language: '@language_en_gb'
      value: 'Lead Investigator'
  -
    fields:
      placeholder: '@investigations_investigation_overdue'
      language: '@language_en_gb'
      value: Overdue
  -
    fields:
      placeholder: '@investigations_investigation_due'
      language: '@language_en_gb'
      value: Due Date
  -
    fields:
      placeholder: '@investigations_investigation_submit'
      language: '@language_en_gb'
      value: 'Save Investigation'
  -
    fields:
      placeholder: '@investigations_module_title'
      language: '@language_en_gb'
      value: Investigations
  -
    fields:
      placeholder: '@investigations_nav_back_to_dashboard'
      language: '@language_en_gb'
      value: 'Back to Dashboard'
  -
    fields:
      placeholder: '@investigations_nav_dashboard'
      language: '@language_en_gb'
      value: Dashboard
  -
    fields:
      placeholder: '@investigations_nav_new_investigation'
      language: '@language_en_gb'
      value: 'New Investigation'
  -
    fields:
      placeholder: '@investigations_nav_events'
      language: '@language_en_gb'
      value: Events
  -
    fields:
      placeholder: '@investigations_nav_administration'
      language: '@language_en_gb'
      value: Administration
  -
    fields:
      placeholder: '@investigations_nav_investigation_page_title'
      language: '@language_en_gb'
      value: 'Investigation #{{id}}'
  -
    fields:
      placeholder: '@investigations_nav_investigation_define_investigation'
      language: '@language_en_gb'
      value: 'Define Investigation'
  -
    fields:
      placeholder: '@investigations_nav_investigation_objectives'
      language: '@language_en_gb'
      value: Objectives
  -
    fields:
      placeholder: '@investigations_nav_investigation_recommendations'
      language: '@language_en_gb'
      value: Recommendations
  -
    fields:
      placeholder: '@investigations_nav_investigation_attachments'
      language: '@language_en_gb'
      value: Attachments
  -
    fields:
      placeholder: '@investigations_success_admin_tools'
      language: '@language_en_gb'
      value: 'Default tools saved successfully'
  -
    fields:
      placeholder: '@investigations_success_third_party_events_rejected'
      language: '@language_en_gb'
      value: 'Event rejected successfully'
  -
    fields:
      placeholder: '@investigations_success_third_party_events_investigation_created'
      language: '@language_en_gb'
      value: 'Investigation created successfully'
  -
    fields:
      placeholder: '@investigations_success_attachment_save'
      language: '@language_en_gb'
      value: 'Attachment saved successfully'
  -
    fields:
      placeholder: '@investigations_success_investigation_save'
      language: '@language_en_gb'
      value: 'Investigation saved successfully'
  -
    fields:
      placeholder: '@investigations_success_investigation_tool_refresh'
      language: '@language_en_gb'
      value: 'Available Investigation tools updated'
  -
    fields:
      placeholder: '@investigations_success_clinical_measurements_save'
      language: '@language_en_gb'
      value: 'Clinical Measurement updated successfully'
  -
    fields:
      placeholder: '@investigations_success_events_saved'
      language: '@language_en_gb'
      value: 'Event saved successfully'
  -
    fields:
      placeholder: '@investigations_success_events_deleted'
      language: '@language_en_gb'
      value: 'Event deleted successfully'
  -
    fields:
      placeholder: '@investigations_success_five_whys_saved'
      language: '@language_en_gb'
      value: 'Five Why saved successfully'
  -
    fields:
      placeholder: '@investigations_success_five_whys_deleted'
      language: '@language_en_gb'
      value: 'Five Why deleted successfully'
  -
    fields:
      placeholder: '@investigations_success_notes_saved'
      language: '@language_en_gb'
      value: 'Note saved successfully'
  -
    fields:
      placeholder: '@investigations_success_notes_deleted'
      language: '@language_en_gb'
      value: 'Note deleted successfully'
  -
    fields:
      placeholder: '@investigations_success_objectives_saved'
      language: '@language_en_gb'
      value: 'Objective saved successfully'
  -
    fields:
      placeholder: '@investigations_success_objectives_deleted'
      language: '@language_en_gb'
      value: 'Objective deleted successfully'
  -
    fields:
      placeholder: '@investigations_success_participants_saved'
      language: '@language_en_gb'
      value: 'Participant saved successfully'
  -
    fields:
      placeholder: '@investigations_success_process_map_saved'
      language: '@language_en_gb'
      value: 'Process Map saved successfully'
  -
    fields:
      placeholder: '@investigations_success_safer_matrix_saved'
      language: '@language_en_gb'
      value: 'SAFER Matrix saved successfully'
  -
    fields:
      placeholder: '@investigations_events'
      language: '@language_en_gb'
      value: Events
  -
    fields:
      placeholder: '@investigations_events_create_investigation_investigation_title'
      language: '@language_en_gb'
      value: 'Investigation Title'
  -
    fields:
      placeholder: '@investigations_events_create_investigation_add_users'
      language: '@language_en_gb'
      value: 'Add Users'
  -
    fields:
      placeholder: '@investigations_events_create_investigation_submit'
      language: '@language_en_gb'
      value: 'Create Investigation'
  -
    fields:
      placeholder: '@investigations_events_reject_event_reason'
      language: '@language_en_gb'
      value: 'Reason for Rejection'
  -
    fields:
      placeholder: '@investigations_events_reject_event_submit'
      language: '@language_en_gb'
      value: 'Reject Event'
  -
    fields:
      placeholder: '@investigations_events_tabs_event_summary'
      language: '@language_en_gb'
      value: 'Event Summary'
  -
    fields:
      placeholder: '@investigations_events_tabs_create_investigation'
      language: '@language_en_gb'
      value: 'Create Investigation'
  -
    fields:
      placeholder: '@investigations_events_tabs_reject_event'
      language: '@language_en_gb'
      value: 'Reject Event'
  -
    fields:
      placeholder: '@investigations_events_reference'
      language: '@language_en_gb'
      value: 'Ref'
  -
    fields:
      placeholder: '@investigations_events_name'
      language: '@language_en_gb'
      value: 'Name'
  -
    fields:
      placeholder: '@investigations_events_owner'
      language: '@language_en_gb'
      value: 'Owner'
  -
    fields:
      placeholder: '@investigations_events_sac_score'
      language: '@language_en_gb'
      value: 'SAC Score'
  -
    fields:
      placeholder: '@investigations_events_person_affected'
      language: '@language_en_gb'
      value: 'Person Affected'
  -
    fields:
      placeholder: '@investigations_events_handler'
      language: '@language_en_gb'
      value: Handler
  -
    fields:
      placeholder: '@investigations_events_occurred'
      language: '@language_en_gb'
      value: Occurred
  -
    fields:
      placeholder: '@investigations_components_time_person_grid_add_event'
      language: '@language_en_gb'
      value: 'Add Event'
  -
    fields:
      placeholder: '@investigations_components_time_person_grid_fit_all_events'
      language: '@language_en_gb'
      value: 'Fit All Events'
  -
    fields:
      placeholder: '@investigations_components_time_person_grid_edit_participant'
      language: '@language_en_gb'
      value: 'Edit Participant'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_opensafermatrix'
      language: '@language_en_gb'
      value: 'Open SAFER Matrix'
  -
    fields:
      placeholder: '@investigations_components_safermatrix'
      language: '@language_en_gb'
      value: 'SAFER Matrix'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_saveandclose'
      language: '@language_en_gb'
      value: 'Save and Close'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_close'
      language: '@language_en_gb'
      value: Close
  -
    fields:
      placeholder: '@investigations_components_safermatrix_matrix'
      language: '@language_en_gb'
      value: Matrix
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors'
      language: '@language_en_gb'
      value: 'Contributory Factors'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors_save'
      language: '@language_en_gb'
      value: 'Save Contributory Factors'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors_add'
      language: '@language_en_gb'
      value: Add
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors_reset'
      language: '@language_en_gb'
      value: Reset
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors_no_contributory_factors'
      language: '@language_en_gb'
      value: 'No Contributory Factors'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_contributoryfactors_new'
      language: '@language_en_gb'
      value: 'New Contributory Factor'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_save'
      language: '@language_en_gb'
      value: Save
  -
    fields:
      placeholder: '@investigations_components_safermatrix_structure'
      language: '@language_en_gb'
      value: Structure
  -
    fields:
      placeholder: '@investigations_components_safermatrix_why'
      language: '@language_en_gb'
      value: 'Why did it happen?'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_how'
      language: '@language_en_gb'
      value: 'How did this happen?'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_process'
      language: '@language_en_gb'
      value: Process
  -
    fields:
      placeholder: '@investigations_components_safermatrix_outcome'
      language: '@language_en_gb'
      value: Outcome
  -
    fields:
      placeholder: '@investigations_components_safermatrix_what'
      language: '@language_en_gb'
      value: 'What happened?'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_patient'
      language: '@language_en_gb'
      value: Patient
  -
    fields:
      placeholder: '@investigations_components_safermatrix_personnel'
      language: '@language_en_gb'
      value: Personnel
  -
    fields:
      placeholder: '@investigations_components_safermatrix_environment'
      language: '@language_en_gb'
      value: 'Environment / equipment'
  -
    fields:
      placeholder: '@investigations_components_safermatrix_organisation'
      language: '@language_en_gb'
      value: Organisation
  -
    fields:
      placeholder: '@investigations_components_safermatrix_regulatoryagencies'
      language: '@language_en_gb'
      value: 'Regulatory Agencies'
  -
    fields:
      placeholder: '@investigations_contact_role_patient'
      language: '@language_en_gb'
      value: Patient
  -
    fields:
      placeholder: '@investigations_contact_role_witness'
      language: '@language_en_gb'
      value: Witness
  -
    fields:
      placeholder: '@investigations_contact_role_nurse'
      language: '@language_en_gb'
      value: Nurse
  -
    fields:
      placeholder: '@investigations_contact_role_doctor'
      language: '@language_en_gb'
      value: Doctor
  -
    fields:
      placeholder: '@investigations_dashboard_no_results'
      language: '@language_en_gb'
      value: 'There are no Investigations matching the specified filter criteria.'
  -
    fields:
      placeholder: '@investigations_dashboard_filter_title'
      language: '@language_en_gb'
      value: 'Filter Investigations'
  -
    fields:
      placeholder: '@investigations_error_not_found'
      language: '@language_en_gb'
      value: 'Investigation Not Found'
  -
    fields:
      placeholder: '@investigations_error_get_form'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Investigation form'
  -
    fields:
      placeholder: '@investigations_error_admin_save'
      language: '@language_en_gb'
      value: 'An error occurred whilst updating the default tools'
  -
    fields:
      placeholder: '@investigations_error_third_party_events_get_collection'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the event list'
  -
    fields:
      placeholder: '@investigations_error_third_party_events_create_investigation'
      language: '@language_en_gb'
      value: 'An error occurred whilst creating the Investigation'
  -
    fields:
      placeholder: '@investigations_error_third_party_events_reject'
      language: '@language_en_gb'
      value: 'An error occurred whilst rejecting the Event'
  -
    fields:
      placeholder: '@investigations_error_clinical_measurements_save'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Clinical Measurement'
  -
    fields:
      placeholder: '@investigations_error_clinical_measurements_save_value'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Clinical Measurement Value'
  -
    fields:
      placeholder: '@investigations_error_events_get_collection'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Events'
  -
    fields:
      placeholder: '@investigations_error_events_delete'
      language: '@language_en_gb'
      value: 'An error occurred whilst deleting the Event'
  -
    fields:
      placeholder: '@investigations_error_five_whys_get_collection'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Five Whys'
  -
    fields:
      placeholder: '@investigations_error_five_whys_save'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Five Whys entry'
  -
    fields:
      placeholder: '@investigations_error_five_whys_delete'
      language: '@language_en_gb'
      value: 'An error occurred whilst deleting the Five Whys entry'
  -
    fields:
      placeholder: '@investigations_error_objectives_delete'
      language: '@language_en_gb'
      value: 'An error occurred whilst deleting the Objective'
  -
    fields:
      placeholder: '@investigations_error_process_maps_save'
      language: '@language_en_gb'
      value: 'An error occurred whilst saving the Process Map'
  -
    fields:
      placeholder: '@investigations_error_file_not_found'
      language: '@language_en_gb'
      value: 'File Not Found'
  -
    fields:
      placeholder: '@investigations_error_attachment_not_found'
      language: '@language_en_gb'
      value: 'Attachment Not Found'
  -
    fields:
      placeholder: '@investigations_error_fishbone_not_found'
      language: '@language_en_gb'
      value: 'Fishbone Diagram Not Found'
  -
    fields:
      placeholder: '@investigations_error_fivewhy_not_found'
      language: '@language_en_gb'
      value: 'Five Why Not Found'
  -
    fields:
      placeholder: '@investigations_error_safer_matrix_not_found'
      language: '@language_en_gb'
      value: 'Safer Matrix'
  -
    fields:
      placeholder: '@investigations_tools_add_tool'
      language: '@language_en_gb'
      value: 'Add Tool'
  -
    fields:
      placeholder: '@investigations_tools_collect_data'
      language: '@language_en_gb'
      value: 'Collect Data'
  -
    fields:
      placeholder: '@investigations_tools_analyse_data'
      language: '@language_en_gb'
      value: 'Analyse Data'
  -
    fields:
      placeholder: '@investigations_tools_event_chronology'
      language: '@language_en_gb'
      value: 'Event Chronology'
  -
    fields:
      placeholder: '@investigations_tools_time_person_grid'
      language: '@language_en_gb'
      value: 'Time Person Grid'
  -
    fields:
      placeholder: '@investigations_tools_clinical_measurements'
      language: '@language_en_gb'
      value: 'Clinical Measurements'
  -
    fields:
      placeholder: '@investigations_tools_safer_matrix'
      language: '@language_en_gb'
      value: 'SAFER Matrix'
  -
    fields:
      placeholder: '@investigations_tools_five_whys'
      language: '@language_en_gb'
      value: 'Five Whys'
  -
    fields:
      placeholder: '@investigations_tools_fishbone_diagram'
      language: '@language_en_gb'
      value: 'Fishbone Diagram'
  -
    fields:
      placeholder: '@investigations_tools_process_maps'
      language: '@language_en_gb'
      value: 'Process Maps'
  -
    fields:
      placeholder: '@investigations_tools_objectives'
      language: '@language_en_gb'
      value: Objectives
  -
    fields:
      placeholder: '@investigations_tools_contributory_factors'
      language: '@language_en_gb'
      value: 'Contributory Factors'
  -
    fields:
      placeholder: '@investigations_tools_add'
      language: '@language_en_gb'
      value: 'Add Tool'
  -
    fields:
      placeholder: '@investigations_tools_no_tools'
      language: '@language_en_gb'
      value: 'No Tools selected for this Investigation Level'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_values'
      language: '@language_en_gb'
      value: Values
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_no_values'
      language: '@language_en_gb'
      value: 'This Clinical Measurement has no Values'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_add_value'
      language: '@language_en_gb'
      value: 'Add Value'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_edit_value'
      language: '@language_en_gb'
      value: 'Edit Value'
  -
    fields:
      placeholder: '@investigations_components_clinical_measurements_start_date'
      language: '@language_en_gb'
      value: 'Start Date'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_id'
      language: '@language_en_gb'
      value: 'Investigation ID'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_id_label'
      language: '@language_en_gb'
      value: 'Investigation ID'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_title'
      language: '@language_en_gb'
      value: 'Investigation Title'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_title_label'
      language: '@language_en_gb'
      value: 'Investigation Title'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_description'
      language: '@language_en_gb'
      value: 'Investigation Description'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_description_label'
      language: '@language_en_gb'
      value: 'Investigation Description'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_target_due_date'
      language: '@language_en_gb'
      value: 'Investigation Target Due Date'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_target_due_date_label'
      language: '@language_en_gb'
      value: 'Investigation Target Due Date'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_level'
      language: '@language_en_gb'
      value: 'Investigation Level'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_level_label'
      language: '@language_en_gb'
      value: 'Investigation Level'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_priority'
      language: '@language_en_gb'
      value: 'Investigation Priority'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_priority_label'
      language: '@language_en_gb'
      value: 'Investigation Priority'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_status'
      language: '@language_en_gb'
      value: 'Investigation Status'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_status_label'
      language: '@language_en_gb'
      value: 'Investigation Status'
  -
    fields:
      placeholder: '@forms_investigation_value_low'
      language: '@language_en_gb'
      value: Low
  -
    fields:
      placeholder: '@forms_investigation_value_medium'
      language: '@language_en_gb'
      value: Medium
  -
    fields:
      placeholder: '@forms_investigation_value_high'
      language: '@language_en_gb'
      value: High
  -
    fields:
      placeholder: '@forms_investigation_title'
      language: '@language_en_gb'
      value: 'Investigations Form 1'
  -
    fields:
      placeholder: '@forms_investigation_summary'
      language: '@language_en_gb'
      value: 'This is the summary for the first Investigations form'
  -
    fields:
      placeholder: '@forms_investigation_section_title_and_details'
      language: '@language_en_gb'
      value: 'Title and Details'
  -
    fields:
      placeholder: '@forms_investigation_section_level'
      language: '@language_en_gb'
      value: 'Investigation Level'
  -
    fields:
      placeholder: '@forms_investigation_field_level_select'
      language: '@language_en_gb'
      value: 'Select a Level'
  -
    fields:
      placeholder: '@forms_investigation_events_source'
      language: '@language_en_gb'
      value: 'This Investigation is linked to Event #{{eventId}}'
  -
    fields:
      placeholder: '@forms_investigation_events_view'
      language: '@language_en_gb'
      value: 'View Event Record'
  -
    fields:
      placeholder: '@investigations_contributory_factors_saved_successfully'
      language: '@language_en_gb'
      value: 'Contributory factor saved successfully'
  -
    fields:
      placeholder: '@investigations_investigation_banners_complete'
      language: '@language_en_gb'
      value: 'This Investigation is Complete'
  -
    fields:
      placeholder: '@investigations_investigation_banners_locked'
      language: '@language_en_gb'
      value: 'This record is locked by {{name}} since {{date}}'
  -
    fields:
      placeholder: '@investigations_investigation_error_locked'
      language: '@language_en_gb'
      value: 'The record is locked and cannot be updated'
  -
    fields:
      placeholder: '@investigations_investigation_error_edit_state'
      language: '@language_en_gb'
      value: 'Completed and rejected investigations cannot be edited'
  -
    fields:
      placeholder: '@investigations_investigation_banners_rejected'
      language: '@language_en_gb'
      value: 'This Investigation has been Rejected by {{name}} on {{date}}'
  -
    fields:
      placeholder: '@investigations_investigation_banners_decommissioned'
      language: '@language_en_gb'
      value: 'This Investigation has been Decommissioned by {{name}} on {{date}}'
  -
    fields:
      placeholder: '@investigations_investigation_banners_reopen'
      language: '@language_en_gb'
      value: 'Reopen Investigation'
  -
    fields:
      placeholder: '@investigations_investigation_reopened_successfully'
      language: '@language_en_gb'
      value: 'Investigation reopened successfully'
  -
    fields:
      placeholder: '@investigations_datasource_investigation_levels'
      language: '@language_en_gb'
      value: 'Investigation Levels'
  -
    fields:
      placeholder: '@investigations_datasource_investigation_priorities'
      language: '@language_en_gb'
      value: 'Investigation Priorities'
  -
    fields:
      placeholder: '@investigations_datasource_investigation_statuses'
      language: '@language_en_gb'
      value: 'Investigation Statuses'
  -
    fields:
      placeholder: '@investigations_form_type_investigation'
      language: '@language_en_gb'
      value: 'Investigation Form'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_rejection'
      language: '@language_en_gb'
      value: 'Reason for Rejection'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_rejection_label'
      language: '@language_en_gb'
      value: 'Reason for Rejection'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioning_title'
      language: '@language_en_gb'
      value: 'Reason for Decommissioning'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioning_label'
      language: '@language_en_gb'
      value: 'Reason for Decommissioning'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_title'
      language: '@language_en_gb'
      value: 'Endorse Investigation Report'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_label'
      language: '@language_en_gb'
      value: 'Endorse Investigation Report'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_title'
      language: '@language_en_gb'
      value: 'Authorised to endorse the Investigation Report on behalf of the CE?'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_label'
      language: '@language_en_gb'
      value: 'Authorised to endorse the Investigation Report on behalf of the CE?'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@forms_investigation_errors_unauthorised_to_decommission'
      language: '@language_en_gb'
      value: 'Decommissioning Not Authorised'
  -
    fields:
      placeholder: '@investigations_success_template_document_attached'
      language: '@language_en_gb'
      value: 'Successfully attached document from template'
  -
    fields:
      placeholder: '@investigations_investigation_location'
      language: '@language_en_gb'
      value: 'Location'
  -
    fields:
      placeholder: '@investigations_nav_admin_events'
      language: '@language_en_gb'
      value: 'Events Config'
  -
    fields:
      placeholder: '@investigation_fields_investigation_report_due_label'
      language: '@language_en_gb'
      value: 'Investigation Report Due'
  -
    fields:
      placeholder: '@investigation_fields_investigation_report_due_title'
      language: '@language_en_gb'
      value: 'Investigation Report Due'
  -
    fields:
      placeholder: '@investigation_fields_feedback_due_to_staff_label'
      language: '@language_en_gb'
      value: 'Feedback Due to Staff'
  -
    fields:
      placeholder: '@investigation_fields_feedback_due_to_staff_title'
      language: '@language_en_gb'
      value: 'Feedback Due to Staff'
  -
    fields:
      placeholder: '@investigation_fields_feedback_due_to_patient_support_person_label'
      language: '@language_en_gb'
      value: 'Feedback Due to Patient/Support Person'
  -
    fields:
      placeholder: '@investigation_fields_feedback_due_to_patient_support_person_title'
      language: '@language_en_gb'
      value: 'Feedback Due to Patient/Support Person'

  # Location types
  - fields:
      placeholder: '@investigation_location_type_title'
      language: '@language_en_gb'
      value: 'Location Type'
  - fields:
      placeholder: '@investigation_location_type_label'
      language: '@language_en_gb'
      value: 'Type'
  - fields:
      placeholder: '@investigation_location_type'
      language: '@language_en_gb'
      value: 'Type'
  - fields:
      placeholder: '@investigation_location_type_origination'
      language: '@language_en_gb'
      value: 'Origination'
  - fields:
      placeholder: '@investigation_location_type_discovery'
      language: '@language_en_gb'
      value: 'Discovery'
  - fields:
      placeholder: '@investigation_document_field_teamleader'
      language: '@language_en_gb'
      value: 'Team Leader'
  - fields:
      placeholder: '@investigation_document_field_teammember'
      language: '@language_en_gb'
      value: 'Team Member'
  - fields:
      placeholder: '@investigations_datasource_contact_roles'
      language: '@language_en_gb'
      value: 'Investigation Contact Roles'
  - fields:
      placeholder: '@investigations_events_event_not_pending'
      language: '@language_en_gb'
      value: 'Event is not pending'
  - fields:
      placeholder: '@investigation_service_type_title'
      value: 'Investigation Service Type'
      language: '@language_en_gb'
  - fields:
      placeholder: '@investigation_service_type_label'
      value: 'Point Of'
      language: '@language_en_gb'
  - fields:
      placeholder: '@investigation_service_type_origination'
      value: 'Origination'
      language: '@language_en_gb'
  - fields:
      placeholder: '@investigation_service_type_discovery'
      value: 'Discovery'
      language: '@language_en_gb'
  - fields:
      placeholder: '@common_exception_template_invalid_target_type'
      language: '@language_en_gb'
      value: 'Template Type was not found'
  - fields:
      placeholder: '@common_approved'
      language: '@language_en_gb'
      value: 'Approved'
  - fields:
      placeholder: '@common_rejected'
      language: '@language_en_gb'
      value: 'Rejected'
  - fields:
      placeholder: '@common_document_page_no_of_pages'
      language: '@language_en_gb'
      value: 'Page {PAGENO} of {nb}'
  - fields:
      placeholder: '@investigations_document_health_districtnetwork'
      language: '@language_en_gb'
      value: 'Health District/Network'
  - fields:
      placeholder: '@investigations_document_final_investigation_report'
      language: '@language_en_gb'
      value: 'Final Investigation Report'
  - fields:
      placeholder: '@investigations_document_reference_numbers_where_applicable'
      language: '@language_en_gb'
      value: 'Reference Numbers (where applicable)'
  - fields:
      placeholder: '@investigations_document_moh_rib_no'
      language: '@language_en_gb'
      value: 'MoH RIB No'
  - fields:
      placeholder: '@investigations_document_ims_rib_no'
      language: '@language_en_gb'
      value: 'ims+ RIB No'
  - fields:
      placeholder: '@investigations_document_lhd_ref_no'
      language: '@language_en_gb'
      value: 'LHD Ref No'
  - fields:
      placeholder: '@investigations_document_lhd_rib_no'
      language: '@language_en_gb'
      value: 'LHD RIB No'
  - fields:
      placeholder: '@investigations_document_ims_investigation_no'
      language: '@language_en_gb'
      value: 'ims+ Investigation No'
  - fields:
      placeholder: '@investigations_document_incident_details'
      language: '@language_en_gb'
      value: 'Incident Details'
  - fields:
      placeholder: '@investigations_document_date_of_incident'
      language: '@language_en_gb'
      value: 'Date of Incident'
  - fields:
      placeholder: '@investigations_document_date_of_incident_notification_in_ims'
      language: '@language_en_gb'
      value: 'Date of Incident Notification in ims+'
  - fields:
      placeholder: '@investigations_document_reporting_details'
      language: '@language_en_gb'
      value: 'Reporting Details'
  - fields:
      placeholder: '@investigations_document_position_responsible_for_feedback_to_staff'
      language: '@language_en_gb'
      value: 'Position responsible for feedback to staff'
  - fields:
      placeholder: '@investigations_document_by_when'
      language: '@language_en_gb'
      value: 'By When'
  - fields:
      placeholder: '@investigations_document_position_responsible_for_feedback_to_patientsupport_person'
      language: '@language_en_gb'
      value: 'Position responsible for feedback to patient/support person'
  - fields:
      placeholder: '@investigations_document_final_investigation_report_signed_off_by_investigation_team_on'
      language: '@language_en_gb'
      value: 'Final investigation report signed off by Investigation Team on'
  - fields:
      placeholder: '@investigations_document_date_report_due_to_ce'
      language: '@language_en_gb'
      value: 'Date report due to CE'
  - fields:
      placeholder: '@investigations_document_date_signed_by_ce'
      language: '@language_en_gb'
      value: 'Date signed by CE'
  - fields:
      placeholder: '@investigations_document_date_due_to_be_submitted_to_nsw_ministry'
      language: '@language_en_gb'
      value: 'Date due to be submitted to NSW Ministry of Health'
  - fields:
      placeholder: '@investigations_document_date_submitted_to_nsw_ministry_of_health'
      language: '@language_en_gb'
      value: 'Date submitted to NSW Ministry of Health'
  - fields:
      placeholder: '@investigations_document_notification_of_decommissioning_of_investigation'
      language: '@language_en_gb'
      value: 'Notification of decommissioning of Investigation'
  - fields:
      placeholder: '@investigations_document_investigation_decommissioned'
      language: '@language_en_gb'
      value: 'Investigation decommissioned'
  - fields:
      placeholder: '@investigations_document_reason_for_decommissioning'
      language: '@language_en_gb'
      value: 'Reason for decommissioning'
  - fields:
      placeholder: '@investigations_document_comments'
      language: '@language_en_gb'
      value: 'Comments'
  - fields:
      placeholder: '@investigations_document_referral_to_other_committeesagencies'
      language: '@language_en_gb'
      value: 'Referral to other committees/agencies'
  - fields:
      placeholder: '@investigations_document_other_please_specify'
      language: '@language_en_gb'
      value: 'Other (please specify)'
  - fields:
      placeholder: '@investigations_document_contact_details'
      language: '@language_en_gb'
      value: 'Contact Details'
  - fields:
      placeholder: '@investigations_document_lhd_contact_person'
      language: '@language_en_gb'
      value: 'LHD Contact Person'
  - fields:
      placeholder: '@investigations_document_telephone_number'
      language: '@language_en_gb'
      value: 'Telephone Number'
  - fields:
      placeholder: '@investigations_document_email_address'
      language: '@language_en_gb'
      value: 'Email Address'
  - fields:
      placeholder: '@investigations_document_description_of_incident_that_was_investigated'
      language: '@language_en_gb'
      value: 'Description of incident that was investigated'
  - fields:
      placeholder: '@investigations_document_chronology_of_events'
      language: '@language_en_gb'
      value: 'Chronology of events'
  - fields:
      placeholder: '@investigations_document_summary_of_investigation_team_findings_and_recommendations'
      language: '@language_en_gb'
      value: 'Summary of Investigation Team findings and recommendations'
  - fields:
      placeholder: '@investigations_document_following_the_investigation_the_investigation_team_was_able'
      language: '@language_en_gb'
      value: 'Following the investigation, the Investigation Team was able to identify'
  - fields:
      placeholder: '@investigations_document_for_internal_use_only'
      language: '@language_en_gb'
      value: 'For internal use only'
  - fields:
      placeholder: '@investigations_document_attached_in_trim'
      language: '@language_en_gb'
      value: 'Attached in TRIM'
  - fields:
      placeholder: '@investigations_document_copied_to_the_cec'
      language: '@language_en_gb'
      value: 'Copied to the CEC'
  - fields:
      placeholder: '@investigations_document_documentation_of_causation_statements_is_a_legislative_requirement'
      language: '@language_en_gb'
      value: 'Documentation of causation statements is a legislative requirement. All causation statements must comply with the Rules of Causation. Each contributing factor displayed must be addressed in the action plan. Describe the contributing factor and categorise appropriately.'
  - fields:
      placeholder: '@investigations_document_filed'
      language: '@language_en_gb'
      value: 'Filed'
  - fields:
      placeholder: '@investigations_document_file_no'
      language: '@language_en_gb'
      value: 'File No'
  - fields:
      placeholder: '@investigations_document_table_1_contributing_factors_table'
      language: '@language_en_gb'
      value: 'Table 1 - Contributing Factors Table'
  - fields:
      placeholder: '@investigations_document_a_requirement_when_causes_have_been_identified'
      language: '@language_en_gb'
      value: '(a requirement when causes have been identified)'
  - fields:
      placeholder: '@investigations_document_item_no'
      language: '@language_en_gb'
      value: 'Item No.'
  - fields:
      placeholder: '@investigations_document_description_of_contributory_factor'
      language: '@language_en_gb'
      value: 'Description of contributory factor'
  - fields:
      placeholder: '@investigations_document_category'
      language: '@language_en_gb'
      value: 'Category'
  - fields:
      placeholder: '@investigations_document_table_2_investigation_team_recommendations'
      language: '@language_en_gb'
      value: 'Table 2 - Investigation Team Recommendations'
  - fields:
      placeholder: '@investigations_document_caustation_statement_item_no'
      language: '@language_en_gb'
      value: 'Causation statement item no.'
  - fields:
      placeholder: '@investigations_document_recommendation_classification'
      language: '@language_en_gb'
      value: 'Recommendation Classification'
  - fields:
      placeholder: '@investigations_document_recommendation_category'
      language: '@language_en_gb'
      value: 'Category'
  - fields:
      placeholder: '@investigations_document_position_of_person_responsible_for_implementing_recommendations'
      language: '@language_en_gb'
      value: 'Position of person responsible for implementing recommendations'
  - fields:
      placeholder: '@investigations_document_outcome_measures'
      language: '@language_en_gb'
      value: 'Outcome measures'
  - fields:
      placeholder: '@investigations_document_completion_date'
      language: '@language_en_gb'
      value: 'Completion date'
  - fields:
      placeholder: '@investigations_document_management_concurrence'
      language: '@language_en_gb'
      value: 'Management concurrence'
  - fields:
      placeholder: '@investigations_document_table_3_systems_improvement_opportunities_unrelated_to'
      language: '@language_en_gb'
      value: 'Table 3 - Systems improvement opportunities unrelated to contributing factors'
  - fields:
      placeholder: '@investigations_document_modification_of_these_issues_would_not_have_helped'
      language: '@language_en_gb'
      value: '(modification of these issues would not have helped to prevent the event)'
  - fields:
      placeholder: '@investigations_document_issues_for_improvement'
      language: '@language_en_gb'
      value: 'Issues for Improvement'
  - fields:
      placeholder: '@investigations_document_description_of_improvement'
      language: '@language_en_gb'
      value: 'Description of Improvement'
  - fields:
      placeholder: '@investigations_document_investigation_report_final_sign_off'
      language: '@language_en_gb'
      value: 'Investigation Report Final Sign Off'
  - fields:
      placeholder: '@investigations_document_name'
      language: '@language_en_gb'
      value: 'Name'
  - fields:
      placeholder: '@investigations_document_endorsed'
      language: '@language_en_gb'
      value: 'Endorsed'
  - fields:
      placeholder: '@investigations_document_reason_for_nonendorsement_of_recommendations'
      language: '@language_en_gb'
      value: 'Reason for non-endorsement of recommendations'
  - fields:
      placeholder: '@investigations_document_cemanagement_alternate_recommendations'
      language: '@language_en_gb'
      value: 'CE/Management Alternate Recommendations'
  - fields:
      placeholder: '@investigations_document_footnote_1_marker'
      language: '@language_en_gb'
      value: '1.'
  - fields:
      placeholder: '@investigations_document_footnote_1'
      language: '@language_en_gb'
      value: 'The number here relates to the numbered causation statement in Table 1 CONTRIBUTING FACTORS TABLE'
  - fields:
      placeholder: '@investigations_document_recommendation'
      language: '@language_en_gb'
      value: 'Recommendation'
  - fields:
      placeholder: '@investigations_document_title'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@investigations_document_id'
      language: '@language_en_gb'
      value: 'ID'
  - fields:
      placeholder: '@investigations_document_date'
      language: '@language_en_gb'
      value: 'Date'
  - fields:
      placeholder: '@investigations_document_on_at_time_hours'
      language: '@language_en_gb'
      value: 'On {{ date }} at {{ time }} hours'
  - fields:
      placeholder: '@investigations_template_template_f_title'
      language: '@language_en_gb'
      value: Investigation Team Appointment
  - fields:
      placeholder: '@investigations_template_template_f_description'
      language: '@language_en_gb'
      value: Investigation Team Appointment document
  - fields:
      placeholder: '@investigations_template_template_f_filename'
      language: '@language_en_gb'
      value: new-team-appointment
  - fields:
      placeholder: '@investigations_template_template_teamletter_title'
      language: '@language_en_gb'
      value: Investigation Letter to Team Member
  - fields:
      placeholder: '@investigations_template_template_teamletter_description'
      language: '@language_en_gb'
      value: Investigation Letter to Team Member document
  - fields:
      placeholder: '@investigations_template_template_teamletter_filename'
      language: '@language_en_gb'
      value: investigation-letter-to-team-member
  - fields:
      placeholder: '@investigations_template_template_j_title'
      language: '@language_en_gb'
      value: Staff Notification of Investigation
  - fields:
      placeholder: '@investigations_template_template_j_description'
      language: '@language_en_gb'
      value: Staff Notification of Investigation document
  - fields:
      placeholder: '@investigations_template_template_j_filename'
      language: '@language_en_gb'
      value: staff.notification
  - fields:
      placeholder: '@investigations_template_final_report_l1_title'
      language: '@language_en_gb'
      value: Investigation Decommissioning Report
  - fields:
      placeholder: '@investigations_template_final_report_l1_description'
      language: '@language_en_gb'
      value: Investigation Decommissioning Report
  - fields:
      placeholder: '@investigations_template_final_report_l1_filename'
      language: '@language_en_gb'
      value: investigation-decommissioning-report
  - fields:
      placeholder: '@investigations_template_final_report_l2_title'
      language: '@language_en_gb'
      value: Investigation Final Report
  - fields:
      placeholder: '@investigations_template_final_report_l2_description'
      language: '@language_en_gb'
      value: Investigation Final Report
  - fields:
      placeholder: '@investigations_template_final_report_l2_filename'
      language: '@language_en_gb'
      value: investigation-final-report
  - fields:
      placeholder: '@investigation_contributory_factors_removed_successfully'
      language: '@language_en_gb'
      value: Contributory Factor removed successfully
  - fields:
      placeholder: '@forms_investigation_field_services_title'
      language: '@language_en_gb'
      value: Services
  - fields:
      placeholder: '@forms_investigation_field_services_label'
      language: '@language_en_gb'
      value: Services
  - fields:
      placeholder: '@forms_investigation_field_locations_title'
      language: '@language_en_gb'
      value: Locations
  - fields:
      placeholder: '@forms_investigation_field_locations_label'
      language: '@language_en_gb'
      value: Locations
  - fields:
      placeholder: '@forms_investigation_field_contacts_title'
      language: '@language_en_gb'
      value: Contacts
  - fields:
      placeholder: '@forms_investigation_field_contacts_label'
      language: '@language_en_gb'
      value: Contacts
  - fields:
      placeholder: '@placeholder_investigations_success_third_party_events_rejection_reason_required'
      language: '@language_en_gb'
      value: A reason must be provided when rejecting an event
  - fields:
      placeholder: '@investigations_user_forbidden_error'
      language: '@language_en_gb'
      value: You may not edit your own permissions
  - fields:
      placeholder: '@investigations_recommendations_contributory_factor_modal_title'
      language: '@language_en_gb'
      value: 'Recommendation: {{recommendationTitle}}'
  - fields:
      placeholder: '@investigations_recommendations_contributory_factor'
      language: '@language_en_gb'
      value: Select a Contributory Factor
  - fields:
      placeholder: '@investigations_recommendations_contributory_factor_no_title'
      language: '@language_en_gb'
      value: Untitled Contributory Factor
  - fields:
      placeholder: '@investigations_recommendations_contributory_factor_no_factor'
      language: '@language_en_gb'
      value: New Contributory Factor
  - fields:
      placeholder: '@investigations_recommendations_contributory_factor_save'
      language: '@language_en_gb'
      value: Save Recommendation
  - fields:
      placeholder: '@investigations_recommendations_save_error'
      language: '@language_en_gb'
      value: An error occurred whilst saving the Recommendation
  - fields:
      placeholder: '@investigations_third_party_events_events_get_collection'
      language: '@language_en_gb'
      value: Failed to read Events Collection
  - fields:
      placeholder: '@investigations_components_participantform_contact_unavailable_label'
      language: '@language_en_gb'
      value: Contact Unavailable
  - fields:
      placeholder: '@investigations_components_participantform_contact_unavailable_message'
      language: '@language_en_gb'
      value: You do not have access to this contact
  - fields:
      placeholder: '@placeholder_investigations_success_investigation_reopened'
      language: '@language_en_gb'
      value: Investigation reopened successfully
  - fields:
      placeholder: '@placeholder_investigations_success_investigation_deleted'
      language: '@language_en_gb'
      value: Investigation deleted successfully
  -  fields:
      placeholder: '@placeholder_investigations_confirm_clinical_measurement_delete'
      language: '@language_en_gb'
      value: Are you sure you want to delete this measurement? This will also delete the values associated with this measurement.
  - fields:
      placeholder: '@placeholder_investigations_success_clinical_measurement_delete'
      language: '@language_en_gb'
      value: Clinical Measurement deleted successfully.
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_actions'
      language: '@language_en_gb'
      value: Actions
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_action_edit_clinical_measurement'
      language: '@language_en_gb'
      value: Edit
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_action_delete_clinical_measurement'
      language: '@language_en_gb'
      value: Delete
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_type_blood_glucose'
      language: '@language_en_gb'
      value: Blood Glucose
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_blood_glucose'
      language: '@language_en_gb'
      value: Blood Glucose
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_molar_concentration'
      language: '@language_en_gb'
      value: Molar Concentration
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_type_capillary_refill'
      language: '@language_en_gb'
      value: Capillary Refill
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_capillary_refill'
      language: '@language_en_gb'
      value: Capillary Refill
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_seconds'
      language: '@language_en_gb'
      value: Seconds
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_type_news2_score'
      language: '@language_en_gb'
      value: NEWS2 Score
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_news2_score'
      language: '@language_en_gb'
      value: NEWS2 Score
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_type_etco2_score'
      language: '@language_en_gb'
      value: ETCO2 Score
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_etco2_score'
      language: '@language_en_gb'
      value: ETCO2 Score
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurement_unit_kilopascals'
      language: '@language_en_gb'
      value: Kilopascals
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_type_pupils'
      language: '@language_en_gb'
      value: Pupils
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_fields_left_pupil_size'
      language: '@language_en_gb'
      value: Left Pupil Size
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_fields_left_pupil_reactive'
      language: '@language_en_gb'
      value: Left Pupil Reactive?
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_fields_right_pupil_size'
      language: '@language_en_gb'
      value: Right Pupil Size
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_fields_right_pupil_reactive'
      language: '@language_en_gb'
      value: Right Pupil Reactive?
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_type_avcpu'
      language: '@language_en_gb'
      value: AVCPU
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_unit_avcpu'
      language: '@language_en_gb'
      value: AVCPU
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_avcpu_alert'
      language: '@language_en_gb'
      value: Alert
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_avcpu_confusion'
      language: '@language_en_gb'
      value: Confusion
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_avcpu_voice'
      language: '@language_en_gb'
      value: Voice
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_avcpu_pain'
      language: '@language_en_gb'
      value: Pain
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_avcpu_unresponsive'
      language: '@language_en_gb'
      value: Unresponsive
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_pupil_reactive_reactive'
      language: '@language_en_gb'
      value: Reactive
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_pupil_reactive_non_reactive'
      language: '@language_en_gb'
      value: Non-Reactive
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_errors_empty_values'
      language: '@language_en_gb'
      value: One or more measurements have no values
  - fields:
      placeholder: '@placeholder_investigations_clinical_measurements_errors_invalid_values'
      language: '@language_en_gb'
      value: One or more measurement values are invalid
