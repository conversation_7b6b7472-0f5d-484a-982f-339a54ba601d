entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@forms_investigation_events_source', language: '@language_ar', value: 'هذا التحقيق مرتبط بالحدث رقم {{eventId}}' } }
    - { fields: { placeholder: '@forms_investigation_events_view', language: '@language_ar', value: 'عرض سجل الأحداث' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_description', language: '@language_ar', value: 'وصف التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_id', language: '@language_ar', value: 'معرف التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_level', language: '@language_ar', value: 'مستوى التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_priority', language: '@language_ar', value: 'أولوية التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_status', language: '@language_ar', value: 'حالة التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_target_due_date', language: '@language_ar', value: 'التحقيق المستحق تاريخ الاستحقاق' } }
    - { fields: { placeholder: '@forms_investigation_field_investigation_title', language: '@language_ar', value: 'عنوان التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_field_level_select', language: '@language_ar', value: 'اختر المستوى' } }
    - { fields: { placeholder: '@forms_investigation_section_level', language: '@language_ar', value: 'مستوى التحقيق' } }
    - { fields: { placeholder: '@forms_investigation_section_title_and_details', language: '@language_ar', value: 'العنوان والتفاصيل' } }
    - { fields: { placeholder: '@forms_investigation_summary', language: '@language_ar', value: 'هذا هو ملخص نموذج التحقيقات الأول' } }
    - { fields: { placeholder: '@forms_investigation_title', language: '@language_ar', value: 'تحقيقات نموذج 1' } }
    - { fields: { placeholder: '@forms_investigation_value_high', language: '@language_ar', value: متوسط } }
    - { fields: { placeholder: '@forms_investigation_value_low', language: '@language_ar', value: منخفض } }
    - { fields: { placeholder: '@forms_investigation_value_medium', language: '@language_ar', value: متوسط } }
    - { fields: { placeholder: '@investigations_admin_is_default', language: '@language_ar', value: افتراضي؟ } }
    - { fields: { placeholder: '@investigations_attachments_no_attachments', language: '@language_ar', value: 'هذا التحقيق ليس له أي مرفقات' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_add', language: '@language_ar', value: 'إضافة القياس السريري' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_add_value', language: '@language_ar', value: 'إضافة قيمة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_details', language: '@language_ar', value: 'تفاصيل القياس السريري' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_edit_value', language: '@language_ar', value: 'تحرير القيمة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_form_invalid', language: '@language_ar', value: 'يرجى ملء جميع الحقول المطلوبة في نموذج القياس' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_chart', language: '@language_ar', value: 'تحميل الرسم البياني' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_clinical_measurement', language: '@language_ar', value: 'تحميل القياس السريري' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_error', language: '@language_ar', value: 'حدث خطأ أثناء استرجاع القياسات السريرية' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_grid', language: '@language_ar', value: 'تحميل الشبكة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_new', language: '@language_ar', value: 'القياس السريري الجديد' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_no_measurements', language: '@language_ar', value: 'هذا التحقيق حاليا ليس لديه قياسات السريرية' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_no_values', language: '@language_ar', value: 'هذا القياس السريري ليس له قيم' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_occurrence_date', language: '@language_ar', value: 'تاريخ الوقوع' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_please_add_values', language: '@language_ar', value: 'مطلوب على الأقل قيمة قياس واحدة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_plural', language: '@language_ar', value: 'القياسات السريرية' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_save_error', language: '@language_ar', value: 'خطأ في حفظ القياس السريري' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_singular', language: '@language_ar', value: 'القياس السريري' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_start_date', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_blood_pressure', language: '@language_ar', value: 'ضغط الدم' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_heart_rate', language: '@language_ar', value: 'معدل ضربات القلب' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_oxygen_saturation', language: '@language_ar', value: 'تشبع الأكسجين' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_pain_score', language: '@language_ar', value: 'نقاط الألم' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_respiratory_rate', language: '@language_ar', value: 'معدل التنفس' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_select', language: '@language_ar', value: 'اختر صنف' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_type_temperature', language: '@language_ar', value: 'درجة الحرارة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_beats_per_minute', language: '@language_ar', value: 'يدق في الدقيقة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_breaths_per_minute', language: '@language_ar', value: 'الأنفاس في الدقيقة' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_degrees_celcius', language: '@language_ar', value: 'درجات مئوية' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_diastolic', language: '@language_ar', value: الانبساطي } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_millimeters_of_mercury', language: '@language_ar', value: 'ملليمتر من الزئبق' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_oxygen_saturation', language: '@language_ar', value: 'تشبع الأكسجين' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_score', language: '@language_ar', value: 'أحرز هدفاً' } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_systolic', language: '@language_ar', value: الانقباضي } }
    - { fields: { placeholder: '@investigations_components_clinical_measurements_values', language: '@language_ar', value: القيم } }
    - { fields: { placeholder: '@investigations_components_eventchronology', language: '@language_ar', value: 'التسلسل الزمني للحدث' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_actions', language: '@language_ar', value: أفعال } }
    - { fields: { placeholder: '@investigations_components_eventchronology_addevent', language: '@language_ar', value: 'أضف حدث' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_addfirstmessage', language: '@language_ar', value: 'انقر فوق الزر أعلاه لإضافة الحدث الأول' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_delete', language: '@language_ar', value: حذف } }
    - { fields: { placeholder: '@investigations_components_eventchronology_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@investigations_components_eventchronology_editevent', language: '@language_ar', value: 'تحرير الحدث' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_enddate', language: '@language_ar', value: 'تاريخ الانتهاء' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_person', language: '@language_ar', value: شخص } }
    - { fields: { placeholder: '@investigations_components_eventchronology_processmaps', language: '@language_ar', value: 'خرائط العمليات' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_role', language: '@language_ar', value: وظيفة } }
    - { fields: { placeholder: '@investigations_components_eventchronology_source', language: '@language_ar', value: مصدر } }
    - { fields: { placeholder: '@investigations_components_eventchronology_startdate', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@investigations_components_eventchronology_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@investigations_components_eventform_addanotherevent', language: '@language_ar', value: 'إضافة حدث آخر؟' } }
    - { fields: { placeholder: '@investigations_components_eventform_cancel', language: '@language_ar', value: إلغاء } }
    - { fields: { placeholder: '@investigations_components_eventform_deleteevent', language: '@language_ar', value: 'حذف الحدث' } }
    - { fields: { placeholder: '@investigations_components_eventform_description', language: '@language_ar', value: وصف } }
    - { fields: { placeholder: '@investigations_components_eventform_end_date', language: '@language_ar', value: 'تاريخ الانتهاء' } }
    - { fields: { placeholder: '@investigations_components_eventform_eventdetails', language: '@language_ar', value: 'تفاصيل الحدث' } }
    - { fields: { placeholder: '@investigations_components_eventform_participant', language: '@language_ar', value: مشارك } }
    - { fields: { placeholder: '@investigations_components_eventform_saveevent', language: '@language_ar', value: 'حفظ الحدث' } }
    - { fields: { placeholder: '@investigations_components_eventform_source', language: '@language_ar', value: مصدر } }
    - { fields: { placeholder: '@investigations_components_eventform_startdate', language: '@language_ar', value: 'تاريخ البدء' } }
    - { fields: { placeholder: '@investigations_components_eventform_title', language: '@language_ar', value: عنوان } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram', language: '@language_ar', value: 'مخطط هيكل السمكة' } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram_add_root_node', language: '@language_ar', value: 'إضافة عقدة الجذر' } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram_fishbone_diagram_title', language: '@language_ar', value: 'عنوان الهيكل السمكي Fishbone' } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram_nodes_confirm_delete', language: '@language_ar', value: 'هل أنت متأكد من أنك تريد حذف هذه النقطة؟' } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram_save', language: '@language_ar', value: 'حفظ وتحديث مخطط  الهيكل السمكي Fishbone' } }
    - { fields: { placeholder: '@investigations_components_fishbone_diagram_saved_successfully', language: '@language_ar', value: 'تم حفظ مخطط الهيكل السمكي Fishbone بنجاح' } }
    - { fields: { placeholder: '@investigations_components_fivewhys', language: '@language_ar', value: 'خمسة أسباب' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_addcontributoryfactor', language: '@language_ar', value: 'إضافة عامل المساهمة' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_addnew', language: '@language_ar', value: 'أضف جديد خمسة إدخالات' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_addwhy', language: '@language_ar', value: 'أضف لماذا' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_areyousure', language: '@language_ar', value: 'هل أنت واثق؟' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_contributoryfactors', language: '@language_ar', value: 'العوامل المساهمة' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_contributoryfactortitle', language: '@language_ar', value: 'عنوان عامل المساهمة' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_delete', language: '@language_ar', value: حذف } }
    - { fields: { placeholder: '@investigations_components_fivewhys_newproblem', language: '@language_ar', value: 'مشكلة جديدة' } }
    - { fields: { placeholder: '@investigations_components_fivewhys_problem', language: '@language_ar', value: مشكلة } }
    - { fields: { placeholder: '@investigations_components_fivewhys_save', language: '@language_ar', value: حفظ } }
    - { fields: { placeholder: '@investigations_components_fivewhys_why', language: '@language_ar', value: 'لماذا ؟' } }
    - { fields: { placeholder: '@investigations_components_participantform_closecontactform', language: '@language_ar', value: 'إغلاق نموذج الاتصال' } }
    - { fields: { placeholder: '@investigations_components_participantform_existingparticipant', language: '@language_ar', value: 'مشارك موجود' } }
    - { fields: { placeholder: '@investigations_components_participantform_newcontact', language: '@language_ar', value: 'جهة اتصال جديدة' } }
    - { fields: { placeholder: '@investigations_components_participantform_newparticipant', language: '@language_ar', value: 'مشارك جديد' } }
    - { fields: { placeholder: '@investigations_components_participantform_newparticipantfromcontact', language: '@language_ar', value: 'مشارك جديد (من سجل الاتصال)' } }
    - { fields: { placeholder: '@investigations_components_participantform_participant', language: '@language_ar', value: مشارك } }
    - { fields: { placeholder: '@investigations_components_participantform_participant_name', language: '@language_ar', value: 'اسم المشارك' } }
    - { fields: { placeholder: '@investigations_components_participantform_participantname', language: '@language_ar', value: 'اسم المشارك' } }
    - { fields: { placeholder: '@investigations_components_participantform_reset_participant', language: '@language_ar', value: 'إعادة تعيين المشارك' } }
    - { fields: { placeholder: '@investigations_components_participantform_role', language: '@language_ar', value: وظيفة } }
    - { fields: { placeholder: '@investigations_components_participantform_saveparticipant', language: '@language_ar', value: 'حفظ المشارك' } }
    - { fields: { placeholder: '@investigations_components_participantform_searchexistingcontacts', language: '@language_ar', value: 'بحث جهات الاتصال الموجودة' } }
    - { fields: { placeholder: '@investigations_components_participantform_usecontact', language: '@language_ar', value: 'استخدام الاتصال' } }
    - { fields: { placeholder: '@investigations_components_participantform_view_contact', language: '@language_ar', value: 'عرض الاتصال' } }
    - { fields: { placeholder: '@investigations_components_safermatrix', language: '@language_ar', value: 'مصفوفة SAFER' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_close', language: '@language_ar', value: قريب } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors', language: '@language_ar', value: 'العوامل المساهمة' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_add', language: '@language_ar', value: إضافة } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_new', language: '@language_ar', value: 'عامل تساهيل جديد' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_no_contributory_factors', language: '@language_ar', value: 'لا توجد عوامل مساهمة' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_reset', language: '@language_ar', value: 'إعادة تعيين' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_save', language: '@language_ar', value: 'حفظ العوامل المساهمة' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_environment', language: '@language_ar', value: 'البيئة / المعدات' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_how', language: '@language_ar', value: 'كيف حدث هذا؟' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_matrix', language: '@language_ar', value: مصفوفة } }
    - { fields: { placeholder: '@investigations_components_safermatrix_opensafermatrix', language: '@language_ar', value: 'افتح المصفوفة SAFER ' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_organisation', language: '@language_ar', value: منظمة } }
    - { fields: { placeholder: '@investigations_components_safermatrix_outcome', language: '@language_ar', value: نتيجة } }
    - { fields: { placeholder: '@investigations_components_safermatrix_patient', language: '@language_ar', value: صبور } }
    - { fields: { placeholder: '@investigations_components_safermatrix_personnel', language: '@language_ar', value: 'شؤون الموظفين' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_process', language: '@language_ar', value: معالجة } }
    - { fields: { placeholder: '@investigations_components_safermatrix_regulatoryagencies', language: '@language_ar', value: 'الهيئات التنظيمية' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_save', language: '@language_ar', value: حفظ } }
    - { fields: { placeholder: '@investigations_components_safermatrix_saveandclose', language: '@language_ar', value: 'حفظ وإغلاق' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_structure', language: '@language_ar', value: بناء } }
    - { fields: { placeholder: '@investigations_components_safermatrix_what', language: '@language_ar', value: 'ماذا حدث؟' } }
    - { fields: { placeholder: '@investigations_components_safermatrix_why', language: '@language_ar', value: 'لماذا حصل هذا؟' } }
    - { fields: { placeholder: '@investigations_components_time_person_grid_add_event', language: '@language_ar', value: 'أضف حدث' } }
    - { fields: { placeholder: '@investigations_components_time_person_grid_edit_participant', language: '@language_ar', value: 'تحرير المشارك' } }
    - { fields: { placeholder: '@investigations_components_time_person_grid_fit_all_events', language: '@language_ar', value: 'تناسب جميع الأحداث' } }
    - { fields: { placeholder: '@investigations_contact_role_doctor', language: '@language_ar', value: طبيب } }
    - { fields: { placeholder: '@investigations_contact_role_nurse', language: '@language_ar', value: ممرضة } }
    - { fields: { placeholder: '@investigations_contact_role_patient', language: '@language_ar', value: صبور } }
    - { fields: { placeholder: '@investigations_contact_role_witness', language: '@language_ar', value: الشاهد } }
    - { fields: { placeholder: '@investigations_contributory_factors_saved_successfully', language: '@language_ar', value: 'عامل المساهمة المحفوظة بنجاح' } }
    - { fields: { placeholder: '@investigations_dashboard_filter_title', language: '@language_ar', value: 'تصفية التحقيقات' } }
    - { fields: { placeholder: '@investigations_dashboard_no_results', language: '@language_ar', value: 'لا توجد تحقيقات مطابقة لمعايير التصفية المحددة.' } }
    - { fields: { placeholder: '@investigations_error_admin_save', language: '@language_ar', value: 'حدث خطأ أثناء تحديث الأدوات الافتراضية' } }
    - { fields: { placeholder: '@investigations_error_attachment_not_found', language: '@language_ar', value: 'لم يتم العثور على المرفق' } }
    - { fields: { placeholder: '@investigations_error_clinical_measurements_save', language: '@language_ar', value: 'حدث خطأ أثناء حفظ القياس السريري' } }
    - { fields: { placeholder: '@investigations_error_clinical_measurements_save_value', language: '@language_ar', value: 'حدث خطأ أثناء حفظ قيمة القياس السريري' } }
    - { fields: { placeholder: '@investigations_error_events_delete', language: '@language_ar', value: 'حدث خطأ أثناء حذف الحدث' } }
    - { fields: { placeholder: '@investigations_error_events_get_collection', language: '@language_ar', value: 'حدث خطأ أثناء استرداد الأحداث' } }
    - { fields: { placeholder: '@investigations_error_file_not_found', language: '@language_ar', value: 'لم يتم العثور على الملف' } }
    - { fields: { placeholder: '@investigations_error_fishbone_not_found', language: '@language_ar', value: 'مخطط الهيكل السمكي Fishbone غير موجود' } }
    - { fields: { placeholder: '@investigations_error_five_whys_delete', language: '@language_ar', value: 'حدث خطأ أثناء حذف مدخلات التحليل السببي   Five Whys' } }
    - { fields: { placeholder: '@investigations_error_five_whys_get_collection', language: '@language_ar', value: 'حدث خطأ أثناء استرداد Five Whys' } }
    - { fields: { placeholder: '@investigations_error_five_whys_save', language: '@language_ar', value: 'حدث خطأ أثناء حفظ مدخلات التحليل السببي Five Whys' } }
    - { fields: { placeholder: '@investigations_error_fivewhy_not_found', language: '@language_ar', value: 'خمسة لماذا لم يتم العثور عليها' } }
    - { fields: { placeholder: '@investigations_error_get_form', language: '@language_ar', value: 'حدث خطأ أثناء استرجاع نموذج التحقيق' } }
    - { fields: { placeholder: '@investigations_error_not_found', language: '@language_ar', value: 'التحقيق غير موجود' } }
    - { fields: { placeholder: '@investigations_error_objectives_delete', language: '@language_ar', value: 'حدث خطأ أثناء حذف الهدف' } }
    - { fields: { placeholder: '@investigations_error_process_maps_save', language: '@language_ar', value: 'حدث خطأ أثناء حفظ خريطة العملية' } }
    - { fields: { placeholder: '@investigations_error_safer_matrix_save', language: '@language_ar', value: 'حدث خطأ أثناء حفظ مصفوفة SAFER' } }
    - { fields: { placeholder: '@investigations_error_safer_matrix_not_found', language: '@language_ar', value: 'مصفوفة أكثر أمانًا' } }
    - { fields: { placeholder: '@investigations_error_third_party_events_create_investigation', language: '@language_ar', value: 'حدث خطأ أثناء إنشاء التحقيق' } }
    - { fields: { placeholder: '@investigations_error_third_party_events_get_collection', language: '@language_ar', value: 'حدث خطأ أثناء استرداد قائمة الأحداث' } }
    - { fields: { placeholder: '@investigations_error_third_party_events_reject', language: '@language_ar', value: 'حدث خطأ أثناء رفض الحدث' } }
    - { fields: { placeholder: '@investigations_events', language: '@language_ar', value: أحداث } }
    - { fields: { placeholder: '@investigations_events_create_investigation_add_users', language: '@language_ar', value: 'إضافة المستخدمين' } }
    - { fields: { placeholder: '@investigations_events_create_investigation_investigation_title', language: '@language_ar', value: 'عنوان التحقيق' } }
    - { fields: { placeholder: '@investigations_events_create_investigation_submit', language: '@language_ar', value: 'إنشاء التحقيق' } }
    - { fields: { placeholder: '@investigations_events_handler', language: '@language_ar', value: معالج } }
    - { fields: { placeholder: '@investigations_events_occurred', language: '@language_ar', value: حدث } }
    - { fields: { placeholder: '@investigations_events_person_affected', language: '@language_ar', value: 'الشخص المتضرر' } }
    - { fields: { placeholder: '@investigations_events_reject_event_reason', language: '@language_ar', value: 'سبب الرفض' } }
    - { fields: { placeholder: '@investigations_events_reject_event_submit', language: '@language_ar', value: 'رفض الحدث' } }
    - { fields: { placeholder: '@investigations_events_tabs_create_investigation', language: '@language_ar', value: 'إنشاء التحقيق' } }
    - { fields: { placeholder: '@investigations_events_tabs_event_summary', language: '@language_ar', value: 'ملخص الحدث' } }
    - { fields: { placeholder: '@investigations_events_tabs_reject_event', language: '@language_ar', value: 'رفض الحدث' } }
    - { fields: { placeholder: '@investigations_investigation_banners_complete', language: '@language_ar', value: 'هذا التحقيق اكتمال' } }
    - { fields: { placeholder: '@investigations_investigation_banners_rejected', language: '@language_ar', value: 'تم رفض هذا التحقيق بواسطة {{name}} في {{date}}' } }
    - { fields: { placeholder: '@investigations_investigation_banners_reopen', language: '@language_ar', value: 'إعادة فتح التحقيق' } }
    - { fields: { placeholder: '@investigations_investigation_due', language: '@language_ar', value: بسبب } }
    - { fields: { placeholder: '@investigations_investigation_lead_investigator', language: '@language_ar', value: 'قائد محقق' } }
    - { fields: { placeholder: '@investigations_investigation_level', language: '@language_ar', value: مستوى } }
    - { fields: { placeholder: '@investigations_investigation_overdue', language: '@language_ar', value: متأخر } }
    - { fields: { placeholder: '@investigations_investigation_priority', language: '@language_ar', value: أفضلية } }
    - { fields: { placeholder: '@investigations_investigation_priority_high', language: '@language_ar', value: متوسط } }
    - { fields: { placeholder: '@investigations_investigation_priority_low', language: '@language_ar', value: منخفض } }
    - { fields: { placeholder: '@investigations_investigation_priority_medium', language: '@language_ar', value: متوسط } }
    - { fields: { placeholder: '@investigations_investigation_reopened_successfully', language: '@language_ar', value: 'إعادة فتح التحقيق بنجاح' } }
    - { fields: { placeholder: '@investigations_investigation_started', language: '@language_ar', value: بدأت } }
    - { fields: { placeholder: '@investigations_investigation_status_completed', language: '@language_ar', value: مكتمل } }
    - { fields: { placeholder: '@investigations_investigation_status_in_progress', language: '@language_ar', value: 'تحت الاجراء' } }
    - { fields: { placeholder: '@investigations_investigation_status_rejected', language: '@language_ar', value: مرفوض } }
    - { fields: { placeholder: '@investigations_investigation_submit', language: '@language_ar', value: 'حفظ التحقيق' } }
    - { fields: { placeholder: '@investigations_loading_objectives', language: '@language_ar', value: 'تحميل الأهداف' } }
    - { fields: { placeholder: '@investigations_loading_events', language: '@language_ar', value: 'جارٍ تحميل الأحداث ...' } }
    - { fields: { placeholder: '@investigations_loading_investigation_form', language: '@language_ar', value: 'تحميل استمارة التحقيق ...' } }
    - { fields: { placeholder: '@investigations_loading_investigations', language: '@language_ar', value: 'جارٍ تحميل التحقيقات ...' } }
    - { fields: { placeholder: '@investigations_loading_tools', language: '@language_ar', value: 'جارٍ تنزيل الأدوات ...' } }
    - { fields: { placeholder: '@investigations_module_title', language: '@language_ar', value: تحقيقات } }
    - { fields: { placeholder: '@investigations_nav_actions_all_actions', language: '@language_ar', value: 'جميع الإجراءات' } }
    - { fields: { placeholder: '@investigations_nav_actions_my_actions', language: '@language_ar', value: أفعالي } }
    - { fields: { placeholder: '@investigations_nav_administration', language: '@language_ar', value: الادارة } }
    - { fields: { placeholder: '@investigations_nav_back_to_dashboard', language: '@language_ar', value: 'رجوع إلى لوحة المعلومات' } }
    - { fields: { placeholder: '@investigations_nav_dashboard', language: '@language_ar', value: 'لوحة القيادة' } }
    - { fields: { placeholder: '@investigations_nav_events', language: '@language_ar', value: أحداث } }
    - { fields: { placeholder: '@investigations_nav_investigation_attachments', language: '@language_ar', value: مرفقات } }
    - { fields: { placeholder: '@investigations_nav_investigation_define_investigation', language: '@language_ar', value: 'تحديد التحقيق' } }
    - { fields: { placeholder: '@investigations_nav_investigation_objectives', language: '@language_ar', value: الأهداف } }
    - { fields: { placeholder: '@investigations_nav_investigation_page_title', language: '@language_ar', value: 'التحقيق # {{id}}' } }
    - { fields: { placeholder: '@investigations_nav_investigation_recommendations', language: '@language_ar', value: توصيات } }
    - { fields: { placeholder: '@investigations_nav_new_investigation', language: '@language_ar', value: 'التحقيق الجديد' } }
    - { fields: { placeholder: '@investigations_objectives_add_objective', language: '@language_ar', value: 'إضافة الهدف' } }
    - { fields: { placeholder: '@investigations_objectives_delete_objective', language: '@language_ar', value: 'حذف الهدف' } }
    - { fields: { placeholder: '@investigations_objectives_details', language: '@language_ar', value: تفاصيل } }
    - { fields: { placeholder: '@investigations_objectives_new_objective', language: '@language_ar', value: 'الهدف الجديد' } }
    - { fields: { placeholder: '@investigations_objectives_no_objectives', language: '@language_ar', value: 'هذا التحقيق ليس له أهداف' } }
    - { fields: { placeholder: '@investigations_objectives_objective_list', language: '@language_ar', value: 'قائمة الأهداف' } }
    - { fields: { placeholder: '@investigations_objectives_save_objective', language: '@language_ar', value: 'حفظ الهدف' } }
    - { fields: { placeholder: '@investigations_objectives_summary', language: '@language_ar', value: ملخص } }
    - { fields: { placeholder: '@investigations_process_maps_form_form_title', language: '@language_ar', value: 'خريطة عملية' } }
    - { fields: { placeholder: '@investigations_process_maps_form_save', language: '@language_ar', value: 'حفظ التغييرات' } }
    - { fields: { placeholder: '@investigations_process_maps_form_save_and_view', language: '@language_ar', value: 'حفظ وعرض خريطة العملية' } }
    - { fields: { placeholder: '@investigations_process_maps_list_add_new', language: '@language_ar', value: 'إضافة خريطة عملية جديدة' } }
    - { fields: { placeholder: '@investigations_process_maps_list_view', language: '@language_ar', value: 'عرض خريطة العملية' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_appearance', language: '@language_ar', value: 'مظهر خارجي' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_body_colour', language: '@language_ar', value: 'لون الجسم' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_details', language: '@language_ar', value: تفاصيل } }
    - { fields: { placeholder: '@investigations_process_maps_tool_fault_tree_shapes', language: '@language_ar', value: 'أشكال التحليل بشجرة الأخطاء' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_form_date_not_set', language: '@language_ar', value: 'التاريخ غير محدد' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_form_end', language: '@language_ar', value: النهاية } }
    - { fields: { placeholder: '@investigations_process_maps_tool_form_start', language: '@language_ar', value: بداية } }
    - { fields: { placeholder: '@investigations_process_maps_tool_investigation_events', language: '@language_ar', value: 'أحداث التحقيق' } }
    - { fields: { placeholder: '@investigations_process_maps_tool_process_model_shapes', language: '@language_ar', value: 'أشكال عملية النموذج' } }
    - { fields: { placeholder: '@investigations_success_admin_tools', language: '@language_ar', value: 'الأدوات الافتراضية المحفوظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_attachment_save', language: '@language_ar', value: 'تم حفظ المرفق بنجاح' } }
    - { fields: { placeholder: '@investigations_success_clinical_measurements_save', language: '@language_ar', value: 'تم تحديث القياس الطبي بنجاح' } }
    - { fields: { placeholder: '@investigations_success_events_deleted', language: '@language_ar', value: 'تم حذف الحدث بنجاح' } }
    - { fields: { placeholder: '@investigations_success_events_saved', language: '@language_ar', value: 'تم حفظ الحدث بنجاح' } }
    - { fields: { placeholder: '@investigations_success_five_whys_deleted', language: '@language_ar', value: 'خمسة لماذا حذف بنجاح' } }
    - { fields: { placeholder: '@investigations_success_five_whys_saved', language: '@language_ar', value: 'خمسة لماذا تم حفظها بنجاح' } }
    - { fields: { placeholder: '@investigations_success_investigation_save', language: '@language_ar', value: 'التحقيق المحفوظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_investigation_tool_refresh', language: '@language_ar', value: 'أدوات التحقيق المتاحة المحدثة' } }
    - { fields: { placeholder: '@investigations_success_notes_deleted', language: '@language_ar', value: 'تم حذف الملاحظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_notes_saved', language: '@language_ar', value: 'تم حفظ الملاحظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_objectives_deleted', language: '@language_ar', value: 'الهدف المحذوف بنجاح' } }
    - { fields: { placeholder: '@investigations_success_objectives_saved', language: '@language_ar', value: 'الهدف المحفوظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_participants_saved', language: '@language_ar', value: 'تم حفظ المشارك بنجاح' } }
    - { fields: { placeholder: '@investigations_success_process_map_saved', language: '@language_ar', value: 'خريطة العملية المحفوظة بنجاح' } }
    - { fields: { placeholder: '@investigations_success_safer_matrix_saved', language: '@language_ar', value: 'تم حفظ SAFER Matrix بنجاح' } }
    - { fields: { placeholder: '@investigations_success_third_party_events_investigation_created', language: '@language_ar', value: 'تم انشاء التحقيق بنجاح' } }
    - { fields: { placeholder: '@investigations_success_third_party_events_rejected', language: '@language_ar', value: 'تم رفض الحدث بنجاح' } }
    - { fields: { placeholder: '@investigations_tools_add', language: '@language_ar', value: 'إضافة أداة' } }
    - { fields: { placeholder: '@investigations_tools_add_tool', language: '@language_ar', value: 'إضافة أداة' } }
    - { fields: { placeholder: '@investigations_tools_analyse_data', language: '@language_ar', value: 'تحليل البيانات' } }
    - { fields: { placeholder: '@investigations_tools_clinical_measurements', language: '@language_ar', value: 'القياسات الطبية' } }
    - { fields: { placeholder: '@investigations_tools_collect_data', language: '@language_ar', value: 'اجمع بيانات' } }
    - { fields: { placeholder: '@investigations_tools_contributory_factors', language: '@language_ar', value: 'العوامل المساهمة' } }
    - { fields: { placeholder: '@investigations_tools_event_chronology', language: '@language_ar', value: 'التسلسل الزمني للحدث' } }
    - { fields: { placeholder: '@investigations_tools_fishbone_diagram', language: '@language_ar', value: 'رسم الهيكل السمكي Fishbone ' } }
    - { fields: { placeholder: '@investigations_tools_five_whys', language: '@language_ar', value: 'خمسة أسباب' } }
    - { fields: { placeholder: '@investigations_tools_no_tools', language: '@language_ar', value: 'لا توجد أدوات محددة لمستوى التحقيق هذا' } }
    - { fields: { placeholder: '@investigations_tools_objectives', language: '@language_ar', value: الأهداف } }
    - { fields: { placeholder: '@investigations_tools_process_maps', language: '@language_ar', value: 'خرائط العمليات' } }
    - { fields: { placeholder: '@investigations_tools_safer_matrix', language: '@language_ar', value: 'مصفوفة SAFER' } }
    - { fields: { placeholder: '@investigations_tools_time_person_grid', language: '@language_ar', value: 'جدول الأشخاص و المهام -Time Person ' } }
    - { fields: { placeholder: '@investigations_user_role', language: '@language_ar', value: وظيفة } }
    - { fields: { placeholder: '@investigations_user_roles_doctor', language: '@language_ar', value: طبيب } }
    - { fields: { placeholder: '@investigations_user_roles_investigator', language: '@language_ar', value: محقق } }
    - { fields: { placeholder: '@investigations_user_roles_lead_investigator', language: '@language_ar', value: 'قائد محقق' } }
    - { fields: { placeholder: '@investigations_user_roles_none', language: '@language_ar', value: 'لا شيء' } }
    - { fields: { placeholder: '@investigations_user_roles_nurse', language: '@language_ar', value: ممرضة } }
    - { fields: { placeholder: '@investigations_user_roles_patient', language: '@language_ar', value: مريض } }
    - { fields: { placeholder: '@investigations_user_roles_recipient', language: '@language_ar', value: مستلم } }
    - { fields: { placeholder: '@investigations_user_roles_subject', language: '@language_ar', value: موضوع } }
    - { fields: { placeholder: '@investigations_user_roles_witness', language: '@language_ar', value: الشاهد } }
    - { fields: { placeholder: '@forms_investigation_field_services_title', language: '@language_ar', value: خدمات } }
    - { fields: { placeholder: '@forms_investigation_field_services_label', language: '@language_ar', value: خدمات } }
    - { fields: { placeholder: '@forms_investigation_field_locations_title', language: '@language_ar', value: مواقع } }
    - { fields: { placeholder: '@forms_investigation_field_locations_label', language: '@language_ar', value: مواقع } }
    - { fields: { placeholder: '@forms_investigation_field_contacts_title', language: '@language_ar', value: 'جهات الاتصال' } }
    - { fields: { placeholder: '@forms_investigation_field_contacts_label', language: '@language_ar', value: 'جهات الاتصال' } }
