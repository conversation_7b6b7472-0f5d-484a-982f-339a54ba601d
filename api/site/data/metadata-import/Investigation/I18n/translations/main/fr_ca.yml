entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@investigations_user_roles_none', language: '@language_fr_ca', value: Aucun } }
  - { fields: { placeholder: '@investigations_user_roles_subject', language: '@language_fr_ca', value: Su<PERSON> } }
  - { fields: { placeholder: '@investigations_user_roles_recipient', language: '@language_fr_ca', value: <PERSON><PERSON><PERSON> } }
  - { fields: { placeholder: '@investigations_user_roles_investigator', language: '@language_fr_ca', value: Enquêteur } }
  - { fields: { placeholder: '@investigations_user_roles_lead_investigator', language: '@language_fr_ca', value: 'Enquêteur principal' } }
  - { fields: { placeholder: '@investigations_user_role', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@investigations_user_roles_patient', language: '@language_fr_ca', value: Patient } }
  - { fields: { placeholder: '@investigations_user_roles_witness', language: '@language_fr_ca', value: Té<PERSON>in } }
  - { fields: { placeholder: '@investigations_user_roles_nurse', language: '@language_fr_ca', value: Infirmière } }
  - { fields: { placeholder: '@investigations_user_roles_doctor', language: '@language_fr_ca', value: Docteur } }
  - { fields: { placeholder: '@investigations_components_fivewhys', language: '@language_fr_ca', value: 'Cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_addnew', language: '@language_fr_ca', value: 'Ajouter une nouvelle entrée Cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_addwhy', language: '@language_fr_ca', value: 'Ajouter pourquoi' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_why', language: '@language_fr_ca', value: 'Pourquoi?' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_problem', language: '@language_fr_ca', value: Problème } }
  - { fields: { placeholder: '@investigations_components_fivewhys_contributoryfactors', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_addcontributoryfactor', language: '@language_fr_ca', value: 'Ajouter un facteur contributif' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_contributoryfactortitle', language: '@language_fr_ca', value: 'Titre de facteur contributif' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_save', language: '@language_fr_ca', value: Sauvegarder } }
  - { fields: { placeholder: '@investigations_components_fivewhys_delete', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@investigations_components_fivewhys_newproblem', language: '@language_fr_ca', value: 'Nouveau problème' } }
  - { fields: { placeholder: '@investigations_components_fivewhys_areyousure', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@investigations_components_participantform_participant', language: '@language_fr_ca', value: Participant } }
  - { fields: { placeholder: '@investigations_components_participantform_participantname', language: '@language_fr_ca', value: 'Nom du participant' } }
  - { fields: { placeholder: '@investigations_components_participantform_usecontact', language: '@language_fr_ca', value: 'Utiliser le contact' } }
  - { fields: { placeholder: '@investigations_components_participantform_closecontactform', language: '@language_fr_ca', value: 'Fermer le formulaire de contact' } }
  - { fields: { placeholder: '@investigations_components_participantform_newcontact', language: '@language_fr_ca', value: 'Nouveau contact' } }
  - { fields: { placeholder: '@investigations_components_participantform_view_contact', language: '@language_fr_ca', value: 'Afficher le contact' } }
  - { fields: { placeholder: '@investigations_components_participantform_searchexistingcontacts', language: '@language_fr_ca', value: 'Rechercher des contacts existants' } }
  - { fields: { placeholder: '@investigations_components_participantform_saveparticipant', language: '@language_fr_ca', value: 'Enregistrer le participant' } }
  - { fields: { placeholder: '@investigations_components_participantform_role', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@investigations_components_participantform_newparticipant', language: '@language_fr_ca', value: 'Nouveau participant' } }
  - { fields: { placeholder: '@investigations_components_participantform_newparticipantfromcontact', language: '@language_fr_ca', value: 'Nouveau participant (à partir de la fiche de contact)' } }
  - { fields: { placeholder: '@investigations_components_participantform_existingparticipant', language: '@language_fr_ca', value: 'Participant existant' } }
  - { fields: { placeholder: '@investigations_components_participantform_participant_name', language: '@language_fr_ca', value: 'Nom du participant' } }
  - { fields: { placeholder: '@investigations_components_participantform_reset_participant', language: '@language_fr_ca', value: 'Réinitialiser le participant' } }
  - { fields: { placeholder: '@investigations_components_eventform_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@investigations_components_eventform_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@investigations_components_eventform_startdate', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@investigations_components_eventform_end_date', language: '@language_fr_ca', value: 'Date de fin' } }
  - { fields: { placeholder: '@investigations_components_eventform_source', language: '@language_fr_ca', value: Source } }
  - { fields: { placeholder: '@investigations_components_eventform_deleteevent', language: '@language_fr_ca', value: 'Supprimer l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventform_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@investigations_components_eventform_addanotherevent', language: '@language_fr_ca', value: 'Ajouter un autre événement?' } }
  - { fields: { placeholder: '@investigations_components_eventform_saveevent', language: '@language_fr_ca', value: 'Enregistrer l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventform_eventdetails', language: '@language_fr_ca', value: 'Détails de l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventform_errors_invalid_end_date', language: '@language_fr_ca', value: 'La date de fin ne doit pas être antérieure à la date de début' } }
  - { fields: { placeholder: '@investigations_components_eventform_participant', language: '@language_fr_ca', value: Participant } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram', language: '@language_fr_ca', value: 'Diagramme en arête de poisson' } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram_fishbone_diagram_title', language: '@language_fr_ca', value: 'Titre du diagramme en arête de poisson' } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram_add_root_node', language: '@language_fr_ca', value: 'Ajouter un nœud racine' } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram_save', language: '@language_fr_ca', value: 'Enregistrer et mettre à jour le diagramme en arête de poisson' } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram_saved_successfully', language: '@language_fr_ca', value: 'Diagramme en arête de poisson enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_components_fishbone_diagram_nodes_confirm_delete', language: '@language_fr_ca', value: 'Êtes-vous certain de vouloir supprimer ce nœud?' } }
  - { fields: { placeholder: '@investigations_process_maps_form_form_title', language: '@language_fr_ca', value: 'Diagramme de processus' } }
  - { fields: { placeholder: '@investigations_process_maps_form_save', language: '@language_fr_ca', value: 'Enregistrer les modifications' } }
  - { fields: { placeholder: '@investigations_process_maps_form_save_and_view', language: '@language_fr_ca', value: 'Enregistrer et afficher le diagramme de processus' } }
  - { fields: { placeholder: '@investigations_objectives_objective_list', language: '@language_fr_ca', value: 'Liste des objectifs' } }
  - { fields: { placeholder: '@investigations_objectives_add_objective', language: '@language_fr_ca', value: 'Ajouter un objectif' } }
  - { fields: { placeholder: '@investigations_objectives_new_objective', language: '@language_fr_ca', value: 'Nouvel objectif' } }
  - { fields: { placeholder: '@investigations_objectives_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@investigations_objectives_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@investigations_objectives_delete_objective', language: '@language_fr_ca', value: 'Supprimer l''objectif' } }
  - { fields: { placeholder: '@investigations_objectives_save_objective', language: '@language_fr_ca', value: 'Enregistrer l''objectif' } }
  - { fields: { placeholder: '@investigations_objectives_no_objectives', language: '@language_fr_ca', value: 'L''enquête l''a pas d''objectifs' } }
  - { fields: { placeholder: '@investigations_components_eventchronology', language: '@language_fr_ca', value: 'Chronologie de l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_startdate', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_enddate', language: '@language_fr_ca', value: 'Date de fin' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_person', language: '@language_fr_ca', value: Personne } }
  - { fields: { placeholder: '@investigations_components_eventchronology_role', language: '@language_fr_ca', value: Rôle } }
  - { fields: { placeholder: '@investigations_components_eventchronology_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@investigations_components_eventchronology_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@investigations_components_eventchronology_source', language: '@language_fr_ca', value: Source } }
  - { fields: { placeholder: '@investigations_components_eventchronology_source_label', language: '@language_fr_ca', value: Source } }
  - { fields: { placeholder: '@investigations_components_eventchronology_addevent', language: '@language_fr_ca', value: 'Ajouter un événement' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@investigations_components_eventchronology_editevent', language: '@language_fr_ca', value: 'Modifier l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_viewevent', language: '@language_fr_ca', value: 'Afficher l''événement' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_processmaps', language: '@language_fr_ca', value: 'Diagrammes de processus' } }
  - { fields: { placeholder: '@investigations_components_eventchronology_delete', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@investigations_components_eventchronology_addfirstmessage', language: '@language_fr_ca', value: 'Cliquez sur le bouton ci-dessus pour ajouter le premier événement' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_millimeters_of_mercury', language: '@language_fr_ca', value: 'Millimètres de mercure' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_beats_per_minute', language: '@language_fr_ca', value: 'Battements par minute' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_oxygen_saturation', language: '@language_fr_ca', value: 'Saturation d''oxygène' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_breaths_per_minute', language: '@language_fr_ca', value: 'Respirations par minute' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_score', language: '@language_fr_ca', value: Note } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_degrees_celcius', language: '@language_fr_ca', value: 'Degrés Celsius' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_systolic', language: '@language_fr_ca', value: Systolique } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_unit_diastolic', language: '@language_fr_ca', value: Diastolique } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_singular', language: '@language_fr_ca', value: 'Mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_plural', language: '@language_fr_ca', value: 'Mesures cliniques' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_save_error', language: '@language_fr_ca', value: 'Erreur lors de l''enregistrement de la mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_new', language: '@language_fr_ca', value: 'Nouvelle mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_add', language: '@language_fr_ca', value: 'Ajouter une mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_no_measurements', language: '@language_fr_ca', value: 'Cette enquête ne comprend actuellement pas de mesures cliniques' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_occurrence_date', language: '@language_fr_ca', value: 'Date d''occurrence' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_details', language: '@language_fr_ca', value: 'Détails de la mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_clinical_measurement', language: '@language_fr_ca', value: 'Chargement de la mesure clinique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_grid', language: '@language_fr_ca', value: 'Chargement de la grille' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_chart', language: '@language_fr_ca', value: 'Chargement du graphique' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_loading_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération des mesures cliniques' } }
  - { fields: { placeholder: '@investigations_attachments_no_attachments', language: '@language_fr_ca', value: 'Cette enquête ne comprend pas de pièce jointe' } }
  - { fields: { placeholder: '@investigations_admin_is_default', language: '@language_fr_ca', value: 'Par défaut?' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_blood_pressure', language: '@language_fr_ca', value: 'Pression sanguine' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_heart_rate', language: '@language_fr_ca', value: 'Rythme cardiaque' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_oxygen_saturation', language: '@language_fr_ca', value: 'Saturation d''oxygène' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_respiratory_rate', language: '@language_fr_ca', value: 'Fréquence respiratoire' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_pain_score', language: '@language_fr_ca', value: 'Score de douleur' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_temperature', language: '@language_fr_ca', value: Température } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_type_select', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_please_add_values', language: '@language_fr_ca', value: 'Au moins une valeur de mesure est requise' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_form_invalid', language: '@language_fr_ca', value: 'Veuillez remplir tous les champs obligatoires dans le formulaire de mesure' } }
  - { fields: { placeholder: '@investigations_process_maps_list_add_new', language: '@language_fr_ca', value: 'Ajouter un nouveau diagramme de processus' } }
  - { fields: { placeholder: '@investigations_process_maps_list_view', language: '@language_fr_ca', value: 'Afficher le diagramme de processus' } }
  - { fields: { placeholder: '@investigations_process_maps_tool_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@investigations_process_maps_tool_appearance', language: '@language_fr_ca', value: Apparence } }
  - { fields: { placeholder: '@investigations_process_maps_tool_body_colour', language: '@language_fr_ca', value: 'Couleur du corps' } }
  - { fields: { placeholder: '@investigations_process_maps_tool_process_model_shapes', language: '@language_fr_ca', value: 'Formes modèles de processus' } }
  - { fields: { placeholder: '@investigations_process_maps_tool_fault_tree_shapes', language: '@language_fr_ca', value: 'Formes d''arbre de défauts' } }
  - { fields: { placeholder: '@investigations_process_maps_tool_investigation_events', language: '@language_fr_ca', value: 'Événements d''enquête' } }
  - { fields: { placeholder: '@investigations_process_maps_tool_form_start', language: '@language_fr_ca', value: Début } }
  - { fields: { placeholder: '@investigations_process_maps_tool_form_end', language: '@language_fr_ca', value: Fin } }
  - { fields: { placeholder: '@investigations_process_maps_tool_form_date_not_set', language: '@language_fr_ca', value: 'Date non définie' } }
  - { fields: { placeholder: '@investigations_loading_investigations', language: '@language_fr_ca', value: 'Chargement des enquêtes...' } }
  - { fields: { placeholder: '@investigations_loading_investigation_form', language: '@language_fr_ca', value: 'Chargement du formulaire d''enquête...' } }
  - { fields: { placeholder: '@investigations_loading_tools', language: '@language_fr_ca', value: 'Chargement des outils...' } }
  - { fields: { placeholder: '@investigations_loading_events', language: '@language_fr_ca', value: 'Chargement des événements...' } }
  - { fields: { placeholder: '@investigations_investigation_levels_level_1', language: '@language_fr_ca', value: '1' } }
  - { fields: { placeholder: '@investigations_investigation_levels_level_2', language: '@language_fr_ca', value: '2' } }
  - { fields: { placeholder: '@investigations_investigation_levels_level_3', language: '@language_fr_ca', value: '3' } }
  - { fields: { placeholder: '@investigations_investigation_status_not_started', language: '@language_fr_ca', value: 'Non démarré' } }
  - { fields: { placeholder: '@investigations_investigation_status_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@investigations_investigation_status_submitted_for_approval', language: '@language_fr_ca', value: 'Soumis pour approbation' } }
  - { fields: { placeholder: '@investigations_investigation_status_completed', language: '@language_fr_ca', value: Finalisé } }
  - { fields: { placeholder: '@investigations_investigation_status_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@investigations_investigation_status_decommissioned', language: '@language_fr_ca', value: Retiré(e) } }
  - { fields: { placeholder: '@investigations_investigation_started', language: '@language_fr_ca', value: Démarré } }
  - { fields: { placeholder: '@investigations_investigation_priority', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@investigations_investigation_priority_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@investigations_investigation_priority_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@investigations_investigation_priority_high', language: '@language_fr_ca', value: Élevée } }
  - { fields: { placeholder: '@investigations_investigation_level', language: '@language_fr_ca', value: Niveau } }
  - { fields: { placeholder: '@investigations_investigation_lead_investigator', language: '@language_fr_ca', value: 'Enquêteur principal' } }
  - { fields: { placeholder: '@investigations_investigation_overdue', language: '@language_fr_ca', value: Échu } }
  - { fields: { placeholder: '@investigations_investigation_due', language: '@language_fr_ca', value: 'Date d''échéance' } }
  - { fields: { placeholder: '@investigations_investigation_submit', language: '@language_fr_ca', value: 'Enregistrer l''enquête' } }
  - { fields: { placeholder: '@investigations_module_title', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@investigations_nav_back_to_dashboard', language: '@language_fr_ca', value: 'Retour au tableau de bord' } }
  - { fields: { placeholder: '@investigations_nav_dashboard', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@investigations_nav_new_investigation', language: '@language_fr_ca', value: 'Nouvelle enquête' } }
  - { fields: { placeholder: '@investigations_nav_events', language: '@language_fr_ca', value: Événements } }
  - { fields: { placeholder: '@investigations_nav_administration', language: '@language_fr_ca', value: Administration } }
  - { fields: { placeholder: '@investigations_nav_investigation_page_title', language: '@language_fr_ca', value: 'Enquête Nº {{id}}' } }
  - { fields: { placeholder: '@investigations_nav_investigation_define_investigation', language: '@language_fr_ca', value: 'Définir l''enquête' } }
  - { fields: { placeholder: '@investigations_nav_investigation_objectives', language: '@language_fr_ca', value: Objectifs } }
  - { fields: { placeholder: '@investigations_nav_investigation_recommendations', language: '@language_fr_ca', value: Recommandations } }
  - { fields: { placeholder: '@investigations_nav_investigation_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@investigations_success_admin_tools', language: '@language_fr_ca', value: 'Outils par défaut enregistrés avec succès' } }
  - { fields: { placeholder: '@investigations_success_third_party_events_rejected', language: '@language_fr_ca', value: 'Événement rejeté avec succès' } }
  - { fields: { placeholder: '@placeholder_investigations_success_third_party_events_rejection_reason_required', language: '@language_fr_ca', value: 'Un motif doit être fourni lors du rejet d''un événement' } }
  - { fields: { placeholder: '@investigations_success_third_party_events_investigation_created', language: '@language_fr_ca', value: 'Enquête créée avec succès' } }
  - { fields: { placeholder: '@investigations_success_attachment_save', language: '@language_fr_ca', value: 'Pièce jointe enregistrée avec succès' } }
  - { fields: { placeholder: '@investigations_success_investigation_save', language: '@language_fr_ca', value: 'Enquête enregistrée avec succès' } }
  - { fields: { placeholder: '@investigations_success_investigation_tool_refresh', language: '@language_fr_ca', value: 'Outils d''enquête disponibles mis à jour' } }
  - { fields: { placeholder: '@investigations_success_clinical_measurements_save', language: '@language_fr_ca', value: 'Mesure clinique mise à jour avec succès' } }
  - { fields: { placeholder: '@investigations_success_events_saved', language: '@language_fr_ca', value: 'Événement enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_success_events_deleted', language: '@language_fr_ca', value: 'Événement supprimé avec succès' } }
  - { fields: { placeholder: '@investigations_success_five_whys_saved', language: '@language_fr_ca', value: 'Cinq pourquoi enregistrés avec succès' } }
  - { fields: { placeholder: '@investigations_success_five_whys_deleted', language: '@language_fr_ca', value: 'Cinq pourquoi supprimés avec succès' } }
  - { fields: { placeholder: '@investigations_success_notes_saved', language: '@language_fr_ca', value: 'Note enregistrée avec succès' } }
  - { fields: { placeholder: '@investigations_success_notes_deleted', language: '@language_fr_ca', value: 'Note supprimée avec succès' } }
  - { fields: { placeholder: '@investigations_success_objectives_saved', language: '@language_fr_ca', value: 'Objectif enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_success_objectives_deleted', language: '@language_fr_ca', value: 'Objectif supprimé avec succès' } }
  - { fields: { placeholder: '@investigations_success_participants_saved', language: '@language_fr_ca', value: 'Participant enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_success_process_map_saved', language: '@language_fr_ca', value: 'Diagramme de processus enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_success_safer_matrix_saved', language: '@language_fr_ca', value: 'Matrice SAFER enregistrée avec succès' } }
  - { fields: { placeholder: '@investigations_events', language: '@language_fr_ca', value: Événements } }
  - { fields: { placeholder: '@investigations_events_create_investigation_investigation_title', language: '@language_fr_ca', value: 'Titre d''enquête' } }
  - { fields: { placeholder: '@investigations_events_create_investigation_add_users', language: '@language_fr_ca', value: 'Ajouter des utilisateurs' } }
  - { fields: { placeholder: '@investigations_events_create_investigation_submit', language: '@language_fr_ca', value: 'Créer une enquête' } }
  - { fields: { placeholder: '@investigations_events_reject_event_reason', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@investigations_events_reject_event_submit', language: '@language_fr_ca', value: 'Rejeter l''événement' } }
  - { fields: { placeholder: '@investigations_events_tabs_event_summary', language: '@language_fr_ca', value: 'Sommaire de l''événement' } }
  - { fields: { placeholder: '@investigations_events_tabs_create_investigation', language: '@language_fr_ca', value: 'Créer une enquête' } }
  - { fields: { placeholder: '@investigations_events_tabs_reject_event', language: '@language_fr_ca', value: 'Rejeter l''événement' } }
  - { fields: { placeholder: '@investigations_events_reference', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@investigations_events_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@investigations_events_owner', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@investigations_events_sac_score', language: '@language_fr_ca', value: 'Note SAC' } }
  - { fields: { placeholder: '@investigations_events_person_affected', language: '@language_fr_ca', value: 'Personne affectée' } }
  - { fields: { placeholder: '@investigations_events_handler', language: '@language_fr_ca', value: Responsable } }
  - { fields: { placeholder: '@investigations_events_occurred', language: '@language_fr_ca', value: 'S''est produit' } }
  - { fields: { placeholder: '@investigations_components_time_person_grid_add_event', language: '@language_fr_ca', value: 'Ajouter un événement' } }
  - { fields: { placeholder: '@investigations_components_time_person_grid_fit_all_events', language: '@language_fr_ca', value: 'Ajuster tous les événements' } }
  - { fields: { placeholder: '@investigations_components_time_person_grid_edit_participant', language: '@language_fr_ca', value: 'Modifier le participant' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_opensafermatrix', language: '@language_fr_ca', value: 'Ouvrir la matrice SAFER' } }
  - { fields: { placeholder: '@investigations_components_safermatrix', language: '@language_fr_ca', value: 'Matrice SAFER' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_saveandclose', language: '@language_fr_ca', value: 'Enregistrer et fermer' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_close', language: '@language_fr_ca', value: Fermer } }
  - { fields: { placeholder: '@investigations_components_safermatrix_matrix', language: '@language_fr_ca', value: Matrice } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_save', language: '@language_fr_ca', value: 'Enregistrer les facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_add', language: '@language_fr_ca', value: Ajouter } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_reset', language: '@language_fr_ca', value: Réinitialiser } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_no_contributory_factors', language: '@language_fr_ca', value: 'Aucun facteur contributif' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_contributoryfactors_new', language: '@language_fr_ca', value: 'Nouveau facteur contributif' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_save', language: '@language_fr_ca', value: Sauvegarder } }
  - { fields: { placeholder: '@investigations_components_safermatrix_structure', language: '@language_fr_ca', value: Structure } }
  - { fields: { placeholder: '@investigations_components_safermatrix_why', language: '@language_fr_ca', value: 'Pourquoi cela s''est-il produit?' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_how', language: '@language_fr_ca', value: 'Comment cela s''est-il produit?' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_process', language: '@language_fr_ca', value: Processus } }
  - { fields: { placeholder: '@investigations_components_safermatrix_outcome', language: '@language_fr_ca', value: Résultat } }
  - { fields: { placeholder: '@investigations_components_safermatrix_what', language: '@language_fr_ca', value: 'Que s''est-il produit?' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_patient', language: '@language_fr_ca', value: Patient } }
  - { fields: { placeholder: '@investigations_components_safermatrix_personnel', language: '@language_fr_ca', value: Personnel } }
  - { fields: { placeholder: '@investigations_components_safermatrix_environment', language: '@language_fr_ca', value: 'Environnement / équipement' } }
  - { fields: { placeholder: '@investigations_components_safermatrix_organisation', language: '@language_fr_ca', value: Organisation } }
  - { fields: { placeholder: '@investigations_components_safermatrix_regulatoryagencies', language: '@language_fr_ca', value: 'Organismes de réglementation' } }
  - { fields: { placeholder: '@investigations_contact_role_patient', language: '@language_fr_ca', value: Patient } }
  - { fields: { placeholder: '@investigations_contact_role_witness', language: '@language_fr_ca', value: Témoin } }
  - { fields: { placeholder: '@investigations_contact_role_nurse', language: '@language_fr_ca', value: Infirmière } }
  - { fields: { placeholder: '@investigations_contact_role_doctor', language: '@language_fr_ca', value: Docteur } }
  - { fields: { placeholder: '@investigations_dashboard_no_results', language: '@language_fr_ca', value: 'Aucune enquête ne correspond aux critères de filtre spécifiés.' } }
  - { fields: { placeholder: '@investigations_dashboard_filter_title', language: '@language_fr_ca', value: 'Filtrer les enquêtes' } }
  - { fields: { placeholder: '@investigations_error_not_found', language: '@language_fr_ca', value: 'Enquête introuvable' } }
  - { fields: { placeholder: '@investigations_error_get_form', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération du formulaire d''enquête' } }
  - { fields: { placeholder: '@investigations_error_admin_save', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la mise à jour des outils par défaut' } }
  - { fields: { placeholder: '@investigations_error_third_party_events_get_collection', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération de la liste d''événements' } }
  - { fields: { placeholder: '@investigations_error_third_party_events_create_investigation', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la création de l''enquête' } }
  - { fields: { placeholder: '@investigations_error_third_party_events_reject', language: '@language_fr_ca', value: 'Une erreur s''est produite lors du rejet de l''événement' } }
  - { fields: { placeholder: '@investigations_error_clinical_measurements_save', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la mesure clinique' } }
  - { fields: { placeholder: '@investigations_error_clinical_measurements_save_value', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la valeur de mesure clinique' } }
  - { fields: { placeholder: '@investigations_error_events_get_collection', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération des événements' } }
  - { fields: { placeholder: '@investigations_error_events_delete', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression de l''événement' } }
  - { fields: { placeholder: '@investigations_error_five_whys_get_collection', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération des cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_error_five_whys_save', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de l''entrée des cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_error_five_whys_delete', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression de l''entrée des cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_error_objectives_delete', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la suppression de l''objectif' } }
  - { fields: { placeholder: '@investigations_error_process_maps_save', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement du diagramme de processus' } }
  - { fields: { placeholder: '@investigations_error_file_not_found', language: '@language_fr_ca', value: 'Fichier introuvable' } }
  - { fields: { placeholder: '@investigations_error_attachment_not_found', language: '@language_fr_ca', value: 'Pièce jointe introuvable' } }
  - { fields: { placeholder: '@investigations_error_fishbone_not_found', language: '@language_fr_ca', value: 'Diagramme en arête de poisson introuvable' } }
  - { fields: { placeholder: '@investigations_error_fivewhy_not_found', language: '@language_fr_ca', value: 'Cinq pourquoi introuvables' } }
  - { fields: { placeholder: '@investigations_error_safer_matrix_not_found', language: '@language_fr_ca', value: 'Matrice Safer' } }
  - { fields: { placeholder: '@investigations_tools_add_tool', language: '@language_fr_ca', value: 'Ajouter l''outil' } }
  - { fields: { placeholder: '@investigations_tools_collect_data', language: '@language_fr_ca', value: 'Collecter les données' } }
  - { fields: { placeholder: '@investigations_tools_analyse_data', language: '@language_fr_ca', value: 'Analyser les données' } }
  - { fields: { placeholder: '@investigations_tools_event_chronology', language: '@language_fr_ca', value: 'Chronologie de l''événement' } }
  - { fields: { placeholder: '@investigations_tools_time_person_grid', language: '@language_fr_ca', value: 'Grille temps-personne' } }
  - { fields: { placeholder: '@investigations_tools_clinical_measurements', language: '@language_fr_ca', value: 'Mesures cliniques' } }
  - { fields: { placeholder: '@investigations_tools_safer_matrix', language: '@language_fr_ca', value: 'Matrice SAFER' } }
  - { fields: { placeholder: '@investigations_tools_five_whys', language: '@language_fr_ca', value: 'Cinq pourquoi' } }
  - { fields: { placeholder: '@investigations_tools_fishbone_diagram', language: '@language_fr_ca', value: 'Diagramme en arête de poisson' } }
  - { fields: { placeholder: '@investigations_tools_process_maps', language: '@language_fr_ca', value: 'Diagrammes de processuss' } }
  - { fields: { placeholder: '@investigations_tools_objectives', language: '@language_fr_ca', value: Objectifs } }
  - { fields: { placeholder: '@investigations_tools_contributory_factors', language: '@language_fr_ca', value: 'Facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_tools_add', language: '@language_fr_ca', value: 'Ajouter l''outil' } }
  - { fields: { placeholder: '@investigations_tools_no_tools', language: '@language_fr_ca', value: 'Aucun outil sélectionné pour ce niveau d''enquête' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_values', language: '@language_fr_ca', value: Valeurs } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_no_values', language: '@language_fr_ca', value: 'Cette mesure clinique n''a pas de valeurs' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_add_value', language: '@language_fr_ca', value: 'Ajouter une valeur' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_edit_value', language: '@language_fr_ca', value: 'Modifier la valeur' } }
  - { fields: { placeholder: '@investigations_components_clinical_measurements_start_date', language: '@language_fr_ca', value: 'Date de début' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_id', language: '@language_fr_ca', value: 'ID d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_id_label', language: '@language_fr_ca', value: 'ID d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_title', language: '@language_fr_ca', value: 'Titre d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_title_label', language: '@language_fr_ca', value: 'Titre d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_description', language: '@language_fr_ca', value: 'Description de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_description_label', language: '@language_fr_ca', value: 'Description de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_target_due_date', language: '@language_fr_ca', value: 'Date d''échéance de la cible de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_target_due_date_label', language: '@language_fr_ca', value: 'Date d''échéance de la cible de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_level', language: '@language_fr_ca', value: 'Niveau d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_level_label', language: '@language_fr_ca', value: 'Niveau d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_priority', language: '@language_fr_ca', value: 'Priorité de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_priority_label', language: '@language_fr_ca', value: 'Priorité de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_status', language: '@language_fr_ca', value: 'État de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_status_label', language: '@language_fr_ca', value: 'État de l''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_rejection', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_rejection_label', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioning_title', language: '@language_fr_ca', value: 'Motif du retrait' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioning_label', language: '@language_fr_ca', value: 'Motif du retrait' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_title', language: '@language_fr_ca', value: 'Approuver le rapport d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_label', language: '@language_fr_ca', value: 'Approuver le rapport d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_title', language: '@language_fr_ca', value: 'Autorisé à approuver le rapport d''enquête au nom du CE?' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_label', language: '@language_fr_ca', value: 'Autorisé à approuver le rapport d''enquête au nom du CE?' } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@forms_investigation_errors_unauthorised_to_decommission', language: '@language_fr_ca', value: 'Retrait non autorisé' } }
  - { fields: { placeholder: '@forms_investigation_value_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@forms_investigation_value_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@forms_investigation_value_high', language: '@language_fr_ca', value: Élevée } }
  - { fields: { placeholder: '@forms_investigation_title', language: '@language_fr_ca', value: 'Formulaire d''enquête 1' } }
  - { fields: { placeholder: '@forms_investigation_summary', language: '@language_fr_ca', value: 'Ceci est le sommaire du premier formulaire d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_section_title_and_details', language: '@language_fr_ca', value: 'Titre et détails' } }
  - { fields: { placeholder: '@forms_investigation_section_level', language: '@language_fr_ca', value: 'Niveau d''enquête' } }
  - { fields: { placeholder: '@forms_investigation_field_level_select', language: '@language_fr_ca', value: 'Sélectionnez un niveau' } }
  - { fields: { placeholder: '@forms_investigation_events_source', language: '@language_fr_ca', value: 'Cette enquête est liée à l''événement Nº {{eventId}}' } }
  - { fields: { placeholder: '@forms_investigation_events_view', language: '@language_fr_ca', value: 'Afficher le dossier de l''événement' } }
  - { fields: { placeholder: '@investigations_contributory_factors_saved_successfully', language: '@language_fr_ca', value: 'Facteur contributif enregistré avec succès' } }
  - { fields: { placeholder: '@investigations_investigation_banners_complete', language: '@language_fr_ca', value: 'Cette enquête est terminée' } }
  - { fields: { placeholder: '@investigations_investigation_banners_rejected', language: '@language_fr_ca', value: 'Cette enquête a été rejetée par {{name}} le {{date}}' } }
  - { fields: { placeholder: '@investigations_investigation_banners_decommissioned', language: '@language_fr_ca', value: 'Cette enquête a été retirée par {{name}} le {{date}}' } }
  - { fields: { placeholder: '@investigations_investigation_banners_locked', language: '@language_fr_ca', value: 'Ce dossier est verrouillé par {{name}} depuis le {{date}}' } }
  - { fields: { placeholder: '@investigations_investigation_error_locked', language: '@language_fr_ca', value: 'Ce dossier est verrouillé et ne peut pas être mis à jour' } }
  - { fields: { placeholder: '@investigations_investigation_error_edit_state', language: '@language_fr_ca', value: 'Les enquêtes terminées et rejetées ne peuvent pas être modifiées' } }
  - { fields: { placeholder: '@investigations_investigation_banners_reopen', language: '@language_fr_ca', value: 'Rouvrir l''enquête' } }
  - { fields: { placeholder: '@investigations_investigation_reopened_successfully', language: '@language_fr_ca', value: 'L''enquête rouverte avec succès' } }
  - { fields: { placeholder: '@investigations_datasource_investigation_levels', language: '@language_fr_ca', value: 'Niveaux d''investigation' } }
  - { fields: { placeholder: '@investigations_datasource_investigation_priorities', language: '@language_fr_ca', value: 'Priorités d''enquête' } }
  - { fields: { placeholder: '@investigations_datasource_investigation_statuses', language: '@language_fr_ca', value: 'États d''enquête' } }
  - { fields: { placeholder: '@investigations_form_type_investigation', language: '@language_fr_ca', value: 'Formulaire d''enquête' } }
  - { fields: { placeholder: '@investigations_success_template_document_attached', language: '@language_fr_ca', value: 'Document joint avec succès à partir du modèle' } }
  - { fields: { placeholder: '@investigations_investigation_location', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@investigations_nav_admin_events', language: '@language_fr_ca', value: 'Configuration des événements' } }
  - { fields: { placeholder: '@investigation_location_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@investigation_location_type_title', language: '@language_fr_ca', value: 'Type d''emplacement' } }
  - { fields: { placeholder: '@investigation_location_type_label', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@investigation_location_type_origination', language: '@language_fr_ca', value: Origine } }
  - { fields: { placeholder: '@investigation_location_type_discovery', language: '@language_fr_ca', value: Découverte } }
  - { fields: { placeholder: '@investigation_fields_investigation_report_due_label', language: '@language_fr_ca', value: 'Rapport d''enquête dû' } }
  - { fields: { placeholder: '@investigation_fields_investigation_report_due_title', language: '@language_fr_ca', value: 'Rapport d''enquête dû' } }
  - { fields: { placeholder: '@investigation_fields_feedback_due_to_staff_label', language: '@language_fr_ca', value: 'Rétroaction due au personnel' } }
  - { fields: { placeholder: '@investigation_fields_feedback_due_to_staff_title', language: '@language_fr_ca', value: 'Rétroaction due au personnel' } }
  - { fields: { placeholder: '@investigation_fields_feedback_due_to_patient_support_person_label', language: '@language_fr_ca', value: 'Rétroaction due au patient ou à la personne de soutien' } }
  - { fields: { placeholder: '@investigation_fields_feedback_due_to_patient_support_person_title', language: '@language_fr_ca', value: 'Rétroaction due au patient ou à la personne de soutien' } }
  - { fields: { placeholder: '@investigation_document_field_teamleader', language: '@language_fr_ca', value: 'Chef d''équipe' } }
  - { fields: { placeholder: '@investigation_document_field_teammember', language: '@language_fr_ca', value: 'Membre de l''équipe' } }
  - { fields: { placeholder: '@investigations_datasource_contact_roles', language: '@language_fr_ca', value: 'Rôles du contact d''enquête' } }
  - { fields: { placeholder: '@investigations_events_event_not_pending', language: '@language_fr_ca', value: 'L''événement n''est pas en attente' } }
  - { fields: { placeholder: '@investigation_service_type_title', language: '@language_fr_ca', value: 'Type de service d''enquête' } }
  - { fields: { placeholder: '@investigation_service_type_label', language: '@language_fr_ca', value: 'Point de' } }
  - { fields: { placeholder: '@investigation_service_type_origination', language: '@language_fr_ca', value: Origine } }
  - { fields: { placeholder: '@investigation_service_type_discovery', language: '@language_fr_ca', value: Découverte } }
  - { fields: { placeholder: '@investigations_document_health_districtnetwork', language: '@language_fr_ca', value: 'District / Réseau de santé' } }
  - { fields: { placeholder: '@investigations_document_final_investigation_report', language: '@language_fr_ca', value: 'Rapport d''enquête final' } }
  - { fields: { placeholder: '@investigations_document_reference_numbers_where_applicable', language: '@language_fr_ca', value: 'Numéros de référence (le cas échéant)' } }
  - { fields: { placeholder: '@investigations_document_moh_rib_no', language: '@language_fr_ca', value: 'Nº RIB MoH' } }
  - { fields: { placeholder: '@investigations_document_ims_rib_no', language: '@language_fr_ca', value: 'Nº RIB ims+' } }
  - { fields: { placeholder: '@investigations_document_lhd_ref_no', language: '@language_fr_ca', value: 'Nº réf LHD' } }
  - { fields: { placeholder: '@investigations_document_lhd_rib_no', language: '@language_fr_ca', value: 'Nº RIB LHD' } }
  - { fields: { placeholder: '@investigations_document_ims_investigation_no', language: '@language_fr_ca', value: 'Nº d''enquête ims+' } }
  - { fields: { placeholder: '@investigations_document_incident_details', language: '@language_fr_ca', value: 'Détails de l''incident' } }
  - { fields: { placeholder: '@investigations_document_date_of_incident', language: '@language_fr_ca', value: 'Date de l''incident' } }
  - { fields: { placeholder: '@investigations_document_date_of_incident_notification_in_ims', language: '@language_fr_ca', value: 'Date de l''avis d''incident dans ims+' } }
  - { fields: { placeholder: '@investigations_document_reporting_details', language: '@language_fr_ca', value: 'Détails de génération de rapport' } }
  - { fields: { placeholder: '@investigations_document_position_responsible_for_feedback_to_staff', language: '@language_fr_ca', value: 'Poste responsable de la rétroaction au personnel' } }
  - { fields: { placeholder: '@investigations_document_by_when', language: '@language_fr_ca', value: Quand } }
  - { fields: { placeholder: '@investigations_document_position_responsible_for_feedback_to_patientsupport_person', language: '@language_fr_ca', value: "Par quand\nPoste responsable de la rétroaction au patient ou à la personne de soutien" } }
  - { fields: { placeholder: '@investigations_document_final_investigation_report_signed_off_by_investigation_team_on', language: '@language_fr_ca', value: 'Rapport d''enquête final signé par l''équipe d''enquête le' } }
  - { fields: { placeholder: '@investigations_document_date_report_due_to_ce', language: '@language_fr_ca', value: 'Date du rapport dû au CE' } }
  - { fields: { placeholder: '@investigations_document_date_signed_by_ce', language: '@language_fr_ca', value: 'Date de signature par le CE' } }
  - { fields: { placeholder: '@investigations_document_date_due_to_be_submitted_to_nsw_ministry', language: '@language_fr_ca', value: 'Date à soumettre au ministère de la Santé de Nouvelle-Galles du Sud' } }
  - { fields: { placeholder: '@investigations_document_date_submitted_to_nsw_ministry_of_health', language: '@language_fr_ca', value: 'Date soumise au ministère de la Santé de Nouvelle-Galles du Sud' } }
  - { fields: { placeholder: '@investigations_document_notification_of_decommissioning_of_investigation', language: '@language_fr_ca', value: 'Avis de retrait d''enquête' } }
  - { fields: { placeholder: '@investigations_document_investigation_decommissioned', language: '@language_fr_ca', value: 'Enquête retirée' } }
  - { fields: { placeholder: '@investigations_document_reason_for_decommissioning', language: '@language_fr_ca', value: 'Motif du retrait' } }
  - { fields: { placeholder: '@investigations_document_comments', language: '@language_fr_ca', value: Commentaires } }
  - { fields: { placeholder: '@investigations_document_referral_to_other_committeesagencies', language: '@language_fr_ca', value: 'Renvoi à d''autres comités ou agences' } }
  - { fields: { placeholder: '@investigations_document_other_please_specify', language: '@language_fr_ca', value: 'Autre (veuillez préciser)' } }
  - { fields: { placeholder: '@investigations_document_contact_details', language: '@language_fr_ca', value: 'Détails du contact' } }
  - { fields: { placeholder: '@investigations_document_lhd_contact_person', language: '@language_fr_ca', value: 'Personne-ressource de LHD' } }
  - { fields: { placeholder: '@investigations_document_telephone_number', language: '@language_fr_ca', value: 'Numéro de téléphone' } }
  - { fields: { placeholder: '@investigations_document_email_address', language: '@language_fr_ca', value: 'Adresse de courriel' } }
  - { fields: { placeholder: '@investigations_document_description_of_incident_that_was_investigated', language: '@language_fr_ca', value: 'Description de l''incident qui a fait l''objet d''une enquête' } }
  - { fields: { placeholder: '@investigations_document_chronology_of_events', language: '@language_fr_ca', value: 'Chronologie des événements' } }
  - { fields: { placeholder: '@investigations_document_summary_of_investigation_team_findings_and_recommendations', language: '@language_fr_ca', value: 'Sommaire des conclusions et recommandations de l''équipe d''enquête' } }
  - { fields: { placeholder: '@investigations_document_following_the_investigation_the_investigation_team_was_able', language: '@language_fr_ca', value: 'À la suite de l''enquête, l''équipe d''enquête a pu identifier' } }
  - { fields: { placeholder: '@investigations_document_for_internal_use_only', language: '@language_fr_ca', value: 'Pour usage interne uniquement' } }
  - { fields: { placeholder: '@investigations_document_attached_in_trim', language: '@language_fr_ca', value: 'Joint dans TRIM' } }
  - { fields: { placeholder: '@investigations_document_copied_to_the_cec', language: '@language_fr_ca', value: 'Copie au CEC' } }
  - { fields: { placeholder: '@investigations_document_documentation_of_causation_statements_is_a_legislative_requirement', language: '@language_fr_ca', value: 'La documentation des énoncés de causalité est une exigence législative. Toutes les déclarations de causalité doivent être conformes aux règles de causalité. Chaque facteur contributif affiché doit être traité dans le plan d''action. Décrivez le facteur contributif et classez-le de manière appropriée.' } }
  - { fields: { placeholder: '@investigations_document_filed', language: '@language_fr_ca', value: 'Classé au dossier' } }
  - { fields: { placeholder: '@investigations_document_file_no', language: '@language_fr_ca', value: 'Nº de fichier' } }
  - { fields: { placeholder: '@investigations_document_table_1_contributing_factors_table', language: '@language_fr_ca', value: 'Tableau 1 - Tableau des facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_document_a_requirement_when_causes_have_been_identified', language: '@language_fr_ca', value: '(une exigence lorsque les causes ont été identifiées)' } }
  - { fields: { placeholder: '@investigations_document_item_no', language: '@language_fr_ca', value: 'Numéro d''article' } }
  - { fields: { placeholder: '@investigations_document_description_of_contributory_factor', language: '@language_fr_ca', value: 'Description du facteur contributif' } }
  - { fields: { placeholder: '@investigations_document_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@investigations_document_table_2_investigation_team_recommendations', language: '@language_fr_ca', value: 'Tableau 2 - Recommandations de l''équipe d''enquête' } }
  - { fields: { placeholder: '@investigations_document_caustation_statement_item_no', language: '@language_fr_ca', value: 'N ° d''article de la déclaration de causalité' } }
  - { fields: { placeholder: '@investigations_document_footnote_1_marker', language: '@language_fr_ca', value: '1' } }
  - { fields: { placeholder: '@investigations_document_footnote_1', language: '@language_fr_ca', value: 'Le numéro ici se rapporte à l''énoncé de causalité numéroté dans le tableau 1 - TABLEAU DES FACTEURS CONTRIBUTIFS' } }
  - { fields: { placeholder: '@investigations_document_recommendation_classification', language: '@language_fr_ca', value: 'Classification des recommandations' } }
  - { fields: { placeholder: '@investigations_document_recommendation_category', language: '@language_fr_ca', value: Catégorie } }
  - { fields: { placeholder: '@investigations_document_position_of_person_responsible_for_implementing_recommendations', language: '@language_fr_ca', value: 'Poste de personne responsable de la mise en œuvre des recommandations' } }
  - { fields: { placeholder: '@investigations_document_outcome_measures', language: '@language_fr_ca', value: 'Mesures des résultats' } }
  - { fields: { placeholder: '@investigations_document_completion_date', language: '@language_fr_ca', value: 'Date d''achèvement' } }
  - { fields: { placeholder: '@investigations_document_management_concurrence', language: '@language_fr_ca', value: 'Accord de gestion' } }
  - { fields: { placeholder: '@investigations_document_table_3_systems_improvement_opportunities_unrelated_to', language: '@language_fr_ca', value: 'Tableau 3 - Possibilités d''amélioration des systèmes sans lien avec les facteurs contributifs' } }
  - { fields: { placeholder: '@investigations_document_modification_of_these_issues_would_not_have_helped', language: '@language_fr_ca', value: '(la modification de ces problèmes n''aurait pas aidé à empêcher l''événement)' } }
  - { fields: { placeholder: '@investigations_document_issues_for_improvement', language: '@language_fr_ca', value: 'Questions à améliorer' } }
  - { fields: { placeholder: '@investigations_document_description_of_improvement', language: '@language_fr_ca', value: 'Description de l''amélioration' } }
  - { fields: { placeholder: '@investigations_document_investigation_report_final_sign_off', language: '@language_fr_ca', value: 'Signature finale du rapport d''enquête' } }
  - { fields: { placeholder: '@investigations_document_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@investigations_document_endorsed', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@investigations_document_reason_for_nonendorsement_of_recommendations', language: '@language_fr_ca', value: 'Raison de la non-approbation des recommandations' } }
  - { fields: { placeholder: '@investigations_document_cemanagement_alternate_recommendations', language: '@language_fr_ca', value: 'Autres recommandations du CE/direction' } }
  - { fields: { placeholder: '@investigations_document_recommendation', language: '@language_fr_ca', value: Recommandation } }
  - { fields: { placeholder: '@investigations_document_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@investigations_document_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@investigations_document_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@investigations_document_on_at_time_hours', language: '@language_fr_ca', value: 'Le {{ date }} à {{ time }} heure' } }
  - { fields: { placeholder: '@investigations_template_template_f_title', language: '@language_fr_ca', value: 'Nomination de l''équipe d''enquête' } }
  - { fields: { placeholder: '@investigations_template_template_f_description', language: '@language_fr_ca', value: 'Document de nomination de l''équipe d''enquête' } }
  - { fields: { placeholder: '@investigations_template_template_f_filename', language: '@language_fr_ca', value: nomination-nouvelle-équipe } }
  - { fields: { placeholder: '@investigations_template_template_j_title', language: '@language_fr_ca', value: 'Avis d''enquête au personnel' } }
  - { fields: { placeholder: '@investigations_template_template_j_description', language: '@language_fr_ca', value: 'Document de notification d''enquête au personnel' } }
  - { fields: { placeholder: '@investigations_template_template_j_filename', language: '@language_fr_ca', value: avis.au.personnel } }
  - { fields: { placeholder: '@investigations_template_template_teamletter_title', language: '@language_fr_ca', value: 'Lettre d''enquête au membre de l''équipe' } }
  - { fields: { placeholder: '@investigations_template_template_teamletter_description', language: '@language_fr_ca', value: 'Document de lettre d''enquête au membre de l''équipe' } }
  - { fields: { placeholder: '@investigations_template_template_teamletter_filename', language: '@language_fr_ca', value: 'lettre-d''enquête-au-membre-d''équipe' } }
  - { fields: { placeholder: '@investigations_template_final_report_l1_title', language: '@language_fr_ca', value: 'Rapport de retrait d''enquête' } }
  - { fields: { placeholder: '@investigations_template_final_report_l1_description', language: '@language_fr_ca', value: 'Rapport de retrait d''enquête' } }
  - { fields: { placeholder: '@investigations_template_final_report_l1_filename', language: '@language_fr_ca', value: 'rapport de retrait d''enquête' } }
  - { fields: { placeholder: '@investigations_template_final_report_l2_title', language: '@language_fr_ca', value: 'Rapport final d''enquête' } }
  - { fields: { placeholder: '@investigations_template_final_report_l2_description', language: '@language_fr_ca', value: 'Rapport final d''enquête' } }
  - { fields: { placeholder: '@investigations_template_final_report_l2_filename', language: '@language_fr_ca', value: rapport-final-enquête } }
  - { fields: { placeholder: '@investigation_contributory_factors_removed_successfully', language: '@language_fr_ca', value: 'Facteur contributif supprimé avec succès' } }
  - { fields: { placeholder: '@forms_investigation_field_services_title', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@forms_investigation_field_services_label', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@forms_investigation_field_locations_title', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@forms_investigation_field_locations_label', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@forms_investigation_field_contacts_title', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@forms_investigation_field_contacts_label', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@investigations_user_forbidden_error', language: '@language_fr_ca', value: 'Vous ne pouvez pas Modifier vos propres permissions' } }
  - { fields: { placeholder: '@investigations_recommendations_contributory_factor_modal_title', language: '@language_fr_ca', value: "Recommandation\_: {{recommendationTitle}}" } }
  - { fields: { placeholder: '@investigations_recommendations_contributory_factor', language: '@language_fr_ca', value: 'Sélectionnez un facteur contributif' } }
  - { fields: { placeholder: '@investigations_recommendations_contributory_factor_no_title', language: '@language_fr_ca', value: 'Facteur contributif sans titre' } }
  - { fields: { placeholder: '@investigations_recommendations_contributory_factor_no_factor', language: '@language_fr_ca', value: 'Nouveau facteur contributif' } }
  - { fields: { placeholder: '@investigations_recommendations_contributory_factor_save', language: '@language_fr_ca', value: 'Enregistrer la recommandation' } }
  - { fields: { placeholder: '@investigations_recommendations_save_error', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de l''enregistrement de la recommandation' } }
  - { fields: { placeholder: '@investigations_third_party_events_events_get_collection', language: '@language_fr_ca', value: 'Impossible de lire la collection d''événements' } }
  - { fields: { placeholder: '@investigations_components_participantform_contact_unavailable_label', language: '@language_fr_ca', value: 'Contact indisponible' } }
  - { fields: { placeholder: '@investigations_components_participantform_contact_unavailable_message', language: '@language_fr_ca', value: 'Vous n''avez pas accès à ce contact' } }
