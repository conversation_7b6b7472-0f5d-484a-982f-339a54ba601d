entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.NONE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_none
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.SUBJECT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_subject
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.RECIPIENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_recipient
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.INVESTIGATOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_investigator
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.LEAD_INVESTIGATOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_lead_investigator
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_role
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.PATIENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_patient
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.WITNESS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_witness
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.NURSE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_nurse
  -
    fields:
      placeholder: INVESTIGATIONS.USER_ROLES.DOCTOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_user_roles_doctor
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.ADDNEW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_addnew
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.ADDWHY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_addwhy
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.WHY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_why
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.PROBLEM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_problem
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.CONTRIBUTORYFACTORS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_contributoryfactors
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.ADDCONTRIBUTORYFACTOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_addcontributoryfactor
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.CONTRIBUTORYFACTORTITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_contributoryfactortitle
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_save
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_delete
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.NEWPROBLEM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_newproblem
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.AREYOUSURE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_areyousure
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FIVEWHYS.WHY_PLACEHOLDER
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fivewhys_why_placeholder
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.PARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_participant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.PARTICIPANTNAME
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_participantname
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.USECONTACT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_usecontact
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.CLOSECONTACTFORM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_closecontactform
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.NEWCONTACT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_newcontact
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.VIEW_CONTACT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_view_contact
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.SEARCHEXISTINGCONTACTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_searchexistingcontacts
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.SAVEPARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_saveparticipant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.ROLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_role
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.NEWPARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_newparticipant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.NEWPARTICIPANTFROMCONTACT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_newparticipantfromcontact
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.EXISTINGPARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_existingparticipant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.PARTICIPANT_NAME
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_participant_name
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.RESET_PARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_participantform_reset_participant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_title
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.DESCRIPTION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_description
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.STARTDATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_startdate
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.END_DATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_end_date
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.SOURCE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_source
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.DELETEEVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_deleteevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.CANCEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_cancel
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.ADDANOTHEREVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_addanotherevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.SAVEEVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_saveevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.EVENTDETAILS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_eventdetails
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.ERRORS.INVALID_END_DATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_errors_invalid_end_date
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTFORM.PARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventform_participant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.FISHBONE_DIAGRAM_TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_fishbone_diagram_title
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.ADD_ROOT_NODE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_add_root_node
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_save
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_saved_successfully
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.CONTRIBUTORY_FACTOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_contributory_factor
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.NODES.CONFIRM_DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_nodes_confirm_delete
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.FISHBONE_DIAGRAM.NODES.DELETE_SUCCESSFUL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_fishbone_diagram_nodes_delete_successful
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.FORM.FORM_TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_process_maps_form_form_title
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.FORM.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_form_save
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.FORM.SAVE_AND_VIEW
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_form_save_and_view
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.OBJECTIVE_LIST
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_objective_list
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.OBJECTIVE_LIST.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_objective_list_label
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.ADD_OBJECTIVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_add_objective
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.NEW_OBJECTIVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_new_objective
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_summary
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.DETAILS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_details
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.DELETE_OBJECTIVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_delete_objective
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.SAVE_OBJECTIVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_save_objective
  -
    fields:
      placeholder: INVESTIGATIONS.OBJECTIVES.NO_OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_objectives_no_objectives
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.STARTDATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_startdate
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.ENDDATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_enddate
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.PERSON
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_person
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.ROLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_role
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_title
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.DESCRIPTION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_description
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.SOURCE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_source
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.SOURCE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_source_label
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.ADDEVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_addevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.ACTIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_actions
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.EDITEVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_editevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.VIEWEVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_viewevent
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.PROCESSMAPS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_processmaps
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_delete
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.EVENTCHRONOLOGY.ADDFIRSTMESSAGE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_eventchronology_addfirstmessage
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.MILLIMETERS_OF_MERCURY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_millimeters_of_mercury
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BEATS_PER_MINUTE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_beats_per_minute
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.OXYGEN_SATURATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_oxygen_saturation
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BREATHS_PER_MINUTE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_breaths_per_minute
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SCORE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_score
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.DEGREES_CELSIUS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_degrees_celcius
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SYSTOLIC
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_systolic
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.DIASTOLIC
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_unit_diastolic
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.SINGULAR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_singular
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.PLURAL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_plural
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.SAVE_ERROR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_save_error
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.NEW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_new
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.ADD
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_add
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.NO_MEASUREMENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_no_measurements
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.OCCURRENCE_DATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_occurrence_date
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.OCCURRENCE_TIME
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_occurrence_time
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.DETAILS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_details
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.LOADING.CLINICAL_MEASUREMENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_loading_clinical_measurement
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.LOADING.GRID
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_loading_grid
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.LOADING.CHART
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_loading_chart
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.LOADING.ERROR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_loading_error
  -
    fields:
      placeholder: INVESTIGATIONS.ATTACHMENTS.NO_ATTACHMENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_attachments_no_attachments
  -
    fields:
      placeholder: INVESTIGATIONS.ADMIN.IS_DEFAULT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_admin_is_default
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.BLOOD_PRESSURE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_blood_pressure
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.HEART_RATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_heart_rate
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.OXYGEN_SATURATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_oxygen_saturation
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.RESPIRATORY_RATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_respiratory_rate
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.PAIN_SCORE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_pain_score
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.TEMPERATURE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_temperature
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.SELECT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_type_select
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.PLEASE_ADD_VALUES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_please_add_values
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.FORM_INVALID
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_form_invalid
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.LIST.ADD_NEW
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_list_add_new
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.LIST.VIEW
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_list_view
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.DETAILS
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_details
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.APPEARANCE
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_appearance
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.BODY_COLOUR
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_body_colour
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.PROCESS_MODEL_SHAPES
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_process_model_shapes
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.FAULT_TREE_SHAPES
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_fault_tree_shapes
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.INVESTIGATION_EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_investigation_events
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.FORM.START
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_form_start
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.FORM.END
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_form_end
  -
    fields:
      placeholder: INVESTIGATIONS.PROCESS_MAPS.TOOL.FORM.DATE_NOT_SET
      type: 0
      domains:
        -
          domain: '@domain_investigations_process_maps'
    ref: investigations_process_maps_tool_form_date_not_set
  -
    fields:
      placeholder: INVESTIGATIONS.LOADING.INVESTIGATIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_loading_investigations
  -
    fields:
      placeholder: INVESTIGATIONS.LOADING.INVESTIGATION_FORM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_loading_investigation_form
  -
    fields:
      placeholder: INVESTIGATIONS.LOADING.TOOLS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_loading_tools
  -
    fields:
      placeholder: INVESTIGATIONS.LOADING.EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_loading_events
  - fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LEVELS.LEVEL_1
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_investigation_levels_level_1
  - fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LEVELS.LEVEL_2
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_investigation_levels_level_2
  - fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LEVELS.LEVEL_3
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_investigation_levels_level_3
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.NOT_STARTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_not_started
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.IN_PROGRESS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_in_progress
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.SUBMITTED_FOR_APPROVAL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_submitted_for_approval
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.COMPLETED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_completed
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.REJECTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_rejected
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STATUS.DECOMMISSIONED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_status_decommissioned
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.STARTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_started
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.PRIORITY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_priority
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.PRIORITY.LOW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_priority_low
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.PRIORITY.MEDIUM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_priority_medium
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.PRIORITY.HIGH
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_priority_high
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LEVEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_level
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LEAD_INVESTIGATOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_lead_investigator
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.OVERDUE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_overdue
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.DUE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_due
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.SUBMIT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_submit
  -
    fields:
      placeholder: INVESTIGATIONS.MODULE_TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_module_title
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.BACK_TO_DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_back_to_dashboard
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.DASHBOARD
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_dashboard
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.NEW_INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_new_investigation
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_events
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ADMINISTRATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_administration
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.INVESTIGATION.PAGE_TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_investigation_page_title
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.INVESTIGATION.DEFINE_INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_investigation_define_investigation
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.INVESTIGATION.OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_investigation_objectives
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.INVESTIGATION.RECOMMENDATIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_investigation_recommendations
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.INVESTIGATION.ATTACHMENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_investigation_attachments
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.ADMIN.TOOLS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_admin_tools
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.THIRD_PARTY_EVENTS.REJECTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_third_party_events_rejected
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.THIRD_PARTY_EVENTS.REJECTION.REASON_REQUIRED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_success_third_party_events_rejection_reason_required
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.THIRD_PARTY_EVENTS.INVESTIGATION_CREATED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_third_party_events_investigation_created
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.ATTACHMENT.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_attachment_save
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.INVESTIGATION.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_investigation_save
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.INVESTIGATION.TOOL_REFRESH
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_investigation_tool_refresh
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.CLINICAL_MEASUREMENTS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_clinical_measurements_save
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.EVENTS.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_events_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.EVENTS.DELETED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_events_deleted
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.FIVE_WHYS.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_five_whys_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.FIVE_WHYS.DELETED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_five_whys_deleted
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.NOTES.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_notes_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.NOTES.DELETED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_notes_deleted
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.OBJECTIVES.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_objectives_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.OBJECTIVES.DELETED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_objectives_deleted
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.PARTICIPANTS.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_participants_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.PROCESS_MAP.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_process_map_saved
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.SAFER_MATRIX.SAVED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_success_safer_matrix_saved
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.CREATE_INVESTIGATION.INVESTIGATION_TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_create_investigation_investigation_title
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.CREATE_INVESTIGATION.ADD_USERS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_create_investigation_add_users
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.CREATE_INVESTIGATION.SUBMIT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_create_investigation_submit
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.REJECT_EVENT.REASON
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_reject_event_reason
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.REJECT_EVENT.SUBMIT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_reject_event_submit
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.TABS.EVENT_SUMMARY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_tabs_event_summary
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.TABS.CREATE_INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_tabs_create_investigation
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.TABS.REJECT_EVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_tabs_reject_event
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.REFERENCE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_reference
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.NAME
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_name
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.OWNER
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_owner
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.SAC_SCORE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_sac_score
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.PERSON_AFFECTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_person_affected
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.HANDLER
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_handler
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.OCCURRED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_occurred
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.TIME_PERSON_GRID.ADD_EVENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_time_person_grid_add_event
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.TIME_PERSON_GRID.FIT_ALL_EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_time_person_grid_fit_all_events
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.TIME_PERSON_GRID.EDIT_PARTICIPANT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_time_person_grid_edit_participant
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.OPENSAFERMATRIX
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_opensafermatrix
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.SAVEANDCLOSE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_saveandclose
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CLOSE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_close
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.MATRIX
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_matrix
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors_save
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS.ADD
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors_add
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS.RESET
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors_reset
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS.NO_CONTRIBUTORY_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors_no_contributory_factors
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.CONTRIBUTORYFACTORS.NEW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_contributoryfactors_new
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_save
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.STRUCTURE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_structure
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.WHY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_why
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.HOW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_how
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.PROCESS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_process
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.OUTCOME
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_outcome
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.WHAT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_what
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.PATIENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_patient
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.PERSONNEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_personnel
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.ENVIRONMENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_environment
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.ORGANISATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_organisation
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.SAFERMATRIX.REGULATORYAGENCIES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_safermatrix_regulatoryagencies
  -
    fields:
      placeholder: INVESTIGATIONS.CONTACT.ROLE.PATIENT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_contact_role_patient
  -
    fields:
      placeholder: INVESTIGATIONS.CONTACT.ROLE.WITNESS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_contact_role_witness
  -
    fields:
      placeholder: INVESTIGATIONS.CONTACT.ROLE.NURSE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_contact_role_nurse
  -
    fields:
      placeholder: INVESTIGATIONS.CONTACT.ROLE.DOCTOR
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_contact_role_doctor
  -
    fields:
      placeholder: INVESTIGATIONS.DASHBOARD.NO_RESULTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_dashboard_no_results
  -
    fields:
      placeholder: INVESTIGATIONS.DASHBOARD.FILTER.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_dashboard_filter_title
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.GET_FORM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_get_form
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.ADMIN.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_admin_save
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.THIRD_PARTY_EVENTS.GET_COLLECTION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_third_party_events_get_collection
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.THIRD_PARTY_EVENTS.CREATE_INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_third_party_events_create_investigation
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.THIRD_PARTY_EVENTS.REJECT
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_third_party_events_reject
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.CLINICAL_MEASUREMENTS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_clinical_measurements_save
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.CLINICAL_MEASUREMENTS.SAVE_VALUE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_clinical_measurements_save_value
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.EVENTS.GET_COLLECTION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_events_get_collection
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.EVENTS.DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_events_delete
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FIVE_WHYS.GET_COLLECTION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_five_whys_get_collection
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FIVE_WHYS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_five_whys_save
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FIVE_WHYS.DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_five_whys_delete
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.OBJECTIVES.DELETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_objectives_delete
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.PROCESS_MAPS.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_process_maps_save
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.SAFER_MATRIX.SAVE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_safer_matrix_save
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FILE.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_file_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.ATTACHMENT.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_attachment_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FISHBONE.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_fishbone_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.FIVEWHY.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_fivewhy_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.ERROR.SAFER_MATRIX.NOT_FOUND
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_error_safer_matrix_not_found
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.ADD_TOOL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_add_tool
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.COLLECT_DATA
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_collect_data
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.ANALYSE_DATA
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_analyse_data
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.EVENT_CHRONOLOGY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_event_chronology
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.TIME_PERSON_GRID
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_time_person_grid
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.CLINICAL_MEASUREMENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_clinical_measurements
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.SAFER_MATRIX
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_safer_matrix
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.FIVE_WHYS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_five_whys
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.FISHBONE_DIAGRAM
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_fishbone_diagram
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.PROCESS_MAPS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_process_maps
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.OBJECTIVES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_objectives
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.CONTRIBUTORY_FACTORS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_contributory_factors
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.ADD
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_add
  -
    fields:
      placeholder: INVESTIGATIONS.TOOLS.NO_TOOLS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_tools_no_tools
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.VALUES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_values
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.NO_VALUES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_no_values
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.ADD_VALUE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_add_value
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.EDIT_VALUE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_edit_value
  -
    fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.START_DATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_components_clinical_measurements_start_date
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_ID
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_id
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_ID.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_id_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_TITLE
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_title
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_TITLE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_title_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_DESCRIPTION
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_description
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_DESCRIPTION.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_description_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_TARGET_DUE_DATE
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_target_due_date
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_TARGET_DUE_DATE.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_target_due_date_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_LEVEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_level
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_LEVEL.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_level_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_PRIORITY
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_priority
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_PRIORITY.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_priority_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_STATUS
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_status
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.INVESTIGATION_STATUS.LABEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_status_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.REJECTED_REASON
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_rejection
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.REJECTED_REASON_LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_rejection_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_REASON.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioning_title
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_REASON.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioning_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_ENDORSE_REPORT.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_title
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_ENDORSE_REPORT.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_ENDORSE_REPORT.YES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_yes
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_ENDORSE_REPORT.NO
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_endorse_report_no
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_AUTHORISED_TO_ENDORSE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_title
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_AUTHORISED_TO_ENDORSE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_label
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_AUTHORISED_TO_ENDORSE.YES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_yes
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELDS.DECOMMISSIONED_AUTHORISED_TO_ENDORSE.NO
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_investigation_reason_for_decommissioned_authorised_to_endorse_no
  -
    fields:
      placeholder: FORMS.INVESTIGATION.ERRORS.UNAUTHORISED_TO_DECOMMISSION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_errors_unauthorised_to_decommission
  -
    fields:
      placeholder: FORMS.INVESTIGATION.VALUE.LOW
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_value_low
  -
    fields:
      placeholder: FORMS.INVESTIGATION.VALUE.MEDIUM
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_value_medium
  -
    fields:
      placeholder: FORMS.INVESTIGATION.VALUE.HIGH
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_value_high
  -
    fields:
      placeholder: FORMS.INVESTIGATION.TITLE
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_title
  -
    fields:
      placeholder: FORMS.INVESTIGATION.SUMMARY
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_summary
  -
    fields:
      placeholder: FORMS.INVESTIGATION.SECTION.TITLE_AND_DETAILS
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_section_title_and_details
  -
    fields:
      placeholder: FORMS.INVESTIGATION.SECTION.LEVEL
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_section_level
  -
    fields:
      placeholder: FORMS.INVESTIGATION.FIELD.LEVEL.SELECT
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_field_level_select
  -
    fields:
      placeholder: FORMS.INVESTIGATION.EVENTS.SOURCE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_events_source
  -
    fields:
      placeholder: FORMS.INVESTIGATION.EVENTS.VIEW
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: forms_investigation_events_view
  -
    fields:
      placeholder: INVESTIGATIONS.CONTRIBUTORY_FACTORS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_contributory_factors_saved_successfully
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.BANNERS.COMPLETE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_banners_complete
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.BANNERS.REJECTED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_banners_rejected
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.BANNERS.DECOMMISSIONED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_banners_decommissioned
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.BANNERS.LOCKED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_banners_locked
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.ERROR.LOCKED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_error_locked
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.ERROR.EDIT.STATE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_error_edit_state
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.BANNERS.REOPEN
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_banners_reopen
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.REOPENED_SUCCESSFULLY
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_investigation_reopened_successfully
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.INVESTIGATION_LEVELS
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_datasource_investigation_levels
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.INVESTIGATION_PRIORITIES
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_datasource_investigation_priorities
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.INVESTIGATION_STATUSES
      type: 1
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_datasource_investigation_statuses
  -
    fields:
      placeholder: INVESTIGATIONS.FORM_TYPE.INVESTIGATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigations_form_type_investigation
  -
    fields:
      placeholder: INVESTIGATIONS.SUCCESS.TEMPLATE_DOCUMENT_ATTACHED
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigations_success_template_document_attached
  -
    fields:
      placeholder: INVESTIGATIONS.INVESTIGATION.LOCATION
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigations_investigation_location
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ADMIN
      pointer: COMMON.ADMIN
      type: 0
      domains:
        -
          domain: '@domain_investigations'
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ADMIN.TOOLS
      pointer: COMMON.ADMINISTRATION.TOOLS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ADMIN.PERMISSIONS
      pointer: COMMON.ADMINISTRATION.PERMISSIONS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
  -
    fields:
      placeholder: INVESTIGATIONS.NAV.ADMIN.EVENTS
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_nav_admin_events
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.LOCATION_TYPES
      type: 2
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_location_type

  - fields:
      placeholder: INVESTIGATION.LOCATION.TYPE.TITLE
      type: 0
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_location_type_title
  -
    fields:
      placeholder: INVESTIGATION.LOCATION.TYPE.LABEL
      type: 0
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_location_type_label
  -
    fields:
      placeholder: INVESTIGATION.LOCATION.TYPE_ORIGINATION
      type: 0
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_location_type_origination
  -
    fields:
      placeholder: INVESTIGATION.LOCATION.TYPE_DISCOVERY
      type: 0
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_location_type_discovery
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.INVESTIGATION_REPORT_DUE.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_investigation_report_due_label
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.INVESTIGATION_REPORT_DUE.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_investigation_report_due_title
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.FEEDBACK_DUE_TO_STAFF.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_feedback_due_to_staff_label
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.FEEDBACK_DUE_TO_STAFF.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_feedback_due_to_staff_title
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.FEEDBACK_DUE_TO_PATIENT_SUPPORT_PERSON.LABEL
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_feedback_due_to_patient_support_person_label
  -
    fields:
      placeholder: INVESTIGATION.FIELDS.FEEDBACK_DUE_TO_PATIENT_SUPPORT_PERSON.TITLE
      type: 0
      domains:
        -
          domain: '@domain_investigations'
        -
          domain: '@domain_form_types'
    ref: investigation_fields_feedback_due_to_patient_support_person_title
  -
    fields:
      placeholder: INVESTIGATION.DOCUMENT.FIELD.TEAMLEADER
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigation_document_field_teamleader
  -
    fields:
      placeholder: INVESTIGATION.DOCUMENT.FIELD.TEAMMEMBER
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigation_document_field_teammember
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.CONTACT_ROLES
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_datasource_contact_roles
  -
    fields:
      placeholder: INVESTIGATIONS.EVENTS.EVENT_NOT_PENDING
      type: 0
      domains:
        -
          domain: '@domain_investigations'
    ref: investigations_events_event_not_pending
  -
    fields:
      placeholder: INVESTIGATIONS.DATASOURCE.SERVICE_TYPES
      type: 2
      domains:
      -
        domain: '@domain_investigations'
    ref: investigation_service_type
  - fields:
      placeholder: INVESTIGATION.SERVICE_TYPE.TITLE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigation_service_type_title
  - fields:
      placeholder: INVESTIGATION.SERVICE_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigation_service_type_label
  - fields:
      placeholder: INVESTIGATION.SERVICE_TYPE.ORIGINATION
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigation_service_type_origination
  - fields:
      placeholder: INVESTIGATION.SERVICE_TYPE.DISCOVERY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigation_service_type_discovery
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.HEALTH_DISTRICTNETWORK
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_health_districtnetwork
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FINAL_INVESTIGATION_REPORT
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_final_investigation_report
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.REFERENCE_NUMBERS_WHERE_APPLICABLE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_reference_numbers_where_applicable
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.MOH_RIB_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_moh_rib_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.IMS_RIB_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_ims_rib_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.LHD_REF_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_lhd_ref_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.LHD_RIB_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_lhd_rib_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.IMS_INVESTIGATION_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_ims_investigation_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.INCIDENT_DETAILS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_incident_details
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_OF_INCIDENT
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_of_incident
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_OF_INCIDENT_NOTIFICATION_IN_IMS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_of_incident_notification_in_ims
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.REPORTING_DETAILS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_reporting_details
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.POSITION_RESPONSIBLE_FOR_FEEDBACK_TO_STAFF
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_position_responsible_for_feedback_to_staff
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.BY_WHEN
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_by_when
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.POSITION_RESPONSIBLE_FOR_FEEDBACK_TO_PATIENTSUPPORT_PERSON
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_position_responsible_for_feedback_to_patientsupport_person
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FINAL_INVESTIGATION_REPORT_SIGNED_OFF_BY_INVESTIGATION_TEAM_ON
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_final_investigation_report_signed_off_by_investigation_team_on
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_REPORT_DUE_TO_CE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_report_due_to_ce
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_SIGNED_BY_CE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_signed_by_ce
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_DUE_TO_BE_SUBMITTED_TO_NSW_MINISTRY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_due_to_be_submitted_to_nsw_ministry
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE_SUBMITTED_TO_NSW_MINISTRY_OF_HEALTH
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date_submitted_to_nsw_ministry_of_health
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.NOTIFICATION_OF_DECOMMISSIONING_OF_INVESTIGATION
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_notification_of_decommissioning_of_investigation
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.INVESTIGATION_DECOMMISSIONED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_investigation_decommissioned
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.REASON_FOR_DECOMMISSIONING
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_reason_for_decommissioning
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.COMMENTS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_comments
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.REFERRAL_TO_OTHER_COMMITTEESAGENCIES
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_referral_to_other_committeesagencies
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.OTHER_PLEASE_SPECIFY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_other_please_specify
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.CONTACT_DETAILS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_contact_details
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.LHD_CONTACT_PERSON
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_lhd_contact_person
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.TELEPHONE_NUMBER
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_telephone_number
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.EMAIL_ADDRESS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_email_address
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DESCRIPTION_OF_INCIDENT_THAT_WAS_INVESTIGATED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_description_of_incident_that_was_investigated
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.CHRONOLOGY_OF_EVENTS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_chronology_of_events
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.SUMMARY_OF_INVESTIGATION_TEAM_FINDINGS_AND_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_summary_of_investigation_team_findings_and_recommendations
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FOLLOWING_THE_INVESTIGATION_THE_INVESTIGATION_TEAM_WAS_ABLE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_following_the_investigation_the_investigation_team_was_able
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FOR_INTERNAL_USE_ONLY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_for_internal_use_only
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ATTACHED_IN_TRIM
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_attached_in_trim
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.COPIED_TO_THE_CEC
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_copied_to_the_cec
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DOCUMENTATION_OF_CAUSATION_STATEMENTS_IS_A_LEGISLATIVE_REQUIREMENT
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_documentation_of_causation_statements_is_a_legislative_requirement
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FILED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_filed
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FILE_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_file_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.TABLE_1_CONTRIBUTING_FACTORS_TABLE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_table_1_contributing_factors_table
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.A_REQUIREMENT_WHEN_CAUSES_HAVE_BEEN_IDENTIFIED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_a_requirement_when_causes_have_been_identified
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ITEM_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_item_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DESCRIPTION_OF_CONTRIBUTORY_FACTOR
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_description_of_contributory_factor
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.CATEGORY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_category
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.TABLE_2_INVESTIGATION_TEAM_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_table_2_investigation_team_recommendations
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.CAUSTATION_STATEMENT_ITEM_NO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_caustation_statement_item_no
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FOOTNOTE_1_MARKER
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_footnote_1_marker
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.FOOTNOTE_1
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_footnote_1
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.RECOMMENDATION_CLASSIFICATION
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_recommendation_classification
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.RECOMMENDATION_CATEGORY
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_recommendation_category
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.POSITION_OF_PERSON_RESPONSIBLE_FOR_IMPLEMENTING_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_position_of_person_responsible_for_implementing_recommendations
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.OUTCOME_MEASURES
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_outcome_measures
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.COMPLETION_DATE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_completion_date
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.MANAGEMENT_CONCURRENCE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_management_concurrence
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.TABLE_3_SYSTEMS_IMPROVEMENT_OPPORTUNITIES_UNRELATED_TO
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_table_3_systems_improvement_opportunities_unrelated_to
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.MODIFICATION_OF_THESE_ISSUES_WOULD_NOT_HAVE_HELPED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_modification_of_these_issues_would_not_have_helped
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ISSUES_FOR_IMPROVEMENT
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_issues_for_improvement
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DESCRIPTION_OF_IMPROVEMENT
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_description_of_improvement
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.INVESTIGATION_REPORT_FINAL_SIGN_OFF
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_investigation_report_final_sign_off
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.NAME
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_name
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ENDORSED
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_endorsed
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.REASON_FOR_NONENDORSEMENT_OF_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_reason_for_nonendorsement_of_recommendations
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.CEMANAGEMENT_ALTERNATE_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_cemanagement_alternate_recommendations
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.RECOMMENDATION
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_recommendation
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.TITLE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_title
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ID
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_id
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.DATE
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_date
  - fields:
      placeholder: INVESTIGATIONS.DOCUMENT.ON_AT_TIME_HOURS
      type: 0
      domains:
        - domain: '@domain_investigations'
    ref: investigations_document_on_at_time_hours
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_F.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_f_title
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_F.DESCRIPTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_f_description
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_F.FILENAME
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_f_filename
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_J.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_j_title
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_J.DESCRIPTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_j_description
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_J.FILENAME
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_j_filename
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_TEAMLETTER.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_teamletter_title
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_TEAMLETTER.DESCRIPTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_teamletter_description
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.TEMPLATE_TEAMLETTER.FILENAME
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_template_teamletter_filename
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L1.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l1_title
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L1.DESCRIPTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l1_description
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L1.FILENAME
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l1_filename
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L2.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l2_title
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L2.DESCRIPTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l2_description
  - fields:
      placeholder: INVESTIGATIONS.TEMPLATE.FINAL_REPORT_L2.FILENAME
      domains:
        - domain: '@domain_investigations'
    ref: investigations_template_final_report_l2_filename
  - fields:
      placeholder: INVESTIGATION.CONTRIBUTORY_FACTORS.REMOVED_SUCCESSFULLY
      domains:
        - domain: '@domain_investigations'
    ref: investigation_contributory_factors_removed_successfully
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.SERVICES.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_services_title
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.SERVICES.LABEL
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_services_label
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.LOCATIONS.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_locations_title
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.LOCATIONS.LABEL
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_locations_label
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.CONTACTS.TITLE
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_contacts_title
  - fields:
      placeholder: FORMS.INVESTIGATION.FIELD.CONTACTS.LABEL
      domains:
        - domain: '@domain_investigations'
    ref: forms_investigation_field_contacts_label
  - fields:
      placeholder: INVESTIGATIONS.USER.FORBIDDEN_ERROR
      domains:
        - domain: '@domain_investigations'
    ref: investigations_user_forbidden_error
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR.MODAL_TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_contributory_factor_modal_title
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_contributory_factor
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR.NO_TITLE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_contributory_factor_no_title
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR.NO_FACTOR
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_contributory_factor_no_factor
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.CONTRIBUTORY_FACTOR.SAVE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_contributory_factor_save
  - fields:
      placeholder: INVESTIGATIONS.RECOMMENDATIONS.SAVE_ERROR
      domains:
        - domain: '@domain_investigations'
    ref: investigations_recommendations_save_error
  - fields:
      placeholder: INVESTIGATIONS.THIRD_PARTY_EVENTS.EVENTS.GET_COLLECTION
      domains:
        - domain: '@domain_investigations'
    ref: investigations_third_party_events_events_get_collection
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.CONTACT_UNAVAILABLE_LABEL
      domains:
        - domain: '@domain_investigations'
    ref: investigations_components_participantform_contact_unavailable_label
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.PARTICIPANTFORM.CONTACT_UNAVAILABLE_MESSAGE
      domains:
        - domain: '@domain_investigations'
    ref: investigations_components_participantform_contact_unavailable_message
  - fields:
      placeholder: INVESTIGATIONS.SUCCESS.INVESTIGATION.REOPENED
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_success_investigation_reopened
  - fields:
      placeholder: INVESTIGATIONS.SUCCESS.INVESTIGATION.DELETED
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_success_investigation_deleted
  - fields:
      placeholder: INVESTIGATIONS.CONFIRM.CLINICAL.MEASUREMENT.DELETE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_confirm_clinical_measurement_delete
  - fields:
      placeholder: INVESTIGATIONS.SUCCESS.CLINICAL.MEASUREMENT.DELETE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_success_clinical_measurement_delete
  - fields:
      placeholder: INVESTIGATIONS.CLINICAL.MEASUREMENT.ACTIONS
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_actions
  - fields:
      placeholder: INVESTIGATIONS.CLINICAL.MEASUREMENT.ACTION.EDIT.CLINICAL.MEASUREMENT
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_action_edit_clinical_measurement
  - fields:
      placeholder: INVESTIGATIONS.CLINICAL.MEASUREMENT.ACTION.DELETE.CLINICAL.MEASUREMENT
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_action_delete_clinical_measurement
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.BLOOD_GLUCOSE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_type_blood_glucose
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.MOLAR_CONCENTRATION
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_molar_concentration
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.BLOOD_GLUCOSE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_blood_glucose
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.CAPILLARY_REFILL
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_type_capillary_refill
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.SECONDS
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_seconds
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.CAPILLARY_REFILL
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_capillary_refill
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.NEWS2_SCORE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_type_news2_score
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.NEWS2_SCORE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_news2_score
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.ETCO2_SCORE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_type_etco2_score
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.KILOPASCALS
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_kilopascals
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.ETCO2_SCORE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurement_unit_etco2_score
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.PUPILS
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_type_pupils
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.FIELDS.LEFT_PUPIL_SIZE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_fields_left_pupil_size
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.FIELDS.LEFT_PUPIL_REACTIVE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_fields_left_pupil_reactive
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.FIELDS.RIGHT_PUPIL_SIZE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_fields_right_pupil_size
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.FIELDS.RIGHT_PUPIL_REACTIVE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_fields_right_pupil_reactive
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.TYPE.AVCPU
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_type_avcpu
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.UNIT.AVCPU
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_unit_avcpu
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.AVCPU.ALERT
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_avcpu_alert
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.AVCPU.CONFUSION
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_avcpu_confusion
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.AVCPU.VOICE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_avcpu_voice
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.AVCPU.PAIN
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_avcpu_pain
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.AVCPU.UNRESPONSIVE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_avcpu_unresponsive
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.PUPIL_REACTIVE.REACTIVE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_pupil_reactive_reactive
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.PUPIL_REACTIVE.NON_REACTIVE
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_pupil_reactive_non_reactive
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.ERRORS.EMPTY_VALUES
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_errors_empty_values
  - fields:
      placeholder: INVESTIGATIONS.COMPONENTS.CLINICAL_MEASUREMENTS.ERRORS.INVALID_VALUES
      domains:
        - domain: '@domain_investigations'
    ref: placeholder_investigations_clinical_measurements_errors_invalid_values
