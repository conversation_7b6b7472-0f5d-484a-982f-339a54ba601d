entityClass: I18n\Entity\Placeholder
priority: 10
data:
  - fields:
      placeholder: CONTROLS.LOADING.CONTROLS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_controls
  - fields:
      placeholder: CONTROLS.LOADING.CONTROL
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_control
  - fields:
      placeholder: CONTROLS.LOADING.RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_recommendations
  - fields:
      placeholder: CONTROLS.LOADING.CONTRIBUTORY_FACTOR
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_contributory_factor
  - fields:
      placeholder: CONTROLS.LOADING.CONTRIBUTORY_FACTOR_CONTROLS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_contributory_factor_controls
  - fields:
      placeholder: CONTROLS.LOADING.CONTRIBUTORY_FACTORS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_contributory_factors
  - fields:
      placeholder: CONTROLS.LOADING.COMPARATORS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_loading_comparators
  - fields:
      placeholder: CONTROLS.SINGULAR
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_singular
  - fields:
      placeholder: CONTROLS.PLURAL
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_plural
  - fields:
      placeholder: CONTROLS.SEARCH
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_search
  - fields:
      placeholder: CONTROLS.CREATE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_create
  - fields:
      placeholder: CONTROLS.EDIT
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_edit
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.NAV.DASHBOARD
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_recommendations_nav_dashboard

  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.POST_SAVE.NO_ACCESS
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_recommendations_post_save_no_access
  - fields:
      placeholder: CONTROLS.SUCCESS.SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_success_save
  - fields:
      placeholder: CONTROLS.SUCCESS.RECOMMENDATION.SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_success_recommendation_save
  - fields:
      placeholder: CONTROLS.SUCCESS.CONTRIBUTORY_FACTORS.SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_success_contributory_factors_save
  - fields:
      placeholder: CONTROLS.FORM.TITLE
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_title
  - fields:
      placeholder: CONTROLS.FORM.TITLE.NEW_CONTROL
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_form_title_new_control
  - fields:
      placeholder: CONTROLS.FORM.FIELD.ADD_CONTROL
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_form_field_add_control
  - fields:
      placeholder: CONTROLS.FORM.SUMMARY
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_summary
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.LABEL
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_label
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.EMPTY_VALUE
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_empty_value
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.OPTIONS.NONE
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_options_none
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.OPTIONS.OPINION
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_options_opinion
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.OPTIONS.EXPERT_OPINION
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_options_expert_opinion
  - fields:
      placeholder: CONTROLS.FORM.SUPPORT.OPTIONS.LITERATURE_REVIEW
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_support_options_literature_review
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.LABEL
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_label
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.EMPTY_VALUE
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_empty_value
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.NONE
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_none
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.ADDITIONAL_STUDY
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_additional_study
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.TRAINING
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_training
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.WARNING_LABEL
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_warning_label
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.DOUBLE_CHECK
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_double_check
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.COGNITIVE_AID
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_cognitive_aid
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.ELIMINATE_DISTRACTIONS
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_eliminate_distractions
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.INCREASE_STAFFING
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_increase_staffing
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.STANDARDISE_EQUIPMENT
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_standardise_equipment
  - fields:
      placeholder: CONTROLS.FORM.EFFECTIVENESS.OPTIONS.SIMPLIFY_PROCESSES
      type: 1
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_form_effectiveness_options_simplify_processes
  - fields:
      placeholder: CONTROLS.FORM.ERROR.RETRIEVING_FORM
      type: 1
      domains:
        - domain: '@domain_common'
    ref: controls_form_error_retrieving_form
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.ID
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_id
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.ID.LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_id_label
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.TITLE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_title
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.TITLE.LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_title_label
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.AGGREGATE_SCORE
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_aggregate_score
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.AGGREGATE_SCORE.LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_investigations'
        - domain: '@domain_enterprise_risk_manager'
    ref: controls_list_columns_aggregate_score_label
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.CONTRIBUTORY_FACTOR
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_list_columns_contributory_factor
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.CONTRIBUTORY_FACTOR.LABEL
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_list_columns_contributory_factor_label
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.AUDITED
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_columns_audited
  - fields:
      placeholder: CONTROLS.CONTROL.UPDATE_STATUS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_update_status
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.RESULTS_FOUND
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_results_found
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.RESULTS_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_results_not_found
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.SEND_FOR_APPROVAL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_send_for_approval
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.APPROVE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_approve
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.PARK
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_park
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.COMPARE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_compare
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.COMPARE.WARNING.SELECT_CONTROLS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_compare_warning_select_controls
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.REJECT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_reject
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.SENT_FOR_APPROVAL.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_sent_for_approval_success_message
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.APPROVED.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_approved_success_message
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.PARK.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_park_success_message
  - fields:
      placeholder: CONTROLS.LIST.ACTIONS.REJECT.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_list_actions_reject_success_message
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATOR_VALUES
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparator_values
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_label
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.COLUMNS.COMPARATOR
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_columns_comparator
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.COLUMNS.VALUE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_columns_value
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.COLUMNS.SCORE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_columns_score
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.VIEW_CONTROL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_view_control
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_save
  - fields:
      placeholder: CONTROLS.CONTROL.APPROVED.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_approved_success_message
  -
    fields:
      placeholder: CONTROLS.CONTROL.REJECTED.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_rejected_success_message
  -
    fields:
      placeholder: CONTROLS.NAV.RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_recommendations
  - fields:
      placeholder: CONTROLS.NAV.ALL_RECOMMENDATIONS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_all_recommendations
  - fields:
      placeholder: CONTROLS.NAV.CONTROLS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_controls
  - fields:
      placeholder: CONTROLS.NAV.ALL_CONTROLS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_all_controls
  - fields:
      placeholder: CONTROLS.NAV.UNDER_REVIEW
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_under_review
  - fields:
      placeholder: CONTROLS.NAV.IN_PROGRESS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_in_progress
  - fields:
      placeholder: CONTROLS.NAV.AWAITING_APPROVAL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_awaiting_approval
  - fields:
      placeholder: CONTROLS.NAV.APPROVED
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_approved
  - fields:
      placeholder: CONTROLS.NAV.REJECTED
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_rejected
  - fields:
      placeholder: CONTROLS.NAV.NEW_CONTROL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_new_control
  - fields:
      placeholder: CONTROLS.NAV.NEW_RECOMMENDATION
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_new_recommendation
  - fields:
      placeholder: CONTROLS.NAV.RECOMMENDATION_DETAILS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_recommendation_details
  - fields:
      placeholder: CONTROLS.NAV.ADMIN_COMPARATORS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_admin_comparators
  - fields:
      placeholder: CONTROLS.NAV.ADMIN_PERMISSIONS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_nav_admin_permissions
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.RESULTS_FOUND
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_results_found
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.RESULTS_NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_results_not_found
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.SEND_FOR_APPROVAL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_send_for_approval
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.APPROVE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_approve
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.PARK
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_park
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.COMPARE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_compare
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.REJECT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_reject
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.APPROVE.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_approve_success_message
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.PARK.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_park_success_message
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.ACTIONS.REJECT.SUCCESS_MESSAGE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_actions_reject_success_message
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.COLUMNS.ID
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_columns_id
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.LIST.COLUMNS.STATEMENT_OF_INTENT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_list_columns_statement_of_intent
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.FIELDS.STATEMENT_OF_INTENT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_fields_statement_of_intent
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.FIELDS.SUMMARY
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_fields_summary
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_save
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_recommendations_saved_successfully
  - fields:
      placeholder: CONTROLS.CONTROL.NAV.DETAILS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_nav_details
  - fields:
      placeholder: CONTROLS.CONTROL.NAV.ACTIONS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_nav_actions
  - fields:
      placeholder: CONTROLS.CONTROL.NAV.ATTACHMENTS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_nav_attachments
  - fields:
      placeholder: CONTROLS.CONTROL.NAV.AUDIT
      type: 0
      domains:
        -
          domain: '@domain_controls'
    ref: controls_control_nav_audit
  -
    fields:
      placeholder: CONTROLS.CONTROL.NAV.ACCESS_CONTROL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_nav_access_control
  - fields:
      placeholder: CONTROLS.CONTROL.NAV.COMPARATORS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_nav_comparators
  - fields:
      placeholder: CONTROLS.CONTROL.EDIT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_edit
  - fields:
      placeholder: CONTROLS.CONTROL.REVIEW_DATE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_review_date
  - fields:
      placeholder: CONTROLS.CONTROL.REVIEW_DATE.NO_DATE_SET
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_review_date_no_date_set
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_label
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.UNDER_REVIEW
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_under_review
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.AWAITING_APPROVAL
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_awaiting_approval
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.APPROVED
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_approved
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.PARKED
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_parked
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.REJECTED
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_rejected
  - fields:
      placeholder: CONTROLS.CONTROL.STATUS.OPTIONS.UNDER_REVISION
      type: 1
      domains:
        - domain: '@domain_controls'
    ref: controls_control_status_options_under_revision
  - fields:
      placeholder: CONTROLS.CONTROL.SUMMARY
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_summary
  - fields:
      placeholder: CONTROLS.CONTROL.CONTENT
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_content
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.ADD
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_add
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.FIELDS.TITLE.LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_fields_title_label
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.DELETE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_delete
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.OPTIONS.LIST.OPTION_LABEL
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_options_list_option_label
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.OPTIONS.LIST.SCORE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_options_list_score
  - fields:
      placeholder: CONTROLS.ADMIN.COMPARATORS.OPTIONS.ADD
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_admin_comparators_options_add
  - fields:
      placeholder: CONTROLS.CONTRIBUTORY_FACTORS.CLASSIFICATION
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_contributory_factors_classification

  # Recommendations
  - fields:
      placeholder: RECOMMENDATIONS.SEARCH.MATCH_CLASSIFICATION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: recommendations_search_match_classification
  - fields:
      placeholder: RECOMMENDATIONS.SEARCH.TITLE
      type: 0
      pointer: COMMON.TITLE
      domains:
        - domain: '@domain_common'
    ref: recommendations_search_title
  - fields:
      placeholder: RECOMMENDATIONS.SEARCH.DESCRIPTION
      type: 0
      pointer: COMMON.DESCRIPTION
      domains:
        - domain: '@domain_common'
    ref: recommendations_search_description
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.PRIORITIES
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.PRIORITIES.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_label
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.PRIORITIES.OPTIONS.LOW
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_options_low
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.PRIORITIES.OPTIONS.MEDIUM
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_options_medium
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.PRIORITIES.OPTIONS.HIGH
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_options_high
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.OWNER
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_owner
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.OWNER.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_owner_label
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.CREATED_AT
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_created_at
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.CREATED_AT.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_priorities_created_at_label
  - fields:
      placeholder: CONTROLS.DATASOURCE.CONTROL_STATUSES
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_datasource_control_statuses
  - fields:
      placeholder: CONTROLS.DATASOURCE.CONTRIBUTORY_FACTOR_CLASSIFICATIONS
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_datasource_contributory_factor_classifications
  - fields:
      placeholder: CONTROLS.DATASOURCE.RECOMMENDATION_PRIORITIES
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_datasource_recommendation_priorities
  - fields:
      placeholder: CONTROLS.FORM_TYPE.CONTROL_FORM
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_form_types'
    ref: control_form_type_control_form
  - fields:
      placeholder: RECOMMENDATIONS.FORM_TYPE.RECOMMENDATION_FORM
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
        - domain: '@domain_form_types'
    ref: recommendations_form_type_recommendation_form
  - fields:
      placeholder: RECOMMENDATIONS.CONTRIBUTORY_FACTORS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: recommendations_contributory_factors_saved_successfully
  - fields:
      placeholder: RECOMMENDATIONS.CONTRIBUTORY_FACTORS.REMOVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: recommendations_contributory_factors_removed_successfully
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.UNTITLED
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_untitled
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.DELETE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_delete
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.MAIN_SAVE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_main_save
  - fields:
      placeholder: CONTROLS.CONTROL.COMPARATORS.OPTION_VALUE
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_control_comparators_option_value
  - fields:
      placeholder: CONTROLS.FILTER.FORM.FORM_TITLE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_investigations'
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_filter_form_form_title
  - fields:
      placeholder: CONTROLS.FILTER.FORM.TITLE.TITLE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_investigations'
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_filter_form_title_title
  - fields:
      placeholder: CONTROLS.FILTER.FORM.TITLE.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_investigations'
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_filter_form_title
  - fields:
      placeholder: CONTROLS.FILTER.FORM.SUMMARY.TITLE
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_investigations'
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_filter_form_summary_title
  - fields:
      placeholder: CONTROLS.FILTER.FORM.SUMMARY.LABEL
      type: 0
      domains:
        - domain: '@domain_enterprise_risk_manager'
        - domain: '@domain_investigations'
        - domain: '@domain_controls'
        - domain: '@domain_recommendations'
    ref: controls_filter_form_summary
  - fields:
      placeholder: CONTROLS.DATASOURCE.RECOMMENDATION_STATUSES
      type: 0
      domains:
        - domain: '@domain_controls'
    ref: controls_datasource_recommendation_statuses
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUSES.OPTIONS.IN_PROGRESS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_statuses_options_in_progress
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUSES.OPTIONS.WAITING
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_statuses_options_waiting
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUSES.OPTIONS.APPROVED
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_statuses_options_approved
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUSES.OPTIONS.REJECTED
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_statuses_options_rejected
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUSES.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_statuses_label
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.REASON_FOR_REJECTION.TITLE
      type: 0
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_reason_for_rejection_title
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.REASON_FOR_REJECTION.LABEL
      type: 0
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_reason_for_rejection_label
  - fields:
      placeholder: RECOMMENDATIONS.FILTER.FORM.TITLE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_filter_form_title
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.FILTER.STATUS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_filter_status
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.FILTER.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_filter_status_label
  - fields:
      placeholder: RECOMMENDATIONS.DEFAULT_FORM.NAME
      type: 1
      domains:
        - domain: '@domain_recommendations'
    ref: recommendations_default_form_name
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.TITLE
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_title
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.TITLE.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_title_label
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.DESCRIPTION
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_description
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.DESCRIPTION.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_description_label
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUS
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_status
  - fields:
      placeholder: RECOMMENDATIONS.RECOMMENDATION.STATUS.LABEL
      type: 1
      domains:
        - domain: '@domain_common'
    ref: recommendations_recommendation_status_label
  - fields:
      placeholder: RECOMMENDATIONS.ATTACHMENTS.NO_ATTACHMENTS
      type: 1
      domains:
        - domain: '@domain_recommendations'
    ref: recommendations_attachments_no_attachments
  - fields:
      placeholder: RECOMMENDATIONS.ERROR.CANNOT_EDIT_REJECTED
      type: 1
      domains:
        - domain: '@domain_recommendations'
    ref: recommendations_error_cannot_edit_rejected
  - fields:
      placeholder: RECOMMENDATIONS.BANNERS.REJECTED
      type: 1
      domains:
        - domain: '@domain_recommendations'
    ref: recommendations_banners_rejected
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.BANNERS.LOCKED
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: recommendations_banners_locked
  - fields:
      placeholder: CONTROLS.CONTROLS.BANNERS.LOCKED
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_banners_locked
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.AUDIT.ENTITIES.CONTROL
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_recommendations_audit_entities_control
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.AUDIT.ENTITIES.CONTRIBUTORY_FACTOR
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_recommendations_audit_entities_contributory_factor
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.AUDIT.ENTITIES.ACTION
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_recommendations_audit_entities_action
  - fields:
      placeholder: CONTROLS.RECOMMENDATIONS.AUDIT.ENTITIES.ATTACHMENT
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_recommendations_audit_entities_attachment
  - fields:
      placeholder: CONTROLS.COMPARATORS.MODAL_TITLE
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_comparators_modal_title
  - fields:
      placeholder: CONTROLS.COMPARATORS.CLOSE
      pointer: COMMON.CLOSE
      type: 0
      domains:
        - domain: '@domain_recommendations'
  - fields:
      placeholder: CONTROLS.COMPARATORS.VIEW_CONTROL
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_comparators_view_control
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.PARKED
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_list_columns_parked
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.PARKED.YES
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_list_columns_parked_yes
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.PARKED.NO
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_list_columns_parked_no
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.AUDITED.YES
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_list_columns_audited_yes
  - fields:
      placeholder: CONTROLS.LIST.COLUMNS.AUDITED.NO
      type: 0
      domains:
        - domain: '@domain_recommendations'
    ref: controls_list_columns_audited_no
