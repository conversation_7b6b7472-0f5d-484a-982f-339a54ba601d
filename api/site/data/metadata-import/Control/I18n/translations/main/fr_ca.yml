entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@controls_loading_controls', language: '@language_fr_ca', value: 'Chargement des contrôles' } }
  - { fields: { placeholder: '@controls_loading_control', language: '@language_fr_ca', value: 'Chargement du contrôle' } }
  - { fields: { placeholder: '@controls_loading_recommendations', language: '@language_fr_ca', value: 'Chargement des recommandations' } }
  - { fields: { placeholder: '@controls_loading_contributory_factor', language: '@language_fr_ca', value: 'Chargement du facteur contributif' } }
  - { fields: { placeholder: '@controls_loading_contributory_factor_controls', language: '@language_fr_ca', value: 'Chargement des contrôles de facteur contributif' } }
  - { fields: { placeholder: '@controls_loading_contributory_factors', language: '@language_fr_ca', value: 'Chargement des facteurs contributifs' } }
  - { fields: { placeholder: '@controls_loading_comparators', language: '@language_fr_ca', value: 'Chargement des comparateurs' } }
  - { fields: { placeholder: '@controls_singular', language: '@language_fr_ca', value: Contrôle } }
  - { fields: { placeholder: '@controls_plural', language: '@language_fr_ca', value: Contrôles } }
  - { fields: { placeholder: '@controls_search', language: '@language_fr_ca', value: 'Rechercher des contrôles' } }
  - { fields: { placeholder: '@controls_create', language: '@language_fr_ca', value: 'Créer un contrôle' } }
  - { fields: { placeholder: '@controls_edit', language: '@language_fr_ca', value: 'Modifier le contrôle' } }
  - { fields: { placeholder: '@controls_recommendations_nav_dashboard', language: '@language_fr_ca', value: 'Tableau de bord' } }
  - { fields: { placeholder: '@controls_recommendations_post_save_no_access', language: '@language_fr_ca', value: 'La recommandation a été enregistrée, mais les permissions d''accès attribuées à votre utilisateur ne vous permettent pas de la voir' } }
  - { fields: { placeholder: '@controls_success_save', language: '@language_fr_ca', value: 'Contrôle enregistré avec succès' } }
  - { fields: { placeholder: '@controls_success_recommendation_save', language: '@language_fr_ca', value: 'Recommandation enregistrée avec succès' } }
  - { fields: { placeholder: '@controls_success_contributory_factors_save', language: '@language_fr_ca', value: 'Facteurs contributifs enregistrés avec succès' } }
  - { fields: { placeholder: '@controls_form_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@controls_form_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@controls_form_support_label', language: '@language_fr_ca', value: Soutien } }
  - { fields: { placeholder: '@controls_form_support_empty_value', language: '@language_fr_ca', value: 'Sélectionnez une valeur' } }
  - { fields: { placeholder: '@controls_form_support_options_none', language: '@language_fr_ca', value: Aucun } }
  - { fields: { placeholder: '@controls_form_support_options_opinion', language: '@language_fr_ca', value: Opinion } }
  - { fields: { placeholder: '@controls_form_support_options_expert_opinion', language: '@language_fr_ca', value: 'Opinion d''expert' } }
  - { fields: { placeholder: '@controls_form_support_options_literature_review', language: '@language_fr_ca', value: 'Examen de la littérature' } }
  - { fields: { placeholder: '@controls_form_effectiveness_label', language: '@language_fr_ca', value: Pertinence } }
  - { fields: { placeholder: '@controls_form_effectiveness_empty_value', language: '@language_fr_ca', value: 'Sélectionnez une valeur' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_none', language: '@language_fr_ca', value: Aucun } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_additional_study', language: '@language_fr_ca', value: 'Étude supplémentaire' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_training', language: '@language_fr_ca', value: Formation } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_warning_label', language: '@language_fr_ca', value: 'Étiquette de mise en garde' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_double_check', language: '@language_fr_ca', value: Revérifier } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_cognitive_aid', language: '@language_fr_ca', value: 'Aide cognitive' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_eliminate_distractions', language: '@language_fr_ca', value: 'Éliminer les distractions' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_increase_staffing', language: '@language_fr_ca', value: 'Augmenter la dotation en personnel' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_standardise_equipment', language: '@language_fr_ca', value: 'Normaliser l''équipement' } }
  - { fields: { placeholder: '@controls_form_effectiveness_options_simplify_processes', language: '@language_fr_ca', value: 'Simplifier les processus' } }
  - { fields: { placeholder: '@controls_form_error_retrieving_form', language: '@language_fr_ca', value: 'Une erreur s''est produite lors de la récupération du formulaire de contrôle' } }
  - { fields: { placeholder: '@controls_list_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@controls_list_columns_id_label', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@controls_list_columns_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@controls_list_columns_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@controls_list_columns_aggregate_score', language: '@language_fr_ca', value: 'Score global' } }
  - { fields: { placeholder: '@controls_list_columns_aggregate_score_label', language: '@language_fr_ca', value: 'Score global' } }
  - { fields: { placeholder: '@controls_list_columns_contributory_factor', language: '@language_fr_ca', value: 'Facteur contributif' } }
  - { fields: { placeholder: '@controls_list_columns_contributory_factor_label', language: '@language_fr_ca', value: 'Facteur contributif' } }
  - { fields: { placeholder: '@controls_list_columns_audited', language: '@language_fr_ca', value: 'Vérifié?' } }
  - { fields: { placeholder: '@controls_list_columns_parked', language: '@language_fr_ca', value: 'Mis en veille?' } }
  - { fields: { placeholder: '@controls_control_update_status', language: '@language_fr_ca', value: 'État de mise à jour' } }
  - { fields: { placeholder: '@controls_list_actions_results_found', language: '@language_fr_ca', value: 'Contrôles trouvés' } }
  - { fields: { placeholder: '@controls_list_actions_results_not_found', language: '@language_fr_ca', value: 'Aucun contrôle trouvé' } }
  - { fields: { placeholder: '@controls_list_actions_send_for_approval', language: '@language_fr_ca', value: 'Envoyer pour approbation' } }
  - { fields: { placeholder: '@controls_list_actions_approve', language: '@language_fr_ca', value: Approuver } }
  - { fields: { placeholder: '@controls_list_actions_park', language: '@language_fr_ca', value: 'Mettre en veille' } }
  - { fields: { placeholder: '@controls_list_actions_compare', language: '@language_fr_ca', value: Comparer } }
  - { fields: { placeholder: '@controls_list_actions_compare_warning_select_controls', language: '@language_fr_ca', value: 'Veuillez sélectionner les contrôles à comparer depuis le tableau de bord des contrôles' } }
  - { fields: { placeholder: '@controls_list_actions_reject', language: '@language_fr_ca', value: Rejeter } }
  - { fields: { placeholder: '@controls_list_actions_sent_for_approval_success_message', language: '@language_fr_ca', value: 'Les contrôles sélectionnés ont été envoyés avec succès pour approbation' } }
  - { fields: { placeholder: '@controls_list_actions_approved_success_message', language: '@language_fr_ca', value: 'Contrôles sélectionnés approuvés avec succès' } }
  - { fields: { placeholder: '@controls_list_actions_park_success_message', language: '@language_fr_ca', value: 'Contrôles sélectionnés mis en veille avec succès' } }
  - { fields: { placeholder: '@controls_list_actions_reject_success_message', language: '@language_fr_ca', value: 'Contrôles sélectionnés rejetés avec succès' } }
  - { fields: { placeholder: '@controls_control_comparator_values', language: '@language_fr_ca', value: 'Valeurs de comparaison' } }
  - { fields: { placeholder: '@controls_control_comparators_label', language: '@language_fr_ca', value: 'Comparateurs de contrôles' } }
  - { fields: { placeholder: '@controls_control_comparators_columns_comparator', language: '@language_fr_ca', value: Comparateur } }
  - { fields: { placeholder: '@controls_control_comparators_columns_value', language: '@language_fr_ca', value: Valeur } }
  - { fields: { placeholder: '@controls_control_comparators_columns_score', language: '@language_fr_ca', value: Note } }
  - { fields: { placeholder: '@controls_control_comparators_view_control', language: '@language_fr_ca', value: 'Afficher le contrôle' } }
  - { fields: { placeholder: '@controls_control_comparators_save', language: '@language_fr_ca', value: 'Enregistrer les valeurs de comparaison' } }
  - { fields: { placeholder: '@controls_control_approved_success_message', language: '@language_fr_ca', value: 'Contrôle approuvé avec succès' } }
  - { fields: { placeholder: '@controls_control_rejected_success_message', language: '@language_fr_ca', value: 'Contrôle rejeté avec succès' } }
  - { fields: { placeholder: '@controls_nav_recommendations', language: '@language_fr_ca', value: Recommandations } }
  - { fields: { placeholder: '@controls_nav_all_recommendations', language: '@language_fr_ca', value: 'Toutes les recommandations' } }
  - { fields: { placeholder: '@controls_nav_controls', language: '@language_fr_ca', value: Contrôles } }
  - { fields: { placeholder: '@controls_nav_all_controls', language: '@language_fr_ca', value: 'Tous les contrôles' } }
  - { fields: { placeholder: '@controls_nav_under_review', language: '@language_fr_ca', value: 'En cours de révision' } }
  - { fields: { placeholder: '@controls_nav_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@controls_nav_awaiting_approval', language: '@language_fr_ca', value: 'En attente d''approbation' } }
  - { fields: { placeholder: '@controls_nav_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@controls_nav_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@controls_nav_new_control', language: '@language_fr_ca', value: 'Créer un nouveau contrôle' } }
  - { fields: { placeholder: '@controls_nav_new_recommendation', language: '@language_fr_ca', value: 'Créer une nouvelle recommandation' } }
  - { fields: { placeholder: '@controls_nav_recommendation_details', language: '@language_fr_ca', value: 'Détails de la recommandation' } }
  - { fields: { placeholder: '@controls_nav_admin_comparators', language: '@language_fr_ca', value: 'Admin - Comparateurs' } }
  - { fields: { placeholder: '@controls_nav_admin_permissions', language: '@language_fr_ca', value: 'Admin - Permissions' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_results_found', language: '@language_fr_ca', value: 'Recommandations trouvées' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_results_not_found', language: '@language_fr_ca', value: 'Aucune recommandation trouvée' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_send_for_approval', language: '@language_fr_ca', value: 'Envoyer pour approbation' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_approve', language: '@language_fr_ca', value: Approuver } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_park', language: '@language_fr_ca', value: 'Mettre en veille' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_compare', language: '@language_fr_ca', value: Comparer } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_reject', language: '@language_fr_ca', value: Rejeter } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_approve_success_message', language: '@language_fr_ca', value: 'Recommandations sélectionnées envoyées avec succès pour approbation' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_park_success_message', language: '@language_fr_ca', value: 'Recommandations sélectionnées mises en veille avec succès' } }
  - { fields: { placeholder: '@controls_recommendations_list_actions_reject_success_message', language: '@language_fr_ca', value: 'Recommandations sélectionnées rejetées avec succès' } }
  - { fields: { placeholder: '@controls_recommendations_list_columns_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@controls_recommendations_list_columns_statement_of_intent', language: '@language_fr_ca', value: 'Déclaration d''intention' } }
  - { fields: { placeholder: '@controls_recommendations_fields_statement_of_intent', language: '@language_fr_ca', value: 'Déclaration d''intention' } }
  - { fields: { placeholder: '@controls_recommendations_fields_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@controls_recommendations_save', language: '@language_fr_ca', value: 'Enregistrer la recommandation' } }
  - { fields: { placeholder: '@controls_recommendations_saved_successfully', language: '@language_fr_ca', value: 'Recommandation enregistrée avec succès' } }
  - { fields: { placeholder: '@controls_control_nav_details', language: '@language_fr_ca', value: 'Détails du contrôle' } }
  - { fields: { placeholder: '@controls_control_nav_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@controls_control_nav_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@controls_control_nav_audit', language: '@language_fr_ca', value: 'Journal d''audit' } }
  - { fields: { placeholder: '@controls_control_nav_access_control', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@controls_control_nav_comparators', language: '@language_fr_ca', value: Comparateurs } }
  - { fields: { placeholder: '@controls_control_edit', language: '@language_fr_ca', value: 'Modifier le contrôle' } }
  - { fields: { placeholder: '@controls_control_review_date', language: '@language_fr_ca', value: 'Date de révision' } }
  - { fields: { placeholder: '@controls_control_review_date_no_date_set', language: '@language_fr_ca', value: 'Aucune date de révision définie' } }
  - { fields: { placeholder: '@controls_control_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@controls_control_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@controls_control_status_options_under_review', language: '@language_fr_ca', value: 'En cours de révision' } }
  - { fields: { placeholder: '@controls_control_status_options_awaiting_approval', language: '@language_fr_ca', value: 'En attente d''approbation' } }
  - { fields: { placeholder: '@controls_control_status_options_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@controls_control_status_options_parked', language: '@language_fr_ca', value: 'Mis en veille' } }
  - { fields: { placeholder: '@controls_control_status_options_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@controls_control_status_options_under_revision', language: '@language_fr_ca', value: 'En cours de révision' } }
  - { fields: { placeholder: '@controls_control_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@controls_control_content', language: '@language_fr_ca', value: Contenu } }
  - { fields: { placeholder: '@controls_admin_comparators_add', language: '@language_fr_ca', value: 'Ajouter un comparateur' } }
  - { fields: { placeholder: '@controls_admin_comparators_fields_title_label', language: '@language_fr_ca', value: 'Titre du comparateur' } }
  - { fields: { placeholder: '@controls_admin_comparators_delete', language: '@language_fr_ca', value: 'Supprimer le comparateur' } }
  - { fields: { placeholder: '@controls_admin_comparators_options_list_option_label', language: '@language_fr_ca', value: 'Étiquette d''option' } }
  - { fields: { placeholder: '@controls_admin_comparators_options_list_score', language: '@language_fr_ca', value: Note } }
  - { fields: { placeholder: '@controls_admin_comparators_options_add', language: '@language_fr_ca', value: 'Ajouter une option de comparateur' } }
  - { fields: { placeholder: '@controls_contributory_factors_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@recommendations_search_match_classification', language: '@language_fr_ca', value: 'Correspondre à la classification' } }
  - { fields: { placeholder: '@recommendations_priorities', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@recommendations_priorities_label', language: '@language_fr_ca', value: Priorité } }
  - { fields: { placeholder: '@recommendations_priorities_options_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@recommendations_priorities_options_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@recommendations_priorities_options_high', language: '@language_fr_ca', value: Élevée } }
  - { fields: { placeholder: '@recommendations_priorities_owner', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@recommendations_priorities_owner_label', language: '@language_fr_ca', value: Titulaire } }
  - { fields: { placeholder: '@recommendations_priorities_created_at', language: '@language_fr_ca', value: 'Créé à' } }
  - { fields: { placeholder: '@recommendations_priorities_created_at_label', language: '@language_fr_ca', value: 'Créé à' } }
  - { fields: { placeholder: '@controls_datasource_control_statuses', language: '@language_fr_ca', value: 'États de contrôle' } }
  - { fields: { placeholder: '@controls_datasource_contributory_factor_classifications', language: '@language_fr_ca', value: 'Classifications des facteurs contributifs' } }
  - { fields: { placeholder: '@controls_datasource_recommendation_priorities', language: '@language_fr_ca', value: 'Priorités de recommandation' } }
  - { fields: { placeholder: '@control_form_type_control_form', language: '@language_fr_ca', value: 'Formulaire de contrôle' } }
  - { fields: { placeholder: '@recommendations_form_type_recommendation_form', language: '@language_fr_ca', value: 'Formulaire de recommandation' } }
  - { fields: { placeholder: '@recommendations_contributory_factors_saved_successfully', language: '@language_fr_ca', value: 'Facteur contributif enregistré avec succès' } }
  - { fields: { placeholder: '@recommendations_contributory_factors_removed_successfully', language: '@language_fr_ca', value: 'Facteur contributif supprimé avec succès' } }
  - { fields: { placeholder: '@controls_control_comparators_untitled', language: '@language_fr_ca', value: 'Sans titre' } }
  - { fields: { placeholder: '@controls_control_comparators_delete', language: '@language_fr_ca', value: 'Supprimer le comparateur' } }
  - { fields: { placeholder: '@controls_control_comparators_main_save', language: '@language_fr_ca', value: 'Enregistrer le comparateur' } }
  - { fields: { placeholder: '@controls_control_comparators_option_value', language: '@language_fr_ca', value: 'Enregistrer le comparateur' } }
  - { fields: { placeholder: '@controls_filter_form_title_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@controls_filter_form_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@controls_filter_form_summary_title', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@controls_filter_form_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@controls_datasource_recommendation_statuses', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_statuses_options_in_progress', language: '@language_fr_ca', value: 'En cours' } }
  - { fields: { placeholder: '@recommendations_statuses_options_waiting', language: '@language_fr_ca', value: 'En attente d''approbation' } }
  - { fields: { placeholder: '@recommendations_statuses_options_approved', language: '@language_fr_ca', value: Approuvé } }
  - { fields: { placeholder: '@recommendations_statuses_options_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@recommendations_statuses_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_recommendation_reason_for_rejection_title', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@recommendations_recommendation_reason_for_rejection_label', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@recommendations_filter_form_title', language: '@language_fr_ca', value: 'Filtrer les recommandations' } }
  - { fields: { placeholder: '@recommendations_filter_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_filter_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_default_form_name', language: '@language_fr_ca', value: 'Formulaire de recommandation' } }
  - { fields: { placeholder: '@recommendations_recommendation_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@recommendations_recommendation_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@recommendations_recommendation_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@recommendations_recommendation_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@recommendations_recommendation_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_recommendation_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@recommendations_attachments_no_attachments', language: '@language_fr_ca', value: 'Cette recommandation n''a pas de pièce jointe' } }
  - { fields: { placeholder: '@recommendations_error_cannot_edit_rejected', language: '@language_fr_ca', value: 'Une recommandation rejetée ne peut être modifiée' } }
  - { fields: { placeholder: '@recommendations_banners_rejected', language: '@language_fr_ca', value: 'Cette recommandation a été rejetée par {{name}} le {{date}}' } }
  - { fields: { placeholder: '@recommendations_banners_locked', language: '@language_fr_ca', value: 'Ce dossier est verrouillé par {{name}} depuis le {{date}}' } }
  - { fields: { placeholder: '@controls_banners_locked', language: '@language_fr_ca', value: 'Ce dossier est verrouillé par {{name}} depuis le {{date}}' } }
  - { fields: { placeholder: '@controls_recommendations_audit_entities_control', language: '@language_fr_ca', value: 'Contrôle Nº {{recordId}}' } }
  - { fields: { placeholder: '@controls_recommendations_audit_entities_contributory_factor', language: '@language_fr_ca', value: 'Facteur contributif Nº {{recordId}}' } }
  - { fields: { placeholder: '@controls_recommendations_audit_entities_action', language: '@language_fr_ca', value: 'Action Nº {{recordId}}' } }
  - { fields: { placeholder: '@controls_recommendations_audit_entities_attachment', language: '@language_fr_ca', value: 'Pièce jointe Nº {{recordId}}' } }
  - { fields: { placeholder: '@controls_filter_form_form_title', language: '@language_fr_ca', value: 'Commandes de filtre' } }
  - { fields: { placeholder: '@controls_form_title_new_control', language: '@language_fr_ca', value: 'Nouveau contrôle' } }
  - { fields: { placeholder: '@controls_form_field_add_control', language: '@language_fr_ca', value: 'Ajouter un autre contrôle?' } }
