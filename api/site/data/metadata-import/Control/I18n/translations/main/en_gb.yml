entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@controls_loading_controls'
      language: '@language_en_gb'
      value: 'Loading Controls'
  -
    fields:
      placeholder: '@controls_loading_control'
      language: '@language_en_gb'
      value: 'Loading Control'
  -
    fields:
      placeholder: '@controls_loading_recommendations'
      language: '@language_en_gb'
      value: 'Loading Recommendations'
  -
    fields:
      placeholder: '@controls_loading_contributory_factor'
      language: '@language_en_gb'
      value: 'Loading Contributory Factor'
  -
    fields:
      placeholder: '@controls_loading_contributory_factor_controls'
      language: '@language_en_gb'
      value: 'Loading Contributory Factor Controls'
  -
    fields:
      placeholder: '@controls_loading_contributory_factors'
      language: '@language_en_gb'
      value: 'Loading Contributory Factors'
  -
    fields:
      placeholder: '@controls_loading_comparators'
      language: '@language_en_gb'
      value: 'Loading Comparators'
  -
    fields:
      placeholder: '@controls_singular'
      language: '@language_en_gb'
      value: Control
  -
    fields:
      placeholder: '@controls_plural'
      language: '@language_en_gb'
      value: Controls
  -
    fields:
      placeholder: '@controls_search'
      language: '@language_en_gb'
      value: 'Search Controls'
  -
    fields:
      placeholder: '@controls_create'
      language: '@language_en_gb'
      value: 'Create Control'
  -
    fields:
      placeholder: '@controls_edit'
      language: '@language_en_gb'
      value: 'Edit Control'
  -
    fields:
      placeholder: '@controls_recommendations_nav_dashboard'
      language: '@language_en_gb'
      value: Dashboard
  -
    fields:
      placeholder: '@controls_recommendations_post_save_no_access'
      language: '@language_en_gb'
      value: 'Recommendation has been saved, but the access permissions assigned to your user do not permit you to see it'
  -
    fields:
      placeholder: '@controls_success_save'
      language: '@language_en_gb'
      value: 'Control saved successfully'
  -
    fields:
      placeholder: '@controls_success_recommendation_save'
      language: '@language_en_gb'
      value: 'Recommendation saved successfully'
  -
    fields:
      placeholder: '@controls_success_contributory_factors_save'
      language: '@language_en_gb'
      value: 'Contributory Factors saved successfully'
  -
    fields:
      placeholder: '@controls_form_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@controls_form_summary'
      language: '@language_en_gb'
      value: Summary
  -
    fields:
      placeholder: '@controls_form_support_label'
      language: '@language_en_gb'
      value: Support
  -
    fields:
      placeholder: '@controls_form_support_empty_value'
      language: '@language_en_gb'
      value: 'Select a Value'
  -
    fields:
      placeholder: '@controls_form_support_options_none'
      language: '@language_en_gb'
      value: None
  -
    fields:
      placeholder: '@controls_form_support_options_opinion'
      language: '@language_en_gb'
      value: Opinion
  -
    fields:
      placeholder: '@controls_form_support_options_expert_opinion'
      language: '@language_en_gb'
      value: 'Expert Opinion'
  -
    fields:
      placeholder: '@controls_form_support_options_literature_review'
      language: '@language_en_gb'
      value: 'Literature Review'
  -
    fields:
      placeholder: '@controls_form_effectiveness_label'
      language: '@language_en_gb'
      value: Effectiveness
  -
    fields:
      placeholder: '@controls_form_effectiveness_empty_value'
      language: '@language_en_gb'
      value: 'Select a Value'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_none'
      language: '@language_en_gb'
      value: None
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_additional_study'
      language: '@language_en_gb'
      value: 'Additional Study'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_training'
      language: '@language_en_gb'
      value: Training
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_warning_label'
      language: '@language_en_gb'
      value: 'Warning Label'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_double_check'
      language: '@language_en_gb'
      value: 'Double Check'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_cognitive_aid'
      language: '@language_en_gb'
      value: 'Cognitive Aid'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_eliminate_distractions'
      language: '@language_en_gb'
      value: 'Eliminate Distractions'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_increase_staffing'
      language: '@language_en_gb'
      value: 'Increase Staffing'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_standardise_equipment'
      language: '@language_en_gb'
      value: 'Standardise Equipment'
  -
    fields:
      placeholder: '@controls_form_effectiveness_options_simplify_processes'
      language: '@language_en_gb'
      value: 'Simplify Processes'
  -
    fields:
      placeholder: '@controls_form_error_retrieving_form'
      language: '@language_en_gb'
      value: 'An error occurred whilst retrieving the Control form'
  -
    fields:
      placeholder: '@controls_list_columns_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@controls_list_columns_id_label'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@controls_list_columns_title'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@controls_list_columns_title_label'
      language: '@language_en_gb'
      value: Title
  -
    fields:
      placeholder: '@controls_list_columns_aggregate_score'
      language: '@language_en_gb'
      value: 'Aggregate Score'
  -
    fields:
      placeholder: '@controls_list_columns_aggregate_score_label'
      language: '@language_en_gb'
      value: 'Aggregate Score'
  -
    fields:
      placeholder: '@controls_list_columns_contributory_factor'
      language: '@language_en_gb'
      value: 'Contributory Factor'
  -
    fields:
      placeholder: '@controls_list_columns_contributory_factor_label'
      language: '@language_en_gb'
      value: 'Contributory Factor'
  -
    fields:
      placeholder: '@controls_list_columns_audited'
      language: '@language_en_gb'
      value: 'Audited?'
  -
    fields:
      placeholder: '@controls_control_update_status'
      language: '@language_en_gb'
      value: 'Update Status'
  -
    fields:
      placeholder: '@controls_list_actions_results_found'
      language: '@language_en_gb'
      value: 'Controls Found'
  -
    fields:
      placeholder: '@controls_list_actions_results_not_found'
      language: '@language_en_gb'
      value: 'No Controls Found'
  -
    fields:
      placeholder: '@controls_list_actions_send_for_approval'
      language: '@language_en_gb'
      value: 'Send For Approval'
  -
    fields:
      placeholder: '@controls_list_actions_approve'
      language: '@language_en_gb'
      value: Approve
  -
    fields:
      placeholder: '@controls_list_actions_park'
      language: '@language_en_gb'
      value: Park
  -
    fields:
      placeholder: '@controls_list_actions_compare'
      language: '@language_en_gb'
      value: Compare
  -
    fields:
      placeholder: '@controls_list_actions_compare_warning_select_controls'
      language: '@language_en_gb'
      value: 'Please select Controls to compare from the Controls dashboard'
  -
    fields:
      placeholder: '@controls_list_actions_reject'
      language: '@language_en_gb'
      value: Reject
  -
    fields:
      placeholder: '@controls_list_actions_sent_for_approval_success_message'
      language: '@language_en_gb'
      value: 'Selected Controls successfully sent for approval'
  -
    fields:
      placeholder: '@controls_list_actions_approved_success_message'
      language: '@language_en_gb'
      value: 'Selected Controls successfully approved'
  -
    fields:
      placeholder: '@controls_list_actions_park_success_message'
      language: '@language_en_gb'
      value: 'Selected Controls successfully parked'
  -
    fields:
      placeholder: '@controls_list_actions_reject_success_message'
      language: '@language_en_gb'
      value: 'Selected Controls successfully rejected'
  -
    fields:
      placeholder: '@controls_control_comparator_values'
      language: '@language_en_gb'
      value: 'Comparator Values'
  -
    fields:
      placeholder: '@controls_control_comparators_label'
      language: '@language_en_gb'
      value: 'Control Comparators'
  -
    fields:
      placeholder: '@controls_control_comparators_columns_comparator'
      language: '@language_en_gb'
      value: Comparator
  -
    fields:
      placeholder: '@controls_control_comparators_columns_value'
      language: '@language_en_gb'
      value: Value
  -
    fields:
      placeholder: '@controls_control_comparators_columns_score'
      language: '@language_en_gb'
      value: Score
  -
    fields:
      placeholder: '@controls_control_comparators_view_control'
      language: '@language_en_gb'
      value: 'View Control'
  -
    fields:
      placeholder: '@controls_control_comparators_save'
      language: '@language_en_gb'
      value: 'Save Comparator Values'
  -
    fields:
      placeholder: '@controls_control_approved_success_message'
      language: '@language_en_gb'
      value: 'Control approved successfully'
  -
    fields:
      placeholder: '@controls_control_rejected_success_message'
      language: '@language_en_gb'
      value: 'Control rejected successfully'
  -
    fields:
      placeholder: '@controls_nav_controls'
      language: '@language_en_gb'
      value: 'Controls'
  -
    fields:
      placeholder: '@controls_nav_all_controls'
      language: '@language_en_gb'
      value: 'All Controls'
  -
    fields:
      placeholder: '@controls_nav_recommendations'
      language: '@language_en_gb'
      value: 'Recommendations'
  -
    fields:
      placeholder: '@controls_nav_all_recommendations'
      language: '@language_en_gb'
      value: 'All Recommendations'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_send_for_approval'
      language: '@language_en_gb'
      value: 'Send For Approval'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_approve'
      language: '@language_en_gb'
      value: Approve
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_park'
      language: '@language_en_gb'
      value: Park
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_compare'
      language: '@language_en_gb'
      value: Compare
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_reject'
      language: '@language_en_gb'
      value: Reject
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_approve_success_message'
      language: '@language_en_gb'
      value: 'Selected Recommendations successfully sent for approval'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_park_success_message'
      language: '@language_en_gb'
      value: 'Selected Recommendations successfully parked'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_reject_success_message'
      language: '@language_en_gb'
      value: 'Selected Recommendations successfully rejected'
  -
    fields:
      placeholder: '@controls_nav_under_review'
      language: '@language_en_gb'
      value: 'Under Review'
  -
    fields:
      placeholder: '@controls_nav_in_progress'
      language: '@language_en_gb'
      value: In Progress
  -
    fields:
      placeholder: '@controls_nav_awaiting_approval'
      language: '@language_en_gb'
      value: 'Awaiting Approval'
  -
    fields:
      placeholder: '@controls_nav_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@controls_nav_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@controls_nav_new_control'
      language: '@language_en_gb'
      value: 'Create New Control'
  -
    fields:
      placeholder: '@controls_nav_new_recommendation'
      language: '@language_en_gb'
      value: 'Create New Recommendation'
  -
    fields:
      placeholder: '@controls_nav_recommendation_details'
      language: '@language_en_gb'
      value: 'Recommendation Details'
  -
    fields:
      placeholder: '@controls_nav_admin_comparators'
      language: '@language_en_gb'
      value: 'Admin - Comparators'
  -
    fields:
      placeholder: '@controls_nav_admin_permissions'
      language: '@language_en_gb'
      value: 'Admin - Permissions'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_results_found'
      language: '@language_en_gb'
      value: 'Recommendations Found'
  -
    fields:
      placeholder: '@controls_recommendations_list_actions_results_not_found'
      language: '@language_en_gb'
      value: 'No Recommendations Found'
  -
    fields:
      placeholder: '@controls_recommendations_list_columns_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@controls_recommendations_list_columns_statement_of_intent'
      language: '@language_en_gb'
      value: 'Statement of Intent'
  -
    fields:
      placeholder: '@controls_recommendations_fields_statement_of_intent'
      language: '@language_en_gb'
      value: 'Statement of Intent'
  -
    fields:
      placeholder: '@controls_recommendations_fields_summary'
      language: '@language_en_gb'
      value: Summary
  -
    fields:
      placeholder: '@controls_recommendations_save'
      language: '@language_en_gb'
      value: 'Save Recommendation'
  -
    fields:
      placeholder: '@controls_recommendations_saved_successfully'
      language: '@language_en_gb'
      value: 'Recommendation saved successfully'
  -
    fields:
      placeholder: '@controls_control_nav_details'
      language: '@language_en_gb'
      value: 'Control Details'
  -
    fields:
      placeholder: '@controls_control_nav_actions'
      language: '@language_en_gb'
      value: Actions
  -
    fields:
      placeholder: '@controls_control_nav_attachments'
      language: '@language_en_gb'
      value: Attachments
  -
    fields:
      placeholder: '@controls_control_nav_audit'
      language: '@language_en_gb'
      value: Audit Log
  -
    fields:
      placeholder: '@controls_control_nav_access_control'
      language: '@language_en_gb'
      value: 'Access Control'
  -
    fields:
      placeholder: '@controls_control_nav_comparators'
      language: '@language_en_gb'
      value: Comparators
  -
    fields:
      placeholder: '@controls_control_edit'
      language: '@language_en_gb'
      value: 'Edit Control'
  -
    fields:
      placeholder: '@controls_control_review_date'
      language: '@language_en_gb'
      value: 'Review Date'
  -
    fields:
      placeholder: '@controls_control_review_date_no_date_set'
      language: '@language_en_gb'
      value: 'No Review Date set'
  -
    fields:
      placeholder: '@controls_control_status'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@controls_control_status_label'
      language: '@language_en_gb'
      value: Status
  -
    fields:
      placeholder: '@controls_control_status_options_under_review'
      language: '@language_en_gb'
      value: 'Under Review'
  -
    fields:
      placeholder: '@controls_control_status_options_awaiting_approval'
      language: '@language_en_gb'
      value: 'Awaiting Approval'
  -
    fields:
      placeholder: '@controls_control_status_options_approved'
      language: '@language_en_gb'
      value: Approved
  -
    fields:
      placeholder: '@controls_control_status_options_parked'
      language: '@language_en_gb'
      value: Parked
  -
    fields:
      placeholder: '@controls_control_status_options_rejected'
      language: '@language_en_gb'
      value: Rejected
  -
    fields:
      placeholder: '@controls_control_status_options_under_revision'
      language: '@language_en_gb'
      value: 'Under Revision'
  -
    fields:
      placeholder: '@controls_control_summary'
      language: '@language_en_gb'
      value: Summary
  -
    fields:
      placeholder: '@controls_control_content'
      language: '@language_en_gb'
      value: Content
  -
    fields:
      placeholder: '@controls_admin_comparators_add'
      language: '@language_en_gb'
      value: 'Add a Comparator'
  -
    fields:
      placeholder: '@controls_admin_comparators_fields_title_label'
      language: '@language_en_gb'
      value: 'Comparator Title'
  -
    fields:
      placeholder: '@controls_admin_comparators_delete'
      language: '@language_en_gb'
      value: 'Delete Comparator'
  -
    fields:
      placeholder: '@controls_admin_comparators_options_list_option_label'
      language: '@language_en_gb'
      value: 'Option Label'
  -
    fields:
      placeholder: '@controls_admin_comparators_options_list_score'
      language: '@language_en_gb'
      value: Score
  -
    fields:
      placeholder: '@controls_admin_comparators_options_add'
      language: '@language_en_gb'
      value: 'Add a Comparator Option'
  -
    fields:
      placeholder: '@controls_contributory_factors_classification'
      language: '@language_en_gb'
      value: Classification

  # Recommendations
  -
    fields:
      placeholder: '@recommendations_search_match_classification'
      language: '@language_en_gb'
      value: 'Match Classification'
  -
    fields:
      placeholder: '@recommendations_priorities'
      language: '@language_en_gb'
      value: Priority
  -
    fields:
      placeholder: '@recommendations_priorities_label'
      language: '@language_en_gb'
      value: Priority
  -
    fields:
      placeholder: '@recommendations_priorities_options_low'
      language: '@language_en_gb'
      value: Low
  -
    fields:
      placeholder: '@recommendations_priorities_options_medium'
      language: '@language_en_gb'
      value: Medium
  -
    fields:
      placeholder: '@recommendations_priorities_options_high'
      language: '@language_en_gb'
      value: High
  -
    fields:
      placeholder: '@recommendations_priorities_owner'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@recommendations_priorities_owner_label'
      language: '@language_en_gb'
      value: Owner
  -
    fields:
      placeholder: '@recommendations_priorities_created_at'
      language: '@language_en_gb'
      value: Created At
  -
    fields:
      placeholder: '@recommendations_priorities_created_at_label'
      language: '@language_en_gb'
      value: Created At
  -
    fields:
      placeholder: '@controls_datasource_control_statuses'
      language: '@language_en_gb'
      value: 'Control Statuses'
  -
    fields:
      placeholder: '@controls_datasource_contributory_factor_classifications'
      language: '@language_en_gb'
      value: 'Contributory Factor Classifications'
  -
    fields:
      placeholder: '@controls_datasource_recommendation_priorities'
      language: '@language_en_gb'
      value: 'Recommendation Priorities'
  -
    fields:
      placeholder: '@control_form_type_control_form'
      language: '@language_en_gb'
      value: 'Control Form'
  -
    fields:
      placeholder: '@recommendations_form_type_recommendation_form'
      language: '@language_en_gb'
      value: 'Recommendation Form'
  -
    fields:
      placeholder: '@recommendations_contributory_factors_saved_successfully'
      language: '@language_en_gb'
      value: 'Contributory Factor saved successfully'
  -
    fields:
      placeholder: '@recommendations_contributory_factors_removed_successfully'
      language: '@language_en_gb'
      value: 'Contributory Factor removed successfully'
  -
    fields:
      placeholder: '@controls_control_comparators_untitled'
      language: '@language_en_gb'
      value: 'Untitled'
  -
    fields:
      placeholder: '@controls_control_comparators_delete'
      language: '@language_en_gb'
      value: 'Delete Comparator'
  -
    fields:
      placeholder: '@controls_control_comparators_main_save'
      language: '@language_en_gb'
      value: 'Save Comparator'
  -
    fields:
      placeholder: '@controls_control_comparators_option_value'
      language: '@language_en_gb'
      value: 'Save Comparator'
  -
    fields:
      placeholder: '@controls_filter_form_form_title'
      language: '@language_en_gb'
      value: 'Filter Controls'
  -
    fields:
      placeholder: '@controls_filter_form_title'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@controls_filter_form_summary'
      language: '@language_en_gb'
      value: 'Summary'
  -
    fields:
      placeholder: '@controls_filter_form_title_title'
      language: '@language_en_gb'
      value: 'Title'
  -
    fields:
      placeholder: '@controls_filter_form_summary_title'
      language: '@language_en_gb'
      value: 'Summary'
  -
    fields:
      placeholder: '@controls_datasource_recommendation_statuses'
      language: '@language_en_gb'
      value: 'Status'
  -
    fields:
      placeholder: '@recommendations_statuses_options_in_progress'
      language: '@language_en_gb'
      value: In Progress
  -
    fields:
      placeholder: '@recommendations_statuses_options_waiting'
      language: '@language_en_gb'
      value: 'Awaiting Approval'
  -
    fields:
      placeholder: '@recommendations_statuses_options_approved'
      language: '@language_en_gb'
      value: 'Approved'
  -
    fields:
      placeholder: '@recommendations_statuses_options_rejected'
      language: '@language_en_gb'
      value: 'Rejected'
  -
    fields:
      placeholder: '@recommendations_statuses_label'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@recommendations_filter_form_title'
      language: '@language_en_gb'
      value: 'Filter Recommendations'
  - fields:
      placeholder: '@recommendations_filter_status'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@recommendations_filter_status_label'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@recommendations_default_form_name'
      language: '@language_en_gb'
      value: 'Recommendation Form'
  - fields:
      placeholder: '@recommendations_recommendation_title'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@recommendations_recommendation_title_label'
      language: '@language_en_gb'
      value: 'Title'
  - fields:
      placeholder: '@recommendations_recommendation_description'
      language: '@language_en_gb'
      value: 'Description'
  - fields:
      placeholder: '@recommendations_recommendation_description_label'
      language: '@language_en_gb'
      value: 'Description'
  - fields:
      placeholder: '@recommendations_recommendation_status'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@recommendations_recommendation_status_label'
      language: '@language_en_gb'
      value: 'Status'
  - fields:
      placeholder: '@recommendations_recommendation_reason_for_rejection_title'
      language: '@language_en_gb'
      value: Reason for Rejection
  - fields:
      placeholder: '@recommendations_recommendation_reason_for_rejection_label'
      language: '@language_en_gb'
      value: Reason for Rejection
  - fields:
      placeholder: '@recommendations_attachments_no_attachments'
      language: '@language_en_gb'
      value: This Recommendation has no Attachments
  - fields:
      placeholder: '@recommendations_error_cannot_edit_rejected'
      language: '@language_en_gb'
      value: Rejected Recommendation cannot be edited
  - fields:
      placeholder: '@recommendations_banners_rejected'
      language: '@language_en_gb'
      value: 'This Recommendation has been Rejected by {{name}} on {{date}}'
  - fields:
      placeholder: '@recommendations_banners_locked'
      language: '@language_en_gb'
      value: 'This record is locked by {{name}} since {{date}}'
  - fields:
      placeholder: '@controls_banners_locked'
      language: '@language_en_gb'
      value: 'This record is locked by {{name}} since {{date}}'
  - fields:
      placeholder: '@controls_recommendations_audit_entities_control'
      language: '@language_en_gb'
      value: 'Control #{{recordId}}'
  - fields:
      placeholder: '@controls_recommendations_audit_entities_contributory_factor'
      language: '@language_en_gb'
      value: 'Contributory Factor #{{recordId}}'
  - fields:
      placeholder: '@controls_recommendations_audit_entities_action'
      language: '@language_en_gb'
      value: 'Action #{{recordId}}'
  - fields:
      placeholder: '@controls_recommendations_audit_entities_attachment'
      language: '@language_en_gb'
      value: 'Attachment #{{recordId}}'
  - fields:
      placeholder: '@controls_comparators_modal_title'
      language: '@language_en_gb'
      value: '{{controlTitle}} - Comparators'
  - fields:
      placeholder: '@controls_comparators_view_control'
      language: '@language_en_gb'
      value: 'View Control'
  - fields:
      placeholder: '@controls_list_columns_parked'
      language: '@language_en_gb'
      value: 'Parked?'
  - fields:
      placeholder: '@controls_list_columns_parked_yes'
      language: '@language_en_gb'
      value: 'Yes'
  - fields:
      placeholder: '@controls_list_columns_parked_no'
      language: '@language_en_gb'
      value: 'No'
  - fields:
      placeholder: '@controls_list_columns_audited_yes'
      language: '@language_en_gb'
      value: 'Yes'
  - fields:
      placeholder: '@controls_list_columns_audited_no'
      language: '@language_en_gb'
      value: 'No'
  - fields:
      placeholder: '@controls_form_title_new_control'
      language: '@language_en_gb'
      value: 'New Control'
  - fields:
      placeholder: '@controls_form_field_add_control'
      language: '@language_en_gb'
      value: 'Add another Control?'
