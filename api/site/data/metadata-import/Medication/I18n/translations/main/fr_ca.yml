entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@medications_singular', language: '@language_fr_ca', value: Médicament } }
  - { fields: { placeholder: '@medications_plural', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@medications_search', language: '@language_fr_ca', value: 'Rechercher des médicaments' } }
  - { fields: { placeholder: '@medications_create', language: '@language_fr_ca', value: 'Créer un médicament' } }
  - { fields: { placeholder: '@medications_edit', language: '@language_fr_ca', value: 'Modifier un médicament' } }
  - { fields: { placeholder: '@medications_form_generic_name', language: '@language_fr_ca', value: 'Nom générique' } }
  - { fields: { placeholder: '@medications_form_generic_name_label', language: '@language_fr_ca', value: 'Nom générique' } }
  - { fields: { placeholder: '@medications_form_select_generic_name', language: '@language_fr_ca', value: 'Sélectionner un nom générique' } }
  - { fields: { placeholder: '@medications_form_ref', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@medications_form_ref_label', language: '@language_fr_ca', value: Réf. } }
  - { fields: { placeholder: '@medications_form_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@medications_form_type_label', language: '@language_fr_ca', value: 'Type of drogue' } }
  - { fields: { placeholder: '@medications_form_select_type', language: '@language_fr_ca', value: 'Sélectionner le type' } }
  - { fields: { placeholder: '@medications_form_class', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@medications_form_class_label', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@medications_form_select_class', language: '@language_fr_ca', value: 'Sélectionner la classification' } }
  - { fields: { placeholder: '@medications_form_subclass', language: '@language_fr_ca', value: Sous-classe } }
  - { fields: { placeholder: '@medications_form_subclass_label', language: '@language_fr_ca', value: Sous-classe } }
  - { fields: { placeholder: '@medications_form_strength', language: '@language_fr_ca', value: Force } }
  - { fields: { placeholder: '@medications_form_strength_label', language: '@language_fr_ca', value: Force } }
  - { fields: { placeholder: '@medications_form_supplier', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@medications_form_supplier_label', language: '@language_fr_ca', value: Fournisseur } }
  - { fields: { placeholder: '@medications_form_select_subclass', language: '@language_fr_ca', value: 'Sélectionner la sous-classification' } }
  - { fields: { placeholder: '@medications_form_brand', language: '@language_fr_ca', value: Marque } }
  - { fields: { placeholder: '@medications_form_brand_label', language: '@language_fr_ca', value: 'Nom de marque' } }
  - { fields: { placeholder: '@medications_form_select_brand', language: '@language_fr_ca', value: 'Sélectionner une marque' } }
  - { fields: { placeholder: '@medications_form_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@medications_form_manufacturer_label', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@medications_form_select_manufacturer', language: '@language_fr_ca', value: 'Sélectionner un fabricant' } }
  - { fields: { placeholder: '@medications_form_controlled', language: '@language_fr_ca', value: 'Médicament contrôlé?' } }
  - { fields: { placeholder: '@medications_form_controlled_label', language: '@language_fr_ca', value: 'Médicament contrôlé?' } }
  - { fields: { placeholder: '@medications_form_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@medications_form_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@medications_relation_id', language: '@language_fr_ca', value: ID } }
  - { fields: { placeholder: '@medications_relation_medication', language: '@language_fr_ca', value: Médicament } }
  - { fields: { placeholder: '@medications_relation_correct', language: '@language_fr_ca', value: Correct } }
  - { fields: { placeholder: '@medications_relation_administered', language: '@language_fr_ca', value: Administré } }
  - { fields: { placeholder: '@medications_relation_route', language: '@language_fr_ca', value: Route } }
  - { fields: { placeholder: '@medications_relation_from', language: '@language_fr_ca', value: De } }
  - { fields: { placeholder: '@medications_form_classification_value_1', language: '@language_fr_ca', value: Narcotiques } }
  - { fields: { placeholder: '@medications_form_classification_value_2', language: '@language_fr_ca', value: Dépresseurs } }
  - { fields: { placeholder: '@medications_form_classification_value_3', language: '@language_fr_ca', value: Stimulants } }
  - { fields: { placeholder: '@medications_form_classification_value_4', language: '@language_fr_ca', value: Hallucinogènes } }
  - { fields: { placeholder: '@medications_form_classification_value_5', language: '@language_fr_ca', value: 'Stéroïdes anabolisants' } }
  - { fields: { placeholder: '@medications_form_type_medication', language: '@language_fr_ca', value: Médicament } }
  - { fields: { placeholder: '@medications_types_analgesic', language: '@language_fr_ca', value: Analgésique } }
  - { fields: { placeholder: '@medications_types_psychedelic', language: '@language_fr_ca', value: Psychédélique } }
  - { fields: { placeholder: '@medications_types_anaesthetic', language: '@language_fr_ca', value: Anesthésique } }
  - { fields: { placeholder: '@medications_types_antidepressant', language: '@language_fr_ca', value: Antidépresseur } }
  - { fields: { placeholder: '@medications_types_eugeroic', language: '@language_fr_ca', value: Eugéroïque } }
  - { fields: { placeholder: '@medications_types_antibiotic', language: '@language_fr_ca', value: Antibiotique } }
  - { fields: { placeholder: '@medications_types_emollient', language: '@language_fr_ca', value: Émollient } }
  - { fields: { placeholder: '@medications_types_vaccine', language: '@language_fr_ca', value: Vaccin } }
  - { fields: { placeholder: '@medications_brands_ibuprin', language: '@language_fr_ca', value: Ibuprin } }
  - { fields: { placeholder: '@medications_brands_lipitor', language: '@language_fr_ca', value: Lipitor } }
  - { fields: { placeholder: '@medications_brands_nexium', language: '@language_fr_ca', value: Nexium } }
  - { fields: { placeholder: '@medications_brands_actos', language: '@language_fr_ca', value: Actos } }
  - { fields: { placeholder: '@medications_names_ibuprofen', language: '@language_fr_ca', value: Ibuprofène } }
  - { fields: { placeholder: '@medications_names_atorvastatin', language: '@language_fr_ca', value: Atorvastatine } }
  - { fields: { placeholder: '@medications_names_esomeprazole', language: '@language_fr_ca', value: Ésoméprazole } }
  - { fields: { placeholder: '@medications_names_pioglitazone', language: '@language_fr_ca', value: Pioglitazone } }
  - { fields: { placeholder: '@medications_manufacturers_living_longer_ltd', language: '@language_fr_ca', value: 'Living Longer Ltd' } }
  - { fields: { placeholder: '@medications_manufacturers_carlton_cares_company', language: '@language_fr_ca', value: 'Carlton Cares Company' } }
  - { fields: { placeholder: '@medications_datasource_brands', language: '@language_fr_ca', value: 'Marques de médicaments' } }
  - { fields: { placeholder: '@medications_datasource_types', language: '@language_fr_ca', value: 'Types de médicaments' } }
  - { fields: { placeholder: '@medications_datasource_classifications', language: '@language_fr_ca', value: 'Classifications de médicaments' } }
  - { fields: { placeholder: '@medications_datasource_manufacturers', language: '@language_fr_ca', value: 'Fabricants de médicaments' } }
  - { fields: { placeholder: '@medications_datasource_names', language: '@language_fr_ca', value: 'Noms de médicaments' } }
  - { fields: { placeholder: '@medications_v2_form_heading_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@medications_v2_form_button_clear', language: '@language_fr_ca', value: Effacer } }
  - { fields: { placeholder: '@medications_v2_form_subheading_drug_administered', language: '@language_fr_ca', value: Administré } }
  - { fields: { placeholder: '@medications_v2_form_button_duplicate', language: '@language_fr_ca', value: Dupliquer } }
  - { fields: { placeholder: '@medications_v2_form_subheading_drug_intended', language: '@language_fr_ca', value: 'Prévu / suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_search_drug_administered', language: '@language_fr_ca', value: 'Rechercher un médicament administré ou omis' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_search_administered', language: '@language_fr_ca', value: 'Commencez à taper le nom du médicament' } }
  - { fields: { placeholder: '@medications_v2_form_messages_search_administered_no_matches_found', language: '@language_fr_ca', value: 'Aucune correspondance trouvée' } }
  - { fields: { placeholder: '@medications_v2_form_label_search_drug_intended', language: '@language_fr_ca', value: 'Recherche de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_search_drug_intended', language: '@language_fr_ca', value: 'Commencez à taper le nom du médicament' } }
  - { fields: { placeholder: '@medications_v2_form_messages_search_drug_intended_no_matches_found', language: '@language_fr_ca', value: 'Aucune correspondance trouvée' } }
  - { fields: { placeholder: '@medications_v2_form_label_search_drug_administered_brand', language: '@language_fr_ca', value: 'Nom de marque du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_search_drug_intended_brand', language: '@language_fr_ca', value: 'Nom de marque de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_manufacturer', language: '@language_fr_ca', value: 'Fabricant du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_manufacturer', language: '@language_fr_ca', value: 'Fabricant de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_class', language: '@language_fr_ca', value: 'Classe du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_class', language: '@language_fr_ca', value: 'Classe de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_strength', language: '@language_fr_ca', value: 'Force du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_strength', language: '@language_fr_ca', value: 'Force du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_supplier', language: '@language_fr_ca', value: 'Fournisseur du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_supplier', language: '@language_fr_ca', value: 'Fournisseur du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_type', language: '@language_fr_ca', value: 'Type de médicament administré, prescrit, dispensé ou omis' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_type', language: '@language_fr_ca', value: 'Type de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_route', language: '@language_fr_ca', value: 'Voie d''administration, d''ordonnance, de délivrance sous ordonnance ou d''omission' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_drug_administered_route_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_route', language: '@language_fr_ca', value: 'Route prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_drug_intended_route_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_dosage', language: '@language_fr_ca', value: 'Dose administrée, d''ordonnance, délivrée sur ordonnance ou omise' } }
  - { fields: { placeholder: '@medications_v2_form_message_drug_administered_dosage_unit', language: '@language_fr_ca', value: 'Entrez une valeur et sélectionnez une unité de mesure' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_drug_administered_units', language: '@language_fr_ca', value: unités } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_dosage', language: '@language_fr_ca', value: 'Dose prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_form_message_drug_intended_dosage_unit', language: '@language_fr_ca', value: 'Entrez une valeur et sélectionnez une unité de mesure' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_drug_intended_units', language: '@language_fr_ca', value: unités } }
  - { fields: { placeholder: '@medications_v2_form_label_form_administered', language: '@language_fr_ca', value: 'Forme administrée, d''ordonnance, délivrée sur ordonnance ou omise' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_form_administered_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@medications_v2_form_label_form_intended', language: '@language_fr_ca', value: 'Forme prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_form_intended_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@medications_v2_form_label_stage_error_occured', language: '@language_fr_ca', value: 'Stade auquel l''erreur s''est produite' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_select_one', language: '@language_fr_ca', value: 'Sélectionner un(e)' } }
  - { fields: { placeholder: '@medications_v2_form_label_error_type', language: '@language_fr_ca', value: 'Type d''erreur' } }
  - { fields: { placeholder: '@medications_v2_form_label_notes', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@medications_v2_form_label_other_important_factors', language: '@language_fr_ca', value: 'Autres facteurs importants' } }
  - { fields: { placeholder: '@medications_v2_form_label_adr_date_reaction_started', language: '@language_fr_ca', value: 'Date de début de la réaction ADR' } }
  - { fields: { placeholder: '@medications_v2_form_label_adr_date_reaction_stopped', language: '@language_fr_ca', value: 'Date de fin de la réaction ADR' } }
  - { fields: { placeholder: '@medications_v2_form_label_frequency_wrong', language: '@language_fr_ca', value: 'Fréquence des mauvais médicaments' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_reaction', language: '@language_fr_ca', value: 'Manifestation de réaction médicamenteuse' } }
  - { fields: { placeholder: '@medications_v2_form_label_error_detected_by', language: '@language_fr_ca', value: 'Erreur détectée par' } }
  - { fields: { placeholder: '@medications_v2_form_label_error_committed_by', language: '@language_fr_ca', value: 'Erreur commise par' } }
  - { fields: { placeholder: '@medications_v2_form_label_did_drug_reaction_reappear', language: '@language_fr_ca', value: 'La réaction indésirable est-elle réapparue lors de la réadministration du médicament?' } }
  - { fields: { placeholder: '@medications_v2_form_label_adr_organ_system', language: '@language_fr_ca', value: 'Champs de systèmes organiques ADR' } }
  - { fields: { placeholder: '@medications_v2_form_label_naranjo_total_score', language: '@language_fr_ca', value: 'Cote totale Naranjo' } }
  - { fields: { placeholder: '@medications_v2_form_label_adr_action_taken', language: '@language_fr_ca', value: 'Mesure ADR prise' } }
  - { fields: { placeholder: '@medications_v2_display_heading_medications', language: '@language_fr_ca', value: Médicaments } }
  - { fields: { placeholder: '@medications_v2_display_label_table_key', language: '@language_fr_ca', value: "Clé de tableau\_:" } }
  - { fields: { placeholder: '@medications_v2_display_label_medication_error', language: '@language_fr_ca', value: 'Erreur de médicament' } }
  - { fields: { placeholder: '@medications_v2_display_subheading_drug_administered', language: '@language_fr_ca', value: Administré } }
  - { fields: { placeholder: '@medications_v2_display_subheading_drug_intended', language: '@language_fr_ca', value: 'Prévu / suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_button_edit_medication', language: '@language_fr_ca', value: 'Modifier un médicament' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered', language: '@language_fr_ca', value: 'Médicament administré ou omis' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended', language: '@language_fr_ca', value: 'Médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_brand', language: '@language_fr_ca', value: 'Nom de marque du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_brand', language: '@language_fr_ca', value: 'Nom de marque de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_manufacturer', language: '@language_fr_ca', value: 'Fabricant du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_intended', language: '@language_fr_ca', value: 'Fabricant de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_class', language: '@language_fr_ca', value: 'Classe du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_class', language: '@language_fr_ca', value: 'Classe de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_strength', language: '@language_fr_ca', value: 'Force du drug administered' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_strength', language: '@language_fr_ca', value: 'Force du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_supplier', language: '@language_fr_ca', value: 'Fournisseur du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_supplier', language: '@language_fr_ca', value: 'Fournisseur du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_type', language: '@language_fr_ca', value: 'Type de médicament administré, prescrit, dispensé ou omis' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_type', language: '@language_fr_ca', value: 'Type de médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_route', language: '@language_fr_ca', value: 'Voie d''administration, d''ordonnance, de délivrance sous ordonnance ou d''omission' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_route', language: '@language_fr_ca', value: 'Route prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_dosage', language: '@language_fr_ca', value: 'Dose administrée' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_dosage', language: '@language_fr_ca', value: 'Dose prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_display_label_form_administered', language: '@language_fr_ca', value: 'Forme administrée, d''ordonnance, délivrée sur ordonnance ou omise' } }
  - { fields: { placeholder: '@medications_v2_display_label_form_intended', language: '@language_fr_ca', value: 'Forme prévue/suspectée' } }
  - { fields: { placeholder: '@medications_v2_display_label_stage_error_occured', language: '@language_fr_ca', value: 'Stade auquel l''erreur s''est produite' } }
  - { fields: { placeholder: '@medications_v2_display_label_error_type', language: '@language_fr_ca', value: 'Type d''erreur' } }
  - { fields: { placeholder: '@medications_v2_display_label_notes', language: '@language_fr_ca', value: Remarques } }
  - { fields: { placeholder: '@medications_v2_display_other_important_factors', language: '@language_fr_ca', value: 'Autres facteurs importants' } }
  - { fields: { placeholder: '@medications_v2_display_button_delete_medication', language: '@language_fr_ca', value: Supprimer } }
  - { fields: { placeholder: '@medications_v2_form_option_route_epidural', language: '@language_fr_ca', value: Péridurale } }
  - { fields: { placeholder: '@medications_v2_form_option_route_inhalation', language: '@language_fr_ca', value: Inhalation } }
  - { fields: { placeholder: '@medications_v2_form_option_route_intramuscular', language: '@language_fr_ca', value: Intramusculaire } }
  - { fields: { placeholder: '@medications_v2_form_option_route_intrathecal', language: '@language_fr_ca', value: Intrathécal } }
  - { fields: { placeholder: '@medications_v2_form_option_route_intravenous', language: '@language_fr_ca', value: Intraveineuse } }
  - { fields: { placeholder: '@medications_v2_form_option_route_centralintravenous', language: '@language_fr_ca', value: 'Intraveineuse centrale' } }
  - { fields: { placeholder: '@medications_v2_form_option_route_peripheralintravenous', language: '@language_fr_ca', value: 'Intraveineuse périphérique' } }
  - { fields: { placeholder: '@medications_v2_form_option_route_intravesicular', language: '@language_fr_ca', value: Intravésiculaire } }
  - { fields: { placeholder: '@medications_v2_form_option_route_nasal', language: '@language_fr_ca', value: Nasal } }
  - { fields: { placeholder: '@medications_v2_form_option_route_nasogastric', language: '@language_fr_ca', value: Naso-gastrique } }
  - { fields: { placeholder: '@medications_v2_form_option_route_not_applicable', language: '@language_fr_ca', value: 'Sans objet' } }
  - { fields: { placeholder: '@medications_v2_form_option_route_optical', language: '@language_fr_ca', value: Optique } }
  - { fields: { placeholder: '@medications_v2_form_option_route_oral', language: '@language_fr_ca', value: Oral } }
  - { fields: { placeholder: '@medications_v2_form_option_route_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@medications_v2_form_option_route_per_ear', language: '@language_fr_ca', value: 'Par l''oreille' } }
  - { fields: { placeholder: '@medications_v2_form_option_route_per_vagina', language: '@language_fr_ca', value: 'Par le vagin' } }
  - { fields: { placeholder: '@medications_v2_form_option_route_rectal', language: '@language_fr_ca', value: Rectal } }
  - { fields: { placeholder: '@medications_v2_form_option_route_subcutaneous', language: '@language_fr_ca', value: Sous-cutané } }
  - { fields: { placeholder: '@medications_v2_form_option_route_sublingual', language: '@language_fr_ca', value: Sublingual } }
  - { fields: { placeholder: '@medications_v2_form_option_route_topical', language: '@language_fr_ca', value: Topique } }
  - { fields: { placeholder: '@medications_v2_form_option_route_unknown', language: '@language_fr_ca', value: Inconnu } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_gram', language: '@language_fr_ca', value: Gramme } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_kilogram', language: '@language_fr_ca', value: Kilogramme } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_litre', language: '@language_fr_ca', value: Litre } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_microgram', language: '@language_fr_ca', value: Microgramme } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_milligram', language: '@language_fr_ca', value: Milligramme } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_millilitre', language: '@language_fr_ca', value: Millilitre } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_nanogram', language: '@language_fr_ca', value: Nanogramme } }
  - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_anaesthetic', language: '@language_fr_ca', value: Anesthésique } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_capsule', language: '@language_fr_ca', value: Capsule } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_cream', language: '@language_fr_ca', value: Crème } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_dermal_patch', language: '@language_fr_ca', value: 'Timbre transdermique' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_ear_drop', language: '@language_fr_ca', value: 'Goutte auriculaire' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_eye_drop', language: '@language_fr_ca', value: 'Goutte ophtalmique' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_eye_ointment', language: '@language_fr_ca', value: 'Onguent ophtalmique' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_gas', language: '@language_fr_ca', value: Gaz } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_gel', language: '@language_fr_ca', value: Gel } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_inhaler', language: '@language_fr_ca', value: Inhalateur } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_intramuscular_injection', language: '@language_fr_ca', value: 'Injection intramusculaire' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_intravenous_infusion', language: '@language_fr_ca', value: 'Perfusion intraveineuse' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_liquid_sachet', language: '@language_fr_ca', value: 'Sachet de liquide' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_lotion', language: '@language_fr_ca', value: Lotion } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_nasal_drop', language: '@language_fr_ca', value: 'Goutte nasale' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_nasal_ointment', language: '@language_fr_ca', value: 'Onguent nasal' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_ointment', language: '@language_fr_ca', value: Onguent } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_pessary', language: '@language_fr_ca', value: Pessaire } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_powder_sachet', language: '@language_fr_ca', value: 'Sachet de poudre' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_subcutaneous_injection', language: '@language_fr_ca', value: 'Injection sous-cutanée' } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_suppository', language: '@language_fr_ca', value: Suppositoire } }
  - { fields: { placeholder: '@medications_v2_form_option_administered_tablet', language: '@language_fr_ca', value: Tablette } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_abbreviation_usage', language: '@language_fr_ca', value: 'Utilisation d''abréviation(s) du nom / de la concentration / de la dose / du mode d''emploi du médicament (par exemple, MTX, 0,1 mg, 1 po)' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_follow_up_failure', language: '@language_fr_ca', value: 'Omission de se référer pour un suivi hospitalier' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_transfer', language: '@language_fr_ca', value: 'Mauvais transfert / transcription d''information entre les formulaires papier ou électroniques' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_communication', language: '@language_fr_ca', value: 'Mauvaise communication (verbale ou écrite) entre les prestataires de soins' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_handwritten_prescription', language: '@language_fr_ca', value: 'Ordonnance manuscrite / tableau difficile à lire' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_omitted_signature', language: '@language_fr_ca', value: 'Signature omise du professionnel de la santé' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_to_follow_instructions', language: '@language_fr_ca', value: 'Manquement du patient / soignant à suivre les instructions' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_of_compliance_aid', language: '@language_fr_ca', value: "Échec de l'aide à la conformité Â\_/ système de dosage surveillé (MDS)" } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_of_adequate_medicines_security', language: '@language_fr_ca', value: 'Échec de la sécurité adéquate des médicaments (par exemple CD manquant)' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_substance_abuse', language: '@language_fr_ca', value: 'Abus de substances (y compris l''alcool)' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_similar_medicines_look_name', language: '@language_fr_ca', value: 'Médicaments avec des noms ou apparences similaires' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_labelling', language: '@language_fr_ca', value: 'Mauvais étiquetage et emballage d''un fabricant commercial' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_supplementary_prescribing', language: '@language_fr_ca', value: 'Praticien de la santé entreprenant une prescription supplémentaire' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_guidelines_variance', language: '@language_fr_ca', value: 'Écart par rapport aux lignes directrices pour des raisons cliniques valables' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_patient_group_direction', language: '@language_fr_ca', value: "Implication d'un médicament fourni sous une direction de groupe de patients (PDG)Â\_" } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_otc_medicine', language: '@language_fr_ca', value: 'Implication d''un médicament en vente libre (OTC)' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_in_monitoring', language: '@language_fr_ca', value: 'Échec de la surveillance / évaluation du traitement médicamenteux' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_clinical_assessment_equipment', language: '@language_fr_ca', value: 'Panne de l''équipement d''évaluation clinique' } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_unknown', language: '@language_fr_ca', value: Inconnu } }
  - { fields: { placeholder: '@medications_v2_form_option_important_factors_not_applicable', language: '@language_fr_ca', value: 'Sans objet' } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_prescribing', language: '@language_fr_ca', value: Prescription } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_administration', language: '@language_fr_ca', value: 'Administration / fourniture d''un médicament à partir d''une zone clinique' } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_advice', language: '@language_fr_ca', value: Conseil } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_monitoring', language: '@language_fr_ca', value: 'Surveillance / suivi de l''utilisation de médicament' } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_otc_supply', language: '@language_fr_ca', value: 'Fourniture ou utilisation d''un médicament en vente libre (OTC)' } }
  - { fields: { placeholder: '@medications_v2_form_option_stage_errors_medicing_preparation', language: '@language_fr_ca', value: 'Préparation de médicaments dans tous les lieux / distribution dans une pharmacie' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_adverse_drug_reaction', language: '@language_fr_ca', value: 'Réaction indésirable au médicament (lorsqu''il est utilisé comme prévu)' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_contra_indication', language: '@language_fr_ca', value: 'Contre-indication à l''utilisation du médicament en relation avec des drogues ou des conditions' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_medicine_mismatch', language: '@language_fr_ca', value: 'Inadéquation entre le patient et le médicament' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_omitted_medicine', language: '@language_fr_ca', value: 'Médicament / ingrédient omis' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_allergy', language: '@language_fr_ca', value: 'Patient allergique au traitement' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_expiry_date', language: '@language_fr_ca', value: 'Date d''expiration incorrecte / omise / dépassée' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_information_leaflet', language: '@language_fr_ca', value: 'Dépliant d''information patient erroné / omis' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_directions', language: '@language_fr_ca', value: 'Instructions verbales du patient incorrectes / omises' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_medicine_label', language: '@language_fr_ca', value: 'Étiquette de médicament incorrecte / transposée / omise' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_dose_strength', language: '@language_fr_ca', value: 'Dose ou concentration incorrecte / incertaine' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_medicine', language: '@language_fr_ca', value: 'Drogue ou médicament incorrect' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_formulation', language: '@language_fr_ca', value: 'Formulation incorrecte' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_frequency', language: '@language_fr_ca', value: 'Fréquence incorrecte' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_preparation_method', language: '@language_fr_ca', value: 'Méthode de préparation ou fourniture incorrecte' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_quantity', language: '@language_fr_ca', value: 'Quantité incorrecte' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_route', language: '@language_fr_ca', value: 'Voie d''administration incorrecte' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_storage', language: '@language_fr_ca', value: 'Entreposage incorrect' } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_other', language: '@language_fr_ca', value: Autre } }
  - { fields: { placeholder: '@medications_v2_form_option_error_type_unknown', language: '@language_fr_ca', value: Inconnu } }
  - { fields: { placeholder: '@medications_v2_form_message_delete_medication_title', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@medications_v2_form_message_delete_medication_content', language: '@language_fr_ca', value: 'Voulez-vous supprimer ce médicament de l''incident?' } }
  - { fields: { placeholder: '@medications_v2_form_message_cancel_medication_content', language: '@language_fr_ca', value: 'Voulez-vous annuler vos modifications?' } }
  - { fields: { placeholder: '@medications_v2_form_message_cancel_medication_title', language: '@language_fr_ca', value: 'Êtes-vous certain?' } }
  - { fields: { placeholder: '@medications_v2_form_button_delete_medication_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@medications_v2_form_button_delete_medication_ok', language: '@language_fr_ca', value: OK } }
  - { fields: { placeholder: '@medications_v2_form_button_cancel_cancel', language: '@language_fr_ca', value: Annuler } }
  - { fields: { placeholder: '@medications_v2_form_button_cancel_ok', language: '@language_fr_ca', value: OK } }
  - { fields: { placeholder: '@medications_v2_form_message_medication_search_no_matches', language: '@language_fr_ca', value: 'Aucun médicament correspondant trouvé' } }
  - { fields: { placeholder: '@medications_v2_form_message_medication_search_exact_matches_only', language: '@language_fr_ca', value: 'Correspondances exactes uniquement. Veuillez entrer deux caractères ou plus pour élargir votre recherche.' } }
  - { fields: { placeholder: '@medications_v2_form_radio_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@medications_v2_form_radio_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@medications_v2_form_radio_unknown', language: '@language_fr_ca', value: Inconnu } }
  - { fields: { placeholder: '@medications_v2_message_search_results_title', language: '@language_fr_ca', value: 'Médicament correspondant' } }
  - { fields: { placeholder: '@medications_v2_message_search_table_generic_name', language: '@language_fr_ca', value: 'Nom générique' } }
  - { fields: { placeholder: '@medications_v2_message_search_table_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@medications_v2_message_search_table_class', language: '@language_fr_ca', value: Classe } }
  - { fields: { placeholder: '@medications_v2_message_search_table_brand', language: '@language_fr_ca', value: Marque } }
  - { fields: { placeholder: '@medications_v2_message_search_table_manufacturer', language: '@language_fr_ca', value: Fabricant } }
  - { fields: { placeholder: '@medications_v2_message_search_table_strength', language: '@language_fr_ca', value: Force } }
  - { fields: { placeholder: '@medications_v2_button_search_table_choose', language: '@language_fr_ca', value: Choisir } }
  - { fields: { placeholder: '@medications_success_medication_saved', language: '@language_fr_ca', value: 'Médicament enregistré avec succès' } }
  - { fields: { placeholder: '@medications_success_medication_deleted', language: '@language_fr_ca', value: 'Médicament supprimé avec succès' } }
  - { fields: { placeholder: '@medications_success_medication_added', language: '@language_fr_ca', value: 'a été ajouté(e)' } }
  - { fields: { placeholder: '@medications_success_medication_updated', language: '@language_fr_ca', value: 'a été mis(e) à jour' } }
  - { fields: { placeholder: '@medications_error_failed_to_get_all_medications', language: '@language_fr_ca', value: 'Échec de l''obtention de tous les médicaments' } }
  - { fields: { placeholder: '@medications_error_failed_to_get_medications_count', language: '@language_fr_ca', value: 'Échec de l''obtention du nombre de médicaments' } }
  - { fields: { placeholder: '@medications_error_failed_to_get_medication', language: '@language_fr_ca', value: 'Échec de l''obtention du médicament' } }
  - { fields: { placeholder: '@medications_error_failed_to_add_medications', language: '@language_fr_ca', value: 'Échec de l''ajout des médicaments' } }
  - { fields: { placeholder: '@medications_error_failed_to_update_medications', language: '@language_fr_ca', value: 'Échec de la modification des médicaments' } }
  - { fields: { placeholder: '@medications_error_failed_to_delete_medications', language: '@language_fr_ca', value: 'Échec de la suppression des médicaments' } }
  - { fields: { placeholder: '@medications_error_medication_not_found', language: '@language_fr_ca', value: 'Médicament introuvable' } }
  - { fields: { placeholder: '@medications_error_cannot_edit_global_medication', language: '@language_fr_ca', value: 'Impossible de modifier un médicament global' } }
  - { fields: { placeholder: '@medications_risk_removal', language: '@language_fr_ca', value: 'Médicament supprimé du risque' } }
  - { fields: { placeholder: '@medications_removed_from_record', language: '@language_fr_ca', value: 'Médicament supprimé de {{record}}' } }
  - { fields: { placeholder: '@medications_risk_added', language: '@language_fr_ca', value: 'Médicament ajouté au risque' } }
  - { fields: { placeholder: '@medications_added_to_record', language: '@language_fr_ca', value: 'Médicament ajouté à {{record}}' } }
  - { fields: { placeholder: '@medications_new_medication', language: '@language_fr_ca', value: 'Nouveau médicament' } }
  - { fields: { placeholder: '@medications_edit_medication', language: '@language_fr_ca', value: 'Modifier un médicament' } }
  - { fields: { placeholder: '@medications_nav_new_medication', language: '@language_fr_ca', value: 'Nouveau médicament' } }
  - { fields: { placeholder: '@medications_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@medications_permissions_subscription_controller_title', language: '@language_fr_ca', value: 'Contrôleur de souscription' } }
  - { fields: { placeholder: '@medications_permissions_local_controller_title', language: '@language_fr_ca', value: 'Contrôleur local' } }
  - { fields: { placeholder: '@medications_v2_form_message_medication_search_invalid_location', language: '@language_fr_ca', value: 'Aucun médicament trouvé. L''utilisateur doit être associé à un seul emplacement' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_administered_source_id', language: '@language_fr_ca', value: 'Numéro d''identification du médicament (DIN) / ID du besoin urgent de santé publique du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_form_label_drug_intended_source_id', language: '@language_fr_ca', value: 'Numéro d''identification du médicament (DIN) / ID du besoin urgent de santé publique du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_administered_source_id', language: '@language_fr_ca', value: 'Numéro d''identification du médicament (DIN) / ID du besoin urgent de santé publique du médicament administré' } }
  - { fields: { placeholder: '@medications_v2_display_label_drug_intended_source_id', language: '@language_fr_ca', value: 'Numéro d''identification du médicament (DIN) / ID du besoin urgent de santé publique du médicament prévu/suspecté' } }
  - { fields: { placeholder: '@medications_v2_form_label_date_first_used', language: '@language_fr_ca', value: 'Date à laquelle le patient a utilisé le médicament pour la première fois' } }
  - { fields: { placeholder: '@medications_v2_form_label_date_last_used', language: '@language_fr_ca', value: 'Date à laquelle le patient a arrêté de prendre le médicament' } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_date_first_used', language: '@language_fr_ca', value: jj/mm/aaaa } }
  - { fields: { placeholder: '@medications_v2_form_placeholder_date_last_used', language: '@language_fr_ca', value: jj/mm/aaaa } }
  - { fields: { placeholder: '@medications_v2_display_label_date_first_used', language: '@language_fr_ca', value: 'Date à laquelle le patient a utilisé le médicament pour la première fois' } }
  - { fields: { placeholder: '@medications_v2_display_label_date_last_used', language: '@language_fr_ca', value: 'Date à laquelle le patient a arrêté de prendre le médicament' } }
  - { fields: { placeholder: '@medications_v2_form_message_invalid_search_length', language: '@language_fr_ca', value: 'Veuillez saisir un minimum de deux caractères pour poursuivre votre recherche' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_withdrawn', language: '@language_fr_ca', value: 'Produit retiré, arrêté, abandonné ou interrompu' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_dose_reduced', language: '@language_fr_ca', value: 'Dose réduite' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_dose_increased', language: '@language_fr_ca', value: 'Dose augmentée' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_dose_not_changed', language: '@language_fr_ca', value: 'Dose inchangée' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_unknown', language: '@language_fr_ca', value: 'Inconnu' } }
  - { fields: { placeholder: '@medications_v2_form_option_action_taken_na', language: '@language_fr_ca', value: 'Sans objet' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_lessened_yes', language: '@language_fr_ca', value: 'Oui' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_lessened_no', language: '@language_fr_ca', value: 'Non' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_lessened_unknown', language: '@language_fr_ca', value: 'Inconnu' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_lessened_na', language: '@language_fr_ca', value: 'Sans objet' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_reappeared_yes', language: '@language_fr_ca', value: 'Oui' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_reappeared_no', language: '@language_fr_ca', value: 'Non' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_reappeared_unknown', language: '@language_fr_ca', value: 'Inconnu' } }
  - { fields: { placeholder: '@medications_v2_form_option_adr_reappeared_na', language: '@language_fr_ca', value: 'Sans objet' } }
