entityClass: I18n\Entity\Translation
priority: 15
data:
  -
    fields:
      placeholder: '@medications_singular'
      language: '@language_en_gb'
      value: Medication
  -
    fields:
      placeholder: '@medications_plural'
      language: '@language_en_gb'
      value: Medications
  -
    fields:
      placeholder: '@medications_search'
      language: '@language_en_gb'
      value: 'Search Medications'
  -
    fields:
      placeholder: '@medications_create'
      language: '@language_en_gb'
      value: 'Create Medication'
  -
    fields:
      placeholder: '@medications_edit'
      language: '@language_en_gb'
      value: 'Edit Medication'
  -
    fields:
      placeholder: '@medications_form_generic_name'
      language: '@language_en_gb'
      value: 'Generic Name'
  -
    fields:
      placeholder: '@medications_form_generic_name_label'
      language: '@language_en_gb'
      value: 'Generic Name'
  -
    fields:
      placeholder: '@medications_form_select_generic_name'
      language: '@language_en_gb'
      value: 'Select a Generic Name'
  -
    fields:
      placeholder: '@medications_form_ref'
      language: '@language_en_gb'
      value: Ref.
  -
    fields:
      placeholder: '@medications_form_ref_label'
      language: '@language_en_gb'
      value: Ref.
  -
    fields:
      placeholder: '@medications_form_type'
      language: '@language_en_gb'
      value: Type
  -
    fields:
      placeholder: '@medications_form_type_label'
      language: '@language_en_gb'
      value: Type of Drug
  -
    fields:
      placeholder: '@medications_form_select_type'
      language: '@language_en_gb'
      value: 'Select Type'
  -
    fields:
      placeholder: '@medications_form_class'
      language: '@language_en_gb'
      value: Class
  -
    fields:
      placeholder: '@medications_form_class_label'
      language: '@language_en_gb'
      value: Class
  -
    fields:
      placeholder: '@medications_form_select_class'
      language: '@language_en_gb'
      value: 'Select Classification'
  -
    fields:
      placeholder: '@medications_form_subclass'
      language: '@language_en_gb'
      value: SubClass
  -
    fields:
      placeholder: '@medications_form_subclass_label'
      language: '@language_en_gb'
      value: SubClass
  -
    fields:
      placeholder: '@medications_form_strength'
      language: '@language_en_gb'
      value: Strength
  -
    fields:
      placeholder: '@medications_form_strength_label'
      language: '@language_en_gb'
      value: Strength
  -
    fields:
      placeholder: '@medications_form_supplier'
      language: '@language_en_gb'
      value: Supplier
  -
    fields:
      placeholder: '@medications_form_supplier_label'
      language: '@language_en_gb'
      value: Supplier
  -
    fields:
      placeholder: '@medications_form_select_subclass'
      language: '@language_en_gb'
      value: 'Select Sub-Classification'
  -
    fields:
      placeholder: '@medications_form_brand'
      language: '@language_en_gb'
      value: Brand
  -
    fields:
      placeholder: '@medications_form_brand_label'
      language: '@language_en_gb'
      value: Brand Name
  -
    fields:
      placeholder: '@medications_form_select_brand'
      language: '@language_en_gb'
      value: 'Select a Brand'
  -
    fields:
      placeholder: '@medications_form_manufacturer'
      language: '@language_en_gb'
      value: Manufacturer
  -
    fields:
      placeholder: '@medications_form_manufacturer_label'
      language: '@language_en_gb'
      value: Manufacturer
  -
    fields:
      placeholder: '@medications_form_select_manufacturer'
      language: '@language_en_gb'
      value: 'Select Manufacturer'
  -
    fields:
      placeholder: '@medications_form_controlled'
      language: '@language_en_gb'
      value: 'Controlled drug?'
  -
    fields:
      placeholder: '@medications_form_controlled_label'
      language: '@language_en_gb'
      value: 'Controlled drug?'
  -
    fields:
      placeholder: '@medications_form_status'
      language: '@language_en_gb'
      value: 'Status'
  -
    fields:
      placeholder: '@medications_form_status_label'
      language: '@language_en_gb'
      value: 'Status'
  -
    fields:
      placeholder: '@medications_relation_id'
      language: '@language_en_gb'
      value: ID
  -
    fields:
      placeholder: '@medications_relation_medication'
      language: '@language_en_gb'
      value: Medication
  -
    fields:
      placeholder: '@medications_relation_correct'
      language: '@language_en_gb'
      value: Correct
  -
    fields:
      placeholder: '@medications_relation_administered'
      language: '@language_en_gb'
      value: Administered
  -
    fields:
      placeholder: '@medications_relation_route'
      language: '@language_en_gb'
      value: Route
  -
    fields:
      placeholder: '@medications_relation_from'
      language: '@language_en_gb'
      value: From
  -
    fields:
      placeholder: '@medications_form_classification_value_1'
      language: '@language_en_gb'
      value: 'Narcotics'
  -
    fields:
      placeholder: '@medications_form_classification_value_2'
      language: '@language_en_gb'
      value: 'Depressants'
  -
    fields:
      placeholder: '@medications_form_classification_value_3'
      language: '@language_en_gb'
      value: 'Stimulants'
  -
    fields:
      placeholder: '@medications_form_classification_value_4'
      language: '@language_en_gb'
      value: 'Hallucinogens'
  -
    fields:
      placeholder: '@medications_form_classification_value_5'
      language: '@language_en_gb'
      value: 'Anabolic steroids'
  -
    fields:
      placeholder: '@medications_form_type_medication'
      language: '@language_en_gb'
      value: 'Medication'
  -
    fields:
      placeholder: '@medications_types_psychedelic'
      language: '@language_en_gb'
      value: 'Psychedelic'
  -
    fields:
      placeholder: '@medications_types_anaesthetic'
      language: '@language_en_gb'
      value: 'Anaesthetic'
  -
    fields:
      placeholder: '@medications_types_analgesic'
      language: '@language_en_gb'
      value: 'Analgesic'
  -
    fields:
      placeholder: '@medications_types_antidepressant'
      language: '@language_en_gb'
      value: 'Antidepressant'
  -
    fields:
      placeholder: '@medications_types_eugeroic'
      language: '@language_en_gb'
      value: 'Eugeroic'
  -
    fields:
      placeholder: '@medications_types_antibiotic'
      language: '@language_en_gb'
      value: 'Antibiotic'
  -
    fields:
      placeholder: '@medications_types_emollient'
      language: '@language_en_gb'
      value: 'Emollient'
  -
    fields:
      placeholder: '@medications_types_vaccine'
      language: '@language_en_gb'
      value: 'Vaccine'
  -
    fields:
      placeholder: '@medications_brands_ibuprin'
      language: '@language_en_gb'
      value: Ibuprin
  -
    fields:
      placeholder: '@medications_brands_lipitor'
      language: '@language_en_gb'
      value: Lipitor
  -
    fields:
      placeholder: '@medications_brands_nexium'
      language: '@language_en_gb'
      value: Nexium
  -
    fields:
      placeholder: '@medications_brands_actos'
      language: '@language_en_gb'
      value: Actos
  -
    fields:
      placeholder: '@medications_names_ibuprofen'
      language: '@language_en_gb'
      value: 'Ibuprofen'
  -
    fields:
      placeholder: '@medications_names_atorvastatin'
      language: '@language_en_gb'
      value: 'Atorvastatin'
  -
    fields:
      placeholder: '@medications_names_esomeprazole'
      language: '@language_en_gb'
      value: 'Esomeprazole'
  -
    fields:
      placeholder: '@medications_names_pioglitazone'
      language: '@language_en_gb'
      value: 'Pioglitazone'
  -
    fields:
      placeholder: '@medications_manufacturers_living_longer_ltd'
      language: '@language_en_gb'
      value: 'Living Longer Ltd'
  -
    fields:
      placeholder: '@medications_manufacturers_carlton_cares_company'
      language: '@language_en_gb'
      value: 'Carlton Cares Company'
  -
    fields:
      placeholder: '@medications_datasource_brands'
      language: '@language_en_gb'
      value: 'Medication Brands'
  -
    fields:
      placeholder: '@medications_datasource_types'
      language: '@language_en_gb'
      value: 'Medication Types'
  -
    fields:
      placeholder: '@medications_datasource_classifications'
      language: '@language_en_gb'
      value: 'Medication Classifications'
  -
    fields:
      placeholder: '@medications_datasource_manufacturers'
      language: '@language_en_gb'
      value: 'Medication Manufacturers'
  -
    fields:
      placeholder: '@medications_datasource_names'
      language: '@language_en_gb'
      value: 'Medication Names'
  -
    fields:
      placeholder: '@medications_v2_form_heading_medications'
      language: '@language_en_gb'
      value: 'Medications'
  -
    fields:
      placeholder: '@medications_v2_form_subheading_drug_administered'
      language: '@language_en_gb'
      value: 'Administered'
  -
    fields:
      placeholder: '@medications_v2_form_button_duplicate'
      language: '@language_en_gb'
      value: 'Duplicate'
  -
    fields:
      placeholder: '@medications_v2_form_button_clear'
      language: '@language_en_gb'
      value: 'Clear'
  -
    fields:
      placeholder: '@medications_v2_form_subheading_drug_intended'
      language: '@language_en_gb'
      value: 'Intended / Suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_search_drug_administered'
      language: '@language_en_gb'
      value: 'Search for drug administered or omitted'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_search_administered'
      language: '@language_en_gb'
      value: 'Start typing name of drug'
  -
    fields:
      placeholder: '@medications_v2_form_messages_search_administered_no_matches_found'
      language: '@language_en_gb'
      value: 'No matches found'
  -
    fields:
      placeholder: '@medications_v2_form_label_search_drug_intended'
      language: '@language_en_gb'
      value: 'Search for intended / suspected drug'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_search_drug_intended'
      language: '@language_en_gb'
      value: 'Start typing name of drug'
  -
    fields:
      placeholder: '@medications_v2_form_messages_search_drug_intended_no_matches_found'
      language: '@language_en_gb'
      value: 'No matches found'
  -
    fields:
      placeholder: '@medications_v2_form_label_search_drug_administered_brand'
      language: '@language_en_gb'
      value: 'Brand name of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_search_drug_intended_brand'
      language: '@language_en_gb'
      value: 'Brand name of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_class'
      language: '@language_en_gb'
      value: 'Class of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_class'
      language: '@language_en_gb'
      value: 'Class of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_strength'
      language: '@language_en_gb'
      value: 'Strength of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_strength'
      language: '@language_en_gb'
      value: 'Strength of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_supplier'
      language: '@language_en_gb'
      value: 'Supplier of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_supplier'
      language: '@language_en_gb'
      value: 'Supplier of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_type'
      language: '@language_en_gb'
      value: 'Type of drug administered,prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_type'
      language: '@language_en_gb'
      value: 'Type of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_route'
      language: '@language_en_gb'
      value: 'Route administered, prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_drug_administered_route_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_route'
      language: '@language_en_gb'
      value: 'Route intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_drug_intended_route_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_dosage'
      language: '@language_en_gb'
      value: 'Dose administered, prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_form_message_drug_administered_dosage_unit'
      language: '@language_en_gb'
      value: 'Enter a value and select a unit of measurement'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_drug_administered_units'
      language: '@language_en_gb'
      value: 'units'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_dosage'
      language: '@language_en_gb'
      value: 'Dose intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_message_drug_intended_dosage_unit'
      language: '@language_en_gb'
      value: 'Enter a value and select a unit of measurement'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_drug_intended_units'
      language: '@language_en_gb'
      value: 'units'
  -
    fields:
      placeholder: '@medications_v2_form_label_form_administered'
      language: '@language_en_gb'
      value: 'Form administered, prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_form_administered_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_form_intended'
      language: '@language_en_gb'
      value: 'Form intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_form_intended_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_stage_error_occured'
      language: '@language_en_gb'
      value: 'Stage at which error occurred'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_error_type'
      language: '@language_en_gb'
      value: 'Type of error'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_notes'
      language: '@language_en_gb'
      value: 'Notes'
  -
    fields:
      placeholder: '@medications_v2_form_label_other_important_factors'
      language: '@language_en_gb'
      value: 'Other Important Factors'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_select_one'
      language: '@language_en_gb'
      value: 'Select one'
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_date_reaction_started'
      language: '@language_en_gb'
      value: 'ADR Date Reaction Started'
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_date_reaction_stopped'
      language: '@language_en_gb'
      value: 'ADR Date Reaction Stopped'
  -
    fields:
      placeholder: '@medications_v2_form_label_frequency_wrong'
      language: '@language_en_gb'
      value: 'Frequency of wrong medication'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_reaction'
      language: '@language_en_gb'
      value: 'Drug Reaction Manifestation'
  -
    fields:
      placeholder: '@medications_v2_form_label_error_detected_by'
      language: '@language_en_gb'
      value: 'Error Detected By'
  -
    fields:
      placeholder: '@medications_v2_form_label_error_committed_by'
      language: '@language_en_gb'
      value: 'Error Committed By'
  -
    fields:
      placeholder: '@medications_v2_form_label_did_drug_reaction_reappear'
      language: '@language_en_gb'
      value: 'Did adverse reaction reappear when the drug was re-administered?'
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_organ_system'
      language: '@language_en_gb'
      value: 'ADR Organ systems fields'
  -
    fields:
      placeholder: '@medications_v2_form_label_naranjo_total_score'
      language: '@language_en_gb'
      value: 'Naranjo Total Score'
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_action_taken'
      language: '@language_en_gb'
      value: 'ADR-Action Taken'
  -
    fields:
      placeholder: '@medications_v2_display_heading_medications'
      language: '@language_en_gb'
      value: 'Medications'
  -
    fields:
      placeholder: '@medications_v2_display_label_table_key'
      language: '@language_en_gb'
      value: 'Table Key:'
  -
    fields:
      placeholder: '@medications_v2_display_label_medication_error'
      language: '@language_en_gb'
      value: 'Medication error'
  -
    fields:
      placeholder: '@medications_v2_display_subheading_drug_administered'
      language: '@language_en_gb'
      value: 'Administered'
  -
    fields:
      placeholder: '@medications_v2_display_subheading_drug_intended'
      language: '@language_en_gb'
      value: 'Intended / Suspected'
  -
    fields:
      placeholder: '@medications_v2_display_button_edit_medication'
      language: '@language_en_gb'
      value: 'Edit Medication'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered'
      language: '@language_en_gb'
      value: 'Drug administered or omitted'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended'
      language: '@language_en_gb'
      value: 'Intended / Suspected drug'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_brand'
      language: '@language_en_gb'
      value: 'Brand name of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_brand'
      language: '@language_en_gb'
      value: 'Brand name of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_intended'
      language: '@language_en_gb'
      value: 'Manufacturer of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_class'
      language: '@language_en_gb'
      value: 'Class of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_class'
      language: '@language_en_gb'
      value: 'Class of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_strength'
      language: '@language_en_gb'
      value: 'Strength of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_strength'
      language: '@language_en_gb'
      value: 'Strength of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_supplier'
      language: '@language_en_gb'
      value: 'Supplier of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_supplier'
      language: '@language_en_gb'
      value: 'Supplier of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_type'
      language: '@language_en_gb'
      value: 'Type of drug administered,prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_type'
      language: '@language_en_gb'
      value: 'Type of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_route'
      language: '@language_en_gb'
      value: 'Route administered, prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_route'
      language: '@language_en_gb'
      value: 'Route intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_dosage'
      language: '@language_en_gb'
      value: 'Dose administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_dosage'
      language: '@language_en_gb'
      value: 'Dose intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_form_administered'
      language: '@language_en_gb'
      value: 'Form administered, prescribed, dispensed or omitted'
  -
    fields:
      placeholder: '@medications_v2_display_label_form_intended'
      language: '@language_en_gb'
      value: 'Form intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_stage_error_occured'
      language: '@language_en_gb'
      value: 'Stage at which error occurred'
  -
    fields:
      placeholder: '@medications_v2_display_label_error_type'
      language: '@language_en_gb'
      value: 'Type of error'
  -
    fields:
      placeholder: '@medications_v2_display_label_notes'
      language: '@language_en_gb'
      value: 'Notes'
  -
    fields:
      placeholder: '@medications_v2_display_other_important_factors'
      language: '@language_en_gb'
      value: 'Other Important Factors'
  -
    fields:
      placeholder: '@medications_v2_display_button_delete_medication'
      language: '@language_en_gb'
      value: 'Delete'
  -
    fields:
      placeholder: '@medications_v2_display_button_edit_medication'
      language: '@language_en_gb'
      value: 'Edit Medication'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_epidural'
      language: '@language_en_gb'
      value: 'Epidural'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_inhalation'
      language: '@language_en_gb'
      value: 'Inhalation'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_intramuscular'
      language: '@language_en_gb'
      value: 'Intramuscular'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_intrathecal'
      language: '@language_en_gb'
      value: 'Intrathecal'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_intravenous'
      language: '@language_en_gb'
      value: 'Intravenous'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_centralintravenous'
      language: '@language_en_gb'
      value: 'Central Intravenous'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_peripheralintravenous'
      language: '@language_en_gb'
      value: 'Peripheral Intravenous'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_intravesicular'
      language: '@language_en_gb'
      value: 'Intravesicular'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_nasal'
      language: '@language_en_gb'
      value: 'Nasal'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_nasogastric'
      language: '@language_en_gb'
      value: 'Naso-gastric'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_not_applicable'
      language: '@language_en_gb'
      value: 'Not applicable'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_optical'
      language: '@language_en_gb'
      value: 'Optical'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_oral'
      language: '@language_en_gb'
      value: 'Oral'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_per_ear'
      language: '@language_en_gb'
      value: 'Per ear'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_per_vagina'
      language: '@language_en_gb'
      value: 'Per vagina'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_rectal'
      language: '@language_en_gb'
      value: 'Rectal'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_subcutaneous'
      language: '@language_en_gb'
      value: 'Subcutaneous'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_sublingual'
      language: '@language_en_gb'
      value: 'Sublingual'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_topical'
      language: '@language_en_gb'
      value: 'Topical'
  -
    fields:
      placeholder: '@medications_v2_form_option_route_unknown'
      language: '@language_en_gb'
      value: 'Unknown'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_gram'
      language: '@language_en_gb'
      value: 'Gram'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_kilogram'
      language: '@language_en_gb'
      value: 'Kilogram'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_litre'
      language: '@language_en_gb'
      value: 'Litre'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_microgram'
      language: '@language_en_gb'
      value: 'Microgram'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_milligram'
      language: '@language_en_gb'
      value: 'Milligram'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_millilitre'
      language: '@language_en_gb'
      value: 'Millilitre'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_nanogram'
      language: '@language_en_gb'
      value: 'Nanogram'
  -
    fields:
      placeholder: '@medications_v2_form_option_dosage_unit_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_anaesthetic'
      language: '@language_en_gb'
      value: 'Anaesthetic'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_capsule'
      language: '@language_en_gb'
      value: 'Capsule'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_cream'
      language: '@language_en_gb'
      value: 'Cream'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_dermal_patch'
      language: '@language_en_gb'
      value: 'Dermal Patch'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_ear_drop'
      language: '@language_en_gb'
      value: 'Ear Drop'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_eye_drop'
      language: '@language_en_gb'
      value: 'Eye Drop'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_eye_ointment'
      language: '@language_en_gb'
      value: 'Eye Ointment'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_gas'
      language: '@language_en_gb'
      value: 'Gas'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_gel'
      language: '@language_en_gb'
      value: 'Gel'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_inhaler'
      language: '@language_en_gb'
      value: 'Inhaler'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_intramuscular_injection'
      language: '@language_en_gb'
      value: 'Intramuscular Injection'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_intravenous_infusion'
      language: '@language_en_gb'
      value: 'Intravenous infusion'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_liquid_sachet'
      language: '@language_en_gb'
      value: 'Liquid sachet'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_lotion'
      language: '@language_en_gb'
      value: 'Lotion'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_nasal_drop'
      language: '@language_en_gb'
      value: 'Nasal Drop'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_nasal_ointment'
      language: '@language_en_gb'
      value: 'Nasal ointment'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_ointment'
      language: '@language_en_gb'
      value: 'Ointment'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_pessary'
      language: '@language_en_gb'
      value: 'Pessary'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_powder_sachet'
      language: '@language_en_gb'
      value: 'Powder sachet'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_subcutaneous_injection'
      language: '@language_en_gb'
      value: 'Subcutaneous Injection'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_suppository'
      language: '@language_en_gb'
      value: 'Suppository'
  -
    fields:
      placeholder: '@medications_v2_form_option_administered_tablet'
      language: '@language_en_gb'
      value: 'Tablet'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_abbreviation_usage'
      language: '@language_en_gb'
      value: 'Use of abbreviation(s) of drug name / strength / dose / directions (e.g. MTX, .1 mg, 1 po)'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_follow_up_failure'
      language: '@language_en_gb'
      value: 'Failure to refer for hospital follow-up'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_poor_transfer'
      language: '@language_en_gb'
      value: 'Poor transfer / transcription of information between paper and/or electronic forms'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_poor_communication'
      language: '@language_en_gb'
      value: 'Poor communication between care providers (verbal or written)'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_handwritten_prescription'
      language: '@language_en_gb'
      value: 'Handwritten prescription / chart difficult to read'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_omitted_signature'
      language: '@language_en_gb'
      value: 'Omitted signature of healthcare practitioner'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_failure_to_follow_instructions'
      language: '@language_en_gb'
      value: 'Patient / carer failure to follow instructions'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_failure_of_compliance_aid'
      language: '@language_en_gb'
      value: 'Failure of compliance aid  / monitored dosage system (MDS)'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_failure_of_adequate_medicines_security'
      language: '@language_en_gb'
      value: 'Failure of adequate medicines security (e.g. missing CD)'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_substance_abuse'
      language: '@language_en_gb'
      value: 'Substance misuse (including alcohol)'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_similar_medicines_look_name'
      language: '@language_en_gb'
      value: 'Medicines with similar looking or sounding names'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_poor_labelling'
      language: '@language_en_gb'
      value: 'Poor labelling and packaging from a commercial manufacturer'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_supplementary_prescribing'
      language: '@language_en_gb'
      value: 'Healthcare practitioner undertaking supplementary prescribing'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_guidelines_variance'
      language: '@language_en_gb'
      value: 'Variance to guidelines for sound clinical reasons'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_patient_group_direction'
      language: '@language_en_gb'
      value: 'Involving a medicine supplied under a Patient Group Direction (PGD) '
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_otc_medicine'
      language: '@language_en_gb'
      value: 'Involving an over-the-counter (OTC) medicine'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_failure_in_monitoring'
      language: '@language_en_gb'
      value: 'Failure in monitoring / assessing medicines therapy'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_failure_clinical_assessment_equipment'
      language: '@language_en_gb'
      value: 'Failure of clinical assessment equipment'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_unknown'
      language: '@language_en_gb'
      value: 'Unknown'
  -
    fields:
      placeholder: '@medications_v2_form_option_important_factors_not_applicable'
      language: '@language_en_gb'
      value: 'Not applicable'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_prescribing'
      language: '@language_en_gb'
      value: 'Prescribing'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_administration'
      language: '@language_en_gb'
      value: 'Administration / supply of a medicine from a clinical area'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_advice'
      language: '@language_en_gb'
      value: 'Advice'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_monitoring'
      language: '@language_en_gb'
      value: 'Monitoring / follow-up of medicine use'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_otc_supply'
      language: '@language_en_gb'
      value: 'Supply or use of over-the-counter (OTC) medicine'
  -
    fields:
      placeholder: '@medications_v2_form_option_stage_errors_medicing_preparation'
      language: '@language_en_gb'
      value: 'Preparation of medicines in all locations / dispensing in a pharmacy'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_adverse_drug_reaction'
      language: '@language_en_gb'
      value: 'Adverse drug reaction (when used as intended)'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_contra_indication'
      language: '@language_en_gb'
      value: 'Contra-indication to the use of the medicine in relation to drugs or conditions'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_patient_medicine_mismatch'
      language: '@language_en_gb'
      value: 'Mismatching between patient and medicine'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_omitted_medicine'
      language: '@language_en_gb'
      value: 'Omitted medicine / ingredient'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_patient_allergy'
      language: '@language_en_gb'
      value: 'Patient allergic to treatment'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_expiry_date'
      language: '@language_en_gb'
      value: 'Wrong / omitted / passed expiry date'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_information_leaflet'
      language: '@language_en_gb'
      value: 'Wrong / omitted patient information leaflet'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_patient_directions'
      language: '@language_en_gb'
      value: 'Wrong / omitted verbal patient directions'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_medicine_label'
      language: '@language_en_gb'
      value: 'Wrong / transposed / omitted medicine label'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_dose_strength'
      language: '@language_en_gb'
      value: 'Wrong / unclear dose or strength'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_medicine'
      language: '@language_en_gb'
      value: 'Wrong drug / medicine'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_formulation'
      language: '@language_en_gb'
      value: 'Wrong formulation'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_frequency'
      language: '@language_en_gb'
      value: 'Wrong frequency'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_preparation_method'
      language: '@language_en_gb'
      value: 'Wrong method of preparation / supply'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_quantity'
      language: '@language_en_gb'
      value: 'Wrong quantity'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_route'
      language: '@language_en_gb'
      value: 'Wrong route'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_storage'
      language: '@language_en_gb'
      value: 'Wrong storage'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_other'
      language: '@language_en_gb'
      value: 'Other'
  -
    fields:
      placeholder: '@medications_v2_form_option_error_type_unknown'
      language: '@language_en_gb'
      value: 'Unknown'
  -
    fields:
      placeholder: '@medications_v2_form_message_delete_medication_title'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@medications_v2_form_message_delete_medication_content'
      language: '@language_en_gb'
      value: 'Do you want to delete this medication from the incident?'
  -
    fields:
      placeholder: '@medications_v2_form_message_cancel_medication_content'
      language: '@language_en_gb'
      value: 'Do you want to discard your changes?'
  -
    fields:
      placeholder: '@medications_v2_form_message_cancel_medication_title'
      language: '@language_en_gb'
      value: 'Are you sure?'
  -
    fields:
      placeholder: '@medications_v2_form_button_delete_medication_cancel'
      language: '@language_en_gb'
      value: 'Cancel'
  -
    fields:
      placeholder: '@medications_v2_form_button_delete_medication_ok'
      language: '@language_en_gb'
      value: 'OK'
  -
    fields:
      placeholder: '@medications_v2_form_button_cancel_cancel'
      language: '@language_en_gb'
      value: 'Cancel'
  -
    fields:
      placeholder: '@medications_v2_form_button_cancel_ok'
      language: '@language_en_gb'
      value: 'OK'
  -
    fields:
      placeholder: '@medications_v2_form_message_medication_search_no_matches'
      language: '@language_en_gb'
      value: 'No matching medication found'
  -
    fields:
      placeholder: '@medications_v2_form_message_medication_search_exact_matches_only'
      language: '@language_en_gb'
      value: 'Exact matches only. Please enter two or more characters to broaden your search.'
  -
    fields:
      placeholder: '@medications_v2_form_radio_yes'
      language: '@language_en_gb'
      value: 'Yes'
  -
    fields:
      placeholder: '@medications_v2_form_radio_no'
      language: '@language_en_gb'
      value: 'No'
  -
    fields:
      placeholder: '@medications_v2_form_radio_unknown'
      language: '@language_en_gb'
      value: 'Unknown'
  -
    fields:
      placeholder: '@medications_v2_message_search_results_title'
      language: '@language_en_gb'
      value: 'Matching Medication'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_generic_name'
      language: '@language_en_gb'
      value: 'Generic name'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_type'
      language: '@language_en_gb'
      value: 'Type'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_class'
      language: '@language_en_gb'
      value: 'Class'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_brand'
      language: '@language_en_gb'
      value: 'Brand'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_manufacturer'
      language: '@language_en_gb'
      value: 'Manufacturer'
  -
    fields:
      placeholder: '@medications_v2_message_search_table_strength'
      language: '@language_en_gb'
      value: 'Strength'
  -
    fields:
      placeholder: '@medications_v2_button_search_table_choose'
      language: '@language_en_gb'
      value: 'Choose'
# Success
  -
    fields:
      placeholder: '@medications_success_medication_saved'
      language: '@language_en_gb'
      value: 'Medication saved successfully'
  -
    fields:
      placeholder: '@medications_success_medication_deleted'
      language: '@language_en_gb'
      value: 'Medication deleted successfully'
  -
    fields:
      placeholder: '@medications_success_medication_added'
      language: '@language_en_gb'
      value: 'has been added'
  -
    fields:
      placeholder: '@medications_success_medication_updated'
      language: '@language_en_gb'
      value: 'has been updated'
# Errors
  -
    fields:
      placeholder: '@medications_error_failed_to_get_all_medications'
      language: '@language_en_gb'
      value: 'Failed to get all medications'
  -
    fields:
      placeholder: '@medications_error_failed_to_get_medications_count'
      language: '@language_en_gb'
      value: 'Failed to get medications count'
  -
    fields:
      placeholder: '@medications_error_failed_to_get_medication'
      language: '@language_en_gb'
      value: 'Failed to get medication'
  -
    fields:
      placeholder: '@medications_error_failed_to_add_medications'
      language: '@language_en_gb'
      value: 'Failed to add medications'
  -
    fields:
      placeholder: '@medications_error_failed_to_update_medications'
      language: '@language_en_gb'
      value: 'Failed to update medications'
  -
    fields:
      placeholder: '@medications_error_failed_to_delete_medications'
      language: '@language_en_gb'
      value: 'Failed to delete medications'
  -
    fields:
      placeholder: '@medications_error_medication_not_found'
      language: '@language_en_gb'
      value: 'Medication not found'
  -
    fields:
      placeholder: '@medications_error_cannot_edit_global_medication'
      language: '@language_en_gb'
      value: 'Cannot edit global medication'

  - fields:
      placeholder: '@medications_risk_removal'
      language: '@language_en_gb'
      value: 'Medication removed from risk'
  - fields:
      placeholder: '@medications_removed_from_record'
      language: '@language_en_gb'
      value: 'Medication removed from {{record}}'
  - fields:
      placeholder: '@medications_risk_added'
      language: '@language_en_gb'
      value: 'Medication added to risk'
  - fields:
      placeholder: '@medications_added_to_record'
      language: '@language_en_gb'
      value: 'Medication added to {{record}}'
  - fields:
      placeholder: '@medications_new_medication'
      language: '@language_en_gb'
      value: 'New Medication'
  - fields:
      placeholder: '@medications_edit_medication'
      language: '@language_en_gb'
      value: 'Edit Medication'
  - fields:
      placeholder: '@medications_nav_new_medication'
      language: '@language_en_gb'
      value: 'New Medication'
  - fields:
      placeholder: '@medications_permissions'
      language: '@language_en_gb'
      value: 'Permissions'
  - fields:
      placeholder: '@medications_permissions_subscription_controller_title'
      language: '@language_en_gb'
      value: 'Subscription controller'
  - fields:
      placeholder: '@medications_permissions_local_controller_title'
      language: '@language_en_gb'
      value: 'Local controller'
  - fields:
      placeholder: '@medications_v2_form_message_medication_search_invalid_location'
      language: '@language_en_gb'
      value: 'No medication found. The user must be linked to only one location'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_administered_source_id'
      language: '@language_en_gb'
      value: 'Drug identification number (DIN) / Urgent Public Health Need ID of drug administered'
  -
    fields:
      placeholder: '@medications_v2_form_label_drug_intended_source_id'
      language: '@language_en_gb'
      value: 'Drug identification number (DIN) / Urgent Public Health Need ID of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_administered_source_id'
      language: '@language_en_gb'
      value: 'Drug identification number (DIN) / Urgent Public Health Need ID of drug administered'
  -
    fields:
      placeholder: '@medications_v2_display_label_drug_intended_source_id'
      language: '@language_en_gb'
      value: 'Drug identification number (DIN) / Urgent Public Health Need ID of drug intended / suspected'
  -
    fields:
      placeholder: '@medications_v2_form_label_date_first_used'
      language: '@language_en_gb'
      value: 'Date on which the patient first used the drug'
  -
    fields:
      placeholder: '@medications_v2_form_label_date_last_used'
      language: '@language_en_gb'
      value: 'Date on which the patient stopped the drug'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_date_first_used'
      language: '@language_en_gb'
      value: 'dd/mm/yyyy'
  -
    fields:
      placeholder: '@medications_v2_form_placeholder_date_last_used'
      language: '@language_en_gb'
      value: 'dd/mm/yyyy'
  -
    fields:
      placeholder: '@medications_v2_display_label_date_first_used'
      language: '@language_en_gb'
      value: 'Date on which the patient first used the drug'
  -
    fields:
      placeholder: '@medications_v2_display_label_date_last_used'
      language: '@language_en_gb'
      value: 'Date on which the patient stopped the drug'
  -
    fields:
      placeholder: '@medications_v2_display_label_reported_to_manufacturer'
      language: '@language_en_gb'
      value: 'Did you also report to the manufacturer?'
  -
    fields:
      placeholder: '@medications_v2_display_label_date_reported_to_manufacturer'
      language: '@language_en_gb'
      value: 'Date reported to the manufacturer'
  -
    fields:
      placeholder: '@medications_v2_display_label_manufacturer_ref'
      language: '@language_en_gb'
      value: "Manufacturer's Reference Number"
  -
    fields:
      placeholder: '@medications_v2_display_label_still_administered'
      language: '@language_en_gb'
      value: 'Is the product still being administered?'
  -
    fields:
      placeholder: '@medications_v2_form_label_reported_to_manufacturer'
      language: '@language_en_gb'
      value: 'Did you also report to the manufacturer?'
  -
    fields:
      placeholder: '@medications_v2_form_label_date_reported_to_manufacturer'
      language: '@language_en_gb'
      value: 'Date reported to the manufacturer'
  -
    fields:
      placeholder: '@medications_v2_form_label_manufacturer_ref'
      language: '@language_en_gb'
      value: "Manufacturer's Reference Number"
  -
    fields:
      placeholder: '@medications_v2_form_label_still_administered'
      language: '@language_en_gb'
      value: 'Is the product still being administered?'
  -
    fields:
      placeholder: '@medications_v2_form_option_still_administered_yes'
      language: '@language_en_gb'
      value: 'Yes, until course completion'
  -
    fields:
      placeholder: '@medications_v2_form_option_still_administered_no'
      language: '@language_en_gb'
      value: 'No, stopped'
  -
    fields:
      placeholder: '@medications_v2_form_label_strength'
      language: '@language_en_gb'
      value: Strength
  -
    fields:
      placeholder: '@medications_v2_display_label_date_strength'
      language: '@language_en_gb'
      value: Strength
  -
    fields:
      placeholder: '@medications_v2_form_label_frequency'
      language: '@language_en_gb'
      value: Frequency
  -
    fields:
      placeholder: '@medications_v2_display_label_date_frequency'
      language: '@language_en_gb'
      value: Frequency
  -
    fields:
      placeholder: '@medications_v2_form_label_product_expiry_date'
      language: '@language_en_gb'
      value: Product expiry date
  -
    fields:
      placeholder: '@medications_v2_display_label_product_expiry_date'
      language: '@language_en_gb'
      value: Product expiry date
  -
    fields:
      placeholder: '@medications_v2_form_label_action_taken'
      language: '@language_en_gb'
      value: Action taken
  -
    fields:
      placeholder: '@medications_v2_display_label_action_taken'
      language: '@language_en_gb'
      value: Action taken
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_lessened'
      language: '@language_en_gb'
      value: ADR lessened or stopped if product stopped or dose reduced?
  -
    fields:
      placeholder: '@medications_v2_display_label_adr_lessened'
      language: '@language_en_gb'
      value: ADR lessened or stopped if product stopped or dose reduced?
  -
    fields:
      placeholder: '@medications_v2_form_label_adr_reappeared'
      language: '@language_en_gb'
      value: ADR reappeared after product reintroduced?
  -
    fields:
      placeholder: '@medications_v2_display_label_adr_reappeared'
      language: '@language_en_gb'
      value: ADR reappeared after product reintroduced?
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_withdrawn'
      language: '@language_en_gb'
      value: Product withdrawn, stopped, discontinued or held
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_dose_reduced'
      language: '@language_en_gb'
      value: Dose reduced
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_dose_increased'
      language: '@language_en_gb'
      value: Dose increased
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_dose_not_changed'
      language: '@language_en_gb'
      value: Dose not changed
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_unknown'
      language: '@language_en_gb'
      value: Unknown
  -
    fields:
      placeholder: '@medications_v2_form_option_action_taken_na'
      language: '@language_en_gb'
      value: Not applicable
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_lessened_yes'
      language: '@language_en_gb'
      value: Yes
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_lessened_no'
      language: '@language_en_gb'
      value: No
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_lessened_unknown'
      language: '@language_en_gb'
      value: Unknown
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_lessened_na'
      language: '@language_en_gb'
      value: Not applicable
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_reappeared_yes'
      language: '@language_en_gb'
      value: Yes
  -
    fields:
      placeholder: '@medications_v2_form_option_adr_reappeared_no'
      language: '@language_en_gb'
      value: No
  - fields:
      placeholder: '@medications_v2_form_option_adr_reappeared_unknown'
      language: '@language_en_gb'
      value: Unknown
  - fields:
      placeholder: '@medications_v2_form_option_adr_reappeared_na'
      language: '@language_en_gb'
      value: Not applicable
  -
    fields:
      placeholder: '@medications_v2_form_message_invalid_search_length'
      language: '@language_en_gb'
      value: Please enter a minimum of two characters to proceed with your search
  - fields:
      placeholder: '@medications_v2_form_option_route_dental'
      language: '@language_en_gb'
      value: Dental
  - fields:
      placeholder: '@medications_v2_form_option_route_dialysis'
      language: '@language_en_gb'
      value: Dialysis
  - fields:
      placeholder: '@medications_v2_form_option_route_instillation'
      language: '@language_en_gb'
      value: Instillation
  - fields:
      placeholder: '@medications_v2_form_option_route_intra_arterial'
      language: '@language_en_gb'
      value: Intra-arterial
  - fields:
      placeholder: '@medications_v2_form_option_route_intra_articular'
      language: '@language_en_gb'
      value: Intra-articular
  - fields:
      placeholder: '@medications_v2_form_option_route_intrabursal'
      language: '@language_en_gb'
      value: Intrabursal
  - fields:
      placeholder: '@medications_v2_form_option_route_intracardiac'
      language: '@language_en_gb'
      value: Intracardiac
  - fields:
      placeholder: '@medications_v2_form_option_route_intradermal'
      language: '@language_en_gb'
      value: Intradermal
  - fields:
      placeholder: '@medications_v2_form_option_route_intragastric'
      language: '@language_en_gb'
      value: Intragastric (nasogastric, orogastric, gastrostomy or jejunostomy)
  - fields:
      placeholder: '@medications_v2_form_option_route_intralesional'
      language: '@language_en_gb'
      value: Intralesional
  - fields:
      placeholder: '@medications_v2_form_option_route_intraocular'
      language: '@language_en_gb'
      value: Intraocular
  - fields:
      placeholder: '@medications_v2_form_option_route_intraosseous'
      language: '@language_en_gb'
      value: Intraosseous
  - fields:
      placeholder: '@medications_v2_form_option_route_intraperitoneal'
      language: '@language_en_gb'
      value: Intraperitoneal
  - fields:
      placeholder: '@medications_v2_form_option_route_intrapleural'
      language: '@language_en_gb'
      value: Intrapleural
  - fields:
      placeholder: '@medications_v2_form_option_route_intraspinal'
      language: '@language_en_gb'
      value: Intraspinal
  - fields:
      placeholder: '@medications_v2_form_option_route_intrasynovial'
      language: '@language_en_gb'
      value: Intrasynovial
  - fields:
      placeholder: '@medications_v2_form_option_route_intrauterine'
      language: '@language_en_gb'
      value: Intrauterine
  - fields:
      placeholder: '@medications_v2_form_option_route_intravascular'
      language: '@language_en_gb'
      value: Intravascular
  - fields:
      placeholder: '@medications_v2_form_option_route_intravitreal'
      language: '@language_en_gb'
      value: Intravitreal
  - fields:
      placeholder: '@medications_v2_form_option_route_irrigation'
      language: '@language_en_gb'
      value: Irrigation
  - fields:
      placeholder: '@medications_v2_form_option_route_intravenous_bolus'
      language: '@language_en_gb'
      value: Intravenous (IV) - bolus
  - fields:
      placeholder: '@medications_v2_form_option_route_intravenous_continuous'
      language: '@language_en_gb'
      value: Intravenous (IV) - continuous
  - fields:
      placeholder: '@medications_v2_form_option_route_intravenous_intermittent'
      language: '@language_en_gb'
      value: Intravenous (IV) - intermittent
  - fields:
      placeholder: '@medications_v2_form_option_route_intravenous_unspecified'
      language: '@language_en_gb'
      value: Intravenous (IV) - unspecified
  - fields:
      placeholder: '@medications_v2_form_option_route_ophthalmic'
      language: '@language_en_gb'
      value: Ophthalmic
  - fields:
      placeholder: '@medications_v2_form_option_route_otic'
      language: '@language_en_gb'
      value: Otic
  - fields:
      placeholder: '@medications_v2_form_option_route_parenteral_unspecified'
      language: '@language_en_gb'
      value: Parenteral - unspecified
  - fields:
      placeholder: '@medications_v2_form_option_route_retrobulbar'
      language: '@language_en_gb'
      value: Retrobulbar
  - fields:
      placeholder: '@medications_v2_form_option_route_subarachnoidal'
      language: '@language_en_gb'
      value: Subarachnoidal
  - fields:
      placeholder: '@medications_v2_form_option_route_transdermal'
      language: '@language_en_gb'
      value: Transdermal
  - fields:
      placeholder: '@medications_v2_form_option_route_urethral'
      language: '@language_en_gb'
      value: Urethral
  - fields:
      placeholder: '@medications_v2_form_option_route_vaginal'
      language: '@language_en_gb'
      value: Vaginal
  - fields:
      placeholder: '@medications_v2_form_option_code_aerosol'
      language: '@language_en_gb'
      value: Aerosol
  - fields:
      placeholder: '@medications_v2_form_option_code_aerosol_metered'
      language: '@language_en_gb'
      value: Aerosol - metered dose
  - fields:
      placeholder: '@medications_v2_form_option_code_capsule'
      language: '@language_en_gb'
      value: Capsule
  - fields:
      placeholder: '@medications_v2_form_option_code_capsule_controlled'
      language: '@language_en_gb'
      value: Capsule - controlled, delayed, extended or slow release
  - fields:
      placeholder: '@medications_v2_form_option_code_cartridge'
      language: '@language_en_gb'
      value: Cartridge
  - fields:
      placeholder: '@medications_v2_form_option_code_chewable_piece'
      language: '@language_en_gb'
      value: Chewable piece
  - fields:
      placeholder: '@medications_v2_form_option_code_douche'
      language: '@language_en_gb'
      value: Douche
  - fields:
      placeholder: '@medications_v2_form_option_code_dressing'
      language: '@language_en_gb'
      value: Dressing
  - fields:
      placeholder: '@medications_v2_form_option_code_elixir'
      language: '@language_en_gb'
      value: Elixir
  - fields:
      placeholder: '@medications_v2_form_option_code_emulsion'
      language: '@language_en_gb'
      value: Emulsion
  - fields:
      placeholder: '@medications_v2_form_option_code_enema'
      language: '@language_en_gb'
      value: Enema
  - fields:
      placeholder: '@medications_v2_form_option_code_granule'
      language: '@language_en_gb'
      value: Granule
  - fields:
      placeholder: '@medications_v2_form_option_code_implant'
      language: '@language_en_gb'
      value: Implant
  - fields:
      placeholder: '@medications_v2_form_option_code_insert'
      language: '@language_en_gb'
      value: Insert
  - fields:
      placeholder: '@medications_v2_form_option_code_kit'
      language: '@language_en_gb'
      value: Kit
  - fields:
      placeholder: '@medications_v2_form_option_code_lozenge'
      language: '@language_en_gb'
      value: Lozenge
  - fields:
      placeholder: '@medications_v2_form_option_code_mouthwash_or_gargle'
      language: '@language_en_gb'
      value: Mouthwash or gargle
  - fields:
      placeholder: '@medications_v2_form_option_code_ovules'
      language: '@language_en_gb'
      value: Ovules
  - fields:
      placeholder: '@medications_v2_form_option_code_paste'
      language: '@language_en_gb'
      value: Paste
  - fields:
      placeholder: '@medications_v2_form_option_code_patch_extended'
      language: '@language_en_gb'
      value: Patch - extended release
  - fields:
      placeholder: '@medications_v2_form_option_code_powder'
      language: '@language_en_gb'
      value: Powder
  - fields:
      placeholder: '@medications_v2_form_option_code_powder_extended'
      language: '@language_en_gb'
      value: Powder - extended release
  - fields:
      placeholder: '@medications_v2_form_option_code_shampoo'
      language: '@language_en_gb'
      value: Shampoo
  - fields:
      placeholder: '@medications_v2_form_option_code_soap'
      language: '@language_en_gb'
      value: Soap
  - fields:
      placeholder: '@medications_v2_form_option_code_solution_injectable'
      language: '@language_en_gb'
      value: Solution - injectable
  - fields:
      placeholder: '@medications_v2_form_option_code_solution_injectable_extended'
      language: '@language_en_gb'
      value: Solution - injectable - extended release
  - fields:
      placeholder: '@medications_v2_form_option_code_spray'
      language: '@language_en_gb'
      value: Spray
  - fields:
      placeholder: '@medications_v2_form_option_code_strip'
      language: '@language_en_gb'
      value: Strip
  - fields:
      placeholder: '@medications_v2_form_option_code_suspension'
      language: '@language_en_gb'
      value: Suspension
  - fields:
      placeholder: '@medications_v2_form_option_code_suspension_extended'
      language: '@language_en_gb'
      value: Suspension - extended release
  - fields:
      placeholder: '@medications_v2_form_option_code_swab'
      language: '@language_en_gb'
      value: Swab
  - fields:
      placeholder: '@medications_v2_form_option_code_syrup'
      language: '@language_en_gb'
      value: Syrup
  - fields:
      placeholder: '@medications_v2_form_option_code_syrup_extended'
      language: '@language_en_gb'
      value: Syrup - extended release
  - fields:
      placeholder: '@medications_v2_form_option_code_tablet_controlled'
      language: '@language_en_gb'
      value: Tablet - controlled, delayed, extended or slow release
  - fields:
      placeholder: '@medications_v2_form_option_code_tablet_orally'
      language: '@language_en_gb'
      value: Tablet - orally disintegrating
  - fields:
      placeholder: '@medications_v2_form_option_code_other'
      language: '@language_en_gb'
      value: Other
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_preparing_selecting_dispensing'
      language: '@language_en_gb'
      value: Preparing, selecting or dispensing (e.g. in pharmacy or medication room)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_administration_plain'
      language: '@language_en_gb'
      value: Administration
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_monitoring_post_admin'
      language: '@language_en_gb'
      value: Monitoring (post-administration)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_advising_counselling'
      language: '@language_en_gb'
      value: Advising or counselling
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_supplying'
      language: '@language_en_gb'
      value: Supplying (e.g. from manufacturer)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_storage_location'
      language: '@language_en_gb'
      value: Storage or location
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_order_documentation'
      language: '@language_en_gb'
      value: Order documentation (e.g. transcription to MAR)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_presentation_packaging'
      language: '@language_en_gb'
      value: Presentation or packaging (includes labeling)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_delivery'
      language: '@language_en_gb'
      value: Delivery (e.g. to ward from pharmacy)
  - fields:
      placeholder: '@medications_v2_form_option_stage_errors_order_verification'
      language: '@language_en_gb'
      value: Order verification - pre-administration) (e.g. verification bar code scanning)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_adverse_drug_reaction_plain'
      language: '@language_en_gb'
      value: Adverse drug reaction
  - fields:
      placeholder: '@medications_v2_form_option_error_type_contraindicated'
      language: '@language_en_gb'
      value: Contraindicated
  - fields:
      placeholder: '@medications_v2_form_option_error_type_contraindicated_failed'
      language: '@language_en_gb'
      value: Contraindicated - failed to ask if patient has allergy
  - fields:
      placeholder: '@medications_v2_form_option_error_type_contraindicated_known'
      language: '@language_en_gb'
      value: Contraindicated - known allergy
  - fields:
      placeholder: '@medications_v2_form_option_error_type_extra_dose'
      language: '@language_en_gb'
      value: Extra dose (additional, unscheduled dose)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_patient'
      language: '@language_en_gb'
      value: Wrong patient
  - fields:
      placeholder: '@medications_v2_form_option_error_type_no_order'
      language: '@language_en_gb'
      value: No order (given without an order)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_omitted_dose'
      language: '@language_en_gb'
      value: Omitted dose (missed dose)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_expired_or_deteriorated'
      language: '@language_en_gb'
      value: Expired or deteriorated product
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_product'
      language: '@language_en_gb'
      value: Wrong product
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_form'
      language: '@language_en_gb'
      value: Wrong form or formulation (e.g. tablet instead of liquid, regular instead of controlled release)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_quantity'
      language: '@language_en_gb'
      value: Wrong quantity (dose, amount, strength or concentration)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_rate'
      language: '@language_en_gb'
      value: Wrong rate or frequency
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_route'
      language: '@language_en_gb'
      value: Wrong route or technique (includes use of wrong IV line or SC butterfly)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_sequence'
      language: '@language_en_gb'
      value: Wrong sequence (given in wrong order)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_storage'
      language: '@language_en_gb'
      value: Wrong storage or location
  - fields:
      placeholder: '@medications_v2_form_option_error_type_wrong_time'
      language: '@language_en_gb'
      value: Wrong time (given early or late)
  - fields:
      placeholder: '@medications_v2_form_option_error_type_contributed_to_fall'
      language: '@language_en_gb'
      value: Contributed to a fall or injury
  - fields:
      placeholder: '@medications_v2_display_error_medication_required'
      language: '@language_en_gb'
      value: Search/Match Medication Required
