entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@medications_added_to_record', language: '@language_en_us', value: "Medication added to {{record}}" } }
    - { fields: { placeholder: '@medications_brands_actos', language: '@language_en_us', value: Actos } }
    - { fields: { placeholder: '@medications_brands_ibuprin', language: '@language_en_us', value: Ibuprin } }
    - { fields: { placeholder: '@medications_brands_lipitor', language: '@language_en_us', value: Lipitor } }
    - { fields: { placeholder: '@medications_brands_nexium', language: '@language_en_us', value: Nexium } }
    - { fields: { placeholder: '@medications_create', language: '@language_en_us', value: Create Medication } }
    - { fields: { placeholder: '@medications_datasource_brands', language: '@language_en_us', value: Medication Brands } }
    - { fields: { placeholder: '@medications_datasource_classifications', language: '@language_en_us', value: Medication Classifications } }
    - { fields: { placeholder: '@medications_datasource_manufacturers', language: '@language_en_us', value: Medication Manufacturers } }
    - { fields: { placeholder: '@medications_datasource_names', language: '@language_en_us', value: Medication Names } }
    - { fields: { placeholder: '@medications_datasource_types', language: '@language_en_us', value: Medication Types } }
    - { fields: { placeholder: '@medications_edit', language: '@language_en_us', value: Edit Medication } }
    - { fields: { placeholder: '@medications_edit_medication', language: '@language_en_us', value: Edit Medication } }
    - { fields: { placeholder: '@medications_error_cannot_edit_global_medication', language: '@language_en_us', value: Cannot edit global medication } }
    - { fields: { placeholder: '@medications_error_failed_to_add_medications', language: '@language_en_us', value: Failed to add medications } }
    - { fields: { placeholder: '@medications_error_failed_to_delete_medications', language: '@language_en_us', value: Failed to delete medications } }
    - { fields: { placeholder: '@medications_error_failed_to_get_all_medications', language: '@language_en_us', value: Failed to get all medications } }
    - { fields: { placeholder: '@medications_error_failed_to_get_medication', language: '@language_en_us', value: Failed to get medication } }
    - { fields: { placeholder: '@medications_error_failed_to_get_medications_count', language: '@language_en_us', value: Failed to get medications count } }
    - { fields: { placeholder: '@medications_error_failed_to_update_medications', language: '@language_en_us', value: Failed to update medications } }
    - { fields: { placeholder: '@medications_error_medication_not_found', language: '@language_en_us', value: Medication not found } }
    - { fields: { placeholder: '@medications_form_type_medication', language: '@language_en_us', value: Medication } }
    - { fields: { placeholder: '@medications_form_brand', language: '@language_en_us', value: Brand } }
    - { fields: { placeholder: '@medications_form_brand_label', language: '@language_en_us', value: Brand Name } }
    - { fields: { placeholder: '@medications_form_class', language: '@language_en_us', value: Class } }
    - { fields: { placeholder: '@medications_form_class_label', language: '@language_en_us', value: Class } }
    - { fields: { placeholder: '@medications_form_classification_value_1', language: '@language_en_us', value: Narcotics } }
    - { fields: { placeholder: '@medications_form_classification_value_2', language: '@language_en_us', value: Depressants } }
    - { fields: { placeholder: '@medications_form_classification_value_3', language: '@language_en_us', value: Stimulants } }
    - { fields: { placeholder: '@medications_form_classification_value_4', language: '@language_en_us', value: Hallucinogens } }
    - { fields: { placeholder: '@medications_form_classification_value_5', language: '@language_en_us', value: Anabolic steroids } }
    - { fields: { placeholder: '@medications_form_controlled', language: '@language_en_us', value: 'Controlled drug?' } }
    - { fields: { placeholder: '@medications_form_controlled_label', language: '@language_en_us', value: 'Controlled drug?' } }
    - { fields: { placeholder: '@medications_form_generic_name', language: '@language_en_us', value: Generic Name } }
    - { fields: { placeholder: '@medications_form_generic_name_label', language: '@language_en_us', value: Generic Name } }
    - { fields: { placeholder: '@medications_form_manufacturer', language: '@language_en_us', value: Manufacturer } }
    - { fields: { placeholder: '@medications_form_manufacturer_label', language: '@language_en_us', value: Manufacturer } }
    - { fields: { placeholder: '@medications_form_ref', language: '@language_en_us', value: Ref. } }
    - { fields: { placeholder: '@medications_form_ref_label', language: '@language_en_us', value: Ref. } }
    - { fields: { placeholder: '@medications_form_select_brand', language: '@language_en_us', value: Select a Brand } }
    - { fields: { placeholder: '@medications_form_select_class', language: '@language_en_us', value: Select Classification } }
    - { fields: { placeholder: '@medications_form_select_generic_name', language: '@language_en_us', value: Select a Generic Name } }
    - { fields: { placeholder: '@medications_form_select_manufacturer', language: '@language_en_us', value: Select Manufacturer } }
    - { fields: { placeholder: '@medications_form_select_subclass', language: '@language_en_us', value: Select Sub-Classification } }
    - { fields: { placeholder: '@medications_form_select_type', language: '@language_en_us', value: Select Type } }
    - { fields: { placeholder: '@medications_form_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@medications_form_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@medications_form_strength', language: '@language_en_us', value: Strength } }
    - { fields: { placeholder: '@medications_form_strength_label', language: '@language_en_us', value: Strength } }
    - { fields: { placeholder: '@medications_form_subclass', language: '@language_en_us', value: SubClass } }
    - { fields: { placeholder: '@medications_form_subclass_label', language: '@language_en_us', value: SubClass } }
    - { fields: { placeholder: '@medications_form_supplier', language: '@language_en_us', value: Supplier } }
    - { fields: { placeholder: '@medications_form_supplier_label', language: '@language_en_us', value: Supplier } }
    - { fields: { placeholder: '@medications_form_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@medications_form_type_label', language: '@language_en_us', value: Type of Drug } }
    - { fields: { placeholder: '@medications_manufacturers_carlton_cares_company', language: '@language_en_us', value: Carlton Cares Company } }
    - { fields: { placeholder: '@medications_manufacturers_living_longer_ltd', language: '@language_en_us', value: Living Longer Ltd } }
    - { fields: { placeholder: '@medications_names_atorvastatin', language: '@language_en_us', value: Atorvastatin } }
    - { fields: { placeholder: '@medications_names_esomeprazole', language: '@language_en_us', value: Esomeprazole } }
    - { fields: { placeholder: '@medications_names_ibuprofen', language: '@language_en_us', value: Ibuprofen } }
    - { fields: { placeholder: '@medications_names_pioglitazone', language: '@language_en_us', value: Pioglitazone } }
    - { fields: { placeholder: '@medications_nav_new_medication', language: '@language_en_us', value: New Medication } }
    - { fields: { placeholder: '@medications_new_medication', language: '@language_en_us', value: New Medication } }
    - { fields: { placeholder: '@medications_permissions', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@medications_plural', language: '@language_en_us', value: Medications } }
    - { fields: { placeholder: '@medications_relation_administered', language: '@language_en_us', value: Administered } }
    - { fields: { placeholder: '@medications_relation_correct', language: '@language_en_us', value: Correct } }
    - { fields: { placeholder: '@medications_relation_from', language: '@language_en_us', value: From } }
    - { fields: { placeholder: '@medications_relation_id', language: '@language_en_us', value: ID } }
    - { fields: { placeholder: '@medications_relation_medication', language: '@language_en_us', value: Medication } }
    - { fields: { placeholder: '@medications_relation_route', language: '@language_en_us', value: Route } }
    - { fields: { placeholder: '@medications_removed_from_record', language: '@language_en_us', value: "Medication removed from {{record}}" } }
    - { fields: { placeholder: '@medications_risk_added', language: '@language_en_us', value: Medication added to risk } }
    - { fields: { placeholder: '@medications_risk_removal', language: '@language_en_us', value: Medication removed from risk } }
    - { fields: { placeholder: '@medications_search', language: '@language_en_us', value: Search Medications } }
    - { fields: { placeholder: '@medications_singular', language: '@language_en_us', value: Medication } }
    - { fields: { placeholder: '@medications_success_medication_added', language: '@language_en_us', value: has been added } }
    - { fields: { placeholder: '@medications_success_medication_deleted', language: '@language_en_us', value: Medication deleted successfully } }
    - { fields: { placeholder: '@medications_success_medication_saved', language: '@language_en_us', value: Medication saved successfully } }
    - { fields: { placeholder: '@medications_success_medication_updated', language: '@language_en_us', value: has been updated } }
    - { fields: { placeholder: '@medications_types_anaesthetic', language: '@language_en_us', value: Anesthetic } }
    - { fields: { placeholder: '@medications_types_analgesic', language: '@language_en_us', value: Analgesic } }
    - { fields: { placeholder: '@medications_types_antibiotic', language: '@language_en_us', value: Antibiotic } }
    - { fields: { placeholder: '@medications_types_antidepressant', language: '@language_en_us', value: Antidepressant } }
    - { fields: { placeholder: '@medications_types_emollient', language: '@language_en_us', value: Emollient } }
    - { fields: { placeholder: '@medications_types_eugeroic', language: '@language_en_us', value: Eugeroic } }
    - { fields: { placeholder: '@medications_types_psychedelic', language: '@language_en_us', value: Psychedelic } }
    - { fields: { placeholder: '@medications_types_vaccine', language: '@language_en_us', value: Vaccine } }
    - { fields: { placeholder: '@medications_v2_button_search_table_choose', language: '@language_en_us', value: Choose } }
    - { fields: { placeholder: '@medications_v2_display_button_delete_medication', language: '@language_en_us', value: Delete } }
    - { fields: { placeholder: '@medications_v2_display_button_edit_medication', language: '@language_en_us', value: Edit Medication } }
    - { fields: { placeholder: '@medications_v2_display_heading_medications', language: '@language_en_us', value: Medications } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered', language: '@language_en_us', value: Drug administered or omitted } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_brand', language: '@language_en_us', value: Brand name of drug administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_class', language: '@language_en_us', value: Class of drug administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_dosage', language: '@language_en_us', value: Dose administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_manufacturer', language: '@language_en_us', value: Manufacturer of drug administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_route', language: '@language_en_us', value: "Route administered, prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_strength', language: '@language_en_us', value: Strength of drug administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_supplier', language: '@language_en_us', value: Supplier of drug administered } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_type', language: '@language_en_us', value: "Type of drug administered,prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended', language: '@language_en_us', value: Intended / Suspected drug } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_brand', language: '@language_en_us', value: Brand name of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_class', language: '@language_en_us', value: Class of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_dosage', language: '@language_en_us', value: Dose intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_administered_intended', language: '@language_en_us', value: Manufacturer of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_route', language: '@language_en_us', value: Route intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_strength', language: '@language_en_us', value: Strength of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_supplier', language: '@language_en_us', value: Supplier of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_drug_intended_type', language: '@language_en_us', value: Type of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_error_type', language: '@language_en_us', value: Type of error } }
    - { fields: { placeholder: '@medications_v2_display_label_form_administered', language: '@language_en_us', value: "Form administered, prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_display_label_form_intended', language: '@language_en_us', value: Form intended / suspected } }
    - { fields: { placeholder: '@medications_v2_display_label_medication_error', language: '@language_en_us', value: Medication error } }
    - { fields: { placeholder: '@medications_v2_display_label_notes', language: '@language_en_us', value: Notes } }
    - { fields: { placeholder: '@medications_v2_display_other_important_factors', language: '@language_en_us', value: Other Important Factors } }
    - { fields: { placeholder: '@medications_v2_display_label_stage_error_occured', language: '@language_en_us', value: Stage at which error occurred } }
    - { fields: { placeholder: '@medications_v2_display_label_table_key', language: '@language_en_us', value: 'Table Key:' } }
    - { fields: { placeholder: '@medications_v2_display_subheading_drug_administered', language: '@language_en_us', value: Administered } }
    - { fields: { placeholder: '@medications_v2_display_subheading_drug_intended', language: '@language_en_us', value: Intended / Suspected } }
    - { fields: { placeholder: '@medications_v2_form_button_cancel_cancel', language: '@language_en_us', value: Cancel } }
    - { fields: { placeholder: '@medications_v2_form_button_cancel_ok', language: '@language_en_us', value: OK } }
    - { fields: { placeholder: '@medications_v2_form_button_clear', language: '@language_en_us', value: Clear } }
    - { fields: { placeholder: '@medications_v2_form_button_delete_medication_cancel', language: '@language_en_us', value: Cancel } }
    - { fields: { placeholder: '@medications_v2_form_button_delete_medication_ok', language: '@language_en_us', value: OK } }
    - { fields: { placeholder: '@medications_v2_form_button_duplicate', language: '@language_en_us', value: Duplicate } }
    - { fields: { placeholder: '@medications_v2_form_heading_medications', language: '@language_en_us', value: Medications } }
    - { fields: { placeholder: '@medications_v2_form_label_adr_action_taken', language: '@language_en_us', value: ADR-Action Taken } }
    - { fields: { placeholder: '@medications_v2_form_label_adr_date_reaction_started', language: '@language_en_us', value: ADR Date Reaction Started } }
    - { fields: { placeholder: '@medications_v2_form_label_adr_date_reaction_stopped', language: '@language_en_us', value: ADR Date Reaction Stopped } }
    - { fields: { placeholder: '@medications_v2_form_label_adr_organ_system', language: '@language_en_us', value: ADR Organ systems fields } }
    - { fields: { placeholder: '@medications_v2_form_label_did_drug_reaction_reappear', language: '@language_en_us', value: 'Did adverse reaction reappear when the drug was re-administered?' } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_class', language: '@language_en_us', value: Class of drug administered } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_dosage', language: '@language_en_us', value: "Dose administered, prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_manufacturer', language: '@language_en_us', value: Manufacturer of drug administered } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_route', language: '@language_en_us', value: "Route administered, prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_strength', language: '@language_en_us', value: Strength of drug administered } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_supplier', language: '@language_en_us', value: Supplier of drug administered } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_administered_type', language: '@language_en_us', value: "Type of drug administered,prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_class', language: '@language_en_us', value: Class of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_dosage', language: '@language_en_us', value: Dose intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_manufacturer', language: '@language_en_us', value: Manufacturer of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_route', language: '@language_en_us', value: Route intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_strength', language: '@language_en_us', value: Strength of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_supplier', language: '@language_en_us', value: Supplier of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_intended_type', language: '@language_en_us', value: Type of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_drug_reaction', language: '@language_en_us', value: Drug Reaction Manifestation } }
    - { fields: { placeholder: '@medications_v2_form_label_error_committed_by', language: '@language_en_us', value: Error Committed By } }
    - { fields: { placeholder: '@medications_v2_form_label_error_detected_by', language: '@language_en_us', value: Error Detected By } }
    - { fields: { placeholder: '@medications_v2_form_label_error_type', language: '@language_en_us', value: Type of error } }
    - { fields: { placeholder: '@medications_v2_form_label_form_administered', language: '@language_en_us', value: "Form administered, prescribed, dispensed or omitted" } }
    - { fields: { placeholder: '@medications_v2_form_label_form_intended', language: '@language_en_us', value: Form intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_frequency_wrong', language: '@language_en_us', value: Frequency of wrong medication } }
    - { fields: { placeholder: '@medications_v2_form_label_naranjo_total_score', language: '@language_en_us', value: Naranjo Total Score } }
    - { fields: { placeholder: '@medications_v2_form_label_notes', language: '@language_en_us', value: Notes } }
    - { fields: { placeholder: '@medications_v2_form_label_other_important_factors', language: '@language_en_us', value: Other Important Factors } }
    - { fields: { placeholder: '@medications_v2_form_label_search_drug_administered', language: '@language_en_us', value: Search for drug administered or omitted } }
    - { fields: { placeholder: '@medications_v2_form_label_search_drug_administered_brand', language: '@language_en_us', value: Brand name of drug administered } }
    - { fields: { placeholder: '@medications_v2_form_label_search_drug_intended', language: '@language_en_us', value: Search for intended / suspected drug } }
    - { fields: { placeholder: '@medications_v2_form_label_search_drug_intended_brand', language: '@language_en_us', value: Brand name of drug intended / suspected } }
    - { fields: { placeholder: '@medications_v2_form_label_stage_error_occured', language: '@language_en_us', value: Stage at which error occurred } }
    - { fields: { placeholder: '@medications_v2_form_message_cancel_medication_content', language: '@language_en_us', value: 'Do you want to discard your changes?' } }
    - { fields: { placeholder: '@medications_v2_form_message_cancel_medication_title', language: '@language_en_us', value: 'Are you sure?' } }
    - { fields: { placeholder: '@medications_v2_form_message_delete_medication_content', language: '@language_en_us', value: 'Do you want to delete this medication from the incident?' } }
    - { fields: { placeholder: '@medications_v2_form_message_delete_medication_title', language: '@language_en_us', value: 'Are you sure?' } }
    - { fields: { placeholder: '@medications_v2_form_message_drug_administered_dosage_unit', language: '@language_en_us', value: Enter a value and select a unit of measurement } }
    - { fields: { placeholder: '@medications_v2_form_message_drug_intended_dosage_unit', language: '@language_en_us', value: Enter a value and select a unit of measurement } }
    - { fields: { placeholder: '@medications_v2_form_message_medication_search_exact_matches_only', language: '@language_en_us', value: Exact matches only. Please enter two or more characters to broaden your search. } }
    - { fields: { placeholder: '@medications_v2_form_message_medication_search_no_matches', language: '@language_en_us', value: No matching medication found } }
    - { fields: { placeholder: '@medications_v2_form_messages_search_administered_no_matches_found', language: '@language_en_us', value: No matches found } }
    - { fields: { placeholder: '@medications_v2_form_messages_search_drug_intended_no_matches_found', language: '@language_en_us', value: No matches found } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_anaesthetic', language: '@language_en_us', value: Anesthetic } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_capsule', language: '@language_en_us', value: Capsule } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_cream', language: '@language_en_us', value: Cream } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_dermal_patch', language: '@language_en_us', value: Dermal Patch } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_ear_drop', language: '@language_en_us', value: Ear Drop } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_eye_drop', language: '@language_en_us', value: Eye Drop } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_eye_ointment', language: '@language_en_us', value: Eye Ointment } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_gas', language: '@language_en_us', value: Gas } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_gel', language: '@language_en_us', value: Gel } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_inhaler', language: '@language_en_us', value: Inhaler } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_intramuscular_injection', language: '@language_en_us', value: Intramuscular Injection } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_intravenous_infusion', language: '@language_en_us', value: Intravenous infusion } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_liquid_sachet', language: '@language_en_us', value: Liquid sachet } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_lotion', language: '@language_en_us', value: Lotion } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_nasal_drop', language: '@language_en_us', value: Nasal Drop } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_nasal_ointment', language: '@language_en_us', value: Nasal ointment } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_ointment', language: '@language_en_us', value: Ointment } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_pessary', language: '@language_en_us', value: Pessary } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_powder_sachet', language: '@language_en_us', value: Powder sachet } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_subcutaneous_injection', language: '@language_en_us', value: Subcutaneous Injection } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_suppository', language: '@language_en_us', value: Suppository } }
    - { fields: { placeholder: '@medications_v2_form_option_administered_tablet', language: '@language_en_us', value: Tablet } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_gram', language: '@language_en_us', value: Gram } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_kilogram', language: '@language_en_us', value: Kilogram } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_litre', language: '@language_en_us', value: Liter } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_microgram', language: '@language_en_us', value: Microgram } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_milligram', language: '@language_en_us', value: Milligram } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_millilitre', language: '@language_en_us', value: Milliliter } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_nanogram', language: '@language_en_us', value: Nanogram } }
    - { fields: { placeholder: '@medications_v2_form_option_dosage_unit_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_adverse_drug_reaction', language: '@language_en_us', value: Adverse drug reaction (when used as intended) } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_contra_indication', language: '@language_en_us', value: Contra-indication to the use of the medicine in relation to drugs or conditions } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_dose_strength', language: '@language_en_us', value: Wrong / unclear dose or strength } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_expiry_date', language: '@language_en_us', value: Wrong / omitted / passed expiry date } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_formulation', language: '@language_en_us', value: Wrong formulation } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_frequency', language: '@language_en_us', value: Wrong frequency } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_information_leaflet', language: '@language_en_us', value: Wrong / omitted patient information leaflet } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_medicine', language: '@language_en_us', value: Wrong drug / medicine } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_medicine_label', language: '@language_en_us', value: Wrong / transposed / omitted medicine label } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_omitted_medicine', language: '@language_en_us', value: Omitted medicine / ingredient } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_allergy', language: '@language_en_us', value: Patient allergic to treatment } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_directions', language: '@language_en_us', value: Wrong / omitted verbal patient directions } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_patient_medicine_mismatch', language: '@language_en_us', value: Mismatching between patient and medicine } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_preparation_method', language: '@language_en_us', value: Wrong method of preparation / supply } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_quantity', language: '@language_en_us', value: Wrong quantity } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_route', language: '@language_en_us', value: Wrong route } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_storage', language: '@language_en_us', value: Wrong storage } }
    - { fields: { placeholder: '@medications_v2_form_option_error_type_unknown', language: '@language_en_us', value: Unknown } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_abbreviation_usage', language: '@language_en_us', value: "Use of abbreviation(s) of drug name / strength / dose / directions (e.g. MTX, .1 mg, 1 po)" } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_clinical_assessment_equipment', language: '@language_en_us', value: Failure of clinical assessment equipment } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_in_monitoring', language: '@language_en_us', value: Failure in monitoring / assessing medicines therapy } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_of_adequate_medicines_security', language: '@language_en_us', value: Failure of adequate medicines security (e.g. missing CD) } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_of_compliance_aid', language: '@language_en_us', value: Failure of compliance aid  / monitored dosage system (MDS) } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_failure_to_follow_instructions', language: '@language_en_us', value: Patient / carer failure to follow instructions } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_follow_up_failure', language: '@language_en_us', value: Failure to refer for hospital follow-up } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_guidelines_variance', language: '@language_en_us', value: Variance to guidelines for sound clinical reasons } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_handwritten_prescription', language: '@language_en_us', value: Handwritten prescription / chart difficult to read } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_not_applicable', language: '@language_en_us', value: Not applicable } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_omitted_signature', language: '@language_en_us', value: Omitted signature of healthcare practitioner } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_otc_medicine', language: '@language_en_us', value: Involving an over-the-counter (OTC) medicine } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_patient_group_direction', language: '@language_en_us', value: Involving a medicine supplied under a Patient Group Direction (PGD)  } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_communication', language: '@language_en_us', value: Poor communication between care providers (verbal or written) } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_labelling', language: '@language_en_us', value: Poor labelling and packaging from a commercial manufacturer } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_poor_transfer', language: '@language_en_us', value: Poor transfer / transcription of information between paper and/or electronic forms } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_similar_medicines_look_name', language: '@language_en_us', value: Medicines with similar looking or sounding names } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_substance_abuse', language: '@language_en_us', value: Substance misuse (including alcohol) } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_supplementary_prescribing', language: '@language_en_us', value: Healthcare practitioner undertaking supplementary prescribing } }
    - { fields: { placeholder: '@medications_v2_form_option_important_factors_unknown', language: '@language_en_us', value: Unknown } }
    - { fields: { placeholder: '@medications_v2_form_option_route_epidural', language: '@language_en_us', value: Epidural } }
    - { fields: { placeholder: '@medications_v2_form_option_route_inhalation', language: '@language_en_us', value: Inhalation } }
    - { fields: { placeholder: '@medications_v2_form_option_route_intramuscular', language: '@language_en_us', value: Intramuscular } }
    - { fields: { placeholder: '@medications_v2_form_option_route_intrathecal', language: '@language_en_us', value: Intrathecal } }
    - { fields: { placeholder: '@medications_v2_form_option_route_intravenous', language: '@language_en_us', value: Intravenous } }
    - { fields: { placeholder: '@medications_v2_form_option_route_intravesicular', language: '@language_en_us', value: Intravesicular } }
    - { fields: { placeholder: '@medications_v2_form_option_route_nasal', language: '@language_en_us', value: Nasal } }
    - { fields: { placeholder: '@medications_v2_form_option_route_not_applicable', language: '@language_en_us', value: Not applicable } }
    - { fields: { placeholder: '@medications_v2_form_option_route_optical', language: '@language_en_us', value: Optical } }
    - { fields: { placeholder: '@medications_v2_form_option_route_oral', language: '@language_en_us', value: Oral } }
    - { fields: { placeholder: '@medications_v2_form_option_route_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@medications_v2_form_option_route_per_ear', language: '@language_en_us', value: Per ear } }
    - { fields: { placeholder: '@medications_v2_form_option_route_per_vagina', language: '@language_en_us', value: Per vagina } }
    - { fields: { placeholder: '@medications_v2_form_option_route_rectal', language: '@language_en_us', value: Rectal } }
    - { fields: { placeholder: '@medications_v2_form_option_route_subcutaneous', language: '@language_en_us', value: Subcutaneous } }
    - { fields: { placeholder: '@medications_v2_form_option_route_sublingual', language: '@language_en_us', value: Sublingual } }
    - { fields: { placeholder: '@medications_v2_form_option_route_topical', language: '@language_en_us', value: Topical } }
    - { fields: { placeholder: '@medications_v2_form_option_route_unknown', language: '@language_en_us', value: Unknown } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_administration', language: '@language_en_us', value: Administration / supply of a medicine from a clinical area } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_advice', language: '@language_en_us', value: Advice } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_medicing_preparation', language: '@language_en_us', value: Preparation of medicines in all locations / dispensing in a pharmacy } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_monitoring', language: '@language_en_us', value: Monitoring / follow-up of medicine use } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_otc_supply', language: '@language_en_us', value: Supply or use of over-the-counter (OTC) medicine } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@medications_v2_form_option_stage_errors_prescribing', language: '@language_en_us', value: Prescribing } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_drug_administered_route_select_one', language: '@language_en_us', value: Select one } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_drug_administered_units', language: '@language_en_us', value: units } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_drug_intended_route_select_one', language: '@language_en_us', value: Select one } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_drug_intended_units', language: '@language_en_us', value: units } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_form_administered_select_one', language: '@language_en_us', value: Select one } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_form_intended_select_one', language: '@language_en_us', value: Select one } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_search_administered', language: '@language_en_us', value: Start typing name of drug } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_search_drug_intended', language: '@language_en_us', value: Start typing name of drug } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_select_one', language: '@language_en_us', value: Select one } }
    - { fields: { placeholder: '@medications_v2_form_radio_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@medications_v2_form_radio_unknown', language: '@language_en_us', value: Unknown } }
    - { fields: { placeholder: '@medications_v2_form_radio_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@medications_v2_form_subheading_drug_administered', language: '@language_en_us', value: Administered } }
    - { fields: { placeholder: '@medications_v2_form_subheading_drug_intended', language: '@language_en_us', value: Intended / Suspected } }
    - { fields: { placeholder: '@medications_v2_message_search_results_title', language: '@language_en_us', value: Matching Medication } }
    - { fields: { placeholder: '@medications_v2_message_search_table_brand', language: '@language_en_us', value: Brand } }
    - { fields: { placeholder: '@medications_v2_message_search_table_class', language: '@language_en_us', value: Class } }
    - { fields: { placeholder: '@medications_v2_message_search_table_generic_name', language: '@language_en_us', value: Generic name } }
    - { fields: { placeholder: '@medications_v2_message_search_table_manufacturer', language: '@language_en_us', value: Manufacturer } }
    - { fields: { placeholder: '@medications_v2_message_search_table_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@medications_v2_form_message_medication_search_invalid_location', language: '@language_en_us', value: No medication found. The user must be linked to only one location } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_date_first_used', language: '@language_en_us', value: mm/dd/yyyy } }
    - { fields: { placeholder: '@medications_v2_form_placeholder_date_last_used', language: '@language_en_us', value: mm/dd/yyyy } }
    - { fields: { placeholder: '@medications_v2_form_option_route_centralintravenous', language: '@language_en_us', value: 'Central Intravenous' } }
    - { fields: { placeholder: '@medications_v2_form_option_route_peripheralintravenous', language: '@language_en_us', value: 'Peripheral Intravenous' } }
    - { fields: { placeholder: '@medications_v2_form_option_route_nasogastric', language: '@language_en_us', value: Naso-gastric } }
    - { fields: { placeholder: '@medications_v2_message_search_table_strength', language: '@language_en_us', value: Strength } }
    - { fields: { placeholder: '@medications_v2_form_message_invalid_search_length', language: '@language_en_us', value: 'Please enter a minimum of two characters to proceed with your search' } }
