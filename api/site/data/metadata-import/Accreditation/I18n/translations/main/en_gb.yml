entityClass: I18n\Entity\Translation
priority: 15
data:
- fields:
    placeholder: '@accreditation_form_std_title'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_form_std_title_label'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_form_std_description'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_form_std_description_label'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_form_std_summary'
    language: '@language_en_gb'
    value: 'Summary'
- fields:
    placeholder: '@accreditation_form_std_summary_label'
    language: '@language_en_gb'
    value: 'Summary'
- fields:
    placeholder: '@accreditation_form_std_additional_information'
    language: '@language_en_gb'
    value: 'Additional Information'
- fields:
    placeholder: '@accreditation_form_std_additional_information_label'
    language: '@language_en_gb'
    value: 'Additional Information'
- fields:
    placeholder: '@accreditation_form_std_concern_level'
    language: '@language_en_gb'
    value: 'Level of concern'
- fields:
    placeholder: '@accreditation_form_std_concern_level_label'
    language: '@language_en_gb'
    value: 'Level of concern'
- fields:
    placeholder: '@accreditation_form_std_concern_level_description'
    language: '@language_en_gb'
    value: 'Level of concern description'
- fields:
    placeholder: '@accreditation_form_std_concern_level_description_label'
    language: '@language_en_gb'
    value: 'Level of concern description'
- fields:
    placeholder: '@accreditation_form_std_classification'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_form_std_classification_label'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_form_std_sub_classification'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_form_std_sub_classification_label'
    language: '@language_en_gb'
    value: 'Sub Classification'
# Programme
- fields:
    placeholder: '@accreditation_programme_title'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_programme_title_label'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_programme_description'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_programme_description_label'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_programme_summary'
    language: '@language_en_gb'
    value: 'Summary'
- fields:
    placeholder: '@accreditation_programme_summary_label'
    language: '@language_en_gb'
    value: 'Summary'
- fields:
    placeholder: '@accreditation_programme_terminology'
    language: '@language_en_gb'
    value: 'Terminology'
- fields:
    placeholder: '@accreditation_programme_terminology_label'
    language: '@language_en_gb'
    value: 'Terminology'
- fields:
    placeholder: '@accreditation_programme_file'
    language: '@language_en_gb'
    value: 'Attachments'
- fields:
    placeholder: '@accreditation_programme_file_label'
    language: '@language_en_gb'
    value: 'Guidance'
- fields:
    placeholder: '@accreditation_programme_field_type'
    language: '@language_en_gb'
    value: 'Field Type'
- fields:
    placeholder: '@accreditation_programme_field_type_label'
    language: '@language_en_gb'
    value: 'Field Type'
- fields:
    placeholder: '@accreditation_programme_classification'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_programme_classification_label'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_programme_sub_classification'
    language: '@language_en_gb'
    value: 'Sub Classification'
- fields:
    placeholder: '@accreditation_programme_sub_classification_label'
    language: '@language_en_gb'
    value: 'Sub Classification'
# Assessment
- fields:
    placeholder: '@accreditation_assessment_title'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_assessment_title_label'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@accreditation_assessment_description'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_assessment_description_label'
    language: '@language_en_gb'
    value: 'Description'
- fields:
    placeholder: '@accreditation_assessment_classification'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accreditation_assessment_classification_label'
    language: '@language_en_gb'
    value: 'Classification'
- fields:
    placeholder: '@accrediation_form_type_programme'
    language: '@language_en_gb'
    value: 'Programme Form'
- fields:
    placeholder: '@accrediation_form_type_standard'
    language: '@language_en_gb'
    value: 'Standard Form'
- fields:
    placeholder: '@accreditation_datasource_item_guidance'
    language: '@language_en_gb'
    value: 'Guidance'
- fields:
    placeholder: '@accreditation_datasource_item_standard'
    language: '@language_en_gb'
    value: 'Standard'
- fields:
    placeholder: '@accreditation_datasource_item_high'
    language: '@language_en_gb'
    value: 'High'
- fields:
    placeholder: '@accreditation_datasource_item_medium'
    language: '@language_en_gb'
    value: 'Medium'
- fields:
    placeholder: '@accreditation_datasource_item_low'
    language: '@language_en_gb'
    value: 'Low'
- fields:
    placeholder: '@accrediatation_datasource_dropdown_options'
    language: '@language_en_gb'
    value: 'Dropdown Options'
- fields:
    placeholder: '@accrediatation_datasource_terminology'
    language: '@language_en_gb'
    value: 'Terminology'
- fields:
    placeholder: '@accrediatation_datasource_level_of_concern'
    language: '@language_en_gb'
    value: 'Level of concern'
- fields:
    placeholder: '@accrediatation_datasource_programme_field_type'
    language: '@language_en_gb'
    value: 'Programme Field Type'
- fields:
    placeholder: '@accrediatation_datasource_programme_classification'
    language: '@language_en_gb'
    value: 'Programme Classification'
- fields:
    placeholder: '@accrediatation_datasource_programme_subclassification'
    language: '@language_en_gb'
    value: 'Programme SubClassification'
- fields:
    placeholder: '@accrediatation_datasource_assessment_classification'
    language: '@language_en_gb'
    value: 'Assessment Classification'
- fields:
    placeholder: '@accreditation_form_name_standard'
    language: '@language_en_gb'
    value: 'Standard Form'
- fields:
    placeholder: '@accreditation_form_name_programme'
    language: '@language_en_gb'
    value: 'Programme Form'
