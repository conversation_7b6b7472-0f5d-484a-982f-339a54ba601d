entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@accreditation_form_std_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_form_std_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_form_std_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_form_std_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_form_std_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@accreditation_form_std_summary_label', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@accreditation_form_std_additional_information', language: '@language_fr_ca', value: 'Information supplémentaire' } }
  - { fields: { placeholder: '@accreditation_form_std_additional_information_label', language: '@language_fr_ca', value: 'Information supplémentaire' } }
  - { fields: { placeholder: '@accreditation_form_std_concern_level', language: '@language_fr_ca', value: 'Niveau de préoccupation' } }
  - { fields: { placeholder: '@accreditation_form_std_concern_level_label', language: '@language_fr_ca', value: 'Niveau de préoccupation' } }
  - { fields: { placeholder: '@accreditation_form_std_concern_level_description', language: '@language_fr_ca', value: 'Description du niveau de préoccupation' } }
  - { fields: { placeholder: '@accreditation_form_std_concern_level_description_label', language: '@language_fr_ca', value: 'Description du niveau de préoccupation' } }
  - { fields: { placeholder: '@accreditation_form_std_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_form_std_classification_label', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_form_std_sub_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_form_std_sub_classification_label', language: '@language_fr_ca', value: Sous-classification } }
  - { fields: { placeholder: '@accreditation_programme_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_programme_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_programme_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_programme_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_programme_summary', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@accreditation_programme_summary_label', language: '@language_fr_ca', value: Sommaire } }
  - { fields: { placeholder: '@accreditation_programme_terminology', language: '@language_fr_ca', value: Terminologie } }
  - { fields: { placeholder: '@accreditation_programme_terminology_label', language: '@language_fr_ca', value: Terminologie } }
  - { fields: { placeholder: '@accreditation_programme_file', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@accreditation_programme_file_label', language: '@language_fr_ca', value: Orientation } }
  - { fields: { placeholder: '@accreditation_programme_field_type', language: '@language_fr_ca', value: 'Type de champ' } }
  - { fields: { placeholder: '@accreditation_programme_field_type_label', language: '@language_fr_ca', value: 'Type de champ' } }
  - { fields: { placeholder: '@accreditation_programme_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_programme_classification_label', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_programme_sub_classification', language: '@language_fr_ca', value: Sous-classification } }
  - { fields: { placeholder: '@accreditation_programme_sub_classification_label', language: '@language_fr_ca', value: Sous-classification } }
  - { fields: { placeholder: '@accreditation_assessment_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_assessment_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@accreditation_assessment_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_assessment_description_label', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@accreditation_assessment_classification', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accreditation_assessment_classification_label', language: '@language_fr_ca', value: Classification } }
  - { fields: { placeholder: '@accrediation_form_type_programme', language: '@language_fr_ca', value: 'Formulaire de programme' } }
  - { fields: { placeholder: '@accrediation_form_type_standard', language: '@language_fr_ca', value: 'Formulaire standard' } }
  - { fields: { placeholder: '@accreditation_datasource_item_guidance', language: '@language_fr_ca', value: Orientation } }
  - { fields: { placeholder: '@accreditation_datasource_item_standard', language: '@language_fr_ca', value: Normal } }
  - { fields: { placeholder: '@accreditation_datasource_item_high', language: '@language_fr_ca', value: Élevée } }
  - { fields: { placeholder: '@accreditation_datasource_item_medium', language: '@language_fr_ca', value: Moyenne } }
  - { fields: { placeholder: '@accreditation_datasource_item_low', language: '@language_fr_ca', value: Faible } }
  - { fields: { placeholder: '@accrediatation_datasource_dropdown_options', language: '@language_fr_ca', value: 'Options de liste déroulante' } }
  - { fields: { placeholder: '@accrediatation_datasource_terminology', language: '@language_fr_ca', value: Terminologie } }
  - { fields: { placeholder: '@accrediatation_datasource_level_of_concern', language: '@language_fr_ca', value: 'Niveau de préoccupation' } }
  - { fields: { placeholder: '@accrediatation_datasource_programme_field_type', language: '@language_fr_ca', value: 'Type de champ de programme' } }
  - { fields: { placeholder: '@accrediatation_datasource_programme_classification', language: '@language_fr_ca', value: 'Classification de programme' } }
  - { fields: { placeholder: '@accrediatation_datasource_programme_subclassification', language: '@language_fr_ca', value: 'Sous-classification de programme' } }
  - { fields: { placeholder: '@accrediatation_datasource_assessment_classification', language: '@language_fr_ca', value: 'Classification d''évaluation' } }
  - { fields: { placeholder: '@accreditation_form_name_standard', language: '@language_fr_ca', value: 'Formulaire standard' } }
  - { fields: { placeholder: '@accreditation_form_name_programme', language: '@language_fr_ca', value: 'Formulaire de programme' } }
