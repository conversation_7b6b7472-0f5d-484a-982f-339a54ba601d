entityClass: I18n\Entity\Placeholder
priority: 10
data:
  -
    fields:
      placeholder: FORMS.MODAL.MOVE_FIELD.TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_modal_move_field_title
  -
    fields:
      placeholder: FORMS.MODAL.MOVE_FIELD.FIELDS.TARGET
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_modal_move_field_fields_target
  -
    fields:
      placeholder: FORMS.MODAL.MOVE_FIELD.FIELDS.TARGET_SELECT
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_modal_move_field_fields_target_select
  -
    fields:
      placeholder: FORMS.MODAL.MOVE_FIELD.FIELDS.TARGET_OPTION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_modal_move_field_fields_target_option
  -
    fields:
      placeholder: FORMS.MODAL.MOVE_FIELD.SUBMIT
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_modal_move_field_submit
  # Forms: Cascading Select
  -
    fields:
      placeholder: FORMS.WIDGET.CASCADING_SELECT.ERROR.FAILED_TO_GET_RECORDS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: forms_widget_cascading_select_error_failed_to_get_records
  ### Field Maintenance
  -
    fields:
      placeholder: FIELD_MAINTENANCE.RESPONSE_OPTIONS.EMPTY
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_response_options_empty
  -
    fields:
      placeholder: FIELD_MAINTENANCE.RESPONSE_OPTIONS.NEW_OPTION
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_response_options_new_option
  -
    fields:
      placeholder: FIELD_MAINTENANCE.FIELD.TITLE.ABBR
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_field_title_abbr
  -
    fields:
      placeholder: FIELD_MAINTENANCE.FIELD.LABEL.ABBR
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_field_label_abbr
  -
    fields:
      placeholder: FIELD_MAINTENANCE.FIELD.MERGE_CODE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_field_merge_code
  -
    fields:
      placeholder: FIELD_MAINTENANCE.FIELD.MERGE_CODE.ABBR
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_field_merge_code_abbr
  # Field Maintenance: Cascading Select
  -
    fields:
      placeholder: FIELD_MAINTENANCE.WIDGET.CASCADING_SELECT.ERROR.FAILED_TO_GET_RECORDS
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_widget_cascading_select_error_failed_to_get_records
  -
    fields:
      placeholder: FIELD_MAINTENANCE.WIDGET.CASCADING_SELECT.SUCCESS.DELETED_RECORD
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_widget_cascading_select_success_deleted_record
  -
    fields:
      placeholder: FIELD_MAINTENANCE.WIDGET.CASCADING_SELECT.ERROR.FAILED_TO_DELETE_RECORD
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_widget_cascading_select_error_failed_to_delete_record
  -
    fields:
      placeholder: FIELD_MAINTENANCE.WIDGET.CASCADING_SELECT.TITLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_widget_cascading_select_title
  - fields:
      placeholder: FIELD_MAINTENANCE.ENTER_A_LABEL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_enter_a_label
  - fields:
      placeholder: FIELD_MAINTENANCE.ENTER_A_FIELD_TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_enter_a_field_type
  - fields:
      placeholder: FIELD_MAINTENANCE.ENTER_AN_INPUT_TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_enter_an_input_type
  - fields:
      placeholder: FIELD_MAINTENANCE.MINIMUM_ONE_FORM_TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_minimum_one_form_type
  -
    fields:
      placeholder: FIELD_MAINTENANCE.ERROR.UNIQUE_NAME
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_error_unique_name
  -
    fields:
      placeholder: FIELD_MAINTENANCE.ERROR.UNIQUE_TITLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: field_maintenance_error_unique_name_title
  -
    fields:
      placeholder: FORM_DESIGNER.ACTION_SEARCH
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_action_search
  -
    fields:
      placeholder: FORM_DESIGNER.DEFAULT_FORM
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_default_form
  -
    fields:
      placeholder: ACTION_SEARCH_TABLE.NAME
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: action_search_table_name
  -
    fields:
      placeholder: ACTION_SEARCH_TABLE.IS_DEFAULT
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: action_search_table_is_default
  -
    fields:
      placeholder: ACTION_SEARCH_TABLE.SEARCH_LABEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: action_search_table_search_label
  -
    fields:
      placeholder: FORM_TABS.FORM_DETAILS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_form_details
  -
    fields:
      placeholder: FORM_TABS.FIELDS_&_SECTIONS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_fields_&_sections
  -
    fields:
      placeholder: FORM_TABS.FORM_PANELS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_form_panels
  -
    fields:
      placeholder: FORM_TABS.TRIGGERS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_triggers
  -
    fields:
      placeholder: FORM_TABS.ACTION_SUBFORM
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_action_subform
  -
    fields:
      placeholder: FORM.SUBFORM_ASSIGNMENT_TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_tabs_subform_assignment_title
  -
    fields:
      placeholder: FORM.SUBFORM_ASSIGNMENT_ASSIGNMENT_TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_subform_assignment_heading_title
  -
    fields:
      placeholder: FORM.SUBFORM_NOT_ASSIGNED
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_subform_not_assigned
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.ACTIONS.FIELD_INFORMATION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_actions_field_information
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.ACTIONS.FIELD_HELP
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_actions_field_help
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.ACTIONS.MOVE_FIELD
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_actions_move_field
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_INFORMATION.TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_information_title
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_INFORMATION.LABEl
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_information_label
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_title
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.MOVE_FIELD.TITLE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_move_field_title
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.HELP_TEXT.LABEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_help_text_label
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.HELP_IMAGE.LABEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_help_image_label
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.HELP_IMAGE.ADD_IMAGE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_help_image_add_image
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.HELP_IMAGE.REMOVE_IMAGE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_help_image_remove_image
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.WEB_LINK.LABEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_web_link_label
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.NEW_WINDOW.LABEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_new_window_label
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.ERROR.SEARCH_FIELDS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_error_search_fields
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.ERROR.REMOVE_FIELDS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_error_remove_fields
  - fields:
      placeholder: FORM.FIELDS_AND_SECTIONS.FIELD_HELP.HELP_LINK.VALIDATION_ERROR
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_fields_and_sections_field_help_help_link_validation_error
  - fields:
      placeholder: FORM.FIELDS.ERROR.DATA_SOURCE_ITEM_INVALID_PARENT
      type: 0
      domains:
        - domain: '@domain_common'
    ref: form_fields_error_data_source_item_invalid_parent
  - fields:
      placeholder: FORM.FIELDS.CASCADING_SELECT.NEW_ROOT_OPTION
      type: 0
      domains:
        - domain: '@domain_common'
    ref: placeholder_forms_fields_cascading_select_new_root_option
  - fields:
      placeholder: FORM.FIELDS.FIELD_TYPE.FIELD
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_fields_field_type_field
  - fields:
      placeholder: FORM.FIELDS.FIELD_TYPE.CORE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_type_core
  - fields:
      placeholder: FORM.FIELDS.FIELD_TYPE.CUSTOM
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_type_custom
  - fields:
      placeholder: FORM.FIELDS.FIELD_TYPE.WIDGET
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_type_widget
  - fields:
      placeholder: FORM.FIELDS.FIELD_TYPE.STANDARD
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_type_standard
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.SHORT_TEXT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_short_text
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.LONG_TEXT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_long_text
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.DATE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_date
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RADIO.YES_NO
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_radio_yes_no
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RADIO.RANGE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_radio_range
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RADIO.BUTTONS
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_radio_buttons
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.CHECKBOXES
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_checkboxes
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RECORD_SEARCH
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_record_search
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.ATTACHMENTS
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_attachments
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.FILE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_file
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RELATIONSHIPS
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_relationships
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.INVESTIGATION_LEVELS
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_investigation_levels
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.DROPDOWN_SINGLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_dropdown_single
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.DROPDOWN_MULTIPLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_dropdown_multiple
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.CONTACT_NUMBER_FILTER
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_contact_number_filter
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.CASCADING_SELECT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_cascading_select
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.HEATMAP
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_heatmap
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.ACL_LIMITED_RELATIONSHIP
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_acl_limited_relationship
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.RELATIONSHIP
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_relationship
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.HIDDEN
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_hidden
  - fields:
      placeholder: FORM.FIELDS.FIELD_CONTROL_TYPES.DISPLAY
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_field_control_types_display
  - fields:
      placeholder: FORM.FIELDS.HEADERS.TITLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_title
  - fields:
      placeholder: FORM.FIELDS.HEADERS.LABEL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_label
  - fields:
      placeholder: FORM.FIELDS.HEADERS.TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_type
  - fields:
      placeholder: FORM.FIELDS.HEADERS.FIELD_TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_field_type
  - fields:
      placeholder: FORM.FIELDS.HEADERS.IS_VISIBLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_is_visible
  - fields:
      placeholder: FORM.FIELDS.HEADERS.IS_MANDATORY
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
        - domain: '@domain_forms'
    ref: placeholder_forms_fields_headers_is_mandatory
  - fields:
      placeholder: FORM.FIELD.RESPONSE_OPTIONS.HEADER
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_response_options_header
  - fields:
      placeholder: FORMS.FIELD.TYPE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_type
  - fields:
      placeholder: FORMS.FIELD.FIELD_TITLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_field_title
  - fields:
      placeholder: FORMS.FIELD.LABEL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_label
  - fields:
      placeholder: FORMS.FIELD.LABEL_ABBREVIATION
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_label_abbreviation
  - fields:
      placeholder: FORMS.FIELD.FIELD_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_field_type_label
  - fields:
      placeholder: FORMS.FIELD.FIELD_TYPE.SELECT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_field_type_select
  - fields:
      placeholder: FORMS.FIELD.INPUT_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_input_type_label
  - fields:
      placeholder: FORMS.FIELD.INPUT_TYPE.SELECT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_input_type_select
  - fields:
      placeholder: FORMS.FIELD.RESPONSE_OPTIONS.OPTION_ID
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_response_options_option_id
  - fields:
      placeholder: FORMS.FIELD.RESPONSE_OPTIONS.SELECT
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_response_options_select
  - fields:
      placeholder: FORMS.FIELD.RESPONSE_OPTIONS.ADD
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_response_options_add
  - fields:
      placeholder: FORMS.FIELD.SAVE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_save
  - fields:
      placeholder: FORMS.FIELD.LOADING.SINGULAR
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_loading_singular
  - fields:
      placeholder: FORMS.FIELD.LOADING.PLURAL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_loading_plural
  - fields:
      placeholder: FORMS.FIELD.FIELD_SETUP
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_field_setup
  - fields:
      placeholder: FORMS.FIELD.FORM_TYPES
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_form_types
  - fields:
      placeholder: FORMS.FORM_TYPES.ALL
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_form_types_all
  - fields:
      placeholder: FORMS.FIELD.INPUT_TYPE.DROPDOWN
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_input_type_dropdown
  - fields:
      placeholder: FORMS.FIELD.SELECTION.MULTIPLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_selection_multiple
  - fields:
      placeholder: FORMS.FIELD.SELECTION.SINGLE
      type: 0
      domains:
        - domain: '@domain_field_maintenance'
    ref: placeholder_forms_field_selection_single
  - fields:
      placeholder: FORMS.ERROR.TOO_LONG
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_forms'
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_forms_error_too_long
  - fields:
      placeholder: FORMS.ERROR.BAD_REQUEST
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_forms'
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_forms_error_bad_request
  - fields:
      placeholder: FORMS.ERROR.FORBIDDEN
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_forms'
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_forms_error_forbidden
  - fields:
      placeholder: FORMS.ERROR.GENERIC
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_forms'
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_forms_error_generic
  - fields:
      placeholder: FORMS.ERROR.NOT_FOUND
      type: 0
      domains:
        - domain: '@domain_common'
        - domain: '@domain_forms'
        - domain: '@domain_enterprise_risk_manager'
    ref: placeholder_forms_error_not_found

  - fields:
      placeholder: FORMS.PANELS.EDIT
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_edit
  - fields:
      placeholder: FORMS.PANELS.SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_saved_successfully
  - fields:
      placeholder: FORMS.PANELS.PANEL.SAVED_SUCCESSFULLY
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_panel_saved_successfully
  - fields:
      placeholder: FORMS.PANELS.ERROR.SAVE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_error_save
  - fields:
      placeholder: FORMS.PANELS.ERROR.SAVE_PANEL
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_error_save_panel
  - fields:
      placeholder: FORMS.PANELS.BUTTON.SAVE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: placeholder_forms_panels_button_save
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.LOADING_TRIGGERS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_loading_triggers
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FORM_MUST_HAVE_AT_LEAST_ONE_SECTION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_form_must_have_at_least_one_section
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.ADD_NEW_TRIGGER
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_add_new_trigger
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.TRIGGER_CONDITIONS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_trigger_conditions
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.TRIGGER_TARGETS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_trigger_targets
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FORM_HAS_NO_TRIGGERS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_form_has_no_triggers
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FIELD
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_field
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.VALUE
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_value
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FIELD_REMOVED
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_field_removed
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.TRIGGER_HAS_NO_CONDITION_SET
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_trigger_has_no_condition_set
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.SECTIONS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_sections
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FIELDS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_fields
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.PANELS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_panels
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.TRIGGER_HAS_NO_TARGET
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_trigger_has_no_target
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.NEW_TRIGGER
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_new_trigger
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.EDIT_TRIGGER
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_edit_trigger
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.SAVE_TRIGGER
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_save_trigger
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FORM_SECTIONS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_form_sections
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.FORM_PANELS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_form_panels
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.ALREADY_TARGET
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_already_target
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.NEW_CONDITION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_new_condition
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.SELECT_FIELD
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_select_field
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.SAVE_CONDITION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_save_condition
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.ADD_CONDITION
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_add_condition
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.CONDITIONS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_conditions
  - fields:
      placeholder: FORM_DESIGNER.FORM.TRIGGERS.TARGETS
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_triggers_targets
  - fields:
      placeholder: FORM_DESIGNER.FORM.PANELS.MANDATORY_HELP_TEXT
      type: 0
      domains:
        - domain: '@domain_forms'
    ref: form_designer_form_panels_mandatory_help_text
