entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@forms_modal_move_field_title', language: '@language_fr_ca', value: '<PERSON>éplace<PERSON> le champ' } }
  - { fields: { placeholder: '@forms_modal_move_field_fields_target', language: '@language_fr_ca', value: "Section cible\_:" } }
  - { fields: { placeholder: '@forms_modal_move_field_fields_target_select', language: '@language_fr_ca', value: 'Sélectionnez une section cible' } }
  - { fields: { placeholder: '@forms_modal_move_field_fields_target_option', language: '@language_fr_ca', value: 'Section {{index}}' } }
  - { fields: { placeholder: '@forms_modal_move_field_submit', language: '@language_fr_ca', value: 'Déplacer le champ' } }
  - { fields: { placeholder: '@forms_widget_cascading_select_error_failed_to_get_records', language: '@language_fr_ca', value: 'Échec de l''obtention des dossiers' } }
  - { fields: { placeholder: '@field_maintenance_response_options_new_option', language: '@language_fr_ca', value: 'Nouvelle option' } }
  - { fields: { placeholder: '@field_maintenance_field_title_abbr', language: '@language_fr_ca', value: 'Ceci est l''étiquette affichée dans les listes de champs personnalisés pour en faciliter l''identification' } }
  - { fields: { placeholder: '@field_maintenance_field_label_abbr', language: '@language_fr_ca', value: 'Ceci est l''étiquette affichée à côté du contrôle de formulaire' } }
  - { fields: { placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_get_records', language: '@language_fr_ca', value: 'Échec de l''obtention des dossiers' } }
  - { fields: { placeholder: '@field_maintenance_widget_cascading_select_success_deleted_record', language: '@language_fr_ca', value: 'Dossier supprimé' } }
  - { fields: { placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_delete_record', language: '@language_fr_ca', value: 'Échec de la suppression du dossier' } }
  - { fields: { placeholder: '@field_maintenance_widget_cascading_select_title', language: '@language_fr_ca', value: 'Gadget de sélection en cascade' } }
  - { fields: { placeholder: '@field_maintenance_enter_a_label', language: '@language_fr_ca', value: 'Veuillez entrer une étiquette' } }
  - { fields: { placeholder: '@field_maintenance_enter_a_field_type', language: '@language_fr_ca', value: 'Veuillez entrer un type de champ' } }
  - { fields: { placeholder: '@field_maintenance_enter_an_input_type', language: '@language_fr_ca', value: 'Veuillez entrer un type d''entrée' } }
  - { fields: { placeholder: '@field_maintenance_minimum_one_form_type', language: '@language_fr_ca', value: 'Il est nécessaire de sélectionner au moins un type de formulaire' } }
  - { fields: { placeholder: '@field_maintenance_error_unique_name', language: '@language_fr_ca', value: 'Le titre du champ existe déjà pour le type de formulaire' } }
  - { fields: { placeholder: '@field_maintenance_error_unique_name_title', language: '@language_fr_ca', value: 'Erreur de champ' } }
  - { fields: { placeholder: '@form_designer_action_search', language: '@language_fr_ca', value: 'Recherche d''action' } }
  - { fields: { placeholder: '@form_designer_default_form', language: '@language_fr_ca', value: 'Ceci est le formulaire par défaut du {{module}}' } }
  - { fields: { placeholder: '@action_search_table_name', language: '@language_fr_ca', value: Nom } }
  - { fields: { placeholder: '@action_search_table_is_default', language: '@language_fr_ca', value: 'Est-ce par défaut?' } }
  - { fields: { placeholder: '@action_search_table_search_label', language: '@language_fr_ca', value: 'Recherche de formulaire d''action' } }
  - { fields: { placeholder: '@form_tabs_form_details', language: '@language_fr_ca', value: 'Détails du formulaire' } }
  - { fields: { placeholder: '@form_tabs_fields_&_sections', language: '@language_fr_ca', value: 'Champs et sections' } }
  - { fields: { placeholder: '@form_tabs_triggers', language: '@language_fr_ca', value: Déclencheurs } }
  - { fields: { placeholder: '@form_tabs_action_subform', language: '@language_fr_ca', value: 'Sous-formulaire d''action' } }
  - { fields: { placeholder: '@form_tabs_subform_assignment_title', language: '@language_fr_ca', value: 'Attribution de formulaire' } }
  - { fields: { placeholder: '@form_subform_assignment_heading_title', language: '@language_fr_ca', value: "{{formType}}\_: {{module}}" } }
  - { fields: { placeholder: '@form_fields_and_sections_actions_field_information', language: '@language_fr_ca', value: 'Information sur le champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_actions_field_help', language: '@language_fr_ca', value: 'Aide du champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_actions_move_field', language: '@language_fr_ca', value: 'Déplacer vers...' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_information_title', language: '@language_fr_ca', value: 'Information sur le champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_information_label', language: '@language_fr_ca', value: 'Information sur le champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_title', language: '@language_fr_ca', value: 'Aide du champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_move_field_title', language: '@language_fr_ca', value: 'Déplacer le champ' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_help_text_label', language: '@language_fr_ca', value: 'Texte d''aide' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_label', language: '@language_fr_ca', value: 'Image d''aide' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_add_image', language: '@language_fr_ca', value: 'Ajouter une image' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_remove_image', language: '@language_fr_ca', value: 'Supprimer l''image' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_web_link_label', language: '@language_fr_ca', value: 'Lien Web' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_new_window_label', language: '@language_fr_ca', value: 'Nouvelle fenêtre?' } }
  - { fields: { placeholder: '@form_fields_and_sections_error_search_fields', language: '@language_fr_ca', value: 'Une erreur s''est produite lors du chargement des champs de recherche de section' } }
  - { fields: { placeholder: '@form_fields_and_sections_field_help_help_link_validation_error', language: '@language_fr_ca', value: 'L''URL doit commencer par http:// ou https://' } }
  - { fields: { placeholder: '@form_fields_error_data_source_item_invalid_parent', language: '@language_fr_ca', value: 'L''option doit être un enfant du parent sélectionné' } }
  - { fields: { placeholder: '@placeholder_forms_fields_cascading_select_new_root_option', language: '@language_fr_ca', value: 'Nouvelle option racine' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_type_field', language: '@language_fr_ca', value: Champ } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_type_core', language: '@language_fr_ca', value: 'Champ de base' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_type_custom', language: '@language_fr_ca', value: 'Champ personnalisé' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_type_widget', language: '@language_fr_ca', value: 'Champ gadget logiciel' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_type_standard', language: '@language_fr_ca', value: 'Champ standard' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_short_text', language: '@language_fr_ca', value: 'Texte court' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_long_text', language: '@language_fr_ca', value: 'Texte long' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_yes_no', language: '@language_fr_ca', value: 'Oui / Non' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_range', language: '@language_fr_ca', value: Plage } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_buttons', language: '@language_fr_ca', value: 'Cases d''option' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_checkboxes', language: '@language_fr_ca', value: 'Cases à cocher' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_record_search', language: '@language_fr_ca', value: 'Recherche de dossier' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_attachments', language: '@language_fr_ca', value: 'Pièces jointes' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_file', language: '@language_fr_ca', value: Fichier } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_relationships', language: '@language_fr_ca', value: Relations } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_investigation_levels', language: '@language_fr_ca', value: 'Niveaux d''investigation' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_dropdown_single', language: '@language_fr_ca', value: 'Liste déroulante (simple)' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_dropdown_multiple', language: '@language_fr_ca', value: 'Liste déroulante (multiple)' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_contact_number_filter', language: '@language_fr_ca', value: 'Filtre de numéro de relation' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_cascading_select', language: '@language_fr_ca', value: 'Sélection en cascade' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_heatmap', language: '@language_fr_ca', value: 'Carte de densité' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_acl_limited_relationship', language: '@language_fr_ca', value: 'Relation limitée LCA' } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_relationship', language: '@language_fr_ca', value: Relation } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_hidden', language: '@language_fr_ca', value: Masqué } }
  - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_display', language: '@language_fr_ca', value: Affichage } }
  - { fields: { placeholder: '@placeholder_forms_fields_headers_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@placeholder_forms_fields_headers_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@placeholder_forms_fields_headers_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@placeholder_forms_fields_headers_field_type', language: '@language_fr_ca', value: 'Type de champ' } }
  - { fields: { placeholder: '@field_maintenance_response_options_empty', language: '@language_fr_ca', value: 'Utilisez le formulaire ci-dessus pour ajouter une réponse' } }
  - { fields: { placeholder: '@form_subform_not_assigned', language: '@language_fr_ca', value: 'Aucun sous-formulaire d''action attribué' } }
  - { fields: { placeholder: '@form_fields_and_sections_error_remove_fields', language: '@language_fr_ca', value: 'Impossible de supprimer le champ sans l''enregistrer dans le formulaire' } }
  - { fields: { placeholder: '@placeholder_forms_field_response_options_header', language: '@language_fr_ca', value: 'Options de réponse' } }
  - { fields: { placeholder: '@placeholder_forms_field_type', language: '@language_fr_ca', value: Type } }
  - { fields: { placeholder: '@placeholder_forms_field_field_title', language: '@language_fr_ca', value: 'Titre du champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_label', language: '@language_fr_ca', value: Étiquette } }
  - { fields: { placeholder: '@placeholder_forms_field_field_type_label', language: '@language_fr_ca', value: 'Type de champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_field_type_select', language: '@language_fr_ca', value: 'Sélectionnez type de champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_input_type_label', language: '@language_fr_ca', value: 'Type d''entrée' } }
  - { fields: { placeholder: '@placeholder_forms_field_input_type_select', language: '@language_fr_ca', value: 'Sélectionnez un type d''entrée' } }
  - { fields: { placeholder: '@placeholder_forms_field_response_options_option_id', language: '@language_fr_ca', value: 'ID d''option' } }
  - { fields: { placeholder: '@placeholder_forms_field_response_options_select', language: '@language_fr_ca', value: Sélectionner } }
  - { fields: { placeholder: '@placeholder_forms_field_response_options_add', language: '@language_fr_ca', value: Ajouter } }
  - { fields: { placeholder: '@placeholder_forms_field_save', language: '@language_fr_ca', value: 'Enregistrer le champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_loading_singular', language: '@language_fr_ca', value: 'Chargement du champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_loading_plural', language: '@language_fr_ca', value: 'Chargement des champs' } }
  - { fields: { placeholder: '@placeholder_forms_field_field_setup', language: '@language_fr_ca', value: 'Définition du champ' } }
  - { fields: { placeholder: '@placeholder_forms_field_form_types', language: '@language_fr_ca', value: 'Types de formulaires' } }
  - { fields: { placeholder: '@placeholder_forms_form_types_all', language: '@language_fr_ca', value: 'Tous les types de formulaires' } }
  - { fields: { placeholder: '@placeholder_forms_field_input_type_dropdown', language: '@language_fr_ca', value: 'Liste déroulante' } }
  - { fields: { placeholder: '@placeholder_forms_field_selection_multiple', language: '@language_fr_ca', value: 'Réponses multiples' } }
  - { fields: { placeholder: '@placeholder_forms_field_selection_single', language: '@language_fr_ca', value: 'Réponse unique' } }
