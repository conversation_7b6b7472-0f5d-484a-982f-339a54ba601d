entityClass: I18n\Entity\Translation
priority: 15
data:
- fields:
    placeholder: '@forms_modal_move_field_title'
    language: '@language_en_gb'
    value: 'Move Field'
- fields:
    placeholder: '@forms_modal_move_field_submit'
    language: '@language_en_gb'
    value: 'Move Field'
- fields:
    placeholder: '@forms_modal_move_field_fields_target'
    language: '@language_en_gb'
    value: 'Target Section:'
- fields:
    placeholder: '@forms_modal_move_field_fields_target_select'
    language: '@language_en_gb'
    value: 'Select a Target Section'
- fields:
    placeholder: '@forms_modal_move_field_fields_target_option'
    language: '@language_en_gb'
    value: 'Section {{index}}'
# Cascading Select
- fields:
    placeholder: '@forms_widget_cascading_select_error_failed_to_get_records'
    language: '@language_en_gb'
    value: 'Failed to get records'
# Field Maintenance
- fields:
    placeholder: '@field_maintenance_response_options_empty'
    language: '@language_en_gb'
    value: 'Use the form above to add a response'
- fields:
    placeholder: '@field_maintenance_response_options_new_option'
    language: '@language_en_gb'
    value: 'New option'
- fields:
    placeholder: '@field_maintenance_field_title_abbr'
    language: '@language_en_gb'
    value: 'This is the label shown in custom field listings to aid field identification'
- fields:
    placeholder: '@field_maintenance_field_label_abbr'
    language: '@language_en_gb'
    value: 'This is the label shown alongside the form control'
- fields:
    placeholder: '@field_maintenance_field_merge_code'
    language: '@language_en_gb'
    value: 'Merge Code'
- fields:
    placeholder: '@field_maintenance_field_merge_code_abbr'
    language: '@language_en_gb'
    value: 'This is the code used for adding custom fields to templates. Must be unique'
# Cascading Select
- fields:
    placeholder: '@field_maintenance_widget_cascading_select_title'
    language: '@language_en_gb'
    value: 'Cascading Select Widget'
- fields:
    placeholder: '@field_maintenance_widget_cascading_select_success_deleted_record'
    language: '@language_en_gb'
    value: 'Deleted record'
- fields:
    placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_get_records'
    language: '@language_en_gb'
    value: 'Failed to get records'
- fields:
    placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_delete_record'
    language: '@language_en_gb'
    value: 'Failed to delete record'
- fields:
    placeholder: '@field_maintenance_enter_a_label'
    language: '@language_en_gb'
    value: 'Please enter a label'
- fields:
    placeholder: '@field_maintenance_enter_a_field_type'
    language: '@language_en_gb'
    value: 'Please enter a field type'
- fields:
    placeholder: '@field_maintenance_enter_an_input_type'
    language: '@language_en_gb'
    value: 'Please enter an input type'
- fields:
    placeholder: '@field_maintenance_minimum_one_form_type'
    language: '@language_en_gb'
    value: 'A minimum of one Form Type must be selected'
- fields:
    placeholder: '@field_maintenance_error_unique_name'
    language: '@language_en_gb'
    value: 'Field Title already exists for Form Type'
- fields:
    placeholder: '@field_maintenance_error_unique_name_title'
    language: '@language_en_gb'
    value: 'Field Error'
- fields:
    placeholder: '@form_designer_action_search'
    language: '@language_en_gb'
    value: 'Action Search'
- fields:
      placeholder: '@form_designer_default_form'
      language: '@language_en_gb'
      value: 'This is the default {{module}} form'
- fields:
    placeholder: '@action_search_table_name'
    language: '@language_en_gb'
    value: 'Name'
- fields:
    placeholder: '@action_search_table_is_default'
    language: '@language_en_gb'
    value: 'Is Default?'
- fields:
    placeholder: '@action_search_table_search_label'
    language: '@language_en_gb'
    value: 'Action Form Search'
- fields:
    placeholder: '@form_tabs_form_details'
    language: '@language_en_gb'
    value: 'Form Details'
- fields:
    placeholder: '@form_tabs_fields_&_sections'
    language: '@language_en_gb'
    value: 'Fields & Sections'
- fields:
    placeholder: '@form_tabs_form_panels'
    language: '@language_en_gb'
    value: 'Form Panels'
- fields:
    placeholder: '@form_tabs_triggers'
    language: '@language_en_gb'
    value: 'Triggers'
- fields:
    placeholder: '@form_tabs_action_subform'
    language: '@language_en_gb'
    value: 'Action Subform'
- fields:
    placeholder: '@form_tabs_subform_assignment_title'
    language: '@language_en_gb'
    value: 'Form Assignment'
- fields:
    placeholder: '@form_subform_assignment_heading_title'
    language: '@language_en_gb'
    value: '{{formType}}: {{module}}'
- fields:
    placeholder: '@form_subform_not_assigned'
    language: '@language_en_gb'
    value: 'No action subform assigned'
- fields:
    placeholder: '@form_fields_and_sections_actions_field_information'
    language: '@language_en_gb'
    value: Field Information
- fields:
    placeholder: '@form_fields_and_sections_field_information_label'
    language: '@language_en_gb'
    value: Field Information
- fields:
    placeholder: '@form_fields_and_sections_actions_field_help'
    language: '@language_en_gb'
    value: Field Help
- fields:
    placeholder: '@form_fields_and_sections_actions_move_field'
    language: '@language_en_gb'
    value: Move to...
- fields:
    placeholder: '@form_fields_and_sections_field_information_title'
    language: '@language_en_gb'
    value: Field Information
- fields:
    placeholder: '@form_fields_and_sections_field_help_title'
    language: '@language_en_gb'
    value: Field Help
- fields:
    placeholder: '@form_fields_and_sections_move_field_title'
    language: '@language_en_gb'
    value: Move Field
- fields:
    placeholder: '@form_fields_and_sections_field_help_help_text_label'
    language: '@language_en_gb'
    value: Help Text
- fields:
    placeholder: '@form_fields_and_sections_field_help_help_image_label'
    language: '@language_en_gb'
    value: Help Image
- fields:
    placeholder: '@form_fields_and_sections_field_help_help_image_add_image'
    language: '@language_en_gb'
    value: Add Image
- fields:
    placeholder: '@form_fields_and_sections_field_help_help_image_remove_image'
    language: '@language_en_gb'
    value: Remove Image
- fields:
    placeholder: '@form_fields_and_sections_field_help_web_link_label'
    language: '@language_en_gb'
    value: Web Link
- fields:
    placeholder: '@form_fields_and_sections_field_help_new_window_label'
    language: '@language_en_gb'
    value: New Window?
- fields:
    placeholder: '@form_fields_and_sections_error_search_fields'
    language: '@language_en_gb'
    value: 'Something went wrong when loading the Section Search Fields'
- fields:
    placeholder: '@form_fields_and_sections_error_remove_fields'
    language: '@language_en_gb'
    value: 'Can not delete the field without saving it to the form'
- fields:
    placeholder: '@form_fields_and_sections_field_help_help_link_validation_error'
    language: '@language_en_gb'
    value: 'The URL must begin with http:// or https://'
- fields:
    placeholder: '@form_fields_error_data_source_item_invalid_parent'
    language: '@language_en_gb'
    value: 'Option must be a child of the selected parent'
- fields:
    placeholder: '@placeholder_forms_fields_cascading_select_new_root_option'
    language: '@language_en_gb'
    value: 'New Root Option'
- fields:
    placeholder: '@placeholder_forms_fields_field_type_core'
    language: '@language_en_gb'
    value: 'Core Field'
- fields:
    placeholder: '@placeholder_forms_fields_field_type_field'
    language: '@language_en_gb'
    value: 'Field'
- fields:
    placeholder: '@placeholder_forms_fields_field_type_custom'
    language: '@language_en_gb'
    value: 'Custom Field'
- fields:
    placeholder: '@placeholder_forms_fields_field_type_widget'
    language: '@language_en_gb'
    value: 'Widget Field'
- fields:
    placeholder: '@placeholder_forms_fields_field_type_standard'
    language: '@language_en_gb'
    value: 'Standard Field'
- fields:
    placeholder: '@placeholder_forms_fields_headers_title'
    language: '@language_en_gb'
    value: 'Title'
- fields:
    placeholder: '@placeholder_forms_fields_headers_label'
    language: '@language_en_gb'
    value: 'Label'
- fields:
    placeholder: '@placeholder_forms_fields_headers_type'
    language: '@language_en_gb'
    value: 'Type'
- fields:
    placeholder: '@placeholder_forms_fields_headers_field_type'
    language: '@language_en_gb'
    value: 'Field Type'
- fields:
    placeholder: '@placeholder_forms_fields_headers_is_visible'
    language: '@language_en_gb'
    value: 'Hidden?'
- fields:
    placeholder: '@placeholder_forms_fields_headers_is_mandatory'
    language: '@language_en_gb'
    value: 'Mandatory?'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_short_text'
    language: '@language_en_gb'
    value: 'Short Text'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_long_text'
    language: '@language_en_gb'
    value: 'Long Text'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_date'
    language: '@language_en_gb'
    value: 'Date'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_radio_yes_no'
    language: '@language_en_gb'
    value: 'Yes / No'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_radio_range'
    language: '@language_en_gb'
    value: 'Range'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_radio_buttons'
    language: '@language_en_gb'
    value: 'Radio Buttons'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_checkboxes'
    language: '@language_en_gb'
    value: 'Checkboxes'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_record_search'
    language: '@language_en_gb'
    value: 'Record Search'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_attachments'
    language: '@language_en_gb'
    value: 'Attachments'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_file'
    language: '@language_en_gb'
    value: 'File'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_relationships'
    language: '@language_en_gb'
    value: 'Relationships'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_investigation_levels'
    language: '@language_en_gb'
    value: 'Investigation Levels'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_dropdown_single'
    language: '@language_en_gb'
    value: 'Dropdown (Single)'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_dropdown_multiple'
    language: '@language_en_gb'
    value: 'Dropdown (Multiple)'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_contact_number_filter'
    language: '@language_en_gb'
    value: 'Contact Number Filter'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_cascading_select'
    language: '@language_en_gb'
    value: 'Cascading Select'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_heatmap'
    language: '@language_en_gb'
    value: 'Heatmap'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_acl_limited_relationship'
    language: '@language_en_gb'
    value: 'ACL Limited Relationship'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_relationship'
    language: '@language_en_gb'
    value: 'Relationship'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_hidden'
    language: '@language_en_gb'
    value: 'Hidden'
- fields:
    placeholder: '@placeholder_forms_fields_field_control_types_display'
    language: '@language_en_gb'
    value: 'Display'

- fields:
    placeholder: '@placeholder_forms_field_response_options_header'
    language: '@language_en_gb'
    value: 'Response Options'
- fields:
    placeholder: '@placeholder_forms_field_type'
    language: '@language_en_gb'
    value: 'Type'
- fields:
    placeholder: '@placeholder_forms_field_field_title'
    language: '@language_en_gb'
    value: 'Field Title'
- fields:
    placeholder: '@placeholder_forms_field_label'
    language: '@language_en_gb'
    value: 'Label'
- fields:
    placeholder: '@placeholder_forms_field_field_type_label'
    language: '@language_en_gb'
    value: 'Field Type'
- fields:
    placeholder: '@placeholder_forms_field_field_type_select'
    language: '@language_en_gb'
    value: 'Select a Field Type'
- fields:
    placeholder: '@placeholder_forms_field_input_type_label'
    language: '@language_en_gb'
    value: 'Input Type'
- fields:
    placeholder: '@placeholder_forms_field_input_type_select'
    language: '@language_en_gb'
    value: 'Select an Input Type'
- fields:
    placeholder: '@placeholder_forms_field_response_options_option_id'
    language: '@language_en_gb'
    value: 'Option ID'
- fields:
    placeholder: '@placeholder_forms_field_response_options_select'
    language: '@language_en_gb'
    value: 'Select'
- fields:
    placeholder: '@placeholder_forms_field_response_options_add'
    language: '@language_en_gb'
    value: 'Add'
- fields:
    placeholder: '@placeholder_forms_field_save'
    language: '@language_en_gb'
    value: 'Save Field'
- fields:
    placeholder: '@placeholder_forms_field_loading_singular'
    language: '@language_en_gb'
    value: 'Loading Field'
- fields:
    placeholder: '@placeholder_forms_field_loading_plural'
    language: '@language_en_gb'
    value: 'Loading Fields'
- fields:
    placeholder: '@placeholder_forms_field_field_setup'
    language: '@language_en_gb'
    value: 'Field Setup'
- fields:
    placeholder: '@placeholder_forms_field_form_types'
    language: '@language_en_gb'
    value: 'Form Types'
- fields:
    placeholder: '@placeholder_forms_form_types_all'
    language: '@language_en_gb'
    value: 'All Form Types'
- fields:
    placeholder: '@placeholder_forms_field_input_type_dropdown'
    language: '@language_en_gb'
    value: 'Dropdown'
- fields:
    placeholder: '@placeholder_forms_field_selection_multiple'
    language: '@language_en_gb'
    value: 'Multiple Responses'
- fields:
    placeholder: '@placeholder_forms_field_selection_single'
    language: '@language_en_gb'
    value: 'Single Response'
- fields:
    placeholder: '@placeholder_forms_error_too_long'
    language: '@language_en_gb'
    value: 'The request took too long to process'
- fields:
    placeholder: '@placeholder_forms_error_bad_request'
    language: '@language_en_gb'
    value: 'A validation error occurred'
- fields:
    placeholder: '@placeholder_forms_error_forbidden'
    language: '@language_en_gb'
    value: 'You do not have permission to access this'
- fields:
    placeholder: '@placeholder_forms_error_generic'
    language: '@language_en_gb'
    value: 'Server unable to complete this request, please try again or contact administrator'
- fields:
    placeholder: '@placeholder_forms_error_not_found'
    language: '@language_en_gb'
    value: 'Request not found'
- fields:
    placeholder: '@placeholder_forms_panels_edit'
    language: '@language_en_gb'
    value: 'Edit panel'
- fields:
    placeholder: '@placeholder_forms_panels_saved_successfully'
    language: '@language_en_gb'
    value: 'New panel order saved successfully'
- fields:
    placeholder: '@placeholder_forms_panels_panel_saved_successfully'
    language: '@language_en_gb'
    value: 'Panel details saved successfully.'
- fields:
    placeholder: '@placeholder_forms_panels_error_save'
    language: '@language_en_gb'
    value: 'Something went wrong whilst saving the updated panel order.'
- fields:
    placeholder: '@placeholder_forms_panels_error_save_panel'
    language: '@language_en_gb'
    value: 'Something went wrong whilst saving the updated panel details.'
- fields:
    placeholder: '@placeholder_forms_panels_button_save'
    language: '@language_en_gb'
    value: 'Save Changes'
- fields:
    placeholder: '@form_designer_form_triggers_loading_triggers'
    language: '@language_en_gb'
    value: 'Loading Triggers...'
- fields:
    placeholder: '@form_designer_form_triggers_form_must_have_at_least_one_section'
    language: '@language_en_gb'
    value: 'To create triggers a form must have at least one section'
- fields:
    placeholder: '@form_designer_form_triggers_add_new_trigger'
    language: '@language_en_gb'
    value: 'Add New Trigger'
- fields:
    placeholder: '@form_designer_form_triggers_trigger_conditions'
    language: '@language_en_gb'
    value: 'Trigger Conditions'
- fields:
    placeholder: '@form_designer_form_triggers_trigger_targets'
    language: '@language_en_gb'
    value: 'Trigger Targets'
- fields:
    placeholder: '@form_designer_form_triggers_form_has_no_triggers'
    language: '@language_en_gb'
    value: 'This Form currently has no Triggers'
- fields:
    placeholder: '@form_designer_form_triggers_field'
    language: '@language_en_gb'
    value: 'Field'
- fields:
    placeholder: '@form_designer_form_triggers_value'
    language: '@language_en_gb'
    value: 'Value'
- fields:
    placeholder: '@form_designer_form_triggers_field_removed'
    language: '@language_en_gb'
    value: 'Field Removed'
- fields:
    placeholder: '@form_designer_form_triggers_trigger_has_no_condition_set'
    language: '@language_en_gb'
    value: 'This Trigger has no Conditions set'
- fields:
    placeholder: '@form_designer_form_triggers_sections'
    language: '@language_en_gb'
    value: 'Sections'
- fields:
    placeholder: '@form_designer_form_triggers_fields'
    language: '@language_en_gb'
    value: 'Fields'
- fields:
    placeholder: '@form_designer_form_triggers_panels'
    language: '@language_en_gb'
    value: 'Panels'
- fields:
    placeholder: '@form_designer_form_triggers_trigger_has_no_target'
    language: '@language_en_gb'
    value: 'This Trigger has no target Sections, Fields or Form Panels'
- fields:
    placeholder: '@form_designer_form_triggers_new_trigger'
    language: '@language_en_gb'
    value: 'New Trigger'
- fields:
    placeholder: '@form_designer_form_triggers_edit_trigger'
    language: '@language_en_gb'
    value: 'Edit Trigger'
- fields:
    placeholder: '@form_designer_form_triggers_save_trigger'
    language: '@language_en_gb'
    value: 'Save Trigger'
- fields:
    placeholder: '@form_designer_form_triggers_form_sections'
    language: '@language_en_gb'
    value: 'Form Sections'
- fields:
    placeholder: '@form_designer_form_triggers_form_panels'
    language: '@language_en_gb'
    value: 'Form Panels'
- fields:
    placeholder: '@form_designer_form_triggers_already_target'
    language: '@language_en_gb'
    value: 'Section, Field or Form Panel is already the target of a different Trigger'
- fields:
    placeholder: '@form_designer_form_triggers_new_condition'
    language: '@language_en_gb'
    value: 'New Condition'
- fields:
    placeholder: '@form_designer_form_triggers_select_field'
    language: '@language_en_gb'
    value: 'Select a Field'
- fields:
    placeholder: '@form_designer_form_triggers_save_condition'
    language: '@language_en_gb'
    value: 'Save Condition'
- fields:
    placeholder: '@form_designer_form_triggers_add_condition'
    language: '@language_en_gb'
    value: 'Add Condition'
- fields:
    placeholder: '@form_designer_form_triggers_conditions'
    language: '@language_en_gb'
    value: 'Conditions'
- fields:
    placeholder: '@form_designer_form_triggers_targets'
    language: '@language_en_gb'
    value: 'Targets'
- fields:
    placeholder: '@form_designer_form_panels_mandatory_help_text'
    language: '@language_en_gb'
    value: 'Selecting the mandatory setting for Controls & Assurance will only make “Controls In Place” mandatory'
