entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@action_search_table_is_default', language: '@language_en_us', value: 'Is Default?' } }
    - { fields: { placeholder: '@action_search_table_name', language: '@language_en_us', value: Name } }
    - { fields: { placeholder: '@action_search_table_search_label', language: '@language_en_us', value: Action Form Search } }
    - { fields: { placeholder: '@field_maintenance_enter_a_field_type', language: '@language_en_us', value: Please enter a field type } }
    - { fields: { placeholder: '@field_maintenance_enter_a_label', language: '@language_en_us', value: Please enter a label } }
    - { fields: { placeholder: '@field_maintenance_enter_an_input_type', language: '@language_en_us', value: Please enter an input type } }
    - { fields: { placeholder: '@field_maintenance_error_unique_name', language: '@language_en_us', value: Field Title already exists for Form Type } }
    - { fields: { placeholder: '@field_maintenance_error_unique_name_title', language: '@language_en_us', value: Field Error } }
    - { fields: { placeholder: '@field_maintenance_response_options_empty', language: '@language_en_us', value: 'Use the form above to add a response' } }
    - { fields: { placeholder: '@field_maintenance_field_label_abbr', language: '@language_en_us', value: This is the label shown alongside the form control } }
    - { fields: { placeholder: '@field_maintenance_field_title_abbr', language: '@language_en_us', value: This is the label shown in custom field listings to aid field identification } }
    - { fields: { placeholder: '@field_maintenance_field_merge_code', language: '@language_en_us', value: Merge Code } }
    - { fields: { placeholder: '@field_maintenance_field_merge_code_abbr', language: '@language_en_us', value: This is the code used for adding custom fields to templates. Must be unique } }
    - { fields: { placeholder: '@field_maintenance_minimum_one_form_type', language: '@language_en_us', value: A minimum of one Form Type must be selected } }
    - { fields: { placeholder: '@field_maintenance_response_options_new_option', language: '@language_en_us', value: New option } }
    - { fields: { placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_delete_record', language: '@language_en_us', value: Failed to delete record } }
    - { fields: { placeholder: '@field_maintenance_widget_cascading_select_error_failed_to_get_records', language: '@language_en_us', value: Failed to get records } }
    - { fields: { placeholder: '@field_maintenance_widget_cascading_select_success_deleted_record', language: '@language_en_us', value: Deleted record } }
    - { fields: { placeholder: '@field_maintenance_widget_cascading_select_title', language: '@language_en_us', value: Cascading Select Widget } }
    - { fields: { placeholder: '@form_designer_action_search', language: '@language_en_us', value: Action Search } }
    - { fields: { placeholder: '@form_designer_default_form', language: '@language_en_us', value: "This is the default {{module}} form" } }
    - { fields: { placeholder: '@form_tabs_action_subform', language: '@language_en_us', value: Action Subform } }
    - { fields: { placeholder: '@form_tabs_fields_&_sections', language: '@language_en_us', value: Fields & Sections } }
    - { fields: { placeholder: '@form_tabs_form_details', language: '@language_en_us', value: Form Details } }
    - { fields: { placeholder: '@form_tabs_triggers', language: '@language_en_us', value: Triggers } }
    - { fields: { placeholder: '@form_fields_and_sections_actions_field_help', language: '@language_en_us', value: Field Help } }
    - { fields: { placeholder: '@form_fields_and_sections_actions_field_information', language: '@language_en_us', value: Field Information } }
    - { fields: { placeholder: '@form_fields_and_sections_actions_move_field', language: '@language_en_us', value: Move to... } }
    - { fields: { placeholder: '@form_fields_and_sections_error_search_fields', language: '@language_en_us', value: Something went wrong when loading the Section Search Fields } }
    - { fields: { placeholder: '@form_fields_and_sections_error_remove_fields', language: '@language_en_us', value: Can not delete the field without saving it to the form } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_add_image', language: '@language_en_us', value: Add Image } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_label', language: '@language_en_us', value: Help Image } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_help_image_remove_image', language: '@language_en_us', value: Remove Image } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_help_text_label', language: '@language_en_us', value: Help Text } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_new_window_label', language: '@language_en_us', value: 'New Window?' } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_title', language: '@language_en_us', value: Field Help } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_web_link_label', language: '@language_en_us', value: Web Link } }
    - { fields: { placeholder: '@form_fields_and_sections_field_information_label', language: '@language_en_us', value: Field Information } }
    - { fields: { placeholder: '@form_fields_and_sections_field_information_title', language: '@language_en_us', value: Field Information } }
    - { fields: { placeholder: '@form_fields_and_sections_move_field_title', language: '@language_en_us', value: Move Field } }
    - { fields: { placeholder: '@form_subform_assignment_heading_title', language: '@language_en_us', value: "{{formType}}: {{module}}" } }
    - { fields: { placeholder: '@form_subform_not_assigned', language: '@language_en_us', value: "No action subform assigned" } }
    - { fields: { placeholder: '@form_tabs_subform_assignment_title', language: '@language_en_us', value: Form Assignment } }
    - { fields: { placeholder: '@forms_modal_move_field_fields_target', language: '@language_en_us', value: 'Target Section:' } }
    - { fields: { placeholder: '@forms_modal_move_field_fields_target_option', language: '@language_en_us', value: "Section {{index}}" } }
    - { fields: { placeholder: '@forms_modal_move_field_fields_target_select', language: '@language_en_us', value: Select a Target Section } }
    - { fields: { placeholder: '@forms_modal_move_field_submit', language: '@language_en_us', value: Move Field } }
    - { fields: { placeholder: '@forms_modal_move_field_title', language: '@language_en_us', value: Move Field } }
    - { fields: { placeholder: '@forms_widget_cascading_select_error_failed_to_get_records', language: '@language_en_us', value: Failed to get records } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_type_core', language: '@language_en_us', value: 'Core Field' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_type_field', language: '@language_en_us', value: 'Field' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_type_custom', language: '@language_en_us', value: 'Custom Field' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_type_widget', language: '@language_en_us', value: 'Widget Field' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_type_standard', language: '@language_en_us', value: 'Standard Field' } }
    - { fields: { placeholder: '@placeholder_forms_fields_headers_title', language: '@language_en_us', value: 'Title' } }
    - { fields: { placeholder: '@placeholder_forms_fields_headers_label', language: '@language_en_us', value: 'Label' } }
    - { fields: { placeholder: '@placeholder_forms_fields_headers_type', language: '@language_en_us', value: 'Type' } }
    - { fields: { placeholder: '@placeholder_forms_fields_headers_field_type', language: '@language_en_us', value: 'Field Type' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_short_text', language: '@language_en_us', value: 'Short Text' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_long_text', language: '@language_en_us', value: 'Long Text' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_date', language: '@language_en_us', value: 'Date' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_yes_no', language: '@language_en_us', value: 'Yes / No' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_range', language: '@language_en_us', value: 'Range' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_radio_buttons', language: '@language_en_us', value: 'Radio Buttons' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_checkboxes', language: '@language_en_us', value: 'Checkboxes' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_record_search', language: '@language_en_us', value: 'Record Search' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_attachments', language: '@language_en_us', value: 'Attachments' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_file', language: '@language_en_us', value: 'File' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_relationships', language: '@language_en_us', value: 'Relationships' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_investigation_levels', language: '@language_en_us', value: 'Investigation Levels' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_dropdown_single', language: '@language_en_us', value: 'Dropdown (Single)' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_dropdown_multiple', language: '@language_en_us', value: 'Dropdown (Multiple)' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_contact_number_filter', language: '@language_en_us', value: 'Contact Number Filter' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_cascading_select', language: '@language_en_us', value: 'Cascading Select' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_heatmap', language: '@language_en_us', value: 'Heatmap' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_acl_limited_relationship', language: '@language_en_us', value: 'ACL Limited Relationship' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_relationship', language: '@language_en_us', value: 'Relationship' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_hidden', language: '@language_en_us', value: 'Hidden' } }
    - { fields: { placeholder: '@placeholder_forms_fields_field_control_types_display', language: '@language_en_us', value: 'Display' } }
    - { fields: { placeholder: '@placeholder_forms_field_response_options_header', language: '@language_en_us', value: 'Response Options' } }
    - { fields: { placeholder: '@placeholder_forms_field_type', language: '@language_en_us', value: 'Type' } }
    - { fields: { placeholder: '@placeholder_forms_field_field_title', language: '@language_en_us', value: 'Field Title' } }
    - { fields: { placeholder: '@placeholder_forms_field_label', language: '@language_en_us', value: 'Label' } }
    - { fields: { placeholder: '@placeholder_forms_field_field_type_label', language: '@language_en_us', value: 'Field Type' } }
    - { fields: { placeholder: '@placeholder_forms_field_field_type_select', language: '@language_en_us', value: 'Select a Field Type' } }
    - { fields: { placeholder: '@placeholder_forms_field_input_type_label', language: '@language_en_us', value: 'Input Type' } }
    - { fields: { placeholder: '@placeholder_forms_field_input_type_select', language: '@language_en_us', value: 'Select an Input Type' } }
    - { fields: { placeholder: '@placeholder_forms_field_response_options_option_id', language: '@language_en_us', value: 'Option ID' } }
    - { fields: { placeholder: '@placeholder_forms_field_response_options_select', language: '@language_en_us', value: 'Select' } }
    - { fields: { placeholder: '@placeholder_forms_field_response_options_add', language: '@language_en_us', value: 'Add' } }
    - { fields: { placeholder: '@placeholder_forms_field_save', language: '@language_en_us', value: 'Save Field' } }
    - { fields: { placeholder: '@placeholder_forms_field_loading_singular', language: '@language_en_us', value: 'Loading Field' } }
    - { fields: { placeholder: '@placeholder_forms_field_loading_plural', language: '@language_en_us', value: 'Loading Fields' } }
    - { fields: { placeholder: '@placeholder_forms_field_field_setup', language: '@language_en_us', value: 'Field Setup' } }
    - { fields: { placeholder: '@placeholder_forms_field_form_types', language: '@language_en_us', value: 'Form Types' } }
    - { fields: { placeholder: '@placeholder_forms_form_types_all', language: '@language_en_us', value: 'All Form Types' } }
    - { fields: { placeholder: '@placeholder_forms_field_input_type_dropdown', language: '@language_en_us', value: 'Dropdown' } }
    - { fields: { placeholder: '@placeholder_forms_field_selection_multiple', language: '@language_en_us', value: 'Multiple Responses' } }
    - { fields: { placeholder: '@placeholder_forms_field_selection_single', language: '@language_en_us', value: 'Single Response' } }
    - { fields: { placeholder: '@form_fields_and_sections_field_help_help_link_validation_error', language: '@language_en_us', value: 'The URL must begin with http:// or https://' } }
    - { fields: { placeholder: '@form_fields_error_data_source_item_invalid_parent', language: '@language_en_us', value: 'Option must be a child of the selected parent' } }
    - { fields: { placeholder: '@placeholder_forms_fields_cascading_select_new_root_option', language: '@language_en_us', value: 'New Root Option' } }
