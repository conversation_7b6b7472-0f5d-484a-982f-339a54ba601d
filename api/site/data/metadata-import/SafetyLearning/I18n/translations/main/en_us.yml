entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@safety_learnings_edit_linked_duplicate', language: '@language_en_us', value: Record already linked to Learning } }
    - { fields: { placeholder: '@safety_learnings_form_type_filter', language: '@language_en_us', value: Learnings Filter Form type } }
    - { fields: { placeholder: '@safety_learnings_not_found', language: '@language_en_us', value: Safety Learning doesn't exist } }
    - { fields: { placeholder: '@safety_learnings_dashboard_last_month', language: '@language_en_us', value: Last Month } }
    - { fields: { placeholder: '@safety_learnings_dashboard_last_week', language: '@language_en_us', value: Last Week } }
    - { fields: { placeholder: '@safety_learnings_dashboard_most_viewed', language: '@language_en_us', value: Most Viewed } }
    - { fields: { placeholder: '@safety_learnings_dashboard_most_voted', language: '@language_en_us', value: Most Voted } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_link_module', language: '@language_en_us', value: Link Module } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_link_note', language: '@language_en_us', value: Link Note } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_link_record', language: '@language_en_us', value: Link Record } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_module_link_id', language: '@language_en_us', value: Module Link ID } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_select_module', language: '@language_en_us', value: Select Module } }
    - { fields: { placeholder: '@safety_learnings_edit_form_record_title', language: '@language_en_us', value: Linked Records } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_details_title', language: '@language_en_us', value: Source Details } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_email', language: '@language_en_us', value: Email } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_event_id', language: '@language_en_us', value: Source Event ID } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_event_type', language: '@language_en_us', value: Source Event Type } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_expired_by', language: '@language_en_us', value: Expired by } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_expired_date', language: '@language_en_us', value: Expired Date } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_forenames', language: '@language_en_us', value: First Name } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_reason', language: '@language_en_us', value: Reason } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_rejected_by', language: '@language_en_us', value: Rejected By } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_rejected_date', language: '@language_en_us', value: Rejected Date } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_surname', language: '@language_en_us', value: Last name } }
    - { fields: { placeholder: '@safety_learnings_edit_form_source_title', language: '@language_en_us', value: Author Details } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_deleted_error', language: '@language_en_us', value: Error deleting linked record } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_deleted_success', language: '@language_en_us', value: Linked record deleted successfully } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_table_affected', language: '@language_en_us', value: Affected } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_table_date', language: '@language_en_us', value: Date } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_table_description', language: '@language_en_us', value: Description } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_table_record_id', language: '@language_en_us', value: Record ID } }
    - { fields: { placeholder: '@safety_learnings_filter_field_adviser_label', language: '@language_en_us', value: Adviser } }
    - { fields: { placeholder: '@safety_learnings_filter_field_adviser_title', language: '@language_en_us', value: Adviser of the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_author_label', language: '@language_en_us', value: Author } }
    - { fields: { placeholder: '@safety_learnings_filter_field_author_title', language: '@language_en_us', value: Author of the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_keywords_label', language: '@language_en_us', value: Keywords } }
    - { fields: { placeholder: '@safety_learnings_filter_field_keywords_title', language: '@language_en_us', value: Keywords for the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_location_label', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@safety_learnings_filter_field_location_title', language: '@language_en_us', value: Location for the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_publish_date_label', language: '@language_en_us', value: Published Date } }
    - { fields: { placeholder: '@safety_learnings_filter_field_publish_date_title', language: '@language_en_us', value: The date the Learning was published } }
    - { fields: { placeholder: '@safety_learnings_filter_field_reviewer_label', language: '@language_en_us', value: Reviewer } }
    - { fields: { placeholder: '@safety_learnings_filter_field_reviewer_title', language: '@language_en_us', value: Reviewer for the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_services_label', language: '@language_en_us', value: Service } }
    - { fields: { placeholder: '@safety_learnings_filter_field_services_title', language: '@language_en_us', value: Service for the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@safety_learnings_filter_field_status_title', language: '@language_en_us', value: The status of the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_field_title_label', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@safety_learnings_filter_field_title_title', language: '@language_en_us', value: Title of the Learning } }
    - { fields: { placeholder: '@safety_learnings_filter_form_title', language: '@language_en_us', value: Filter Learnings } }
    - { fields: { placeholder: '@safety_learnings_columns_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@safety_learnings_columns_author', language: '@language_en_us', value: Author } }
    - { fields: { placeholder: '@safety_learnings_columns_details', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@safety_learnings_columns_expires', language: '@language_en_us', value: Expires } }
    - { fields: { placeholder: '@safety_learnings_columns_published_date', language: '@language_en_us', value: Date Published } }
    - { fields: { placeholder: '@safety_learnings_columns_rating_helpful', language: '@language_en_us', value: Helpful } }
    - { fields: { placeholder: '@safety_learnings_columns_rating_unhelpful', language: '@language_en_us', value: Unhelpful } }
    - { fields: { placeholder: '@safety_learnings_columns_reason_for_rejection', language: '@language_en_us', value: Reason for Rejection } }
    - { fields: { placeholder: '@safety_learnings_columns_views', language: '@language_en_us', value: Views } }
    - { fields: { placeholder: '@safety_learnings_contact_role_handler', language: '@language_en_us', value: Handler } }
    - { fields: { placeholder: '@safety_learnings_contact_role_label', language: '@language_en_us', value: Safety Learnings Contact Roles } }
    - { fields: { placeholder: '@safety_learnings_contact_role_title', language: '@language_en_us', value: Safety Learnings Contact Roles } }
    - { fields: { placeholder: '@safety_learnings_create', language: '@language_en_us', value: Create Safety Learning } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_contact', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_contact_label', language: '@language_en_us', value: Contacts } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_details', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_details_label', language: '@language_en_us', value: Details } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_date', language: '@language_en_us', value: Expiry Date } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_date_label', language: '@language_en_us', value: Expiry Date } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_reason', language: '@language_en_us', value: Reason for Expiry } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_reason_label', language: '@language_en_us', value: Reason for Expiry } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_incident', language: '@language_en_us', value: Incident } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_incident_label', language: '@language_en_us', value: Incident } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_investigation', language: '@language_en_us', value: Investigations } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_investigation_label', language: '@language_en_us', value: Investigations } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_keywords', language: '@language_en_us', value: Keywords } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_keywords_label', language: '@language_en_us', value: Keywords } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_lessons_and_outcome', language: '@language_en_us', value: Key Lessons and Outcomes } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_lessons_and_outcome_label', language: '@language_en_us', value: Key Lessons and Outcomes } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_location', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_location_label', language: '@language_en_us', value: Locations } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_location_required', language: '@language_en_us', value: Please set at least one location to proceed } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_rejected_date', language: '@language_en_us', value: Rejected At } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_rejected_date_label', language: '@language_en_us', value: Rejected At } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_reason_for_rejection', language: '@language_en_us', value: Reason for rejection } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_reason_for_rejection_label', language: '@language_en_us', value: Reason for rejection } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author', language: '@language_en_us', value: 'Keep Author Anonymous?' } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_label', language: '@language_en_us', value: 'Keep Author Anonymous?' } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_message', language: '@language_en_us', value: 'Your author details will no longer be visible on this record, however this data will ONLY be available to system administrators to ensure the validity of this record and that no personal or sensitive information have been added anywhere in the record.' } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_services', language: '@language_en_us', value: Services } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_services_and_location_required', language: '@language_en_us', value: Please set both location and service to proceed } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_services_label', language: '@language_en_us', value: Services } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_services_required', language: '@language_en_us', value: At least one service must be set } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_status', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_status_label', language: '@language_en_us', value: Status } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_title', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@safety_learnings_default_form_fields_title_label', language: '@language_en_us', value: Title } }
    - { fields: { placeholder: '@safety_learnings_edit', language: '@language_en_us', value: Edit Safety Learning } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_access', language: '@language_en_us', value: Access Control } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_actions', language: '@language_en_us', value: Actions } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_actions_all_actions', language: '@language_en_us', value: All Actions } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_details', language: '@language_en_us', value: Learning Details } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_heading', language: '@language_en_us', value: "Safety Learning #{{id}}" } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_actions_my_actions', language: '@language_en_us', value: My Actions } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_record', language: '@language_en_us', value: Linked Records } }
    - { fields: { placeholder: '@safety_learnings_edit_nav_source', language: '@language_en_us', value: Source Details } }
    - { fields: { placeholder: '@safety_learnings_form_type_safety_learnings', language: '@language_en_us', value: Safety Learnings Form } }
    - { fields: { placeholder: '@safety_learnings_form_1', language: '@language_en_us', value: Main Form } }
    - { fields: { placeholder: '@safety_learnings_learning_created', language: '@language_en_us', value: Successfully created learning } }
    - { fields: { placeholder: '@safety_learnings_learning_edited', language: '@language_en_us', value: Safety Learning record edited successfully } }
    - { fields: { placeholder: '@safety_learnings_module_title', language: '@language_en_us', value: Safety Learnings } }
    - { fields: { placeholder: '@safety_learnings_nav_admin_permissions', language: '@language_en_us', value: Permissions } }
    - { fields: { placeholder: '@safety_learnings_nav_dashboard', language: '@language_en_us', value: Published Learnings } }
    - { fields: { placeholder: '@safety_learnings_nav_my_learnings', language: '@language_en_us', value: My Learnings } }
    - { fields: { placeholder: '@safety_learnings_nav_new', language: '@language_en_us', value: Create New Learning } }
    - { fields: { placeholder: '@safety_learnings_nav_unpublished_learnings', language: '@language_en_us', value: Unpublished Learnings } }
    - { fields: { placeholder: '@safety_learnings_notice_expired', language: '@language_en_us', value: "This learning was expired by {{user}} - {{job_title}} on {{date}}." } }
    - { fields: { placeholder: '@safety_learnings_notice_rejected', language: '@language_en_us', value: "This learning was rejected by {{user}} - {{job_title}} on {{date}}." } }
    - { fields: { placeholder: '@safety_learnings_notice_update_expires_date', language: '@language_en_us', value: "Expiry date has passed, this must be set sometime in the future" } }
    - { fields: { placeholder: '@safety_learnings_plural', language: '@language_en_us', value: Safety Learnings } }
    - { fields: { placeholder: '@safety_learnings_search', language: '@language_en_us', value: Search Safety Learnings } }
    - { fields: { placeholder: '@safety_learnings_section_safety_learnings', language: '@language_en_us', value: Safety Learnings Details } }
    - { fields: { placeholder: '@safety_learnings_singular', language: '@language_en_us', value: Safety Learning } }
    - { fields: { placeholder: '@safety_learnings_records', language: '@language_en_us', value: Records } }
    - { fields: { placeholder: '@safety_learnings_other', language: '@language_en_us', value: Other } }
    - { fields: { placeholder: '@safety_learnings_status_draft', language: '@language_en_us', value: Draft } }
    - { fields: { placeholder: '@safety_learnings_status_expired', language: '@language_en_us', value: Expired } }
    - { fields: { placeholder: '@safety_learnings_status_published', language: '@language_en_us', value: Published } }
    - { fields: { placeholder: '@safety_learnings_status_rejected', language: '@language_en_us', value: Rejected } }
    - { fields: { placeholder: '@safety_learnings_status_submitted_for_review', language: '@language_en_us', value: Submitted For Review } }
    - { fields: { placeholder: '@safety_learnings_status_under_review', language: '@language_en_us', value: Under Review } }
    - { fields: { placeholder: '@safety_learnings_update_expiry_date', language: '@language_en_us', value: Please first update the 'Expiry Date' to a date in the future before attempting to change the status of an expired learning. } }
    - { fields: { placeholder: '@safety_learnings_voted_success', language: '@language_en_us', value: Successfully voted } }
    - { fields: { placeholder: '@safety_learnings_notice_anonymous_author_banner', language: '@language_en_us', value: This record was reported anonymously } }
    - { fields: { placeholder: '@safety_learnings_edit_linked_added_success', language: '@language_en_us', value: 'Linked record added successfully' } }
