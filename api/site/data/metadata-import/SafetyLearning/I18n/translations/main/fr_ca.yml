entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@safety_learnings_module_title', language: '@language_fr_ca', value: 'Apprentissages de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_singular', language: '@language_fr_ca', value: 'Apprentissage de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_plural', language: '@language_fr_ca', value: 'Apprentissages de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_search', language: '@language_fr_ca', value: 'Rechercher des apprentissages de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_create', language: '@language_fr_ca', value: 'Créer un apprentissage de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_edit', language: '@language_fr_ca', value: 'Modifier un apprentissage de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_records', language: '@language_fr_ca', value: 'Enregistrements' } }
  - { fields: { placeholder: '@safety_learnings_other', language: '@language_fr_ca', value: 'Autre' } }
  - { fields: { placeholder: '@safety_learnings_nav_dashboard', language: '@language_fr_ca', value: 'Apprentissages publiés' } }
  - { fields: { placeholder: '@safety_learnings_nav_my_learnings', language: '@language_fr_ca', value: 'Mes apprentissages' } }
  - { fields: { placeholder: '@safety_learnings_nav_unpublished_learnings', language: '@language_fr_ca', value: 'Apprentissages non publiés' } }
  - { fields: { placeholder: '@safety_learnings_nav_new', language: '@language_fr_ca', value: 'Créer un nouvel apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_nav_admin_permissions', language: '@language_fr_ca', value: Permissions } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_heading', language: '@language_fr_ca', value: 'Apprentissage de la sécurité Nº {{id}}' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_details', language: '@language_fr_ca', value: 'Détails de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_source', language: '@language_fr_ca', value: 'Détails de la source' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_record', language: '@language_fr_ca', value: 'Dossiers liés' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_access', language: '@language_fr_ca', value: 'Contrôle d''accès' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_actions_my_actions', language: '@language_fr_ca', value: 'Mes actions' } }
  - { fields: { placeholder: '@safety_learnings_edit_nav_actions_all_actions', language: '@language_fr_ca', value: 'Toutes actions' } }
  - { fields: { placeholder: '@safety_learnings_status_draft', language: '@language_fr_ca', value: Ébauche } }
  - { fields: { placeholder: '@safety_learnings_status_submitted_for_review', language: '@language_fr_ca', value: 'Soumis(e) à la révision' } }
  - { fields: { placeholder: '@safety_learnings_status_rejected', language: '@language_fr_ca', value: Rejeté } }
  - { fields: { placeholder: '@safety_learnings_status_expired', language: '@language_fr_ca', value: Expiré } }
  - { fields: { placeholder: '@safety_learnings_status_under_review', language: '@language_fr_ca', value: 'En cours de révision' } }
  - { fields: { placeholder: '@safety_learnings_status_published', language: '@language_fr_ca', value: Publié } }
  - { fields: { placeholder: '@safety_learnings_columns_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@safety_learnings_columns_author', language: '@language_fr_ca', value: Auteur } }
  - { fields: { placeholder: '@safety_learnings_columns_reason_for_rejection', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@safety_learnings_columns_expires', language: '@language_fr_ca', value: Expire } }
  - { fields: { placeholder: '@safety_learnings_columns_published_date', language: '@language_fr_ca', value: 'Date de publication' } }
  - { fields: { placeholder: '@safety_learnings_columns_views', language: '@language_fr_ca', value: Vues } }
  - { fields: { placeholder: '@safety_learnings_columns_actions', language: '@language_fr_ca', value: Actions } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_title', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_status', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_reason_for_rejection', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_reason_for_rejection_label', language: '@language_fr_ca', value: 'Motif du rejet' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_details', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_details_label', language: '@language_fr_ca', value: Détails } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_lessons_and_outcome', language: '@language_fr_ca', value: 'Leçons clés et résultats' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_lessons_and_outcome_label', language: '@language_fr_ca', value: 'Leçons clés et résultats' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_keywords', language: '@language_fr_ca', value: Mots-clés } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_keywords_label', language: '@language_fr_ca', value: Mots-clés } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_incident', language: '@language_fr_ca', value: Incident } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_incident_label', language: '@language_fr_ca', value: Incident } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_investigation', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_investigation_label', language: '@language_fr_ca', value: Enquêtes } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author', language: '@language_fr_ca', value: 'Conserver l''auteur anonyme?' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_label', language: '@language_fr_ca', value: 'Conserver l''auteur anonyme?' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_anonymous_author_message', language: '@language_fr_ca', value: 'Vos détails d''auteur ne seront plus visibles sur ce dossier, mais ces données seront UNIQUEMENT disponibles pour les administrateurs système pour garantir la validité de ce dossier et qu''aucune information personnelle ou sensible n''a été ajoutée à quelque endroit que ce soit dans le dossier.' } }
  - { fields: { placeholder: '@safety_learnings_form_1', language: '@language_fr_ca', value: 'Formulaire principal' } }
  - { fields: { placeholder: '@safety_learnings_form_type_safety_learnings', language: '@language_fr_ca', value: 'Formulaire d''apprentissages de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_section_safety_learnings', language: '@language_fr_ca', value: 'Détails des apprentissages de la sécurité' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_services', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_services_label', language: '@language_fr_ca', value: Services } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_location', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_location_label', language: '@language_fr_ca', value: Emplacements } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_location_required', language: '@language_fr_ca', value: 'Veuillez définir au moins un emplacement pour continuer' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_contact', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_contact_label', language: '@language_fr_ca', value: Contacts } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_rejected_date', language: '@language_fr_ca', value: 'Rejeté à' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_rejected_date_label', language: '@language_fr_ca', value: 'Rejeté à' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_reason', language: '@language_fr_ca', value: 'Motif de l''expiration' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_reason_label', language: '@language_fr_ca', value: 'Motif de l''expiration' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_date', language: '@language_fr_ca', value: 'Date d''expiration' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_expiry_date_label', language: '@language_fr_ca', value: 'Date d''expiration' } }
  - { fields: { placeholder: '@safety_learnings_columns_rating_helpful', language: '@language_fr_ca', value: Utile } }
  - { fields: { placeholder: '@safety_learnings_columns_rating_unhelpful', language: '@language_fr_ca', value: Inutile } }
  - { fields: { placeholder: '@safety_learnings_learning_created', language: '@language_fr_ca', value: 'Apprentissage créé avec succès' } }
  - { fields: { placeholder: '@safety_learnings_learning_edited', language: '@language_fr_ca', value: 'Dossier d''apprentissage de la sécurité modifié avec succès' } }
  - { fields: { placeholder: '@safety_learnings_form_type_filter', language: '@language_fr_ca', value: 'Type de formulaire de filtrage d''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_title_title', language: '@language_fr_ca', value: 'Titre de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_title_label', language: '@language_fr_ca', value: Titre } }
  - { fields: { placeholder: '@safety_learnings_filter_field_keywords_title', language: '@language_fr_ca', value: 'Mots-clés de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_keywords_label', language: '@language_fr_ca', value: Mots-clés } }
  - { fields: { placeholder: '@safety_learnings_filter_field_location_title', language: '@language_fr_ca', value: 'Emplacement de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_location_label', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@safety_learnings_filter_field_services_title', language: '@language_fr_ca', value: 'Service pour l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_services_label', language: '@language_fr_ca', value: Service } }
  - { fields: { placeholder: '@safety_learnings_filter_field_reviewer_title', language: '@language_fr_ca', value: 'Réviseur de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_reviewer_label', language: '@language_fr_ca', value: Examinateur } }
  - { fields: { placeholder: '@safety_learnings_filter_field_author_title', language: '@language_fr_ca', value: 'Auteur de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_author_label', language: '@language_fr_ca', value: Auteur } }
  - { fields: { placeholder: '@safety_learnings_filter_field_adviser_title', language: '@language_fr_ca', value: 'Conseiller de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_adviser_label', language: '@language_fr_ca', value: Conseiller } }
  - { fields: { placeholder: '@safety_learnings_filter_field_publish_date_title', language: '@language_fr_ca', value: 'La date de publication de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_publish_date_label', language: '@language_fr_ca', value: 'Date de publication' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_status_title', language: '@language_fr_ca', value: 'L''état de l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_filter_field_status_label', language: '@language_fr_ca', value: État } }
  - { fields: { placeholder: '@safety_learnings_voted_success', language: '@language_fr_ca', value: 'Vote effectué avec succès' } }
  - { fields: { placeholder: '@safety_learnings_update_expiry_date', language: '@language_fr_ca', value: "Veuillez d'abord mettre à jour la «\_Date d'expiration\_» à une date ultérieure avant de tenter de modifier le statut d'un apprentissage expiré." } }
  - { fields: { placeholder: '@safety_learnings_filter_form_title', language: '@language_fr_ca', value: 'Filtrer les apprentissages' } }
  - { fields: { placeholder: '@safety_learnings_notice_rejected', language: '@language_fr_ca', value: 'Cet apprentissage a été rejeté par {{user}} - {{job_title}} le {{date}}.' } }
  - { fields: { placeholder: '@safety_learnings_notice_expired', language: '@language_fr_ca', value: 'Cet apprentissage est considéré comme expiré par {{user}} - {{job_title}} le {{date}}.' } }
  - { fields: { placeholder: '@safety_learnings_notice_update_expires_date', language: '@language_fr_ca', value: 'La date d''expiration est révolue; elle doit être indiquée à un moment du futur' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_title', language: '@language_fr_ca', value: 'Dossiers liés' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_link_module', language: '@language_fr_ca', value: 'Relier le module' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_select_module', language: '@language_fr_ca', value: 'Sélectionner un module' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_module_link_id', language: '@language_fr_ca', value: 'ID du lien du module' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_link_note', language: '@language_fr_ca', value: 'Lier une note' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_record_link_record', language: '@language_fr_ca', value: 'Lier un dossier' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_title', language: '@language_fr_ca', value: 'Détails de l''auteur' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_forenames', language: '@language_fr_ca', value: Prénoms } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_surname', language: '@language_fr_ca', value: 'Nom de famille' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_email', language: '@language_fr_ca', value: 'Adresse courriel' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_details_title', language: '@language_fr_ca', value: 'Détails de la source' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_event_type', language: '@language_fr_ca', value: 'Type d''événement source' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_event_id', language: '@language_fr_ca', value: 'ID d''événement source' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_rejected_by', language: '@language_fr_ca', value: 'Rejeté par' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_rejected_date', language: '@language_fr_ca', value: 'Date de rejet' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_expired_by', language: '@language_fr_ca', value: 'Expiré par' } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_reason', language: '@language_fr_ca', value: Motif } }
  - { fields: { placeholder: '@safety_learnings_edit_form_source_expired_date', language: '@language_fr_ca', value: 'Date d''expiration' } }
  - { fields: { placeholder: '@safety_learnings_dashboard_last_week', language: '@language_fr_ca', value: 'Semaine dernière' } }
  - { fields: { placeholder: '@safety_learnings_dashboard_last_month', language: '@language_fr_ca', value: 'Mois dernier' } }
  - { fields: { placeholder: '@safety_learnings_dashboard_most_voted', language: '@language_fr_ca', value: 'Le plus voté' } }
  - { fields: { placeholder: '@safety_learnings_dashboard_most_viewed', language: '@language_fr_ca', value: 'Le plus vu' } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_table_record_id', language: '@language_fr_ca', value: 'ID d''enregistrement' } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_table_affected', language: '@language_fr_ca', value: Affecté } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_table_date', language: '@language_fr_ca', value: Date } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_table_description', language: '@language_fr_ca', value: Description } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_added_success', language: '@language_fr_ca', value: 'Dossier lié ajouté avec succès' } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_deleted_success', language: '@language_fr_ca', value: 'Dossier lié supprimé avec succès' } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_deleted_error', language: '@language_fr_ca', value: 'Erreur à la suppression du dossier lié' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_services_required', language: '@language_fr_ca', value: 'Il faut définir au moins un service' } }
  - { fields: { placeholder: '@safety_learnings_default_form_fields_services_and_location_required', language: '@language_fr_ca', value: 'Veuillez définir à la fois l''emplacement et le service pour continuer' } }
  - { fields: { placeholder: '@safety_learnings_edit_linked_duplicate', language: '@language_fr_ca', value: 'Dossier déjà lié à l''apprentissage' } }
  - { fields: { placeholder: '@safety_learnings_not_found', language: '@language_fr_ca', value: 'L''apprentissage de la sécurité n''existe pas' } }
  - { fields: { placeholder: '@safety_learnings_contact_role_title', language: '@language_fr_ca', value: 'Rôles des contacts de l''apprentissage de la sécurités' } }
  - { fields: { placeholder: '@safety_learnings_contact_role_label', language: '@language_fr_ca', value: 'Rôles des contacts de l''apprentissage de la sécurités' } }
  - { fields: { placeholder: '@safety_learnings_contact_role_handler', language: '@language_fr_ca', value: Responsable } }
  - { fields: { placeholder: '@safety_learnings_notice_anonymous_author_banner', language: '@language_fr_ca', value: 'Ce dossier a été signalé de façon anonyme' } }
