entityClass: I18n\Entity\Placeholder
priority: 10
data:

# Core
-
  fields:
    placeholder: SAFETY_LEARNINGS.MODULE_TITLE
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_module_title
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.BACK_TO_DASHBOARD
    pointer: NAV.BACK_TO_DASHBOARD
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_back_to_dashboard
-
  fields:
    placeholder: SAFETY_LEARNINGS.SINGULAR
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_singular
-
  fields:
    placeholder: SAFETY_LEARNINGS.PLURAL
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_plural
-
  fields:
    placeholder: SAFETY_LEARNINGS.SEARCH
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_search
-
  fields:
    placeholder: SAFETY_LEARNINGS.CREATE
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_create
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_edit
-
  fields:
    placeholder: SAFETY_LEARNINGS.RECORDS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_records
-
  fields:
    placeholder: SAFETY_LEARNINGS.OTHER
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_other
# Navigation
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.DASHBOARD
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_nav_dashboard
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.MY_LEARNINGS
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_nav_my_learnings
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.UNPUBLISHED_LEARNINGS
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_nav_unpublished_learnings
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.NEW
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_nav_new
-
  fields:
    placeholder: SAFETY_LEARNINGS.NAV.ADMIN.PERMISSIONS
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_nav_admin_permissions
# Edit Nav
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.HEADING
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_heading
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.DETAILS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_details
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.SOURCE
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_source
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.RECORD
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_record
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.ACCESS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_access
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.ACTIONS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_actions
-
  fields:
    placeholder: SAFETY_LEARNINGS.EDIT_NAV.MY_ACTIONS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_actions_my_actions
-
  fields:
      placeholder: SAFETY_LEARNINGS.EDIT_NAV.ALL_ACTIONS
      domains:
        -
          domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_nav_actions_all_actions
# Status
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.DRAFT
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_draft
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.SUBMITTED_FOR_REVIEW
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_submitted_for_review
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.REJECTED
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_rejected
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.EXPIRED
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_expired
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.UNDER_REVIEW
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_under_review
-
  fields:
    placeholder: SAFETY_LEARNINGS.STATUS.PUBLISHED
    domains:
    -
      domain: '@domain_safety_learnings'
  ref: safety_learnings_status_published

# Listing Columns
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.ID
    pointer: COMMON.ID
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_id
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.TITLE
    pointer: COMMON.TITLE
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_title
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.DETAILS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_details
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.LOCATION
    pointer: LOCATIONS.SINGULAR
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_location
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.STATUS
    pointer: COMMON.STATUS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_status
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.AUTHOR
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_author
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.REASON_FOR_REJECTION
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_reason_for_rejection
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.DESCRIPTION
    pointer: COMMON.DESCRIPTION
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_description
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.EXPIRES
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_expires
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.PUBLISHED_DATE
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_published_date
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.VIEWS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_views
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.ACTIONS
    domains:
      -
        domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_actions
# From
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_title
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.TITLE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_title_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.STATUS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_status
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.STATUS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_status_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.REJECTION_REASON
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_reason_for_rejection
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.REJECTION_REASON.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_reason_for_rejection_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.DESCRIPTION
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_description
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.DESCRIPTION.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_description_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.DETAILS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_details
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.DETAILS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_details_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.LESSONS_AND_OUTCOME
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_lessons_and_outcome
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.LESSONS_AND_OUTCOME.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_lessons_and_outcome_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.KEYWORDS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_keywords
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.KEYWORDS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_keywords_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.INCIDENT
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_incident
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.INCIDENT.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_incident_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.FEEDBACK
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_feedback
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.FEEDBACK.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_feedback_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.INVESTIGATION
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_investigation
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.INVESTIGATION.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_investigation_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.ANONYMOUS_AUTHOR
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_anonymous_author
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.ANONYMOUS_AUTHOR.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_anonymous_author_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.ANONYMOUS_AUTHOR.YES
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_anonymous_author_yes
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.ANONYMOUS_AUTHOR.NO
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_anonymous_author_no
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.ANONYMOUS_AUTHOR.MESSAGE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_anonymous_author_message
-
  fields:
    placeholder: SAFETY_LEARNINGS.FORMS.FORM1
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_form_1
-
  fields:
    placeholder: SAFETY_LEARNINGS.FORM_TYPE.SAFETY_LEARNINGS
    domains:
      - domain: '@domain_safety_learnings'
      - domain: '@domain_form_types'
  ref: safety_learnings_form_type_safety_learnings
-
  fields:
    placeholder: SAFETY_LEARNINGS.SECTION.SAFETY_LEARNINGS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_section_safety_learnings
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.SERVICES
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_services
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.SERVICES.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_services_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.LOCATION
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_location
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.LOCATION.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_location_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.LOCATIONS.REQUIRED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_location_required
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.CONTACT
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_contact
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.CONTACT.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_contact_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.REJECTED_DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_rejected_date
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.REJECTED_DATE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_rejected_date_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.EXPIRY_REASON
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_expiry_reason
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.EXPIRY_REASON.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_expiry_reason_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.EXPIRY_DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_expiry_date
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.EXPIRY_DATE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_expiry_date_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.RATING.HELPFUL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_rating_helpful
-
  fields:
    placeholder: SAFETY_LEARNINGS.COLUMNS.RATING.UNHELPFUL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_columns_rating_unhelpful
-
  fields:
    placeholder: SAFETY_LEARNINGS.LEARNING_CREATED
    domains:
    - domain: '@domain_safety_learnings'
  ref: safety_learnings_learning_created
-
  fields:
    placeholder: SAFETY_LEARNINGS.LEARNING_EDITED
    domains:
    - domain: '@domain_safety_learnings'
  ref: safety_learnings_learning_edited
-
  fields:
    placeholder: LEARNING.FORM_TYPE.LEARNING_FILTER_FORM
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_form_type_filter
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.TITLE.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_title_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.TITLE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_title_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.KEYWORDS.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_keywords_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.KEYWORDS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_keywords_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.LOCATIONS.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_location_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.LOCATIONS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_location_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.SERVICES.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_services_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.SERVICES.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_services_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.REVIEWER.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_reviewer_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.REVIEWER.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_reviewer_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.AUTHOR.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_author_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.AUTHOR.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_author_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.ADVISER.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_adviser_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.ADVISER.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_adviser_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.PUBLISH_DATE.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_publish_date_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.PUBLISH_DATE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_publish_date_label
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.STATUS.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_status_title
-
  fields:
    placeholder: LEARNINGS.FILTER.FORM.FIELDS.STATUS.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_field_status_label
- fields:
    placeholder: SAFETY_LEARNINGS.VOTED_SUCCESS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_voted_success
- fields:
    placeholder: SAFETY_LEARNINGS.UPDATE_EXPIRY_DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_update_expiry_date
- fields:
    placeholder: LEARNINGS.FILTER.FORM.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_filter_form_title
# Notifications
- fields:
    placeholder: SAFETY_LEARNINGS.NOTICE.REJECTED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_notice_rejected
- fields:
    placeholder: SAFETY_LEARNINGS.NOTICE.EXPIRED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_notice_expired
- fields:
    placeholder: SAFETY_LEARNINGS.NOTICE.UPDATE_EXPIRES_DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_notice_update_expires_date
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_title
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.LINK.MODULE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_link_module
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.SELECT.MODULE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_select_module
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.MODULE.LINK.ID
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_module_link_id
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.LINK.NOTE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_link_note
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.RECORD.LINK.RECORD
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_record_link_record
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_title
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.FORENAMES
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_forenames
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.SURNAME
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_surname
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.EMAIL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_email
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.DETAILS.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_details_title
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.EVENT.TYPE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_event_type
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.EVENT.ID
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_event_id
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.REJECTED.BY
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_rejected_by
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.REJECTED.DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_rejected_date
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.EXPIRED.BY
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_expired_by
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.REASON
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_reason
-
  fields:
    placeholder: LEARNINGS.EDIT.FORM.SOURCE.EXPIRED.DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_form_source_expired_date
-
  fields:
    placeholder: LEARNINGS.DASHBOARD.LAST.WEEK
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_dashboard_last_week
-
  fields:
    placeholder: LEARNINGS.DASHBOARD.LAST.MONTH
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_dashboard_last_month
-
  fields:
    placeholder: LEARNINGS.DASHBOARD.MOST.VOTED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_dashboard_most_voted
-
  fields:
    placeholder: LEARNINGS.DASHBOARD.MOST.VIEWED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_dashboard_most_viewed
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.TABLE.RECORD_ID
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_table_record_id
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.TABLE.AFFECTED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_table_affected
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.TABLE.DATE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_table_date
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.TABLE.DESCRIPTION
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_table_description
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.ADDED.SUCCESS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_added_success
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.DELETED.SUCCESS
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_deleted_success
-
  fields:
    placeholder: LEARNINGS.EDIT.LINKED.DELETED.ERROR
    domains:
    - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_deleted_error
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.SERVICES.REQUIRED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_services_required
-
  fields:
    placeholder: SAFETY_LEARNINGS.DEFAULT_FORM.FIELDS.SERVICES_AND_LOCATION.REQUIRED
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_default_form_fields_services_and_location_required
-
  fields:
    placeholder: LEARNING.EDIT.LINKED.DUPLICATE
    domains:
    - domain: '@domain_safety_learnings'
  ref: safety_learnings_edit_linked_duplicate
-
  fields:
    placeholder: LEARNING.NOT_FOUND
    domains:
    - domain: '@domain_safety_learnings'
  ref: safety_learnings_not_found
-
  fields:
    placeholder: SAFETY_LEARNINGS.CONTACT_ROLE.TITLE
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_contact_role_title
-
  fields:
    placeholder: SAFETY_LEARNINGS.CONTACT_ROLE.LABEL
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_contact_role_label
-
  fields:
    placeholder: SAFETY_LEARNINGS.CONTACT_ROLE.HANDLER
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_contact_role_handler
-
  fields:
    placeholder: SAFETY_LEARNINGS.NOTICE.ANONYMOUS_AUTHOR_BANNER
    domains:
      - domain: '@domain_safety_learnings'
  ref: safety_learnings_notice_anonymous_author_banner
