entityClass: I18n\Entity\Placeholder
priority: 10
data:
  # Sections
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.SECTIONS.FACILITY_DETAILS.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_details_name
  - fields:
      placeholder: BEN<PERSON><PERSON>RK.FACILITY_PROFILE.SECTIONS.FACILITY_INDICATORS_AND_RATIOS.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_indicators_and_ratios_name
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.SECTIONS.FACILITY_INDICATORS.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_indicators_name
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.SECTIONS.FACILITY_RATIOS.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_ratios_name
  - fields:
      placeholder: BEN<PERSON><PERSON>RK.FACILITY_PROFILE.SECTIONS.FACILITY_ACCREDITATION.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_accreditation_name
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.SECTIONS.FACILITY_CONTACTS.NAME
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_profile_sections_facility_contacts_name

  # Healthcare Sector
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.MINISTRY_OF_HEALTH
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_ministry_of_health
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.KING_FAISAL_SPECIALIST_HOSPITAL_AND_RESEARCH_CENTRE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_king_faisal_specialist_hospital_and_research_centre
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.NATIONAL_GUARD_HEALTH_AFFAIRS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_national_guard_health_affairs
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.JOHNS_HOPKINS_ARAMCO_HEALTHCARE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_johns_hopkins_aramco_healthcare
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.ROYAL_COMMISSION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_royal_commission
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.MINISTRY_OF_INTERIOR
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_ministry_of_interior
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.MINISTRY_OF_DEFENCE_AFFAIRS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_ministry_of_defence_affairs
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.SAUDI_RED_CRESCENT_AUTHORITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_saudi_red_crescent_authority
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.PRIVATE_SECTOR
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_private_sector
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.ROYAL_CLINICS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_royal_clinics
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.MILITARY_INTELLIGENCE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_military_intelligence
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.SAUDI_AIRLINES_CLINICS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_saudi_airlines_clinics
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.UNIVERSITY_HOSPITALS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_university_hospitals
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.SAUDI_CENTER_FOR_THE_ACCREDITATION_OF_HEALTH_FACILITIES
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_saudi_center_for_the_accreditation_of_health_facilities
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.GENERAL_AUTHORITY_FOR_FOOD_AND_DRUG_ADMINISTRATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_general_authority_for_food_and_drug_administration
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.SAUDI_HEALTH_COUNCIL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_saudi_health_council
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.COUNCIL_OF_COOPERATIVE_HEALTH_INSURANCE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_council_of_cooperative_health_insurance
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.SAUDI_COMMISSION_FOR_HEALTH_SPECIALTIES
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_saudi_commission_for_health_specialties
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.GENERAL_ORGANIZATION_FOR_SOCIAL_INSURANCE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_general_organization_for_social_insurance
  - fields:
      placeholder: BENCHMARK.HEALTHCARE_SECTOR.OTHERS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_healthcare_sector_others

  # Facility Category
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.GENERAL_FACILITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_general_facility
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.REFERRAL_FACILITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_referral_facility
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.AMBULATORY_FACILITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_ambulatory_facility
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.GP_FACILITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_gp_facility
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.DENTAL_FACILITY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_dental_facility
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.COMMUNITY_PHARMACY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_category_community_pharmacy
  # Facility Type
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.HOSPITAL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_hospital
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.PRIMARY_HEALTHCARE_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_primary_healthcare_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.MEDICAL_CENTER_POLYCLINIC
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_medical_center_polyclinic
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.PHYSIOTHERAPY_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_physiotherapy_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.NUTRITION_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_nutrition_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.REHABILITATION_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_rehabilitation_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.HOME_CARE_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_home_care_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.DIALYSIS_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_dialysis_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.PSYCHOLOGICAL_CONSULTING_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_psychological_consulting_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.ALTERNATIVE_MEDICINE_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_alternative_medicine_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.SPECIALISED_HEALTHCARE_CLINIC
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_specialised_healthcare_clinic
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.DENTAL_CLINIC
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_dental_clinic
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.REGIONAL_LAB
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_regional_lab
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.LABORATORY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_laboratory
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.RADIOLOGY_CENTER
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_radiology_center
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.DENTAL_LAB
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_dental_lab
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.COMMUNITY_PHARMACY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_community_pharmacy
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.OPTICS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_optics
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.AMBULANCE_STATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_type_ambulance_station

  # Facility Speciality
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.CARDIOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_cardiology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.DENTISTRY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_dentistry
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.DERMATOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_dermatology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.ENT
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_ent
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.FAMILY_MEDICINE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_family_medicine
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.GENERAL_SURGERY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_general_surgery
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.INTERNAL_MEDICINE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_internal_medicine
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.LABORATORY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_laboratory
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.NEUROLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_neurology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.NEUROSURGERY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_neurosurgery
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.OBSTETRICS_GYNAECOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_obstetrics_gynaecology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.ONCOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_oncology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.OPHTHALMOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_ophthalmology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.ORTHOPEDICS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_orthopedics
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.PEDIATRICS
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_pediatrics
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.PHYSIOTHERAPY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_physiotherapy
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.PLASTIC_SURGERY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_plastic_surgery
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.PSYCHIATRY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_psychiatry
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.RADIOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_radiology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.RHEUMATOLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_rheumatology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.UROLOGY
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_urology
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.VASCULAR
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_benchmark_facility_speciality_vascular

  # Is Facility Accredited
  - fields:
      placeholder: BENCHMARK.IS_FACILITY_ACCREDITED.YES
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_is_facility_accredited_yes
  - fields:
      placeholder: BENCHMARK.IS_FACILITY_ACCREDITED.NO
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_is_facility_accredited_no

  # Facility Regulators
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.CBHAI_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_cbhai_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.JCI_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_jci_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.CANADIAN_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_canadian_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.AUSTRALIAN_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_australian_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.CAP_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_cap_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.MAGNET_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_magnet_accreditation
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.CQC
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_cqc
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.ISO_CERTIFICATE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_iso_certificate
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.HCAC
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_hcac
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.HASP
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_hasp
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.HIPPA
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_hippa

  # Is Data Used for Benchmarking
  - fields:
      placeholder: BENCHMARK.DATA_USED_FOR_BENCHMARKING.YES
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_data_used_for_benchmarking_yes
  - fields:
      placeholder: BENCHMARK.DATA_USED_FOR_BENCHMARKING.NO
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: placeholder_data_used_for_benchmarking_no

  # Fields
  # Section 1
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.COUNTRY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_country_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.COUNTRY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_country_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.YEAR.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_year_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.YEAR.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_year_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.HEALTHCARE_SECTOR.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_healthcare_sector_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.HEALTHCARE_SECTOR.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_healthcare_sector_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_CATEGORY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_category_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_CATEGORY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_category_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PARENT_FACILITY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_parent_facility_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PARENT_FACILITY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_parent_facility_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_TYPE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_type_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_TYPE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_type_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_NAME.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_name_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_NAME.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_name_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_LOGO.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_logo_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_LOGO.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_logo_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_LOGO.INTRODUCTORY_TEXT
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_logo_introductory_text
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_SPECIALITY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_speciality_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACILITY_SPECIALITY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_facility_speciality_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.POPULATION_COVERED.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_population_covered_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.POPULATION_COVERED.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_population_covered_label

  # Section 2.1
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.YEARLY_BUDGET.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_yearly_budget_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.YEARLY_BUDGET.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_yearly_budget_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_BEDS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_beds_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_BEDS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_beds_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_STAFF.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_staff_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_STAFF.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_staff_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PHYSICIANS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_physicians_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PHYSICIANS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_physicians_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_NURSES.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_nurses_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_NURSES.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_nurses_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PHARMACISTS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_pharmacists_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PHARMACISTS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_pharmacists_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ALLIED_HEALTH.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_allied_health_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ALLIED_HEALTH.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_allied_health_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_DISCHARGES.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_discharges_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_DISCHARGES.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_discharges_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ADMISSIONS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_admissions_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ADMISSIONS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_admissions_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_OPD_VISITS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_opd_visits_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_OPD_VISITS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_opd_visits_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ER_CASES.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_er_cases_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_ER_CASES.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_er_cases_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_CRITICAL_CARE_ADMISSIONS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_critical_care_admissions_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_CRITICAL_CARE_ADMISSIONS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_critical_care_admissions_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PARAMEDICS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_paramedics_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PARAMEDICS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_paramedics_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_AMBULANCE_CARS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_ambulance_cars_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_AMBULANCE_CARS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_ambulance_cars_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PRESCRIPTIONS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_prescriptions_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PRESCRIPTIONS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_prescriptions_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PRESCRIBED_MEDS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_prescribed_meds_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_PRESCRIBED_MEDS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_prescribed_meds_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_SURGERIES.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_surgeries_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_SURGERIES.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_surgeries_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_DELIVERIES.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_deliveries_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NUM_DELIVERIES.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_num_deliveries_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NORMAL_DELIVERY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_normal_delivery_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NORMAL_DELIVERY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_normal_delivery_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.ABNORMAL_DELIVERY.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_abnormal_delivery_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.ABNORMAL_DELIVERY.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_abnormal_delivery_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.MATERNITY_RATIO.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_maternity_ratio_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.MATERNITY_RATIO.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_maternity_ratio_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.AVG_DAILY_BED_COST.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_avg_daily_bed_cost_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.AVG_DAILY_BED_COST.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_avg_daily_bed_cost_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BED_OCCUPANCY_RATE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_bed_occupancy_rate_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BED_OCCUPANCY_RATE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_bed_occupancy_rate_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LENGTH_OF_STAY_RATE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_length_of_stay_rate_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LENGTH_OF_STAY_RATE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_length_of_stay_rate_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BED_TURNOVER_RATE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_bed_turnover_rate_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BED_TURNOVER_RATE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_bed_turnover_rate_label

  # Section 2.2
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PHYSICIANS_PATIENTS_RATIO.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_physicians_patients_ratio_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PHYSICIANS_PATIENTS_RATIO.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_physicians_patients_ratio_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BEDS_PATIENTS_RATIO.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_beds_patients_ratio_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.BEDS_PATIENTS_RATIO.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_beds_patients_ratio_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NURSES_PATIENTS_RATIO.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_nurses_patients_ratio_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NURSES_PATIENTS_RATIO.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_nurses_patients_ratio_label

  # Section 3
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.IS_FACILITY_ACCREDITED.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_is_facility_accredited_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.IS_FACILITY_ACCREDITED.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_is_facility_accredited_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.ACCREDITATION_REGULATORS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_accreditation_regulators_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.ACCREDITATION_REGULATORS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_accreditation_regulators_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.DATA_USED_FOR_BENCHMARKING.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_data_used_for_benchmarking_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.DATA_USED_FOR_BENCHMARKING.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_data_used_for_benchmarking_label
  - fields:
      placeholder: BENCHMARK.FACILITY_REGULATORS.DATA_COMMISSION_ON_ACCREDITATION
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_regulators_data_commission_on_accreditation

  # Section 4
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.EMAIL_ADDRESS.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_email_address_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.EMAIL_ADDRESS.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_email_address_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PHONE_NUMBER.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_phone_number_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.PHONE_NUMBER.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_phone_number_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FAX.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_fax_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FAX.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_fax_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.WEBSITE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_website_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.WEBSITE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_website_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LOCATION.VALIDATION_ERROR
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_location_validation_error
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FORMS.LOCATION.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_location_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FORMS.LOCATION.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_profile_fields_location_label
  - fields:
      placeholder: BENCHMARK.FACILITY_SPECIALITY.NA
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_speciality_na_label
  - fields:
      placeholder: BENCHMARK.FACILITY_TYPE.NA
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_type_na_label
  - fields:
      placeholder: BENCHMARK.FACILITY_CATEGORY.NA
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_category_na_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NATIONAL_ORGANISATION_CODE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_national_organisation_code_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.NATIONAL_ORGANISATION_CODE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_national_organisation_code_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LATITUDE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_latitude_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LATITUDE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_latitude_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LONGITUDE.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_longitude_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.LONGITUDE.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_longitude_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.TWITTER_URL.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_twitter_url_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.TWITTER_URL.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_twitter_url_label
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACEBOOK_URL.TITLE
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_facebook_url_title
  - fields:
      placeholder: BENCHMARK.FACILITY_PROFILE.FIELDS.FACEBOOK_URL.LABEL
      type: 0
      domains:
        - domain: '@domain_benchmarks'
    ref: benchmark_facility_facebook_url_label
