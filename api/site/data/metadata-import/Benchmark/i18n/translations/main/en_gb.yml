entityClass: I18n\Entity\Translation
priority: 15
data:
  - fields:
      placeholder: '@placeholder_benchmark_module_title'
      language: '@language_en_gb'
      value: Benchmarking
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_create'
      language: '@language_en_gb'
      value: Create New Country Profile
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_create'
      language: '@language_en_gb'
      value: Create New Healthcare Facility Profile
  - fields:
      placeholder: '@placeholder_benchmark_columns_type'
      language: '@language_en_gb'
      value: Type
  - fields:
      placeholder: '@placeholder_benchmark_columns_country'
      language: '@language_en_gb'
      value: Country
  - fields:
      placeholder: '@placeholder_benchmark_columns_year'
      language: '@language_en_gb'
      value: Year
  - fields:
      placeholder: '@placeholder_benchmark_columns_id'
      language: '@language_en_gb'
      value: ID

  # Form Types
  - fields:
      placeholder: '@placeholder_benchmark_form_type_profile_country'
      language: '@language_en_gb'
      value: Country Profile
  - fields:
      placeholder: '@placeholder_benchmark_form_type_profile_healthcare'
      language: '@language_en_gb'
      value: Healthcare Facility Profile

  # Forms
  - fields:
      placeholder: '@placeholder_benchmark_form_profile_country'
      language: '@language_en_gb'
      value: Country Profile
  - fields:
      placeholder: '@placeholder_benchmark_form_profile_healthcare'
      language: '@language_en_gb'
      value: Healthcare Facility Profile
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_demographic_indicators_name'
      language: '@language_en_gb'
      value: Demographic Indicators
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_economic_indicators_name'
      language: '@language_en_gb'
      value: Economic Indicators
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_name'
      language: '@language_en_gb'
      value: Healthcare Facilities
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_general_indicators_name'
      language: '@language_en_gb'
      value: Healthcare Facilities General Indicators
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_indicators_name'
      language: '@language_en_gb'
      value: Healthcare Facilities Indicators
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_human_resources_name'
      language: '@language_en_gb'
      value: Human Resources
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_sections_mortality_indicators_name'
      language: '@language_en_gb'
      value: Mortality Indicators
  - fields:
      placeholder: '@placeholder_benchmark_region_middle_east'
      language: '@language_en_gb'
      value: Middle East
  - fields:
      placeholder: '@placeholder_benchmark_country_saudi_arabia'
      language: '@language_en_gb'
      value: Saudi Arabia
  - fields:
      placeholder: '@placeholder_benchmark_currency_saudi_riyal'
      language: '@language_en_gb'
      value: Saudi Riyal
  - fields:
      placeholder: '@placeholder_benchmark_language_arabic'
      language: '@language_en_gb'
      value: Arabic
  - fields:
      placeholder: '@placeholder_benchmark_language_english'
      language: '@language_en_gb'
      value: English
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_year_title'
      language: '@language_en_gb'
      value: Year
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_year_label'
      language: '@language_en_gb'
      value: Year
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_region_title'
      language: '@language_en_gb'
      value: Region
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_region_label'
      language: '@language_en_gb'
      value: Region
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_country_title'
      language: '@language_en_gb'
      value: Country
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_country_label'
      language: '@language_en_gb'
      value: Country
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_currency_title'
      language: '@language_en_gb'
      value: Currency
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_currency_label'
      language: '@language_en_gb'
      value: Currency
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_language_title'
      language: '@language_en_gb'
      value: Language
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_language_label'
      language: '@language_en_gb'
      value: Language
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_total_population_title'
      language: '@language_en_gb'
      value: Total population
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_total_population_label'
      language: '@language_en_gb'
      value: Total population
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_title'
      language: '@language_en_gb'
      value: Total population under 15
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_label'
      language: '@language_en_gb'
      value: Total population under 15
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_title'
      language: '@language_en_gb'
      value: Annual population growth rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_label'
      language: '@language_en_gb'
      value: Annual population growth rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_title'
      language: '@language_en_gb'
      value: Life expectancy
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_label'
      language: '@language_en_gb'
      value: Life expectancy
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_median_age_title'
      language: '@language_en_gb'
      value: Median age (years)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_median_age_label'
      language: '@language_en_gb'
      value: Median age (years)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_title'
      language: '@language_en_gb'
      value: Percentage population under 15
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_label'
      language: '@language_en_gb'
      value: Percentage population under 15
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_title'
      language: '@language_en_gb'
      value: Percentage population over 60
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_label'
      language: '@language_en_gb'
      value: Percentage population over 60
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_title'
      language: '@language_en_gb'
      value: Crude birth rate (per 1,000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_label'
      language: '@language_en_gb'
      value: Crude birth rate (per 1,000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_title'
      language: '@language_en_gb'
      value: Crude death rate (per 1,000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_label'
      language: '@language_en_gb'
      value: Crude death rate (per 1,000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_title'
      language: '@language_en_gb'
      value: Infant mortality rate (per 1,000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_label'
      language: '@language_en_gb'
      value: Infant mortality rate (per 1,000 live births)
  #Section two
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_title'
      language: '@language_en_gb'
      value: GDP per capita
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_label'
      language: '@language_en_gb'
      value: GDP per capita
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_title'
      language: '@language_en_gb'
      value: Total expenditure on health per capita
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_label'
      language: '@language_en_gb'
      value: Total expenditure on health per capita
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_title'
      language: '@language_en_gb'
      value: Total expenditure on health as % of GDP
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_label'
      language: '@language_en_gb'
      value: Total expenditure on health as % of GDP
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_title'
      language: '@language_en_gb'
      value: Private expenditure on health as % of total expenditure on health
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_label'
      language: '@language_en_gb'
      value: Private expenditure on health as % of total expenditure on health
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_title'
      language: '@language_en_gb'
      value: Government expenditure on health as % of total government expenditure
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_label'
      language: '@language_en_gb'
      value: Government expenditure on health as % of total government expenditure
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_title'
      language: '@language_en_gb'
      value: Average daily bed cost
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_label'
      language: '@language_en_gb'
      value: Average daily bed cost

  #Section 3.1
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_title'
      language: '@language_en_gb'
      value: Total number of visits to Hospitals OPDs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_label'
      language: '@language_en_gb'
      value: Total number of visits to Hospitals OPDs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_title'
      language: '@language_en_gb'
      value: Total number of visits to PHCs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_label'
      language: '@language_en_gb'
      value: Total number of visits to PHCs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_title'
      language: '@language_en_gb'
      value: Total number of visits to ERD
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_label'
      language: '@language_en_gb'
      value: Total number of visits to ERD
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_title'
      language: '@language_en_gb'
      value: Total number of operations
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_label'
      language: '@language_en_gb'
      value: Total number of operations
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_title'
      language: '@language_en_gb'
      value: Total number of admissions
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_label'
      language: '@language_en_gb'
      value: Total number of admissions
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_title'
      language: '@language_en_gb'
      value: Average bed turnover rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_label'
      language: '@language_en_gb'
      value: Average bed turnover rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_title'
      language: '@language_en_gb'
      value: Average length of stay
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_label'
      language: '@language_en_gb'
      value: Average length of stay
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_title'
      language: '@language_en_gb'
      value: Average bed occupancy rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_label'
      language: '@language_en_gb'
      value: Average bed occupancy rate
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_title'
      language: '@language_en_gb'
      value: Total number of deliveries
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_label'
      language: '@language_en_gb'
      value: Total number of deliveries
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_title'
      language: '@language_en_gb'
      value: Normal deliveries
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_label'
      language: '@language_en_gb'
      value: Normal deliveries
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_title'
      language: '@language_en_gb'
      value: Abnormal deliveries
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_label'
      language: '@language_en_gb'
      value: Abnormal deliveries
  #Section 3.2
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_title'
      language: '@language_en_gb'
      value: Total number of hospitals
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_label'
      language: '@language_en_gb'
      value: Total number of hospitals
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_title'
      language: '@language_en_gb'
      value: Total number of PHCs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_label'
      language: '@language_en_gb'
      value: Total number of PHCs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_title'
      language: '@language_en_gb'
      value: Total number of Community Pharmacies
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_label'
      language: '@language_en_gb'
      value: Total number of Community Pharmacies
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_title'
      language: '@language_en_gb'
      value: Total number of Medical Centers / Poly Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_label'
      language: '@language_en_gb'
      value: Total number of Medical Centers / Poly Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_title'
      language: '@language_en_gb'
      value: Total number of Physiotherapy Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_label'
      language: '@language_en_gb'
      value: Total number of Physiotherapy Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_title'
      language: '@language_en_gb'
      value: Total number of Nutrition Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_label'
      language: '@language_en_gb'
      value: Total number of Nutrition Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_title'
      language: '@language_en_gb'
      value: Total number of Rehabilitation Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_label'
      language: '@language_en_gb'
      value: Total number of Rehabilitation Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_title'
      language: '@language_en_gb'
      value: Total number of Home Care Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_label'
      language: '@language_en_gb'
      value: Total number of Home Care Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_title'
      language: '@language_en_gb'
      value: Total number of Dialysis Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_label'
      language: '@language_en_gb'
      value: Total number of Dialysis Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_title'
      language: '@language_en_gb'
      value: Total number of Psychological Consulting Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_label'
      language: '@language_en_gb'
      value: Total number of Psychological Consulting Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_title'
      language: '@language_en_gb'
      value: Total number of Alternative Medication Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_label'
      language: '@language_en_gb'
      value: Total number of Alternative Medication Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_title'
      language: '@language_en_gb'
      value: Total number of Specialized Healthcare Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_label'
      language: '@language_en_gb'
      value: Total number of Specialized Healthcare Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_title'
      language: '@language_en_gb'
      value: Total number of Dental Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_label'
      language: '@language_en_gb'
      value: Total number of Dental Clinics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_title'
      language: '@language_en_gb'
      value: Total number of Regional Labs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_label'
      language: '@language_en_gb'
      value: Total number of Regional Labs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_title'
      language: '@language_en_gb'
      value: Total number of Laboratories
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_label'
      language: '@language_en_gb'
      value: Total number of Laboratories
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_title'
      language: '@language_en_gb'
      value: Total number of Radiology Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_label'
      language: '@language_en_gb'
      value: Total number of Radiology Centers
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_title'
      language: '@language_en_gb'
      value: Total number of Dental Labs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_label'
      language: '@language_en_gb'
      value: Total number of Dental Labs
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_title'
      language: '@language_en_gb'
      value: Total number of Optics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_label'
      language: '@language_en_gb'
      value: Total number of Optics
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_title'
      language: '@language_en_gb'
      value: Total number of Ambulance Stations
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_label'
      language: '@language_en_gb'
      value: Total number of Ambulance Stations

  #Section 3.2
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_title'
      language: '@language_en_gb'
      value: Total number of physicians (including dentists)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_label'
      language: '@language_en_gb'
      value: Total number of physicians (including dentists)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_title'
      language: '@language_en_gb'
      value: Total number of nurses
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_label'
      language: '@language_en_gb'
      value: Total number of nurses
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_title'
      language: '@language_en_gb'
      value: Total number of pharmacists
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_label'
      language: '@language_en_gb'
      value: Total number of pharmacists
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_title'
      language: '@language_en_gb'
      value: Total number of allied health professionals
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_label'
      language: '@language_en_gb'
      value: Total number of allied health professionals
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_title'
      language: '@language_en_gb'
      value: Physicians density (per 1000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_label'
      language: '@language_en_gb'
      value: Physicians density (per 1000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_title'
      language: '@language_en_gb'
      value: Nursing and midwifery personnel density (per 1000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_label'
      language: '@language_en_gb'
      value: Nursing and midwifery personnel density (per 1000 population)
  #Section 5
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_title'
      language: '@language_en_gb'
      value: Probability of dying under five (per 1000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_label'
      language: '@language_en_gb'
      value: Probability of dying under five (per 1000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_title'
      language: '@language_en_gb'
      value: Probability of dying between 15 and 60 years m/f (per 1000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_label'
      language: '@language_en_gb'
      value: Probability of dying between 15 and 60 years m/f (per 1000 population)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_title'
      language: '@language_en_gb'
      value: Under-five mortality rate (probability of dying under age 5 per 1000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_label'
      language: '@language_en_gb'
      value: Under-five mortality rate (probability of dying under age 5 per 1000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_title'
      language: '@language_en_gb'
      value: Maternal mortality ratio (per 1000,000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_label'
      language: '@language_en_gb'
      value: Maternal mortality ratio (per 1000,000 live births)
  - fields:
      placeholder: '@placeholder_benchmarking_profile_type_country'
      language: '@language_en_gb'
      value: Country Profile
  - fields:
      placeholder: '@placeholder_benchmark_profile_type_facility'
      language: '@language_en_gb'
      value: Healthcare Facility Profile
  - fields:
      placeholder: '@placeholder_benchmark_columns_location'
      language: '@language_en_gb'
      value: Location
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_label'
      language: '@language_en_gb'
      value: Neonatal mortality rate (per 1000 live births)
  - fields:
      placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_title'
      language: '@language_en_gb'
      value: Neonatal mortality rate (per 1000 live births)
  - fields:
      placeholder: '@benchmarking_profile_validation_location_year_error'
      language: '@language_en_gb'
      value: Location and year combination already used
