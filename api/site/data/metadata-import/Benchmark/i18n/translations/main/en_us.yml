entityClass: I18n\Entity\Translation
priority: 15
data:
    - { fields: { placeholder: '@placeholder_benchmark_columns_country', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@placeholder_benchmark_columns_location', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@placeholder_benchmark_columns_type', language: '@language_en_us', value: Type } }
    - { fields: { placeholder: '@placeholder_benchmark_columns_year', language: '@language_en_us', value: Year } }
    - { fields: { placeholder: '@placeholder_benchmark_country_saudi_arabia', language: '@language_en_us', value: Saudi Arabia } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_create', language: '@language_en_us', value: Create New Country Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_currency_saudi_riyal', language: '@language_en_us', value: Saudi Riyal } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_label', language: '@language_en_us', value: Abnormal deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_title', language: '@language_en_us', value: Abnormal deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_label', language: '@language_en_us', value: Average bed occupancy rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_title', language: '@language_en_us', value: Average bed occupancy rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_label', language: '@language_en_us', value: Average bed turnover rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_title', language: '@language_en_us', value: Average bed turnover rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_label', language: '@language_en_us', value: Average daily bed cost } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_title', language: '@language_en_us', value: Average daily bed cost } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_label', language: '@language_en_us', value: Average length of stay } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_title', language: '@language_en_us', value: Average length of stay } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_country_label', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_country_title', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_label', language: '@language_en_us', value: "Crude birth rate (per 1,000 population)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_title', language: '@language_en_us', value: "Crude birth rate (per 1,000 population)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_label', language: '@language_en_us', value: "Crude death rate (per 1,000 population)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_title', language: '@language_en_us', value: "Crude death rate (per 1,000 population)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_currency_label', language: '@language_en_us', value: Currency } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_currency_title', language: '@language_en_us', value: Currency } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_label', language: '@language_en_us', value: Probability of dying under five (per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_title', language: '@language_en_us', value: Probability of dying under five (per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_label', language: '@language_en_us', value: Probability of dying between 15 and 60 years m/f (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_title', language: '@language_en_us', value: Probability of dying between 15 and 60 years m/f (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_label', language: '@language_en_us', value: GDP per capita } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_title', language: '@language_en_us', value: GDP per capita } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_label', language: '@language_en_us', value: Government expenditure on health as % of total government expenditure } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_title', language: '@language_en_us', value: Government expenditure on health as % of total government expenditure } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_label', language: '@language_en_us', value: Total expenditure on health per capita } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_title', language: '@language_en_us', value: Total expenditure on health per capita } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_label', language: '@language_en_us', value: Total expenditure on health as % of GDP } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_title', language: '@language_en_us', value: Total expenditure on health as % of GDP } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_label', language: '@language_en_us', value: "Infant mortality rate (per 1,000 live births)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_title', language: '@language_en_us', value: "Infant mortality rate (per 1,000 live births)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_language_label', language: '@language_en_us', value: Language } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_language_title', language: '@language_en_us', value: Language } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_label', language: '@language_en_us', value: Life expectancy } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_title', language: '@language_en_us', value: Life expectancy } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_label', language: '@language_en_us', value: "Maternal mortality ratio (per 1000,000 live births)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_title', language: '@language_en_us', value: "Maternal mortality ratio (per 1000,000 live births)" } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_median_age_label', language: '@language_en_us', value: Median age (years) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_median_age_title', language: '@language_en_us', value: Median age (years) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_label', language: '@language_en_us', value: Neonatal mortality rate (per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_title', language: '@language_en_us', value: Neonatal mortality rate (per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_label', language: '@language_en_us', value: Normal deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_title', language: '@language_en_us', value: Normal deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_label', language: '@language_en_us', value: Total number of admissions } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_title', language: '@language_en_us', value: Total number of admissions } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_label', language: '@language_en_us', value: Total number of allied health professionals } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_title', language: '@language_en_us', value: Total number of allied health professionals } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_label', language: '@language_en_us', value: Total number of Alternative Medication Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_title', language: '@language_en_us', value: Total number of Alternative Medication Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_label', language: '@language_en_us', value: Total number of Ambulance Stations } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_title', language: '@language_en_us', value: Total number of Ambulance Stations } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_label', language: '@language_en_us', value: Total number of Community Pharmacies } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_title', language: '@language_en_us', value: Total number of Community Pharmacies } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_label', language: '@language_en_us', value: Total number of deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_title', language: '@language_en_us', value: Total number of deliveries } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_label', language: '@language_en_us', value: Total number of Dental Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_title', language: '@language_en_us', value: Total number of Dental Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_label', language: '@language_en_us', value: Total number of Dental Labs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_title', language: '@language_en_us', value: Total number of Dental Labs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_label', language: '@language_en_us', value: Total number of Dialysis Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_title', language: '@language_en_us', value: Total number of Dialysis Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_label', language: '@language_en_us', value: Total number of Home Care Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_title', language: '@language_en_us', value: Total number of Home Care Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_label', language: '@language_en_us', value: Total number of hospitals } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_title', language: '@language_en_us', value: Total number of hospitals } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_label', language: '@language_en_us', value: Total number of Laboratories } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_title', language: '@language_en_us', value: Total number of Laboratories } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_label', language: '@language_en_us', value: Total number of Medical Centers / Poly Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_title', language: '@language_en_us', value: Total number of Medical Centers / Poly Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_label', language: '@language_en_us', value: Total number of nurses } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_title', language: '@language_en_us', value: Total number of nurses } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_label', language: '@language_en_us', value: Total number of Nutrition Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_title', language: '@language_en_us', value: Total number of Nutrition Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_label', language: '@language_en_us', value: Total number of operations } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_title', language: '@language_en_us', value: Total number of operations } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_label', language: '@language_en_us', value: Total number of Optics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_title', language: '@language_en_us', value: Total number of Optics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_label', language: '@language_en_us', value: Total number of pharmacists } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_title', language: '@language_en_us', value: Total number of pharmacists } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_label', language: '@language_en_us', value: Total number of PHCs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_title', language: '@language_en_us', value: Total number of PHCs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_label', language: '@language_en_us', value: Total number of physicians (including dentists) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_title', language: '@language_en_us', value: Total number of physicians (including dentists) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_label', language: '@language_en_us', value: Total number of Physiotherapy Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_title', language: '@language_en_us', value: Total number of Physiotherapy Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_label', language: '@language_en_us', value: Total number of Psychological Consulting Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_title', language: '@language_en_us', value: Total number of Psychological Consulting Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_label', language: '@language_en_us', value: Total number of Radiology Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_title', language: '@language_en_us', value: Total number of Radiology Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_label', language: '@language_en_us', value: Total number of Regional Labs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_title', language: '@language_en_us', value: Total number of Regional Labs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_label', language: '@language_en_us', value: Total number of Rehabilitation Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_title', language: '@language_en_us', value: Total number of Rehabilitation Centers } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_label', language: '@language_en_us', value: Total number of Specialized Healthcare Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_title', language: '@language_en_us', value: Total number of Specialized Healthcare Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_label', language: '@language_en_us', value: Nursing and midwifery personnel density (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_title', language: '@language_en_us', value: Nursing and midwifery personnel density (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_label', language: '@language_en_us', value: Percentage population over 60 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_title', language: '@language_en_us', value: Percentage population over 60 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_label', language: '@language_en_us', value: Percentage population under 15 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_title', language: '@language_en_us', value: Percentage population under 15 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_label', language: '@language_en_us', value: Physicians density (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_title', language: '@language_en_us', value: Physicians density (per 1000 population) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_label', language: '@language_en_us', value: Annual population growth rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_title', language: '@language_en_us', value: Annual population growth rate } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_label', language: '@language_en_us', value: Private expenditure on health as % of total expenditure on health } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_title', language: '@language_en_us', value: Private expenditure on health as % of total expenditure on health } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_region_label', language: '@language_en_us', value: Region } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_region_title', language: '@language_en_us', value: Region } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_label', language: '@language_en_us', value: Total population under 15 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_title', language: '@language_en_us', value: Total population under 15 } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_label', language: '@language_en_us', value: Total population } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_title', language: '@language_en_us', value: Total population } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_label', language: '@language_en_us', value: Under-five mortality rate (probability of dying under age 5 per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_title', language: '@language_en_us', value: Under-five mortality rate (probability of dying under age 5 per 1000 live births) } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_label', language: '@language_en_us', value: Total number of visits to ERD } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_title', language: '@language_en_us', value: Total number of visits to ERD } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_label', language: '@language_en_us', value: Total number of visits to Hospitals OPDs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_title', language: '@language_en_us', value: Total number of visits to Hospitals OPDs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_label', language: '@language_en_us', value: Total number of visits to PHCs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_title', language: '@language_en_us', value: Total number of visits to PHCs } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_year_label', language: '@language_en_us', value: Year } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_year_title', language: '@language_en_us', value: Year } }
    - { fields: { placeholder: '@placeholder_benchmark_language_arabic', language: '@language_en_us', value: Arabic } }
    - { fields: { placeholder: '@placeholder_benchmark_language_english', language: '@language_en_us', value: English } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_demographic_indicators_name', language: '@language_en_us', value: Demographic Indicators } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_economic_indicators_name', language: '@language_en_us', value: Economic Indicators } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_general_indicators_name', language: '@language_en_us', value: Healthcare Facilities General Indicators } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_indicators_name', language: '@language_en_us', value: Healthcare Facilities Indicators } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_name', language: '@language_en_us', value: Healthcare Facilities } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_human_resources_name', language: '@language_en_us', value: Human Resources } }
    - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_mortality_indicators_name', language: '@language_en_us', value: Mortality Indicators } }
    - { fields: { placeholder: '@placeholder_data_used_for_benchmarking_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@placeholder_data_used_for_benchmarking_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_ambulatory_facility', language: '@language_en_us', value: Ambulatory Facility } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_community_pharmacy', language: '@language_en_us', value: Community Pharmacy } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_dental_facility', language: '@language_en_us', value: Dental Facility } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_general_facility', language: '@language_en_us', value: General Facility } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_gp_facility', language: '@language_en_us', value: GP Facility } }
    - { fields: { placeholder: '@benchmark_facility_category_na_label', language: '@language_en_us', value: N/A } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_category_referral_facility', language: '@language_en_us', value: Referral Facility } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_create', language: '@language_en_us', value: Create New Healthcare Facility Profile } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_label', language: '@language_en_us', value: Abnormal delivery } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_title', language: '@language_en_us', value: Abnormal delivery } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_label', language: '@language_en_us', value: Accreditation Regulators } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_title', language: '@language_en_us', value: Accreditation Regulators } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_label', language: '@language_en_us', value: Average daily bed cost } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_title', language: '@language_en_us', value: Average daily bed cost } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_label', language: '@language_en_us', value: Bed occupancy rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_title', language: '@language_en_us', value: Bed occupancy rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_label', language: '@language_en_us', value: Bed turnover rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_title', language: '@language_en_us', value: Bed turnover rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_label', language: '@language_en_us', value: Beds ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_title', language: '@language_en_us', value: Beds ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_country_label', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_country_title', language: '@language_en_us', value: Country } }
    - { fields: { placeholder: '@benchmark_facility_regulators_data_commission_on_accreditation', language: '@language_en_us', value: Commission on Accreditation of Rehabilitation Facilities } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_label', language: '@language_en_us', value: 'Can the data be used for benchmarking?' } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_title', language: '@language_en_us', value: 'Can the data be used for benchmarking?' } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_email_address_label', language: '@language_en_us', value: Email address } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_email_address_title', language: '@language_en_us', value: Email address } }
    - { fields: { placeholder: '@benchmark_facility_facebook_url_label', language: '@language_en_us', value: Health Organization Facebook URL } }
    - { fields: { placeholder: '@benchmark_facility_facebook_url_title', language: '@language_en_us', value: Health Organization Facebook URL } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_category_label', language: '@language_en_us', value: Facility category } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_category_title', language: '@language_en_us', value: Facility category } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_label', language: '@language_en_us', value: Facility Logo } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_title', language: '@language_en_us', value: Facility Logo } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_introductory_text', language: '@language_en_us', value: Logo size must be 200px x 200px } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_name_label', language: '@language_en_us', value: Facility name } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_name_title', language: '@language_en_us', value: Facility name } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_speciality_label', language: '@language_en_us', value: Facility specialty } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_speciality_title', language: '@language_en_us', value: Facility specialty } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_type_label', language: '@language_en_us', value: Facility Type } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_type_title', language: '@language_en_us', value: Facility Type } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_fax_label', language: '@language_en_us', value: Fax } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_fax_title', language: '@language_en_us', value: Fax } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_healthcare_sector_label', language: '@language_en_us', value: Health care sector } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_healthcare_sector_title', language: '@language_en_us', value: Health care sector } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_label', language: '@language_en_us', value: 'Is the facility accredited?' } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_title', language: '@language_en_us', value: 'Is the facility accredited?' } }
    - { fields: { placeholder: '@benchmark_facility_latitude_label', language: '@language_en_us', value: Latitude } }
    - { fields: { placeholder: '@benchmark_facility_latitude_title', language: '@language_en_us', value: Latitude } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_label', language: '@language_en_us', value: Length of stay rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_title', language: '@language_en_us', value: Length of stay rate } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_location_validation_error', language: '@language_en_us', value: Location invalid for this year please select another } }
    - { fields: { placeholder: '@benchmark_facility_longitude_label', language: '@language_en_us', value: Longitude } }
    - { fields: { placeholder: '@benchmark_facility_longitude_title', language: '@language_en_us', value: Longitude } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_maternity_ratio_label', language: '@language_en_us', value: Maternity ratio } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_maternity_ratio_title', language: '@language_en_us', value: Maternity ratio } }
    - { fields: { placeholder: '@benchmark_facility_national_organisation_code_label', language: '@language_en_us', value: National Organization Code } }
    - { fields: { placeholder: '@benchmark_facility_national_organisation_code_title', language: '@language_en_us', value: National Organization Code } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_normal_delivery_label', language: '@language_en_us', value: Normal delivery } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_normal_delivery_title', language: '@language_en_us', value: Normal delivery } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_admissions_label', language: '@language_en_us', value: Number of admissions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_admissions_title', language: '@language_en_us', value: Number of admissions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_allied_health_label', language: '@language_en_us', value: Number of allied health } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_allied_health_title', language: '@language_en_us', value: Number of allied health } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_label', language: '@language_en_us', value: Number of ambulance cars } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_title', language: '@language_en_us', value: Number of ambulance cars } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_beds_label', language: '@language_en_us', value: Number of beds } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_beds_title', language: '@language_en_us', value: Number of beds } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_label', language: '@language_en_us', value: Number of critical care units admissions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_title', language: '@language_en_us', value: Number of critical care units admissions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_deliveries_label', language: '@language_en_us', value: Number of deliveries } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_deliveries_title', language: '@language_en_us', value: Number of deliveries } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_discharges_label', language: '@language_en_us', value: Number of discharges } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_discharges_title', language: '@language_en_us', value: Number of discharges } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_er_cases_label', language: '@language_en_us', value: Number of ER cases } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_er_cases_title', language: '@language_en_us', value: Number of ER cases } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_nurses_label', language: '@language_en_us', value: Number of nurses } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_nurses_title', language: '@language_en_us', value: Number of nurses } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_opd_visits_label', language: '@language_en_us', value: Number of opd visits } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_opd_visits_title', language: '@language_en_us', value: Number of opd visits } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_paramedics_label', language: '@language_en_us', value: Number of paramedics } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_paramedics_title', language: '@language_en_us', value: Number of paramedics } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_pharmacists_label', language: '@language_en_us', value: Number of pharmacists } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_pharmacists_title', language: '@language_en_us', value: Number of pharmacists } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_physicians_label', language: '@language_en_us', value: Number of physicians } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_physicians_title', language: '@language_en_us', value: Number of physicians } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_label', language: '@language_en_us', value: Number of prescribed medications } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_title', language: '@language_en_us', value: Number of prescribed medications } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescriptions_label', language: '@language_en_us', value: Number of prescriptions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescriptions_title', language: '@language_en_us', value: Number of prescriptions } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_staff_label', language: '@language_en_us', value: Number of staff } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_staff_title', language: '@language_en_us', value: Number of staff } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_surgeries_label', language: '@language_en_us', value: Number of surgeries } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_num_surgeries_title', language: '@language_en_us', value: Number of surgeries } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_label', language: '@language_en_us', value: Nurses ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_title', language: '@language_en_us', value: Nurses ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_parent_facility_label', language: '@language_en_us', value: Parent facility } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_parent_facility_title', language: '@language_en_us', value: Parent facility } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_phone_number_label', language: '@language_en_us', value: Phone number } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_phone_number_title', language: '@language_en_us', value: Phone number } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_label', language: '@language_en_us', value: Physicians ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_title', language: '@language_en_us', value: Physicians ratio vs patients } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_population_covered_label', language: '@language_en_us', value: Population covered } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_population_covered_title', language: '@language_en_us', value: Population covered } }
    - { fields: { placeholder: '@benchmark_facility_twitter_url_label', language: '@language_en_us', value: Health Organization Twitter URL } }
    - { fields: { placeholder: '@benchmark_facility_twitter_url_title', language: '@language_en_us', value: Health Organization Twitter URL } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_website_label', language: '@language_en_us', value: Website } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_website_title', language: '@language_en_us', value: Website } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_year_label', language: '@language_en_us', value: Year } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_year_title', language: '@language_en_us', value: Year } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_yearly_budget_label', language: '@language_en_us', value: Yearly budget } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_yearly_budget_title', language: '@language_en_us', value: Yearly budget } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_location_label', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@benchmark_facility_profile_fields_location_title', language: '@language_en_us', value: Location } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_accreditation_name', language: '@language_en_us', value: Facility Accreditation } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_contacts_name', language: '@language_en_us', value: Facility Contacts } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_details_name', language: '@language_en_us', value: Facility Details } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_and_ratios_name', language: '@language_en_us', value: Facility Indicators and Ratios } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_name', language: '@language_en_us', value: Facility Indicators } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_ratios_name', language: '@language_en_us', value: Facility Ratios } }
    - { fields: { placeholder: '@benchmark_facility_regulators_australian_accreditation', language: '@language_en_us', value: Australian Accreditation } }
    - { fields: { placeholder: '@benchmark_facility_regulators_canadian_accreditation', language: '@language_en_us', value: Canadian Accreditation } }
    - { fields: { placeholder: '@benchmark_facility_regulators_cap_accreditation', language: '@language_en_us', value: CAP Accreditation } }
    - { fields: { placeholder: '@benchmark_facility_regulators_cbhai_accreditation', language: '@language_en_us', value: CBHAI Accreditation } }
    - { fields: { placeholder: '@benchmark_facility_regulators_cqc', language: '@language_en_us', value: CQC } }
    - { fields: { placeholder: '@benchmark_facility_regulators_hasp', language: '@language_en_us', value: HASP } }
    - { fields: { placeholder: '@benchmark_facility_regulators_hcac', language: '@language_en_us', value: HCAC } }
    - { fields: { placeholder: '@benchmark_facility_regulators_hippa', language: '@language_en_us', value: HIPPA } }
    - { fields: { placeholder: '@benchmark_facility_regulators_iso_certificate', language: '@language_en_us', value: ISO Certificate } }
    - { fields: { placeholder: '@benchmark_facility_regulators_jci_accreditation', language: '@language_en_us', value: JCI Accreditation } }
    - { fields: { placeholder: '@benchmark_facility_regulators_magnet_accreditation', language: '@language_en_us', value: Magnet Accreditation } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_cardiology', language: '@language_en_us', value: Cardiology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_dentistry', language: '@language_en_us', value: Dentistry } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_dermatology', language: '@language_en_us', value: Dermatology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_ent', language: '@language_en_us', value: ENT } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_family_medicine', language: '@language_en_us', value: Family Medicine } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_general_surgery', language: '@language_en_us', value: General Surgery } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_internal_medicine', language: '@language_en_us', value: Internal Medicine } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_laboratory', language: '@language_en_us', value: Laboratory } }
    - { fields: { placeholder: '@benchmark_facility_speciality_na_label', language: '@language_en_us', value: N/A } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_neurology', language: '@language_en_us', value: Neurology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_neurosurgery', language: '@language_en_us', value: Neurosurgery } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_obstetrics_gynaecology', language: '@language_en_us', value: Obstetrics & Gynecology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_oncology', language: '@language_en_us', value: Oncology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_ophthalmology', language: '@language_en_us', value: Ophthalmology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_orthopedics', language: '@language_en_us', value: Orthopedics } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_pediatrics', language: '@language_en_us', value: Pediatrics } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_physiotherapy', language: '@language_en_us', value: Physiotherapy } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_plastic_surgery', language: '@language_en_us', value: Plastic Surgery } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_psychiatry', language: '@language_en_us', value: Psychiatry } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_radiology', language: '@language_en_us', value: Radiology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_rheumatology', language: '@language_en_us', value: Rheumatology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_urology', language: '@language_en_us', value: Urology } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_vascular', language: '@language_en_us', value: Vascular } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_alternative_medicine_center', language: '@language_en_us', value: Alternative Medicine Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_ambulance_station', language: '@language_en_us', value: Ambulance Station } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_community_pharmacy', language: '@language_en_us', value: Community Pharmacy } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_dental_clinic', language: '@language_en_us', value: Dental Clinic } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_dental_lab', language: '@language_en_us', value: Dental Lab } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_dialysis_center', language: '@language_en_us', value: Dialysis Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_home_care_center', language: '@language_en_us', value: Home Care Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_hospital', language: '@language_en_us', value: Hospital } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_laboratory', language: '@language_en_us', value: Laboratory } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_medical_center_polyclinic', language: '@language_en_us', value: Medical Center / Polyclinic } }
    - { fields: { placeholder: '@benchmark_facility_type_na_label', language: '@language_en_us', value: N/A } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_nutrition_center', language: '@language_en_us', value: Nutrition Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_optics', language: '@language_en_us', value: Optics } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_physiotherapy_center', language: '@language_en_us', value: Physiotherapy Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_primary_healthcare_center', language: '@language_en_us', value: Primary Healthcare Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_psychological_consulting_center', language: '@language_en_us', value: Psychological Consulting Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_radiology_center', language: '@language_en_us', value: Radiology Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_regional_lab', language: '@language_en_us', value: Regional Lab } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_rehabilitation_center', language: '@language_en_us', value: Rehabilitation Center } }
    - { fields: { placeholder: '@placeholder_benchmark_facility_type_specialised_healthcare_clinic', language: '@language_en_us', value: Specialized Healthcare Clinic } }
    - { fields: { placeholder: '@placeholder_benchmark_form_type_profile_country', language: '@language_en_us', value: Country Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_form_type_profile_healthcare', language: '@language_en_us', value: Healthcare Facility Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_form_profile_country', language: '@language_en_us', value: Country Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_form_profile_healthcare', language: '@language_en_us', value: Healthcare Facility Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_council_of_cooperative_health_insurance', language: '@language_en_us', value: Council of Cooperative Health Insurance } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_general_authority_for_food_and_drug_administration', language: '@language_en_us', value: General Authority for Food and Drug Administration } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_general_organization_for_social_insurance', language: '@language_en_us', value: General Organization for Social Insurance } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_johns_hopkins_aramco_healthcare', language: '@language_en_us', value: Johns Hopkins Aramco Healthcare } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_king_faisal_specialist_hospital_and_research_centre', language: '@language_en_us', value: King Faisal Specialist Hospital & Research Centre } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_military_intelligence', language: '@language_en_us', value: Military Intelligence } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_defence_affairs', language: '@language_en_us', value: Ministry of Defense Affairs } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_health', language: '@language_en_us', value: Ministry of Health } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_interior', language: '@language_en_us', value: Ministry of Interior } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_national_guard_health_affairs', language: '@language_en_us', value: National Guard Health Affairs } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_others', language: '@language_en_us', value: Others } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_private_sector', language: '@language_en_us', value: Private Sector } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_royal_clinics', language: '@language_en_us', value: Royal clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_royal_commission', language: '@language_en_us', value: Royal Commission } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_airlines_clinics', language: '@language_en_us', value: Saudi Airlines Clinics } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_center_for_the_accreditation_of_health_facilities', language: '@language_en_us', value: Saudi Center for the accreditation of health facilities } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_commission_for_health_specialties', language: '@language_en_us', value: Saudi Commission for Health Specialties } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_health_council', language: '@language_en_us', value: Saudi Health Council } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_red_crescent_authority', language: '@language_en_us', value: Saudi Red Crescent Authority } }
    - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_university_hospitals', language: '@language_en_us', value: University Hospitals } }
    - { fields: { placeholder: '@placeholder_is_facility_accredited_no', language: '@language_en_us', value: No } }
    - { fields: { placeholder: '@placeholder_is_facility_accredited_yes', language: '@language_en_us', value: Yes } }
    - { fields: { placeholder: '@placeholder_benchmark_module_title', language: '@language_en_us', value: Benchmarking } }
    - { fields: { placeholder: '@placeholder_benchmark_region_middle_east', language: '@language_en_us', value: Middle East } }
    - { fields: { placeholder: '@placeholder_benchmarking_profile_type_country', language: '@language_en_us', value: Country Profile } }
    - { fields: { placeholder: '@placeholder_benchmark_profile_type_facility', language: '@language_en_us', value: Healthcare Facility Profile } }
    - { fields: { placeholder: '@benchmarking_profile_validation_location_year_error', language: '@language_en_us', value: Location and year combination already used } }
    - { fields: { placeholder: '@placeholder_benchmark_columns_id', language: '@language_en_us', value: ID } }
