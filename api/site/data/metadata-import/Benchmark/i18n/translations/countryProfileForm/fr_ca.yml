entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_demographic_indicators_name', language: '@language_fr_ca', value: 'Indicateurs démographiques' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_economic_indicators_name', language: '@language_fr_ca', value: 'Indicateurs économiques' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_name', language: '@language_fr_ca', value: 'Établissements de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_general_indicators_name', language: '@language_fr_ca', value: 'Indicateurs généraux des établissements de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_healthcare_facilities_indicators_name', language: '@language_fr_ca', value: 'Indicateurs des établissements de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_human_resources_name', language: '@language_fr_ca', value: 'Ressources humaines' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_sections_mortality_indicators_name', language: '@language_fr_ca', value: 'Indicateurs de mortalité' } }
  - { fields: { placeholder: '@placeholder_benchmark_region_middle_east', language: '@language_fr_ca', value: Moyen-Orient } }
  - { fields: { placeholder: '@placeholder_benchmark_country_saudi_arabia', language: '@language_fr_ca', value: 'Arabie Saoudite' } }
  - { fields: { placeholder: '@placeholder_benchmark_currency_saudi_riyal', language: '@language_fr_ca', value: 'Riyal saoudien' } }
  - { fields: { placeholder: '@placeholder_benchmark_language_arabic', language: '@language_fr_ca', value: Arabe } }
  - { fields: { placeholder: '@placeholder_benchmark_language_english', language: '@language_fr_ca', value: Anglais } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_year_title', language: '@language_fr_ca', value: Année } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_year_label', language: '@language_fr_ca', value: Année } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_region_title', language: '@language_fr_ca', value: Région } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_region_label', language: '@language_fr_ca', value: Région } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_country_title', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_country_label', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_currency_title', language: '@language_fr_ca', value: Devise } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_currency_label', language: '@language_fr_ca', value: Devise } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_language_title', language: '@language_fr_ca', value: Langue } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_language_label', language: '@language_fr_ca', value: Langue } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_title', language: '@language_fr_ca', value: 'Population totale' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_label', language: '@language_fr_ca', value: 'Population totale' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_title', language: '@language_fr_ca', value: "Population totale de moins de 15\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_total_population_under_fifteen_label', language: '@language_fr_ca', value: "Population totale de moins de 15\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_title', language: '@language_fr_ca', value: 'Taux de croissance démographique annuel' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_population_growth_rate_label', language: '@language_fr_ca', value: 'Taux de croissance démographique annuel' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_title', language: '@language_fr_ca', value: 'Espérance de vie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_life_expectancy_label', language: '@language_fr_ca', value: 'Espérance de vie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_median_age_title', language: '@language_fr_ca', value: 'Âge médian (ans)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_median_age_label', language: '@language_fr_ca', value: 'Âge médian (ans)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_title', language: '@language_fr_ca', value: "Pourcentage de la population de moins de 15\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_under_fifteen_label', language: '@language_fr_ca', value: "Pourcentage de la population de moins de 15\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_title', language: '@language_fr_ca', value: "Pourcentage de la population de plus de 60\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_percentage_population_over_sixty_label', language: '@language_fr_ca', value: "Pourcentage de la population de plus de 60\_ans" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_title', language: '@language_fr_ca', value: 'Taux brut de natalité (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_birth_rate_label', language: '@language_fr_ca', value: 'Taux brut de natalité (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_title', language: '@language_fr_ca', value: 'Taux brut de mortalité (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_crude_death_rate_label', language: '@language_fr_ca', value: 'Taux brut de mortalité (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_title', language: '@language_fr_ca', value: 'Taux de mortalité infantile (pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_infant_mortality_rate_label', language: '@language_fr_ca', value: 'Taux de mortalité infantile (pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_title', language: '@language_fr_ca', value: 'PIB par habitant' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gdp_per_capita_label', language: '@language_fr_ca', value: 'PIB par habitant' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_title', language: '@language_fr_ca', value: 'Dépenses totales de santé par habitant' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_per_capita_label', language: '@language_fr_ca', value: 'Dépenses totales de santé par habitant' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_title', language: '@language_fr_ca', value: 'Dépenses totales de santé en % du PIB' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_health_spend_percantage_of_gdp_label', language: '@language_fr_ca', value: 'Dépenses totales de santé en % du PIB' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_title', language: '@language_fr_ca', value: 'Dépenses privées de santé en % des dépenses totales de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_private_health_spend_of_total_label', language: '@language_fr_ca', value: 'Dépenses privées de santé en % des dépenses totales de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_title', language: '@language_fr_ca', value: 'Dépenses publiques de santé en % des dépenses publiques totales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_gov_health_spend_of_total_gov_spend_label', language: '@language_fr_ca', value: 'Dépenses publiques de santé en % des dépenses publiques totales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_title', language: '@language_fr_ca', value: 'Coût quotidien moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_daily_bed_cost_label', language: '@language_fr_ca', value: 'Coût quotidien moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_title', language: '@language_fr_ca', value: 'Nombre total de visites aux OPD des hôpitaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_opds_label', language: '@language_fr_ca', value: 'Nombre total de visites aux OPD des hôpitaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_title', language: '@language_fr_ca', value: 'Nombre total de visites aux soins de santé primaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_PHCS_label', language: '@language_fr_ca', value: 'Nombre total de visites aux soins de santé primaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_title', language: '@language_fr_ca', value: 'Nombre total de visites au ERD' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_visits_to_erd_label', language: '@language_fr_ca', value: 'Nombre total de visites au ERD' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_title', language: '@language_fr_ca', value: 'Nombre total d''opérations' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_operations_label', language: '@language_fr_ca', value: 'Nombre total d''opérations' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_title', language: '@language_fr_ca', value: 'Nombre total d''admissions' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_admissions_label', language: '@language_fr_ca', value: 'Nombre total d''admissions' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_title', language: '@language_fr_ca', value: 'Taux de rotation moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_turnover_label', language: '@language_fr_ca', value: 'Taux de rotation moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_title', language: '@language_fr_ca', value: 'Durée moyenne de séjour' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_stay_length_label', language: '@language_fr_ca', value: 'Durée moyenne de séjour' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_title', language: '@language_fr_ca', value: 'Taux d''occupation moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_avg_bed_occupancy_label', language: '@language_fr_ca', value: 'Taux d''occupation moyen des lits' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_title', language: '@language_fr_ca', value: 'Nombre total de livraisons' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_deliveries_label', language: '@language_fr_ca', value: 'Nombre total de livraisons' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_title', language: '@language_fr_ca', value: 'Livraisons normales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_normal_deliveries_label', language: '@language_fr_ca', value: 'Livraisons normales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_title', language: '@language_fr_ca', value: 'Livraisons anormales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_abnormal_deliveries_label', language: '@language_fr_ca', value: 'Livraisons anormales' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_title', language: '@language_fr_ca', value: 'Nombre total d''hôpitaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_hospitals_label', language: '@language_fr_ca', value: 'Nombre total d''hôpitaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_title', language: '@language_fr_ca', value: 'Nombre total de PHC' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_phcs_label', language: '@language_fr_ca', value: 'Nombre total de PHC' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_title', language: '@language_fr_ca', value: 'Nombre total de pharmacies communautaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_community_pharmacies_label', language: '@language_fr_ca', value: 'Nombre total de pharmacies communautaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres médicaux ou polycliniques' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_medical_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres médicaux ou polycliniques' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de physiothérapie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physio_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de physiothérapie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de diététique' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nutrition_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de diététique' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de revalidation' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_rehab_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de revalidation' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de soins à domicile' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_home_care_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de soins à domicile' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de dialyse' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dialysis_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de dialyse' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de consultation psychologique' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_psych_consult_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de consultation psychologique' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de médicaments alternatifs' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_alt_med_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de médicaments alternatifs' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_title', language: '@language_fr_ca', value: 'Nombre total de cliniques de santé spécialisées' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_special_care_clinics_label', language: '@language_fr_ca', value: 'Nombre total de cliniques de santé spécialisées' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_title', language: '@language_fr_ca', value: 'Nombre total de cliniques dentaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_clinics_label', language: '@language_fr_ca', value: 'Nombre total de cliniques dentaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_title', language: '@language_fr_ca', value: 'Nombre total de laboratoires régionaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_regional_labs_label', language: '@language_fr_ca', value: 'Nombre total de laboratoires régionaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_title', language: '@language_fr_ca', value: 'Nombre total de laboratoires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_labs_label', language: '@language_fr_ca', value: 'Nombre total de laboratoires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_title', language: '@language_fr_ca', value: 'Nombre total de centres de radiologie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_radiology_centers_label', language: '@language_fr_ca', value: 'Nombre total de centres de radiologie' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_title', language: '@language_fr_ca', value: 'Nombre total de laboratoires dentaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_dental_labs_label', language: '@language_fr_ca', value: 'Nombre total de laboratoires dentaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_title', language: '@language_fr_ca', value: 'Nombre total d''opticiens' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_optics_label', language: '@language_fr_ca', value: 'Nombre total d''opticiens' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_title', language: '@language_fr_ca', value: 'Nombre total de postes d''ambulance' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_ambulance_stations_label', language: '@language_fr_ca', value: 'Nombre total de postes d''ambulance' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_title', language: '@language_fr_ca', value: 'Nombre total de médecins (y compris les dentistes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_physicians_inc_dentists_label', language: '@language_fr_ca', value: 'Nombre total de médecins (y compris les dentistes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_title', language: '@language_fr_ca', value: 'Nombre total d''infirmières' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_nurses_label', language: '@language_fr_ca', value: 'Nombre total d''infirmières' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_title', language: '@language_fr_ca', value: 'Nombre total de pharmaciens' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_pharmacists_label', language: '@language_fr_ca', value: 'Nombre total de pharmaciens' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_title', language: '@language_fr_ca', value: 'Nombre total d''assistants médicaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_num_allied_health_professionals_label', language: '@language_fr_ca', value: 'Nombre total d''assistants médicaux' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_title', language: '@language_fr_ca', value: 'Densité de médecins (par 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_physicians_density_label', language: '@language_fr_ca', value: 'Densité de médecins (par 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_title', language: '@language_fr_ca', value: 'Densité du personnel infirmier et obstétrical (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_nursing_density_label', language: '@language_fr_ca', value: 'Densité du personnel infirmier et obstétrical (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_title', language: '@language_fr_ca', value: 'Probabilité de mourir avant cinq ans (pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_under_five_label', language: '@language_fr_ca', value: 'Probabilité de mourir avant cinq ans (pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_title', language: '@language_fr_ca', value: 'Probabilité de mourir entre 15 et 60 ans m/f (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_death_chance_fifteen_to_sixty_label', language: '@language_fr_ca', value: 'Probabilité de mourir entre 15 et 60 ans m/f (pour 1000 habitants)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_title', language: '@language_fr_ca', value: 'Taux de mortalité des moins de 5 ans (probabilité de mourir avant l''âge de 5 ans pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_under_five_mortality_label', language: '@language_fr_ca', value: 'Taux de mortalité des moins de 5 ans (probabilité de mourir avant l''âge de 5 ans pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_title', language: '@language_fr_ca', value: "Taux de mortalité maternelle (pour 1\_000\_000 naissances vivantes)" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_maternal_mortality_ratio_label', language: '@language_fr_ca', value: "Taux de mortalité maternelle (pour 1\_000\_000 naissances vivantes)" } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_title', language: '@language_fr_ca', value: 'Taux de mortalité néonatale (pour 1000 naissances vivantes)' } }
  - { fields: { placeholder: '@placeholder_benchmark_country_profile_fields_neonatal_mortality_rate_label', language: '@language_fr_ca', value: 'Taux de mortalité néonatale (pour 1000 naissances vivantes)' } }
