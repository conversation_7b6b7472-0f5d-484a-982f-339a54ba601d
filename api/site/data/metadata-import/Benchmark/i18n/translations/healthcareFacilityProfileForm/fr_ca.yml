entityClass: I18n\Entity\Translation
priority: 15
data:
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_details_name', language: '@language_fr_ca', value: 'Détails de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_and_ratios_name', language: '@language_fr_ca', value: 'Indicateurs et ratios de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_name', language: '@language_fr_ca', value: 'Indicateurs de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_ratios_name', language: '@language_fr_ca', value: 'Ratios de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_accreditation_name', language: '@language_fr_ca', value: 'Accréditation de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_profile_sections_facility_contacts_name', language: '@language_fr_ca', value: 'Coordonnées de l''installation' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_health', language: '@language_fr_ca', value: 'Ministre de la santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_king_faisal_specialist_hospital_and_research_centre', language: '@language_fr_ca', value: 'Hôpital spécialisé et centre de recherche Roi Faisal' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_national_guard_health_affairs', language: '@language_fr_ca', value: 'Affaires sanitaires de la Garde nationale' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_johns_hopkins_aramco_healthcare', language: '@language_fr_ca', value: 'Johns Hopkins Aramco Healthcare' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_royal_commission', language: '@language_fr_ca', value: 'Commission royale' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_interior', language: '@language_fr_ca', value: 'Ministre de l''intérieur' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_defence_affairs', language: '@language_fr_ca', value: 'Ministère des affaires de défense' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_red_crescent_authority', language: '@language_fr_ca', value: 'Autorité du Croissant-Rouge saoudien' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_private_sector', language: '@language_fr_ca', value: 'Secteur privé' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_royal_clinics', language: '@language_fr_ca', value: 'Cliniques royales' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_military_intelligence', language: '@language_fr_ca', value: 'Renseignement militaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_airlines_clinics', language: '@language_fr_ca', value: 'Cliniques Saudi Airlines' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_university_hospitals', language: '@language_fr_ca', value: 'Hôpitaux universitaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_center_for_the_accreditation_of_health_facilities', language: '@language_fr_ca', value: 'Centre saoudien d''accréditation des établissements de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_general_authority_for_food_and_drug_administration', language: '@language_fr_ca', value: 'Autorité générale pour la Food and Drug Administration' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_health_council', language: '@language_fr_ca', value: 'Conseil saoudien de la santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_council_of_cooperative_health_insurance', language: '@language_fr_ca', value: 'Conseil de l''assurance maladie coopérative' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_saudi_commission_for_health_specialties', language: '@language_fr_ca', value: 'Commission saoudienne des spécialités de santé' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_general_organization_for_social_insurance', language: '@language_fr_ca', value: 'Organisation générale des assurances sociales' } }
  - { fields: { placeholder: '@placeholder_benchmark_healthcare_sector_others', language: '@language_fr_ca', value: Autres } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_general_facility', language: '@language_fr_ca', value: 'Installation générale' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_referral_facility', language: '@language_fr_ca', value: 'Installation de référence' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_ambulatory_facility', language: '@language_fr_ca', value: 'Installation ambulatoire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_gp_facility', language: '@language_fr_ca', value: 'Installation GP' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_dental_facility', language: '@language_fr_ca', value: 'Installation dentaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_category_community_pharmacy', language: '@language_fr_ca', value: 'Pharmacie communautaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_hospital', language: '@language_fr_ca', value: Hôpital } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_primary_healthcare_center', language: '@language_fr_ca', value: 'Centre de soins primaires' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_medical_center_polyclinic', language: '@language_fr_ca', value: 'Centre médical ou polyclinique' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_physiotherapy_center', language: '@language_fr_ca', value: 'Centre de physiothérapie' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_nutrition_center', language: '@language_fr_ca', value: 'Centre de diététique' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_rehabilitation_center', language: '@language_fr_ca', value: 'Centre de revalidation' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_home_care_center', language: '@language_fr_ca', value: 'Centre de soins à domicile' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_dialysis_center', language: '@language_fr_ca', value: 'Centre de dialyse' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_psychological_consulting_center', language: '@language_fr_ca', value: 'Centre de consultation psychologique' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_alternative_medicine_center', language: '@language_fr_ca', value: 'Centre de médecine alternative' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_specialised_healthcare_clinic', language: '@language_fr_ca', value: 'Clinique de santé spécialisée' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_dental_clinic', language: '@language_fr_ca', value: 'Clinique dentaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_regional_lab', language: '@language_fr_ca', value: 'Labo régional' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_laboratory', language: '@language_fr_ca', value: Laboratoire } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_radiology_center', language: '@language_fr_ca', value: 'Centre de radiologie' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_dental_lab', language: '@language_fr_ca', value: 'Labo dentaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_community_pharmacy', language: '@language_fr_ca', value: 'Pharmacie communautaire' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_optics', language: '@language_fr_ca', value: Optique } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_type_ambulance_station', language: '@language_fr_ca', value: 'Poste d''ambulance' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_cardiology', language: '@language_fr_ca', value: Cardiologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_dentistry', language: '@language_fr_ca', value: Dentisterie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_dermatology', language: '@language_fr_ca', value: Dermatologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_ent', language: '@language_fr_ca', value: Oto-rhino-laryngologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_family_medicine', language: '@language_fr_ca', value: 'Médecine familiale' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_general_surgery', language: '@language_fr_ca', value: 'Chirurgie générale' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_internal_medicine', language: '@language_fr_ca', value: 'Médecine interne' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_laboratory', language: '@language_fr_ca', value: Laboratoire } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_neurology', language: '@language_fr_ca', value: Neurologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_neurosurgery', language: '@language_fr_ca', value: Neurochirurgie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_obstetrics_gynaecology', language: '@language_fr_ca', value: 'Obstétrique et gynécologie' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_oncology', language: '@language_fr_ca', value: Oncologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_ophthalmology', language: '@language_fr_ca', value: Ophthalmologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_orthopedics', language: '@language_fr_ca', value: Orthopédie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_pediatrics', language: '@language_fr_ca', value: Pédiatre } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_physiotherapy', language: '@language_fr_ca', value: Physiothérapie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_plastic_surgery', language: '@language_fr_ca', value: 'Chirurgie plastique' } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_psychiatry', language: '@language_fr_ca', value: Psychiatrie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_radiology', language: '@language_fr_ca', value: Radiologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_rheumatology', language: '@language_fr_ca', value: Rheumatologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_urology', language: '@language_fr_ca', value: Urologie } }
  - { fields: { placeholder: '@placeholder_benchmark_facility_speciality_vascular', language: '@language_fr_ca', value: Vasculaire } }
  - { fields: { placeholder: '@placeholder_is_facility_accredited_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@placeholder_is_facility_accredited_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@benchmark_facility_regulators_cbhai_accreditation', language: '@language_fr_ca', value: 'Accréditation CBHAI' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_jci_accreditation', language: '@language_fr_ca', value: 'Accréditation JCI' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_canadian_accreditation', language: '@language_fr_ca', value: 'Accréditation canadienne' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_australian_accreditation', language: '@language_fr_ca', value: 'Accréditation australienne' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_cap_accreditation', language: '@language_fr_ca', value: 'Accréditation CAP' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_magnet_accreditation', language: '@language_fr_ca', value: 'Accréditation Magnet' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_cqc', language: '@language_fr_ca', value: CQC } }
  - { fields: { placeholder: '@benchmark_facility_regulators_iso_certificate', language: '@language_fr_ca', value: 'Certificat ISO' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_hcac', language: '@language_fr_ca', value: HCAC } }
  - { fields: { placeholder: '@benchmark_facility_regulators_hasp', language: '@language_fr_ca', value: HASP } }
  - { fields: { placeholder: '@benchmark_facility_regulators_hippa', language: '@language_fr_ca', value: HIPPA } }
  - { fields: { placeholder: '@placeholder_data_used_for_benchmarking_yes', language: '@language_fr_ca', value: Oui } }
  - { fields: { placeholder: '@placeholder_data_used_for_benchmarking_no', language: '@language_fr_ca', value: Non } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_country_title', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_country_label', language: '@language_fr_ca', value: Pays } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_year_title', language: '@language_fr_ca', value: Année } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_year_label', language: '@language_fr_ca', value: Année } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_healthcare_sector_title', language: '@language_fr_ca', value: 'Secteur de la santé' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_healthcare_sector_label', language: '@language_fr_ca', value: 'Secteur de la santé' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_category_title', language: '@language_fr_ca', value: 'Catégorie d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_category_label', language: '@language_fr_ca', value: 'Catégorie d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_parent_facility_title', language: '@language_fr_ca', value: 'Établissement parent' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_parent_facility_label', language: '@language_fr_ca', value: 'Établissement parent' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_type_title', language: '@language_fr_ca', value: 'Type d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_type_label', language: '@language_fr_ca', value: 'Type d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_name_title', language: '@language_fr_ca', value: 'Nom d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_name_label', language: '@language_fr_ca', value: 'Nom d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_title', language: '@language_fr_ca', value: 'Logo d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_label', language: '@language_fr_ca', value: 'Logo d''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_logo_introductory_text', language: '@language_fr_ca', value: "Les dimensions du Logo doivent être de 200\_px X 200\_px" } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_speciality_title', language: '@language_fr_ca', value: 'Spécialité de l''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_facility_speciality_label', language: '@language_fr_ca', value: 'Spécialité de l''établissement' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_population_covered_title', language: '@language_fr_ca', value: 'Population couverte' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_population_covered_label', language: '@language_fr_ca', value: 'Population couverte' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_yearly_budget_title', language: '@language_fr_ca', value: 'Budget annuel' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_yearly_budget_label', language: '@language_fr_ca', value: 'Budget annuel' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_beds_title', language: '@language_fr_ca', value: 'Nombre de lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_beds_label', language: '@language_fr_ca', value: 'Nombre de lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_staff_title', language: '@language_fr_ca', value: 'Nombre d''employés' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_staff_label', language: '@language_fr_ca', value: 'Nombre d''employés' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_physicians_title', language: '@language_fr_ca', value: 'Nombre de médecins' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_physicians_label', language: '@language_fr_ca', value: 'Nombre de médecins' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_nurses_title', language: '@language_fr_ca', value: 'Nombre d''infirmières' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_nurses_label', language: '@language_fr_ca', value: 'Nombre d''infirmières' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_pharmacists_title', language: '@language_fr_ca', value: 'Nombre de pharmaciens' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_pharmacists_label', language: '@language_fr_ca', value: 'Nombre de pharmaciens' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_allied_health_title', language: '@language_fr_ca', value: 'Nombre d''assistants médicaux' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_allied_health_label', language: '@language_fr_ca', value: 'Nombre d''assistants médicaux' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_discharges_title', language: '@language_fr_ca', value: 'Nombre de décharges' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_discharges_label', language: '@language_fr_ca', value: 'Nombre de décharges' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_admissions_title', language: '@language_fr_ca', value: 'Nombre d''admissions' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_admissions_label', language: '@language_fr_ca', value: 'Nombre d''admissions' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_opd_visits_title', language: '@language_fr_ca', value: 'Nombre de visites d''opd' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_opd_visits_label', language: '@language_fr_ca', value: 'Nombre de visites d''opd' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_er_cases_title', language: '@language_fr_ca', value: 'Nombre de cas ER' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_er_cases_label', language: '@language_fr_ca', value: 'Nombre de cas ER' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_title', language: '@language_fr_ca', value: 'Nombre d''admissions dans les unités de soins intensifs' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_label', language: '@language_fr_ca', value: 'Nombre d''admissions dans les unités de soins intensifs' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_paramedics_title', language: '@language_fr_ca', value: 'Nombre d''ambulanciers' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_paramedics_label', language: '@language_fr_ca', value: 'Nombre d''ambulanciers' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_title', language: '@language_fr_ca', value: 'Nombre de voitures ambulances' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_label', language: '@language_fr_ca', value: 'Nombre de voitures ambulances' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescriptions_title', language: '@language_fr_ca', value: 'Nombre de prescriptions' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescriptions_label', language: '@language_fr_ca', value: 'Nombre de prescriptions' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_title', language: '@language_fr_ca', value: 'Nombre de médicaments prescrits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_label', language: '@language_fr_ca', value: 'Nombre de médicaments prescrits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_surgeries_title', language: '@language_fr_ca', value: 'Nombre de chirurgies' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_surgeries_label', language: '@language_fr_ca', value: 'Nombre de chirurgies' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_deliveries_title', language: '@language_fr_ca', value: 'Nombre de livraisons' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_num_deliveries_label', language: '@language_fr_ca', value: 'Nombre de livraisons' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_normal_delivery_title', language: '@language_fr_ca', value: 'Livraison normale' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_normal_delivery_label', language: '@language_fr_ca', value: 'Livraison normale' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_title', language: '@language_fr_ca', value: 'Livraison anormale' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_label', language: '@language_fr_ca', value: 'Livraison anormale' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_maternity_ratio_title', language: '@language_fr_ca', value: 'Ratio de maternité' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_maternity_ratio_label', language: '@language_fr_ca', value: 'Ratio de maternité' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_title', language: '@language_fr_ca', value: 'Coût quotidien moyen des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_label', language: '@language_fr_ca', value: 'Coût quotidien moyen des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_title', language: '@language_fr_ca', value: 'Taux d''occupation des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_label', language: '@language_fr_ca', value: 'Taux d''occupation des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_title', language: '@language_fr_ca', value: 'Taux de durée du séjour' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_label', language: '@language_fr_ca', value: 'Taux de durée du séjour' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_title', language: '@language_fr_ca', value: 'Taux de rotation des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_label', language: '@language_fr_ca', value: 'Taux de rotation des lits' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_title', language: '@language_fr_ca', value: 'Rapport médecins/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_label', language: '@language_fr_ca', value: 'Rapport médecins/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_title', language: '@language_fr_ca', value: 'Rapport lits/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_label', language: '@language_fr_ca', value: 'Rapport lits/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_title', language: '@language_fr_ca', value: 'Rapport infirmières/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_label', language: '@language_fr_ca', value: 'Rapport infirmières/patients' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_title', language: '@language_fr_ca', value: 'L''établissement est-il accrédité?' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_label', language: '@language_fr_ca', value: 'L''établissement est-il accrédité?' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_title', language: '@language_fr_ca', value: 'Régulateurs d''accréditation' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_label', language: '@language_fr_ca', value: 'Régulateurs d''accréditation' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_title', language: '@language_fr_ca', value: 'Les données peuvent-elles être utilisées pour l''analyse comparative?' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_label', language: '@language_fr_ca', value: 'Les données peuvent-elles être utilisées pour l''analyse comparative?' } }
  - { fields: { placeholder: '@benchmark_facility_regulators_data_commission_on_accreditation', language: '@language_fr_ca', value: 'Commission d''accréditation des installations de revalidation' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_email_address_title', language: '@language_fr_ca', value: 'Adresse courriel' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_email_address_label', language: '@language_fr_ca', value: 'Adresse courriel' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_phone_number_title', language: '@language_fr_ca', value: 'Numéro de téléphone' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_phone_number_label', language: '@language_fr_ca', value: 'Numéro de téléphone' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_fax_title', language: '@language_fr_ca', value: Télécopieur } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_fax_label', language: '@language_fr_ca', value: Télécopieur } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_website_title', language: '@language_fr_ca', value: 'Site Web' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_website_label', language: '@language_fr_ca', value: 'Site Web' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_location_validation_error', language: '@language_fr_ca', value: 'Emplacement non valide pour cette année, veuillez en sélectionner un autre' } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_location_title', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@benchmark_facility_profile_fields_location_label', language: '@language_fr_ca', value: Emplacement } }
  - { fields: { placeholder: '@benchmark_facility_speciality_na_label', language: '@language_fr_ca', value: S.O. } }
  - { fields: { placeholder: '@benchmark_facility_type_na_label', language: '@language_fr_ca', value: S.O. } }
  - { fields: { placeholder: '@benchmark_facility_category_na_label', language: '@language_fr_ca', value: S.O. } }
  - { fields: { placeholder: '@benchmark_facility_national_organisation_code_title', language: '@language_fr_ca', value: 'Code national de l''organisation' } }
  - { fields: { placeholder: '@benchmark_facility_national_organisation_code_label', language: '@language_fr_ca', value: 'Code national de l''organisation' } }
  - { fields: { placeholder: '@benchmark_facility_latitude_title', language: '@language_fr_ca', value: Latitude } }
  - { fields: { placeholder: '@benchmark_facility_latitude_label', language: '@language_fr_ca', value: Latitude } }
  - { fields: { placeholder: '@benchmark_facility_longitude_title', language: '@language_fr_ca', value: Longitude } }
  - { fields: { placeholder: '@benchmark_facility_longitude_label', language: '@language_fr_ca', value: Longitude } }
  - { fields: { placeholder: '@benchmark_facility_twitter_url_title', language: '@language_fr_ca', value: 'URL Twitter de l''organisme de santé' } }
  - { fields: { placeholder: '@benchmark_facility_twitter_url_label', language: '@language_fr_ca', value: 'URL Twitter de l''organisme de santé' } }
  - { fields: { placeholder: '@benchmark_facility_facebook_url_title', language: '@language_fr_ca', value: 'URL Facebook de l''organisme de santé' } }
  - { fields: { placeholder: '@benchmark_facility_facebook_url_label', language: '@language_fr_ca', value: 'URL Facebook de l''organisme de santé' } }
