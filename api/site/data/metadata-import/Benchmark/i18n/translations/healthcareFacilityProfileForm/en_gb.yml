entityClass: I18n\Entity\Translation
priority: 15
data:
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_details_name'
      language: '@language_en_gb'
      value: Facility Details
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_and_ratios_name'
      language: '@language_en_gb'
      value: Facility Indicators and Ratios
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_indicators_name'
      language: '@language_en_gb'
      value: Facility Indicators
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_ratios_name'
      language: '@language_en_gb'
      value: Facility Ratios
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_accreditation_name'
      language: '@language_en_gb'
      value: Facility Accreditation
  - fields:
      placeholder: '@placeholder_benchmark_facility_profile_sections_facility_contacts_name'
      language: '@language_en_gb'
      value: Facility Contacts
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_health'
      language: '@language_en_gb'
      value: Ministry of Health
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_king_faisal_specialist_hospital_and_research_centre'
      language: '@language_en_gb'
      value: King Faisal Specialist Hospital & Research Centre
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_national_guard_health_affairs'
      language: '@language_en_gb'
      value: National Guard Health Affairs
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_johns_hopkins_aramco_healthcare'
      language: '@language_en_gb'
      value: Johns Hopkins Aramco Healthcare
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_royal_commission'
      language: '@language_en_gb'
      value: Royal Commission
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_interior'
      language: '@language_en_gb'
      value: Ministry of Interior
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_ministry_of_defence_affairs'
      language: '@language_en_gb'
      value: Ministry of Defence Affairs
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_saudi_red_crescent_authority'
      language: '@language_en_gb'
      value: Saudi Red Crescent Authority
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_private_sector'
      language: '@language_en_gb'
      value: Private Sector
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_royal_clinics'
      language: '@language_en_gb'
      value: Royal clinics
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_military_intelligence'
      language: '@language_en_gb'
      value: Military Intelligence
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_saudi_airlines_clinics'
      language: '@language_en_gb'
      value: Saudi Airlines Clinics
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_university_hospitals'
      language: '@language_en_gb'
      value: University Hospitals
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_saudi_center_for_the_accreditation_of_health_facilities'
      language: '@language_en_gb'
      value: Saudi Center for the accreditation of health facilities
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_general_authority_for_food_and_drug_administration'
      language: '@language_en_gb'
      value: General Authority for Food and Drug Administration
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_saudi_health_council'
      language: '@language_en_gb'
      value: Saudi Health Council
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_council_of_cooperative_health_insurance'
      language: '@language_en_gb'
      value: Council of Cooperative Health Insurance
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_saudi_commission_for_health_specialties'
      language: '@language_en_gb'
      value: Saudi Commission for Health Specialties
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_general_organization_for_social_insurance'
      language: '@language_en_gb'
      value: General Organization for Social Insurance
  - fields:
      placeholder: '@placeholder_benchmark_healthcare_sector_others'
      language: '@language_en_gb'
      value: Others
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_general_facility'
      language: '@language_en_gb'
      value: General Facility
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_referral_facility'
      language: '@language_en_gb'
      value: Referral Facility
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_ambulatory_facility'
      language: '@language_en_gb'
      value: Ambulatory Facility
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_gp_facility'
      language: '@language_en_gb'
      value: GP Facility
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_dental_facility'
      language: '@language_en_gb'
      value: Dental Facility
  - fields:
      placeholder: '@placeholder_benchmark_facility_category_community_pharmacy'
      language: '@language_en_gb'
      value: Community Pharmacy
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_hospital'
      language: '@language_en_gb'
      value: Hospital
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_primary_healthcare_center'
      language: '@language_en_gb'
      value: Primary Healthcare Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_medical_center_polyclinic'
      language: '@language_en_gb'
      value: Medical Center / Polyclinic
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_physiotherapy_center'
      language: '@language_en_gb'
      value: Physiotherapy Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_nutrition_center'
      language: '@language_en_gb'
      value: Nutrition Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_rehabilitation_center'
      language: '@language_en_gb'
      value: Rehabilitation Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_home_care_center'
      language: '@language_en_gb'
      value: Home Care Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_dialysis_center'
      language: '@language_en_gb'
      value: Dialysis Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_psychological_consulting_center'
      language: '@language_en_gb'
      value: Psychological Consulting Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_alternative_medicine_center'
      language: '@language_en_gb'
      value: Alternative Medicine Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_specialised_healthcare_clinic'
      language: '@language_en_gb'
      value: Specialised Healthcare Clinic
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_dental_clinic'
      language: '@language_en_gb'
      value: Dental Clinic
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_regional_lab'
      language: '@language_en_gb'
      value: Regional Lab
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_laboratory'
      language: '@language_en_gb'
      value: Laboratory
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_radiology_center'
      language: '@language_en_gb'
      value: Radiology Center
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_dental_lab'
      language: '@language_en_gb'
      value: Dental Lab
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_community_pharmacy'
      language: '@language_en_gb'
      value: Community Pharmacy
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_optics'
      language: '@language_en_gb'
      value: Optics
  - fields:
      placeholder: '@placeholder_benchmark_facility_type_ambulance_station'
      language: '@language_en_gb'
      value: Ambulance Station
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_cardiology'
      language: '@language_en_gb'
      value: Cardiology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_dentistry'
      language: '@language_en_gb'
      value: Dentistry
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_dermatology'
      language: '@language_en_gb'
      value: Dermatology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_ent'
      language: '@language_en_gb'
      value: ENT
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_family_medicine'
      language: '@language_en_gb'
      value: Family Medicine
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_general_surgery'
      language: '@language_en_gb'
      value: General Surgery
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_internal_medicine'
      language: '@language_en_gb'
      value: Internal Medicine
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_laboratory'
      language: '@language_en_gb'
      value: Laboratory
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_neurology'
      language: '@language_en_gb'
      value: Neurology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_neurosurgery'
      language: '@language_en_gb'
      value: Neurosurgery
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_obstetrics_gynaecology'
      language: '@language_en_gb'
      value: Obstetrics & Gynaecology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_oncology'
      language: '@language_en_gb'
      value: Oncology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_ophthalmology'
      language: '@language_en_gb'
      value: Ophthalmology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_orthopedics'
      language: '@language_en_gb'
      value: Orthopedics
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_pediatrics'
      language: '@language_en_gb'
      value: Pediatrics
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_physiotherapy'
      language: '@language_en_gb'
      value: Physiotherapy
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_plastic_surgery'
      language: '@language_en_gb'
      value: Plastic Surgery
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_psychiatry'
      language: '@language_en_gb'
      value: Psychiatry
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_radiology'
      language: '@language_en_gb'
      value: Radiology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_rheumatology'
      language: '@language_en_gb'
      value: Rheumatology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_urology'
      language: '@language_en_gb'
      value: Urology
  - fields:
      placeholder: '@placeholder_benchmark_facility_speciality_vascular'
      language: '@language_en_gb'
      value: Vascular
  - fields:
      placeholder: '@benchmark_facility_profile_fields_country_title'
      language: '@language_en_gb'
      value: Country
  - fields:
      placeholder: '@benchmark_facility_profile_fields_country_label'
      language: '@language_en_gb'
      value: Country
  - fields:
      placeholder: '@benchmark_facility_profile_fields_year_title'
      language: '@language_en_gb'
      value: Year
  - fields:
      placeholder: '@benchmark_facility_profile_fields_year_label'
      language: '@language_en_gb'
      value: Year
  - fields:
      placeholder: '@benchmark_facility_profile_fields_healthcare_sector_title'
      language: '@language_en_gb'
      value: Health care sector
  - fields:
      placeholder: '@benchmark_facility_profile_fields_healthcare_sector_label'
      language: '@language_en_gb'
      value: Health care sector
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_category_title'
      language: '@language_en_gb'
      value: Facility category
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_category_label'
      language: '@language_en_gb'
      value: Facility category
  - fields:
      placeholder: '@benchmark_facility_profile_fields_parent_facility_title'
      language: '@language_en_gb'
      value: Parent facility
  - fields:
      placeholder: '@benchmark_facility_profile_fields_parent_facility_label'
      language: '@language_en_gb'
      value: Parent facility
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_type_title'
      language: '@language_en_gb'
      value: Facility Type
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_type_label'
      language: '@language_en_gb'
      value: Facility Type
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_name_title'
      language: '@language_en_gb'
      value: Facility name
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_name_label'
      language: '@language_en_gb'
      value: Facility name
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_logo_title'
      language: '@language_en_gb'
      value: Facility Logo
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_logo_label'
      language: '@language_en_gb'
      value: Facility Logo
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_logo_introductory_text'
      language: '@language_en_gb'
      value: Logo size must be 200px x 200px
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_speciality_title'
      language: '@language_en_gb'
      value: Facility speciality
  - fields:
      placeholder: '@benchmark_facility_profile_fields_facility_speciality_label'
      language: '@language_en_gb'
      value: Facility speciality
  - fields:
      placeholder: '@benchmark_facility_profile_fields_population_covered_title'
      language: '@language_en_gb'
      value: Population covered
  - fields:
      placeholder: '@benchmark_facility_profile_fields_population_covered_label'
      language: '@language_en_gb'
      value: Population covered
  - fields:
      placeholder: '@benchmark_facility_profile_fields_yearly_budget_title'
      language: '@language_en_gb'
      value: Yearly budget
  - fields:
      placeholder: '@benchmark_facility_profile_fields_yearly_budget_label'
      language: '@language_en_gb'
      value: Yearly budget
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_beds_title'
      language: '@language_en_gb'
      value: Number of beds
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_beds_label'
      language: '@language_en_gb'
      value: Number of beds
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_staff_title'
      language: '@language_en_gb'
      value: Number of staff
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_staff_label'
      language: '@language_en_gb'
      value: Number of staff
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_physicians_title'
      language: '@language_en_gb'
      value: Number of physicians
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_physicians_label'
      language: '@language_en_gb'
      value: Number of physicians
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_nurses_title'
      language: '@language_en_gb'
      value: Number of nurses
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_nurses_label'
      language: '@language_en_gb'
      value: Number of nurses
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_pharmacists_title'
      language: '@language_en_gb'
      value: Number of pharmacists
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_pharmacists_label'
      language: '@language_en_gb'
      value: Number of pharmacists
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_allied_health_title'
      language: '@language_en_gb'
      value: Number of allied health
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_allied_health_label'
      language: '@language_en_gb'
      value: Number of allied health
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_discharges_title'
      language: '@language_en_gb'
      value: Number of discharges
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_discharges_label'
      language: '@language_en_gb'
      value: Number of discharges
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_admissions_title'
      language: '@language_en_gb'
      value: Number of admissions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_admissions_label'
      language: '@language_en_gb'
      value: Number of admissions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_opd_visits_title'
      language: '@language_en_gb'
      value: Number of opd visits
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_opd_visits_label'
      language: '@language_en_gb'
      value: Number of opd visits
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_er_cases_title'
      language: '@language_en_gb'
      value: Number of ER cases
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_er_cases_label'
      language: '@language_en_gb'
      value: Number of ER cases
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_title'
      language: '@language_en_gb'
      value: Number of critical care units admissions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_critical_care_admissions_label'
      language: '@language_en_gb'
      value: Number of critical care units admissions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_paramedics_title'
      language: '@language_en_gb'
      value: Number of paramedix
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_paramedics_label'
      language: '@language_en_gb'
      value: Number of paramedix
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_title'
      language: '@language_en_gb'
      value: Number of ambulance cars
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_ambulance_cars_label'
      language: '@language_en_gb'
      value: Number of ambulance cars
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_prescriptions_title'
      language: '@language_en_gb'
      value: Number of prescriptions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_prescriptions_label'
      language: '@language_en_gb'
      value: Number of prescriptions
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_title'
      language: '@language_en_gb'
      value: Number of prescribed medications
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_prescribed_meds_label'
      language: '@language_en_gb'
      value: Number of prescribed medications
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_surgeries_title'
      language: '@language_en_gb'
      value: Number of surgeries
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_surgeries_label'
      language: '@language_en_gb'
      value: Number of surgeries
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_deliveries_title'
      language: '@language_en_gb'
      value: Number of deliveries
  - fields:
      placeholder: '@benchmark_facility_profile_fields_num_deliveries_label'
      language: '@language_en_gb'
      value: Number of deliveries
  - fields:
      placeholder: '@benchmark_facility_profile_fields_normal_delivery_title'
      language: '@language_en_gb'
      value: Normal delivery
  - fields:
      placeholder: '@benchmark_facility_profile_fields_normal_delivery_label'
      language: '@language_en_gb'
      value: Normal delivery
  - fields:
      placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_title'
      language: '@language_en_gb'
      value: Abnormal delivery
  - fields:
      placeholder: '@benchmark_facility_profile_fields_abnormal_delivery_label'
      language: '@language_en_gb'
      value: Abnormal delivery
  - fields:
      placeholder: '@benchmark_facility_profile_fields_maternity_ratio_title'
      language: '@language_en_gb'
      value: Maternity ratio
  - fields:
      placeholder: '@benchmark_facility_profile_fields_maternity_ratio_label'
      language: '@language_en_gb'
      value: Maternity ratio
  - fields:
      placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_title'
      language: '@language_en_gb'
      value: Average daily bed cost
  - fields:
      placeholder: '@benchmark_facility_profile_fields_avg_daily_bed_cost_label'
      language: '@language_en_gb'
      value: Average daily bed cost
  - fields:
      placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_title'
      language: '@language_en_gb'
      value: Bed occupancy rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_bed_occupancy_rate_label'
      language: '@language_en_gb'
      value: Bed occupancy rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_title'
      language: '@language_en_gb'
      value: Length of stay rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_length_of_stay_rate_label'
      language: '@language_en_gb'
      value: Length of stay rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_title'
      language: '@language_en_gb'
      value: Bed turnover rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_bed_turnover_rate_label'
      language: '@language_en_gb'
      value: Bed turnover rate
  - fields:
      placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_title'
      language: '@language_en_gb'
      value: Physicians ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_physicians_patients_ratio_label'
      language: '@language_en_gb'
      value: Physicians ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_title'
      language: '@language_en_gb'
      value: Beds ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_beds_patients_ratio_label'
      language: '@language_en_gb'
      value: Beds ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_title'
      language: '@language_en_gb'
      value: Nurses ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_nurses_patients_ratio_label'
      language: '@language_en_gb'
      value: Nurses ratio vs patients
  - fields:
      placeholder: '@benchmark_facility_profile_fields_email_address_title'
      language: '@language_en_gb'
      value: Email address
  - fields:
      placeholder: '@benchmark_facility_profile_fields_email_address_label'
      language: '@language_en_gb'
      value: Email address
  - fields:
      placeholder: '@benchmark_facility_profile_fields_phone_number_title'
      language: '@language_en_gb'
      value: Phone number
  - fields:
      placeholder: '@benchmark_facility_profile_fields_phone_number_label'
      language: '@language_en_gb'
      value: Phone number
  - fields:
      placeholder: '@benchmark_facility_profile_fields_fax_title'
      language: '@language_en_gb'
      value: Fax
  - fields:
      placeholder: '@benchmark_facility_profile_fields_fax_label'
      language: '@language_en_gb'
      value: Fax
  - fields:
      placeholder: '@benchmark_facility_profile_fields_website_title'
      language: '@language_en_gb'
      value: Website
  - fields:
      placeholder: '@benchmark_facility_profile_fields_website_label'
      language: '@language_en_gb'
      value: Website
  - fields:
      placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_title'
      language: '@language_en_gb'
      value: "Is the facility accredited?"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_is_facility_accredited_label'
      language: '@language_en_gb'
      value: "Is the facility accredited?"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_title'
      language: '@language_en_gb'
      value: "Accreditation Regulators"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_accreditation_regulators_label'
      language: '@language_en_gb'
      value: "Accreditation Regulators"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_title'
      language: '@language_en_gb'
      value: "Can the data be used for benchmarking?"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_data_used_for_benchmarking_label'
      language: '@language_en_gb'
      value: "Can the data be used for benchmarking?"
  - fields:
      placeholder: '@placeholder_is_facility_accredited_yes'
      language: '@language_en_gb'
      value: "Yes"
  - fields:
      placeholder: '@placeholder_is_facility_accredited_no'
      language: '@language_en_gb'
      value: "No"
  - fields:
      placeholder: '@benchmark_facility_regulators_cbhai_accreditation'
      language: '@language_en_gb'
      value: "CBHAI Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_jci_accreditation'
      language: '@language_en_gb'
      value: "JCI Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_canadian_accreditation'
      language: '@language_en_gb'
      value: "Canadian Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_australian_accreditation'
      language: '@language_en_gb'
      value: "Australian Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_cap_accreditation'
      language: '@language_en_gb'
      value: "CAP Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_magnet_accreditation'
      language: '@language_en_gb'
      value: "Magnet Accreditation"
  - fields:
      placeholder: '@benchmark_facility_regulators_cqc'
      language: '@language_en_gb'
      value: "CQC"
  - fields:
      placeholder: '@benchmark_facility_regulators_iso_certificate'
      language: '@language_en_gb'
      value: "ISO Certificate"
  - fields:
      placeholder: '@benchmark_facility_regulators_hcac'
      language: '@language_en_gb'
      value: "HCAC"
  - fields:
      placeholder: '@benchmark_facility_regulators_hasp'
      language: '@language_en_gb'
      value: "HASP"
  - fields:
      placeholder: '@benchmark_facility_regulators_hippa'
      language: '@language_en_gb'
      value: "HIPPA"
  - fields:
      placeholder: '@placeholder_data_used_for_benchmarking_yes'
      language: '@language_en_gb'
      value: "Yes"
  - fields:
      placeholder: '@placeholder_data_used_for_benchmarking_no'
      language: '@language_en_gb'
      value: "No"
  - fields:
      placeholder: '@benchmark_facility_profile_fields_location_validation_error'
      language: '@language_en_gb'
      value: 'Location invalid for this year please select another'
  - fields:
      placeholder: '@benchmark_facility_profile_fields_location_title'
      language: '@language_en_gb'
      value: 'Location'
  - fields:
      placeholder: '@benchmark_facility_profile_fields_location_label'
      language: '@language_en_gb'
      value: 'Location'
  - fields:
      placeholder: '@benchmark_facility_speciality_na_label'
      language: '@language_en_gb'
      value: 'N/A'
  - fields:
      placeholder: '@benchmark_facility_type_na_label'
      language: '@language_en_gb'
      value: 'N/A'
  - fields:
      placeholder: '@benchmark_facility_category_na_label'
      language: '@language_en_gb'
      value: 'N/A'
  - fields:
      placeholder: '@benchmark_facility_national_organisation_code_title'
      language: '@language_en_gb'
      value: 'National Organisation Code'
  - fields:
      placeholder: '@benchmark_facility_national_organisation_code_label'
      language: '@language_en_gb'
      value: 'National Organisation Code'
  - fields:
      placeholder: '@benchmark_facility_latitude_title'
      language: '@language_en_gb'
      value: 'Latitude'
  - fields:
      placeholder: '@benchmark_facility_latitude_label'
      language: '@language_en_gb'
      value: 'Latitude'
  - fields:
      placeholder: '@benchmark_facility_longitude_title'
      language: '@language_en_gb'
      value: 'Longitude'
  - fields:
      placeholder: '@benchmark_facility_longitude_label'
      language: '@language_en_gb'
      value: 'Longitude'
  - fields:
      placeholder: '@benchmark_facility_twitter_url_title'
      language: '@language_en_gb'
      value: 'Health Organisation Twitter URL'
  - fields:
      placeholder: '@benchmark_facility_twitter_url_label'
      language: '@language_en_gb'
      value: 'Health Organisation Twitter URL'
  - fields:
      placeholder: '@benchmark_facility_facebook_url_title'
      language: '@language_en_gb'
      value: 'Health Organisation Facebook URL'
  - fields:
      placeholder: '@benchmark_facility_facebook_url_label'
      language: '@language_en_gb'
      value: 'Health Organisation Facebook URL'
  - fields:
      placeholder: '@benchmark_facility_regulators_data_commission_on_accreditation'
      language: '@language_en_gb'
      value: 'Commission on Accreditation of Rehabilitation Facilities'
