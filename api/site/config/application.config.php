<?php

use <PERSON>inas\Cache\Storage\Adapter\Filesystem;
use Laminas\Cache\Storage\Adapter\Memory;

require_once __DIR__ . '/../bootstrap/dotenv.php';

$loadModules = [
    'modules' => [
        '<PERSON><PERSON>\\Di',
        '<PERSON><PERSON>\\Hydrator',
        '<PERSON><PERSON>\\Router',
        '<PERSON><PERSON>\\I18n',
        '<PERSON><PERSON>\\Serializer',
        '<PERSON><PERSON>\\Log',
        '<PERSON><PERSON>\\Cache',
        'Lam<PERSON>\\Db',
        'Laminas\\Paginator',
        'Laminas\\Form',
        'Laminas\\InputFilter',
        'Laminas\\Filter',
        '<PERSON>inas\\Validator',
        'EnliteMonolog',
        'DoctrineModule',
        'DoctrineORMModule',
        'MagmaDigital',
        'ApiSkeletons\\DoctrineORMHydrationModule',
        'Laminas\\ApiTools',
        'Laminas\\ApiTools\\Provider',
        'Laminas\\ApiTools\\ApiProblem',
        'Laminas\\ApiTools\\Hal',
        'Laminas\\ApiTools\\ContentNegotiation',
        'Laminas\\ApiTools\\ContentValidation',
        'Lam<PERSON>\\ApiTools\\Rest',
        'Laminas\\ApiTools\\Rpc',
        'Laminas\\ApiTools\\Versioning',
        'Laminas\\ApiTools\\Doctrine\\Server',
        Memory::class,
        Filesystem::class,

        // Apigility Modules
        'Tenant',
        'Config',
        'Acl',
        'User',
        'Action',
        'Form',
        'Application',
        'Identity',
        'Prince',
        'Contact',
        'File',
        'RiskRegister',
        'SafetyLearning',
        'SafetyAlert',
        'Control',
        'Device',
        'Medication',
        'Investigation',
        'Location',
        'Service',
        'Checklist',
        'Accreditation',
        'ClinicalAudit',
        'Rounding',
        'I18n',
        'Notification',
        'Sync',
        'HealthCheck',
        'Etl',
        'DistributionList',
        'ReportableIncident',
        'Benchmark',
        'NotificationCentre',
        'Bjp',
    ],
    'module_listener_options' => [
        'use_laminas_loader' => false,
        'check_dependencies' => false,
        'config_glob_paths' => [
            './config/autoload/{,*.}{global,local}.php',
        ],
        'config_cache_key' => 'application.config.cache',
        'config_cache_enabled' => true,
        'module_map_cache_key' => 'application.module.cache',
        'module_map_cache_enabled' => true,
        'cache_dir' => 'data/cache/',
    ],
];

return $loadModules;
