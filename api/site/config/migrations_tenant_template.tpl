<?php

declare(strict_types=1);

namespace <namespace>;

use Doctrine\DBAL\Schema\Schema;

final class <className> extends TenantAwareMigration
{
    public function getDescription(): string
    {
        return '';
    }

    protected function tenantPostUp(Schema $schema, array $tenant): void
    {
        $tenantId = (int) $tenant['id'];

        /*
        * Do not include any table structure change inside this method.
        * Use preTenantPostUp or postTenantPostUp for that
        * Also consider not using TenantAwareMigration if tenantID is not used during the migration
        */
    }
}
