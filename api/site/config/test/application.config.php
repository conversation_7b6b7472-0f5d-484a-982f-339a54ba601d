<?php

use Laminas\Cache\Storage\Adapter\Filesystem;
use Laminas\Cache\Storage\Adapter\Memory;

/**
 * Configuration file generated by ZF Apigility Admin
 *
 * The previous config file has been stored in ./config/application.config.old
 */

return [
    'modules' => [
        '<PERSON><PERSON>\\Di',
        'Lam<PERSON>\\Hydrator',
        '<PERSON><PERSON>\\Router',
        'Laminas\\I18n',
        'Laminas\\Serializer',
        'Laminas\\Log',
        'Laminas\\Cache',
        'Lam<PERSON>\\Db',
        'Laminas\\Paginator',
        'Laminas\\Form',
        'Laminas\\InputFilter',
        'Laminas\\Filter',
        'Lam<PERSON>\\Validator',
        'EnliteMonolog',
        'DoctrineModule',
        'DoctrineORMModule',
        'MagmaDigital',
        'ApiSkeletons\\DoctrineORMHydrationModule',
        'Laminas\\ApiTools',
        'Laminas\\ApiTools\\Provider',
        'Laminas\\ApiTools\\ApiProblem',
        'Laminas\\ApiTools\\Hal',
        'Laminas\\ApiTools\\ContentNegotiation',
        'Lam<PERSON>\\ApiTools\\ContentValidation',
        'Laminas\\ApiTools\\Rest',
        'Laminas\\ApiTools\\Rpc',
        'Laminas\\ApiTools\\Versioning',
        'Laminas\\ApiTools\\Doctrine\\Server',
        Memory::class,
        Filesystem::class,

        // Apigility Modules
        'Tenant',
        'Config',
        'Acl',
        'User',
        'Action',
        'Form',
        'Application',
        'Identity',
        'Prince',
        'Contact',
        'File',
        'RiskRegister',
        'SafetyLearning',
        'SafetyAlert',
        'Control',
        'Device',
        'Medication',
        'Investigation',
        'Location',
        'Service',
        'Checklist',
        'Accreditation',
        'ClinicalAudit',
        'Rounding',
        'I18n',
        'Notification',
        'Sync',
        'HealthCheck',
        'Etl',
        'ReportableIncident',
        'DistributionList',
        'Benchmark',
        'Bjp',
    ],
    'module_listener_options' => [
        'use_laminas_loader' => false,
        'check_dependencies' => false,
        'config_glob_paths' => [
            __DIR__ . '/../autoload/{,*.}{global,local,local-test}.php',
        ],
        'config_cache_key' => 'application.config.cache',
        'config_cache_enabled' => false,
        'module_map_cache_key' => 'application.module.cache',
        'module_map_cache_enabled' => false,
        'cache_dir' => 'data/cache/',
    ],
];
