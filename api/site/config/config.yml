db:
  tables:
    cache: cache
    risk: risk
    risk_review: risk_review
    risk_rating: risk_rating
    risk_type: risk_type
    risk_user: risk_user
    control: control
    control_record: control_record
    control_module: control_module
    module: module

module:
  risk_register:
    id: 1
    name: risk_register
    title: Risk Register

installation:
  client:
    id: 831bbaaa-05d7-4d93-921a-2e43007beb87

organisation:
  type:
    trust:
      id: 0
      name: trust
    directorate:
      id: 1
      name: directorate
    speciality:
      id: 2
      name: speciality

risk:
  rating:
    grades:
      boundaries:
        green:
          key: green
          min: 0
          max: 3
          label: Low
        yellow:
          key: yellow
          min: 4
          max: 7
          label: Moderate
        orange:
          key: orange
          min: 8
          max: 14
          label: High
        red:
          key: red
          min: 15
          label: Extreme

    type:
      initial:
        name: initial
        id: 1
      current:
        name: current
        id: 2
      target:
        name: target
        id: 3

  status:
    user_transfer:
      default:
        id: 0
        name: no_transfer
      escalated:
        id: 1
        name: escalated
      de_escalated:
        id: 2
        name: de_escalated
      rejected:
        id: 3
        name: rejected

    primary:
      inactive:
        id: 0
        name: inactive
      active:
        id: 1
        name: active
      closed:
        id: 2
        name: closed
      rejected:
        id: 3
        name: rejected