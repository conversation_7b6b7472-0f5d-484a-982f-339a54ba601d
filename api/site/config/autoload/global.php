<?php

use Acl\Passthru\Rule;

$logLevel = (int) getenv('LOG_LEVEL') ?: Monolog\Logger::NOTICE;

return [
    'db' => [
        'host' => 'mssql',
        'port' => '1433',
        'database' => 'carlton',
        'user' => 'sa',
        'password' => 'C1aws0nite',
    ],
    'authentication' => [
        'passthru' => [
            'allowAll' => false,
            'rules' => [
                [
                    'type' => Rule::TYPE_URL,
                    'pattern' => '/healthcheck',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/public_me',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist_parent/checklist_response',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist_parent/checklist_form',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/i18n/domain',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/auth/login',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/prince_navigation_public',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/messages_public',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/users/api_user',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/public_file',
                ],
            ],
        ],
    ],
    'EnliteMonolog' => [
        'EnliteMonologService' => [
            // Logger name
            'name' => 'DatixCarlton',

            // Handlers, it can be service locator alias(string) or config(array)
            'handlers' => [
                // by config
                'default' => [
                    'name' => Monolog\Handler\StreamHandler::class,
                    'args' => [
                        'stream' => 'php://stderr',
                        'level' => $logLevel,
                        'bubble' => true,
                    ],
                    'formatter' => Application\Log\LogFormatter::class,
                ],
                'otel' => [
                    'name' => OpenTelemetry\Contrib\Logs\Monolog\Handler::class,
                    'args' => [
                        'loggerProvider' => OpenTelemetry\API\Globals::loggerProvider(),
                        'level' => $logLevel,
                    ],
                ],
            ],
        ],
    ],
    'service_manager' => [
        'factories' => [
            'doctrine.sql_logger' => Application\Log\SqlLoggerFactory::class,
            'MSSQLAdapter' => function($container) {
                $config = $container->get('config')['mssql'];
                $mssqlHost = 'mssql';
                $mssqlPort = '1433';
                $mssqlUser = 'sa';
                $mssqlPassword = 'C1aws0nite';
                $mssqlDatabase = 'carlton';
                $mssqlDriver = 'pdo_sqlsrv';
                return new PDO(
                    'sqlsrv:Server=' . $mssqlHost . ',' . $mssqlPort . ';Database=' . $mssqlDatabase,
                    $mssqlUser,
                    $mssqlPassword
                );
            },
        ],
    ],
];
