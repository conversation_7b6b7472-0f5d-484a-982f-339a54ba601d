<?php

use Acl\Passthru\Rule;

return [
    'db' => [],
    'authentication' => [
        'passthru' => [
            'allowAll' => false,
            'rules' => [
                [
                    'type' => Rule::TYPE_URL,
                    'pattern' => '/healthcheck',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/public_me',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist_parent/checklist_response',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/checklists_public/checklist_parent/checklist_form',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/i18n/domain',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/auth/login',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/prince_navigation_public',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/messages_public',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/users/api_user',
                ],
                [
                    'type' => Rule::TYPE_ROUTE,
                    'pattern' => 'datix_base.v1/public_file',
                ],
            ],
        ],
    ],
    'EnliteMonolog' => [
        'EnliteMonologService' => [
            // Logger name
            'name' => 'DatixCarlton',

            // Handlers, it can be service locator alias(string) or config(array)
            'handlers' => [
                // by config
                'default' => [
                    'name' => Monolog\Handler\StreamHandler::class,
                    'args' => [
                        'stream' => 'php://stderr',
                        'level' => (int) getenv('LOG_LEVEL') ?: Monolog\Logger::NOTICE,
                        'bubble' => true,
                    ],
                    'formatter' => Application\Log\LogFormatter::class,
                ],
                'otel' => [
                    'name' => OpenTelemetry\Contrib\Logs\Monolog\Handler::class,
                    'args' => [
                        'loggerProvider' => OpenTelemetry\API\Globals::loggerProvider(),
                        'level' => Monolog\Logger::NOTICE,
                    ],
                ],
            ],
        ],
    ],
    'service_manager' => [
        'factories' => [
            'doctrine.sql_logger' => Application\Log\SqlLoggerFactory::class,
        ],
    ],
];
