<?php

// zend-di AoT configuration

$factories = [];
$controllerFactories = [];
if (getenv('MODE') !== Application\Environment::DEVELOPMENT) {
    $factoriesFile = __DIR__ . '/../../data/cache/aot/factories.php';
    if (file_exists($factoriesFile)) {
        $factories = include $factoriesFile;
    }

    $controllersFile = __DIR__ . '/../../data/cache/aot/controllers.php';
    if (file_exists($controllersFile)) {
        $controllerFactories = include $controllersFile;
    }
}

return [
    // Because of controllers are using ControllerManager we need to have provided initializers also here
    'controllers' => [
        'factories' => $controllerFactories,
        'initializers' => [
            Application\Initialiser\AttributeInitializer::class,
            Application\Initialiser\EntityManagerInitializer::class,
            EnliteMonolog\Service\MonologServiceInitializer::class,
            Tenant\ServiceManager\Initializer\TenantServiceAwareInitializer::class,
            User\ServiceManager\UserServiceAwareInitializer::class,
        ],
        'abstract_factories' => [
            Laminas\Di\Container\ServiceManager\AutowireFactory::class,
        ],
    ],
    'service_manager' => [
        'factories' => array_merge(
            [
                'validator.recaptcha' => Datix\Validate\RecaptchaFactory::class,
                Doctrine\Laminas\Hydrator\DoctrineObject::class => DoctrineORMModule\Service\DoctrineObjectHydratorFactory::class,
            ],
            $factories,
        ),
    ],
    'api-tools-doctrine-query-provider' => [
        'abstract_factories' => [
            MagmaDigital\ApigilityDoctrine\Query\Provider\OrmProviderFactory::class,
            User\Entity\QueryProvider\UserFilterQueryProviderFactory::class,
        ],
    ],
    'dependencies' => [
        'auto' => [
            'aot' => [
                'namespace' => 'Application\\AoT\\Generated',
                'directory' => __DIR__ . '/../../data/cache/aot',
            ],
        ],
    ],
];
