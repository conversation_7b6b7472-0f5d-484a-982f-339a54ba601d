<?php

use Doctrine\Common\Cache\Psr6\DoctrineProvider;
use Symfony\Component\Cache\Adapter\ArrayAdapter;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;
use Laminas\Db\Adapter\Driver\Pdo\Pdo;

$mysqlHost = getenv('MYSQL_HOST');
$mysqlPort = getenv('MYSQL_PORT') ?: '3306';
$mysqlUser = getenv('MYSQL_USER');
$mysqlPassword = getenv('MYSQL_PASSWORD');
$mysqlDatabase = getenv('MYSQL_DATABASE');

// MSSQL Configuration - Use environment variables for better flexibility
$mssqlHost = 'mssql';
$mssqlPort = '1433';
$mssqlUser = 'sa';
$mssqlPassword = 'C1aws0nite';
$mssqlDatabase = 'carlton';
$mssqlDriver = 'pdo_sqlsrv';


//
//// MSSQL connection using PDO
//$dsn = 'sqlsrv:Server=' . $mssqlHost . ',' . $mssqlPort . ';Database=' . $mssqlDatabase;
//try {
//    $pdoMssql = new PDO($dsn, $mssqlUser, $mssqlPassword);
//    // Connection successful
////    error_log('===================================================\n\n');
////    error_log('MSSQL connection successful');
////    error_log('===================================================\n\n');
//    echo "===================================================\n";
//    echo "MSSQL connection successful\n";
//    echo "===================================================\n";
//} catch (PDOException $e) {
//    // Handle error
//    error_log($e->getMessage());
//}

$config = [
    'doctrine' => [
        'cache' => [
            'filesystem' => [
                'class' => 'fileCache',
            ],
            'array' => [
                'class' => 'memoryCache',
            ],
        ],
        'configuration' => [
            'cache_dir' => __DIR__ . '/../../data/DoctrineORMModule/Cache',
            'orm_default' => [
                // Generate proxies automatically (turn off for production)
                'generate_proxies' => false,
                'proxy_dir' => __DIR__ . '/../../data/DoctrineORMModule/Proxy',
                'query_cache' => (bool) getenv('DOCTRINE_QUERY_CACHE_ENABLED') ? 'filesystem' : 'array',
                'metadata_cache' => (bool) getenv('DOCTRINE_METADATA_CACHE_ENABLED') ? 'filesystem' : 'array',
            ],
        ],
        'orm' => [
            'mappings' => [
                'App' => [
                    'type' => 'attribute',
                ],
            ],
        ],
        'connection' => [
            'orm_default' => [
                'driverClass' => Doctrine\DBAL\Driver\PDO\SQLSrv\Driver::class, // MSSQL driver
                'params' => [
                    'host'     => $mssqlHost,          // e.g. "mssql" if using Docker service
                    'port'     => $mssqlPort ?? 1433,  // default MSSQL port
                    'user'     => $mssqlUser,          // e.g. "sa"
                    'password' => trim($mssqlPassword),
                    'dbname'   => $mssqlDatabase,      // e.g. "capture"
                    'driver'   => $mssqlDriver,        // switch from mysql
                    'charset'  => 'UTF-8',
                ],
            ],
        ],
        'migrations_configuration' => [
            'orm_default' => [
                'table_storage' => [
                    'table_name' => 'migrations',
                ],
                'migrations_paths' => [
                    'Datix\Migration' => __DIR__ . '/../../data/migrations',
                ],
                'custom_template' => __DIR__ . '/../migrations_template.tpl',
            ],
        ],
        'eventmanager' => [
            'orm_default' => [
                'subscribers' => [
                    Gedmo\Sluggable\SluggableListener::class,
                    Gedmo\SoftDeleteable\SoftDeleteableListener::class,
                    Gedmo\Timestampable\TimestampableListener::class,
                    Gedmo\Tree\TreeListener::class,
                    Gedmo\Uploadable\UploadableListener::class,
                ],
            ],
        ],
    ],
    'service_manager' => [
        'debug' => true,
        'factories' => [
            Gedmo\Loggable\LoggableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Sluggable\SluggableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\SoftDeleteable\SoftDeleteableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Timestampable\TimestampableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Tree\TreeListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Uploadable\UploadableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            'fileCache' => static function () {
                return DoctrineProvider::wrap(new FilesystemAdapter(directory: __DIR__ . '/../../data/cache'));
            },
            'memoryCache' => static function () {
                return DoctrineProvider::wrap(new ArrayAdapter());
            },
        ],
    ],
    'db' => [
        'database' => $mssqlDatabase,
        'driver' => $mssqlDriver,
        'hostname' => $mssqlHost,
        'port' => $mssqlPort,
        'username' => $mssqlUser,
        'password' => $mssqlPassword,
        'dsn' => "mysql:host={$mssqlHost};dbname={$mssqlDatabase};port={$mssqlPort}",
    ],
];

if (getenv('ENABLE_SQL_LOGGING')) {
    $config['doctrine']['configuration']['orm_default']['sql_logger'] = 'doctrine.sql_logger';
}

return $config;
