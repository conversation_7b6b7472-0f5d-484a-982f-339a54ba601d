<?php

use Doctrine\Common\Cache\Psr6\DoctrineProvider;
use Symfony\Component\Cache\Adapter\ArrayAdapter;
use Symfony\Component\Cache\Adapter\FilesystemAdapter;

$mysqlHost = getenv('MYSQL_HOST');
$mysqlPort = getenv('MYSQL_PORT') ?: '3306';
$mysqlUser = getenv('MYSQL_USER');
$mysqlPassword = getenv('MYSQL_PASSWORD');
$mysqlDatabase = getenv('MYSQL_DATABASE');

$mssqlHost = 'localhost';
$mssqlPort = '1434';
$mssqlUser = 'SA';
$mssqlPassword = 'C1aws0nite';
$mssqlDatabase = 'Sample1';

$config = [
    'doctrine' => [
        'cache' => [
            'filesystem' => [
                'class' => 'fileCache',
            ],
            'array' => [
                'class' => 'memoryCache',
            ],
        ],
        'configuration' => [
            'cache_dir' => __DIR__ . '/../../data/DoctrineORMModule/Cache',
            'orm_default' => [
                // Generate proxies automatically (turn off for production)
                'generate_proxies' => false,
                'proxy_dir' => __DIR__ . '/../../data/DoctrineORMModule/Proxy',
                'query_cache' => (bool) getenv('DOCTRINE_QUERY_CACHE_ENABLED') ? 'filesystem' : 'array',
                'metadata_cache' => (bool) getenv('DOCTRINE_METADATA_CACHE_ENABLED') ? 'filesystem' : 'array',
            ],
            // Optional: configuration for second entity manager
            'orm_mssql' => [
                'generate_proxies' => false,
                'proxy_dir' => __DIR__ . '/../../data/DoctrineORMModule/Proxy',
                'query_cache' => 'array',
                'metadata_cache' => 'array',
            ],
        ],
        'orm' => [
            'mappings' => [
                'App' => [
                    'type' => 'attribute',
                ],
            ],
        ],
        'connection' => [
            'orm_default' => [
                'driverClass' => Doctrine\DBAL\Driver\PDO\MySQL\Driver::class,
                'params' => [
                    'host' => $mysqlHost,
                    'port' => $mysqlPort,
                    'user' => $mysqlUser,
                    'password' => trim($mysqlPassword),
                    'dbname' => $mysqlDatabase,
                    'driver' => 'pdo_mysql',
                    'charset' => 'UTF8',
                ],
            ],
            'orm_mssql' => [ // MSSQL
                'driverClass' => Doctrine\DBAL\Driver\PDO\SQLSrv\Driver::class,
                'params' => [
                    'host' => $mssqlHost,
                    'port' => $mssqlPort,
                    'user' => $mssqlUser,
                    'password' => trim($mssqlPassword),
                    'dbname' => $mssqlDatabase,
                    'driver' => 'pdo_sqlsrv', // or 'pdo_dblib' for FreeTDS on Linux
                    'charset' => 'UTF8',
                ],
            ],
        ],
        'migrations_configuration' => [
            'orm_default' => [
                'table_storage' => [
                    'table_name' => 'migrations',
                ],
                'migrations_paths' => [
                    'Datix\Migration' => __DIR__ . '/../../data/migrations',
                ],
                'custom_template' => __DIR__ . '/../migrations_template.tpl',
            ],
        ],
        'eventmanager' => [
            'orm_default' => [
                'subscribers' => [
                    Gedmo\Sluggable\SluggableListener::class,
                    Gedmo\SoftDeleteable\SoftDeleteableListener::class,
                    Gedmo\Timestampable\TimestampableListener::class,
                    Gedmo\Tree\TreeListener::class,
                    Gedmo\Uploadable\UploadableListener::class,
                ],
            ],
        ],
    ],
    'service_manager' => [
        'debug' => true,
        'factories' => [
            Gedmo\Loggable\LoggableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Sluggable\SluggableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\SoftDeleteable\SoftDeleteableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Timestampable\TimestampableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Tree\TreeListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            Gedmo\Uploadable\UploadableListener::class => Laminas\ServiceManager\Factory\InvokableFactory::class,
            'fileCache' => static function () {
                return DoctrineProvider::wrap(new FilesystemAdapter(directory: __DIR__ . '/../../data/cache'));
            },
            'memoryCache' => static function () {
                return DoctrineProvider::wrap(new ArrayAdapter());
            },
        ],
    ],
    'db' => [
        'database' => $mysqlDatabase,
        'driver' => 'PDO_Mysql',
        'hostname' => $mysqlHost,
        'port' => $mysqlPort,
        'username' => $mysqlUser,
        'password' => $mysqlPassword,
        'dsn' => "mysql:host={$mysqlHost};dbname={$mysqlDatabase};port={$mysqlPort}",
    ],
    'db_mssql' => [
        // Legacy Laminas-style DB adapter for MSSQL
        'database' => $mssqlDatabase,
        'driver' => 'PDO_Sqlsrv',
        'hostname' => $mssqlHost,
        'port' => $mssqlPort,
        'username' => $mssqlUser,
        'password' => $mssqlPassword,
        'dsn' => "sqlsrv:Server={$mssqlHost},{$mssqlPort};Database={$mssqlDatabase}",
    ],
];

if (getenv('ENABLE_SQL_LOGGING')) {
    $config['doctrine']['configuration']['orm_default']['sql_logger'] = 'doctrine.sql_logger';
    $config['doctrine']['configuration']['orm_mssql']['sql_logger'] = 'doctrine.sql_logger';
}

return $config;
