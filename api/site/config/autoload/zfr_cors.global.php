<?php

$allowedOrigins = explode(',', getenv('CORS_ALLOWED_ORIGINS'));

$allowedOrigins = array_map(
    static function ($item) {
        return trim($item);
    },
    $allowedOrigins,
);

return [
    'zfr_cors' => [
        'allowed_origins' => $allowedOrigins,
        'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        'allowed_headers' => ['Authorization', 'Content-type', 'Content-length'],
        'allowed_credentials' => true,
    ],
];
