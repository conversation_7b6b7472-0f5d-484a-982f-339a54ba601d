<?php

declare(strict_types=1);

return [
    'EnliteMonolog' => [
        'EnliteMonologService' => [
            'name' => 'DatixCarlton',
            'handlers' => [
                'default' => [
                    'name' => Monolog\Handler\NullHandler::class,
                    'args' => [
                        'stream' => 'php://stderr',
                        'level' => (int) getenv('LOG_LEVEL') ?: Monolog\Logger::ERROR,
                        'bubble' => true,
                    ],
                    'formatter' => null,
                ],
            ],
        ],
    ],
];
