NEWRELIC_LICENSE=50ce0d9a5cfc57ab2fb8d5fcec14923e818037cd
NEWRELIC_APP_NAME=aaron-test
MYSQL_DATABASE=datix-rms-api
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=root
MSSQL_HOST=mssql
MSSQL_PORT=1433
MSSQL_USER=sa
MSSQL_PASSWORD=C1aws0nite
MSSQL_DATABASE=capture1
CORS_ALLOWED_ORIGINS=http://localhost.datix,127.0.0.1,chrome-extension://*,https://localhost.datix
INBOUND_JWT_USERNAME=prince.api
INBOUND_JWT_PASSWORD=Pa55word
PRINCE_BASE_URL=https://localhost.datix/capture/
PRINCE_API_BASE_URL=http://capture:8020/api/
PRINCE_JWT_USERNAME=carlton
PRINCE_JWT_PASSWORD=72ttSjTvz8vfshgf
DISABLE_PRINCE_SYNC=0
JWT_SECRET=df70874k3SWuZ3AMvCakyGHFvDaBLpqUT5Crv6QDHcflc4U7l3k0Ap6pI1RyXHFFEUmne8rLVVmWEAc0hVfx3g=
JWT_ALGORITHM=HS256
JWT_ISSUER=DATIX
ADMIN_LOGIN=<EMAIL>-seed
MODE=DEV
VERBOSE_DEBUG=1
ENABLE_SQL_LOGGING=0
LOG_LEVEL=100
POLICIES_AND_GUIDELINES_URL="http://************:8080/kb/#/en-US/home"
LOGIN_URL=https://localhost.datix/auth
LOGOUT_URL=https://localhost.datix/auth/logout
NOTIFICATIONS=0
HTTP_KAFKA=http://httpkafka:9111/v1
S3_ACCESS_KEY=COOLIO
S3_SECRET=BECAUSEIGOTITLIKETHAT
S3_BUCKET=dev
S3_UPLOADS_FOLDER=carlton
S3_REGION="us-east-1"
MONGO_URI=mongodb://mongo:27017
MONGO_DB=dev-etl
MONGO_COLLECTION=etl_data
MONGO_EMAIL_DB=dev-notifications
MONGO_EMAIL_COLLECTION=prince_emails
MONGO_DOMAIN_WHITELIST_AUDIT_COLLECTION=domain_whitelist_audit
MONGO_DOMAIN_WHITELIST_COLLECTION=domain_whitelist
REPORTING_URL=0
PUBLIC_URL=https://localhost.datix
ENABLE_RECORD_LOCKING=1
ENABLE_USER_DELEGATION=1
KAFKA_PROXY_URL=http://kafka_proxy:9090
KAFKA_ETL_TOPIC=dev_etl
ENABLE_LOCAL_AUTH=1
KEYCLOAK_URL=http://keycloak:8080
KEYCLOAK_REALM_USERS=datix
KEYCLOAK_REALM_ADMIN=master
KEYCLOAK_USER=admin
KEYCLOAK_PASS=admin
KEYCLOAK_LIFESPAN=86400
CENTRALADMIN_ENABLED=1
CENTRALADMIN_TARGET=0
CUSTOMER_DOCUMENT_TEMPLATES=NSWH
ENABLE_ACTION_SUBFORMS=1
NOTIFICATIONCENTRE_ENABLED=1
ENABLE_NOTIFICATION_CENTRE_FOR_ERM=0
ENABLE_NOTIFICATION_CENTRE_FOR_RIBS=0
ENABLE_NOTIFICATION_CENTRE_FOR_RECOMMENDATIONS_AND_CONTROLS=0
ENABLE_NOTIFICATION_CENTRE_FOR_INVESTIGATION_EVENT=0
NOTIFICATIONCENTRE_URL=http://notifications-centre:80
NOTIFICATION_CENTRE_EXTERNAL_URL=https://localhost.datix/notifications/api
LOCALADMINNULLLOCATION_ENABLED=0
EMAILREPORTS_ENABLED=1
HELP_SERVICE=Madcap
MEDICATIONS_UI_URL=https://localhost.datix/medications/
HOTSPOTS_URL=https://localhost.datix/hotspots
CARLTONMEDICATIONS_ENABLED=1
NAV_RESOURCE_COVID_ENABLED=1
NAV_RESOURCE_COVID_URL=https://www.rldatix.com/covid-19
MEDICATIONLOCATIONFILTERING_ENABLED=1
MEDICATIONLOCATIONFILTERING_LEVEL=3
EXPOSE_CONFIG=1
CARLTONEXTRACTION_ENABLED=1
LOCATION_DELETE_PARENT_NODES=1
LOCATION_MEMORY_LIMIT_MB=256
GRANULARMODULEADMIN_ENABLED=1
INCLUDEESCALATIONMODEL_ENABLED=0
ACL_WHITELIST_FULL_RECALC_ENABLED=0
CONTACTMERGING_ENABLED=0
REMOVE_MANDATORY_CONTACT_FIELDS=0
ENABLE_BJP=1
BJP_API_BASE_URL=http://background-job-processor:2999/
LOCATIONSERVICESYNC_ENABLED=1
PENDO_ENABLED=0
PENDO_API_KEY=
AUDIT_BC_CONTACT_ENABLED=0
S3_CONTACT_AUDIT_ACCESS_KEY=COOLIO
S3_CONTACT_AUDIT_SECRET=BECAUSEIGOTITLIKETHAT
S3_CONTACT_AUDIT_BUCKET=dev
S3_CONTACT_AUDIT_UPLOADS_FOLDER=contact-audit
S3_CONTACT_AUDIT_REGION="us-east-1"
CONTACT_AUDIT_MINIO_ENABLED=1
CONTACT_AUDIT_MINIO_ENDPOINT=http://s3:6969
DOCTRINE_METADATA_CACHE_ENABLED=0
DOCTRINE_QUERY_CACHE_ENABLED=0
ERMEXPORT_ENABLED=1
ERMEXPORT_RECORDLIMIT=2500
EMAILAUDIT_ENABLED=1
NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED=1
EDITUSERIDENTITY_ENABLED=0
EDITUSERIDENTITY_EXCEPTIONS=1,9
SECONDARYUSERLOOKUP_ENABLED=0
ENABLE_YELLOWFIN_ETL=1
