# BUILD Stage
FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/nginx:1.29.0 AS builder

ARG USE_ZSCALER_CERT

USER root

COPY docker/ZscalerRootCertificate-2048-SHA256.crt /etc/pki/ca-trust/source/anchors/
RUN if [ "$USE_ZSCALER_CERT" = "1" ]; then update-ca-trust extract; fi

RUN dnf upgrade -y && \
    dnf install -y --nodocs nc && \
    dnf clean all

# Runtime
FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/nginx:1.29.0 AS prod

# Copy ncat (which provides nc functionality) and its dependencies
COPY --from=builder /usr/bin/ncat /usr/bin/ncat
COPY --from=builder /usr/bin/nc /usr/bin/nc
COPY --from=builder /usr/lib64/libnl* /usr/lib64/
COPY --from=builder /usr/lib64/libpcap* /usr/lib64/
COPY --from=builder /usr/lib64/libibverbs* /usr/lib64/

COPY --chown=nginx:nginx --chmod=644 docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY --chown=nginx:nginx --chmod=755 docker/nginx/wait-for-php-fpm.sh /docker-entrypoint.d/
COPY --chown=nginx:nginx --chmod=755 docker/nginx/override-settings.sh /docker-entrypoint.d/

COPY --chown=nginx:nginx --chmod=644 site/public /var/www/api/site/public

USER nginx