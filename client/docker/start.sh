#!/bin/bash
set -e

# Prepare working directory
rm -rf /var/www/html
FRONTEND_DIR="/var/www/frontend"
cd ${FRONTEND_DIR}

# Prepare phing config
phing -verbose prepare-config

# MODE:PRODUCTION
MODE=${MODE:="DEVELOPMENT"}
if [[ ${MODE} == "PRODUCTION" ]]
then
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<API_URL>|$API_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<CONFIG_API_URL>|$CONFIG_API_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<SYSTEM_DATE_LOCALE>|$SYSTEM_DATE_LOCALE|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DIF_TIMEOUT_MINS>|$DIF_TIMEOUT_MINS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<RECAPTCHA_PUBLIC_KEY>|$RECAPTCHA_PUBLIC_KEY|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|\"<TEST_MODE>\"|$TEST_MODE|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DISABLE_PENDING_USERS_SERVICE>|$DISABLE_PENDING_USERS_SERVICE|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<LOGOUT_URL>|$LOGOUT_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<TOKEN_RENEW_API>|$TOKEN_RENEW_API|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<GRAPH_QL_ENDPOINT>|$GRAPH_QL_ENDPOINT|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_ACTION_SUBFORMS>|$ENABLE_ACTION_SUBFORMS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<FEATURE_SA_HEALTH_REQUIREMENTS>|$FEATURE_SA_HEALTH_REQUIREMENTS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_EXPORT_IMPORT>|$ENABLE_EXPORT_IMPORT|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_USER_DELEGATION>|$ENABLE_USER_DELEGATION|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<LOCALAUTH_ENABLED>|$LOCALAUTH_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_RISK_NOTIFICATIONS>|$ENABLE_RISK_NOTIFICATIONS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_PRIVATE_ATTACHMENTS>|$ENABLE_PRIVATE_ATTACHMENTS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<BENCHMARKING_ENABLED>|$BENCHMARKING_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_TEMPLATE_ATTACHMENTS>|$ENABLE_TEMPLATE_ATTACHMENTS|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<CUSTOMLOGO_ENABLED>|$CUSTOMLOGO_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DELEGATIONCARLTONACCESS_ENABLED>|$DELEGATIONCARLTONACCESS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ADMINCONTACTS_ENABLED>|$ADMINCONTACTS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<LOCALADMINAUDIT_ENABLED>|$LOCALADMINAUDIT_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ADMINAUDITLOG_ENABLED>|$ADMINAUDITLOG_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<RECOMMENDATIONAUDITLOG_ENABLED>|$RECOMMENDATIONAUDITLOG_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<NOTIFICATIONCENTRE_ENABLED>|$NOTIFICATIONCENTRE_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<NOTIFICATION_CENTRE_API_URL>|$NOTIFICATION_CENTRE_API_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<PRINCE_API_BASE_URL>|$PRINCE_API_BASE_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<EMAILREPORTS_ENABLED>|$EMAILREPORTS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ADUSERS_ENABLED>|$ADUSERS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<CARLTONMEDICATIONS_ENABLED>|$CARLTONMEDICATIONS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<MEDICATIONS_V2_API_URL>|$MEDICATIONS_V2_API_URL|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<MEDICATIONLOCATIONFILTERING_ENABLED>|$MEDICATIONLOCATIONFILTERING_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<CENTRALADMIN_TARGET>|$CENTRALADMIN_TARGET|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DRAFTLOCATIONS_ENABLED>|$DRAFTLOCATIONS_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DRAFTSERVICES_ENABLED>|$DRAFTSERVICES_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<EXPOSE_CONFIG>|$EXPOSE_CONFIG|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<LOCATION_DELETE_PARENT_NODES>|$LOCATION_DELETE_PARENT_NODES|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<SERVICE_DELETE_PARENT_NODES>|$SERVICE_DELETE_PARENT_NODES|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<DCIQ_VERSION_NUMBER>|$DCIQ_VERSION_NUMBER|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<INCLUDEESCALATIONMODEL_ENABLED>|$INCLUDEESCALATIONMODEL_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<EMAILAUDIT_ENABLED>|$EMAILAUDIT_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<PENDO_ENABLED>|$PENDO_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<PENDO_API_KEY>|$PENDO_API_KEY|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ETLTABLESYNCUI_ENABLED>|$ETLTABLESYNCUI_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<CONTACTSYNCINGUI_ENABLED>|$CONTACTSYNCINGUI_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ACTIONSYNCINGUI_ENABLED>|$ACTIONSYNCINGUI_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ERMEXPORT_ENABLED>|$ERMEXPORT_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<ENABLE_BJP>|$ENABLE_BJP|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED>|$NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED|g" "$i"; done
	for i in ${FRONTEND_DIR}/dist/scripts/*; do sed -i "s|<TODOLIST_ENABLED>|$TODOLIST_ENABLED|g" "$i"; done

  ln -s ${FRONTEND_DIR}/dist /var/www/html
# MODE:..
else
  sed -i "s|<API_URL>|$API_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<CONFIG_API_URL>|$CONFIG_API_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<SYSTEM_DATE_LOCALE>|$SYSTEM_DATE_LOCALE|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DIF_TIMEOUT_MINS>|$DIF_TIMEOUT_MINS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
	sed -i "s|<RECAPTCHA_PUBLIC_KEY>|$RECAPTCHA_PUBLIC_KEY|g" "${FRONTEND_DIR}/app/scripts/constants.js"
	sed -i "s|\"<TEST_MODE>\"|$TEST_MODE|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DISABLE_PENDING_USERS_SERVICE>|$DISABLE_PENDING_USERS_SERVICE|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<LOGOUT_URL>|$LOGOUT_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<TOKEN_RENEW_API>|$TOKEN_RENEW_API|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<GRAPH_QL_ENDPOINT>|$GRAPH_QL_ENDPOINT|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_ACTION_SUBFORMS>|$ENABLE_ACTION_SUBFORMS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<FEATURE_SA_HEALTH_REQUIREMENTS>|$FEATURE_SA_HEALTH_REQUIREMENTS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_EXPORT_IMPORT>|$ENABLE_EXPORT_IMPORT|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_USER_DELEGATION>|$ENABLE_USER_DELEGATION|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<LOCALAUTH_ENABLED>|$LOCALAUTH_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_RISK_NOTIFICATIONS>|$ENABLE_RISK_NOTIFICATIONS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_PRIVATE_ATTACHMENTS>|$ENABLE_PRIVATE_ATTACHMENTS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<BENCHMARKING_ENABLED>|$BENCHMARKING_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_TEMPLATE_ATTACHMENTS>|$ENABLE_TEMPLATE_ATTACHMENTS|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<CUSTOMLOGO_ENABLED>|$CUSTOMLOGO_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DELEGATIONCARLTONACCESS_ENABLED>|$DELEGATIONCARLTONACCESS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ADMINCONTACTS_ENABLED>|$ADMINCONTACTS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<LOCALADMINAUDIT_ENABLED>|$LOCALADMINAUDIT_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ADMINAUDITLOG_ENABLED>|$ADMINAUDITLOG_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<RECOMMENDATIONAUDITLOG_ENABLED>|$RECOMMENDATIONAUDITLOG_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<NOTIFICATIONCENTRE_ENABLED>|$NOTIFICATIONCENTRE_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<NOTIFICATION_CENTRE_API_URL>|$NOTIFICATION_CENTRE_API_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<PRINCE_API_BASE_URL>|$PRINCE_API_BASE_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<EMAILREPORTS_ENABLED>|$EMAILREPORTS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ADUSERS_ENABLED>|$ADUSERS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<CARLTONMEDICATIONS_ENABLED>|$CARLTONMEDICATIONS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<MEDICATIONS_V2_API_URL>|$MEDICATIONS_V2_API_URL|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<MEDICATIONLOCATIONFILTERING_ENABLED>|$MEDICATIONLOCATIONFILTERING_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<CENTRALADMIN_TARGET>|$CENTRALADMIN_TARGET|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DRAFTLOCATIONS_ENABLED>|$DRAFTLOCATIONS_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DRAFTSERVICES_ENABLED>|$DRAFTSERVICES_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<EXPOSE_CONFIG>|$EXPOSE_CONFIG|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<LOCATION_DELETE_PARENT_NODES>|$LOCATION_DELETE_PARENT_NODES|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<SERVICE_DELETE_PARENT_NODES>|$SERVICE_DELETE_PARENT_NODES|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<DCIQ_VERSION_NUMBER>|$DCIQ_VERSION_NUMBER|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<INCLUDEESCALATIONMODEL_ENABLED>|$INCLUDEESCALATIONMODEL_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<EMAILAUDIT_ENABLED>|$EMAILAUDIT_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<PENDO_ENABLED>|$PENDO_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<PENDO_API_KEY>|$PENDO_API_KEY|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ETLTABLESYNCUI_ENABLED>|$ETLTABLESYNCUI_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<CONTACTSYNCINGUI_ENABLED>|$CONTACTSYNCINGUI_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ACTIONSYNCINGUI_ENABLED>|$ACTIONSYNCINGUI_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ERMEXPORT_ENABLED>|$ERMEXPORT_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<ENABLE_BJP>|$ENABLE_BJP|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED>|$NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"
  sed -i "s|<TODOLIST_ENABLED>|$TODOLIST_ENABLED|g" "${FRONTEND_DIR}/app/scripts/constants.js"

  # HOT_MODE:1
  HOT_MODE=${HOT_MODE:=0}
  if [[ ${HOT_MODE} == 1 ]]
  then
    echo "Hot mode enabled - serving compiled files from build directory"
    rm -rf ${FRONTEND_DIR}/.tmp
    mkdir ${FRONTEND_DIR}/.tmp
    ln -s ${FRONTEND_DIR}/.tmp /var/www/html
    ln -s ${FRONTEND_DIR}/app/scripts /var/www/html/scripts
    ln -s ${FRONTEND_DIR}/app/bower_components /var/www/html/bower_components
    ln -s ${FRONTEND_DIR}/app/index.html /var/www/html/index.html
    ln -s ${FRONTEND_DIR}/app/404.html /var/www/html/404.html
    ln -s ${FRONTEND_DIR}/app/.htaccess /var/www/html/.htaccess
    ln -s ${FRONTEND_DIR}/app/favicon.ico /var/www/html/favicon.ico
  else
    ln -s ${FRONTEND_DIR}/app /var/www/html
  fi
fi

# Update HOSTNAME handlebar in 000-default for redirect
sed -i "s|<HOSTNAME>|$PUBLIC_URL|g" "/etc/apache2/sites-enabled/000-default.conf"

#Please work
source /etc/apache2/envvars

# Exec command
echo "Done. Starting apache now..."
apachectl -DFOREGROUND -e debug

