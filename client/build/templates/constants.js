/**
 * Autogenerated from client/build/templates/constants.js
 * Please don't make changes to the client/app/script/constants.js file directly
 */
(function (window) {
    'use strict';

    window.__env = window.__env || {};

    // API url
    window.__env.api_url = "<API_URL>";

    // Config api url
    window.__env.config_api_url = "<CONFIG_API_URL>";

    // Date formatting locale
    var env_var_locale = "<SYSTEM_DATE_LOCALE>";
    window.__env.date_locale = env_var_locale == "" ? 'en_gb' : env_var_locale;

    // DIF TIMEOUT value - TODO:Remove once the config service works properly
    window.__env.dif_timeout_mins = "<DIF_TIMEOUT_MINS>";

    window.__env.recaptcha_public_key = "<RECAPTCHA_PUBLIC_KEY>";
    window.__env.test_mode = "<TEST_MODE>";

    window.__env.disable_pending_users_service = "<DISABLE_PENDING_USERS_SERVICE>";

    window.__env.logout_url = "<LOGOUT_URL>";

    window.__env.renew_token_url = "<TOKEN_RENEW_API>";

    window.__env.graph_ql_endpoint = "<GRAPH_QL_ENDPOINT>";

    const enableActionSubforms = "<ENABLE_ACTION_SUBFORMS>";
    window.__env.enable_action_subforms = (enableActionSubforms === '' ? 0 : parseInt(enableActionSubforms, 10));

    const enablePrivateAttachments = "<ENABLE_PRIVATE_ATTACHMENTS>";
    window.__env.enable_private_attachments = (enablePrivateAttachments === '' ? 0 : parseInt(enablePrivateAttachments, 10));

    var saHealthRequirements = '<FEATURE_SA_HEALTH_REQUIREMENTS>';
    window.__env.featureSaHealthRequirements = (saHealthRequirements === '' ? 0 : parseInt(saHealthRequirements, 10));

    // Feature flag to enable config import/export feature on admin
    window.__env.enable_import_export = "<ENABLE_EXPORT_IMPORT>";

    // Debug Mode
    // Setting this to true will add the XDEBUG query parameter to all API requests
    window.__env.debugMode = false;

    const enableUserDelegation = '<ENABLE_USER_DELEGATION>';
    window.__env.enable_user_delegation = (enableUserDelegation === '' ? 0 : parseInt(enableUserDelegation, 10));

    const enableNewUserForm = '<LOCALAUTH_ENABLED>';
    window.__env.enable_user_form = (enableNewUserForm === '' ? 0 : parseInt(enableNewUserForm, 10));

    const enableRiskNotifications = '<ENABLE_RISK_NOTIFICATIONS>';
    window.__env.enable_risk_notifications = (enableRiskNotifications === '' ? 0 : parseInt(enableRiskNotifications, 10));

    const enableBenchmarking = '<BENCHMARKING_ENABLED>';
    window.__env.enable_benchmarking = (enableBenchmarking === '' ? 0 : parseInt(enableBenchmarking, 10));

    const enableTemplateAttachments = '<ENABLE_TEMPLATE_ATTACHMENTS>';
    window.__env.enable_template_attachments = (enableTemplateAttachments === '' ? 0 : parseInt(enableTemplateAttachments, 10));

    const customLogoEnabled = '<CUSTOMLOGO_ENABLED>';
    window.__env.custom_logo_enabled = (customLogoEnabled === '' ? 0 : parseInt(customLogoEnabled, 10));

    const delegationCarltonAccessEnabled = '<DELEGATIONCARLTONACCESS_ENABLED>';
    window.__env.delegation_carlton_access_enabled = (delegationCarltonAccessEnabled === '' ? 0 : parseInt(delegationCarltonAccessEnabled, 10));

    const adminContactsEnabled = '<ADMINCONTACTS_ENABLED>';
    window.__env.admin_contacts_enabled = (adminContactsEnabled === '' ? 0 : parseInt(adminContactsEnabled, 10));

    const localAdminAuditEnabled = '<LOCALADMINAUDIT_ENABLED>';
    window.__env.local_admin_audit_enabled = (localAdminAuditEnabled === '' ? 0 : parseInt(localAdminAuditEnabled, 10));

    const adminAuditLogEnabled = '<ADMINAUDITLOG_ENABLED>';
    window.__env.admin_audit_log_enabled = (adminAuditLogEnabled === '' ? 0 : parseInt(adminAuditLogEnabled, 10));

    const recommendationAuditLogEnabled = '<RECOMMENDATIONAUDITLOG_ENABLED>';
    window.__env.recommendation_audit_log_enabled = (recommendationAuditLogEnabled === '' ? 0 : parseInt(recommendationAuditLogEnabled, 10));

    const notificationCentreEnabled = '<NOTIFICATIONCENTRE_ENABLED>';
    window.__env.notification_centre_enabled = (notificationCentreEnabled === '' ? 0 : parseInt(notificationCentreEnabled, 10));

    window.__env.notification_centre_api_url = '<NOTIFICATION_CENTRE_API_URL>';

    window.__env.prince_api_base_url = '<PRINCE_API_BASE_URL>';

    const emailReportsEnabled = '<EMAILREPORTS_ENABLED>';
    window.__env.email_reports_enabled = (emailReportsEnabled === '' ? 0 : parseInt(emailReportsEnabled, 10));

    const adUsersEnabled = '<ADUSERS_ENABLED>';
    window.__env.active_directory_users_enabled = (adUsersEnabled === '' ? 0 : parseInt(adUsersEnabled, 10));

    const carltonMedicationsEnabled = '<CARLTONMEDICATIONS_ENABLED>';
    window.__env.carlton_medications_enabled = (carltonMedicationsEnabled === '' ? 0 : parseInt(carltonMedicationsEnabled, 10));

    window.__env.medications_v2_api_url = '<MEDICATIONS_V2_API_URL>';

    const medicationLocationFilteringEnabled = '<MEDICATIONLOCATIONFILTERING_ENABLED>';
    window.__env.medication_location_filtering_enabled = (medicationLocationFilteringEnabled === '' ? 0 : parseInt(medicationLocationFilteringEnabled, 10));

    const centralAdminTarget = '<CENTRALADMIN_TARGET>';
    window.__env.centraladmin_target = (centralAdminTarget === '' ? 0 : parseInt(centralAdminTarget, 10));

    const draftLocationsEnabled = '<DRAFTLOCATIONS_ENABLED>';
    window.__env.draft_locations_enabled = (draftLocationsEnabled === '' ? 0 : parseInt(draftLocationsEnabled, 10));

    const draftServicesEnabled = '<DRAFTSERVICES_ENABLED>';
    window.__env.draft_services_enabled = (draftServicesEnabled === '' ? 0 : parseInt(draftServicesEnabled, 10));

    const exposeConfig = '<EXPOSE_CONFIG>';
    window.__env.expose_config = (exposeConfig === '' ? 0 : parseInt(exposeConfig, 10));

    const locationDeleteParentNodes = '<LOCATION_DELETE_PARENT_NODES>';
    window.__env.location_delete_parent_nodes = (parseInt(locationDeleteParentNodes, 10) === 1);

    const serviceDeleteParentNodes = '<SERVICE_DELETE_PARENT_NODES>';
    window.__env.service_delete_parent_nodes = parseInt(serviceDeleteParentNodes, 10);

    window.__env.dciq_version_number = '<DCIQ_VERSION_NUMBER>';

    const includeEscalationModel = '<INCLUDEESCALATIONMODEL_ENABLED>';
    window.__env.include_escalation_model = (includeEscalationModel === '' ? 0 : parseInt(includeEscalationModel , 10));

    const emailAuditEnabled = '<EMAILAUDIT_ENABLED>';
    window.__env.email_audit_enabled = (emailAuditEnabled === '' ? 0 : parseInt(emailAuditEnabled , 10));

    const pendoEnabled = '<PENDO_ENABLED>';
    window.__env.pendo_enabled = (pendoEnabled === '' ? 0 : parseInt(pendoEnabled , 10));

    window.__env.pendo_api_key = '<PENDO_API_KEY>';

    const etlTableSyncUiEnabled = '<ETLTABLESYNCUI_ENABLED>';
    window.__env.etl_table_sync_ui_enabled = (parseInt(etlTableSyncUiEnabled , 10) === 1);

    const contactSyncingUiEnabled = '<CONTACTSYNCINGUI_ENABLED>';
    window.__env.contact_syncing_ui_enabled = (parseInt(contactSyncingUiEnabled , 10) === 1);

    const actionSyncingUiEnabled = '<ACTIONSYNCINGUI_ENABLED>';
    window.__env.action_syncing_ui_enabled = (parseInt(actionSyncingUiEnabled , 10) === 1);

    const ermExportEnabled = '<ERMEXPORT_ENABLED>';
    window.__env.erm_export_enabled = (parseInt(ermExportEnabled , 10) === 1);

    const bjpEnabled = '<ENABLE_BJP>';
    window.__env.bjp_enabled = (parseInt(bjpEnabled, 10) === 1);

    const notificationCentreWhitelistEnabled = '<NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED>';
    window.__env.notification_centre_whitelist_enabled = (parseInt(notificationCentreWhitelistEnabled , 10) === 1);

    const todoListEnabled = '<TODOLIST_ENABLED>';
    window.__env.todolist_enabled = (todoListEnabled === '' ? 1 : parseInt(todoListEnabled , 10));

}(this));
