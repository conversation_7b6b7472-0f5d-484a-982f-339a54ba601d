(function (angular) {
    'use strict';

    /**
     * Module definitions
     *
     * @todo: Eventually this should be refactored so that the module definitions take place
     *        within the modules themselves.
     */

    // Directives
    angular.module('directivesModule', ['baseModule', 'magma.formRenderer', 'magma.components']);

    // Services
    angular.module('servicesModule', []);

    // Filters
    angular.module('filtersModule', []);

    // Config
    angular.module('configModule', []);

    // Application Constants
    angular.module('constantsModule', []);

    // Application Variables
    angular.module('variablesModule', []);

    angular.module('riskRegisterModule', []);

    // Users
    angular.module('usersModule', []);

    // Investigations module
    angular.module('investigationsModule', []);

    // Rounding Module
    angular.module('roundingModule', []);

    // Clinical Audit Module (Old)
    // @todo: deprecate
    angular.module('clinicalAuditModuleOld', []);

    // Accreditations Module
    angular.module('accreditationModule', []);

    // Filters
    angular.module('filtersModule', []);

    // Devices
    angular.module('devicesModule', []);

    angular.module('equipmentModule', ['uuid4']);

    // Medications
    angular.module('medicationsModule', ['uuid4']);

    // Services (CDM)
    angular.module('servicesCdmModule', []);

    // Actions
    angular.module('actionsModule', []);

    // Notifications
    angular.module('notificationsModule', []);

    // Prince
    angular.module('princeModule', []);

    // Recommendations
    angular.module('aclModule', []);

    // System configuration
    angular.module('systemConfigurationModule', []);

    // Form import/export
    angular.module('configPortationModule', []);


    /**
     * Main application definition
     */
    angular.module('datixClient', [
        'ngAnimate',
        'ngCookies',
        'ngMessages',
        'ngResource',
        'ngSanitize',
        'ngTouch',
        'ui.router',
        'ui.sortable',
        'ui.bootstrap',
        'ui.bootstrap.datetimepicker',
        'moment-picker',
        'pascalprecht.translate',
        'datatables',
        'ngVis',
        'ui.tree',
        'ui.select',
        'angularMoment',
        'angular-loading-bar',
        'wysiwyg.module',
        'toastr',
        'flow',
        'chart.js',
        'ng-fusioncharts',
        'ngFileSaver',
        'checklist-model',
        'dcbImgFallback',
        'ngclipboard',
        'vcRecaptcha',
        'LocalStorageModule',
        'textAngular',
        'directivesModule',
        'filtersModule',
        'servicesModule',
        'configModule',
        'constantsModule',
        'variablesModule',
        'baseModule',
        'roundingModule',
        'clinicalAuditModuleOld',
        'devicesModule',
        'medicationsModule',
        'locationsModule',
        'servicesCdmModule',
        'usersModule',
        'accreditationModule',
        'contactsModule',
        'actionsModule',
        'princeModule',
        'aclModule',
        'systemConfigurationModule',
        'safetyLearningsModule',
        'safetyAlertsModule',
        'distributionListsModule',

        /**
         * New modules
         *
         * As of the start of Beta modules are registered within the modules themselves (*Module.js) instead of here,
         * meaning that each module is responsible for its own registration. This means that, in the need to disable
         * certain modules, only its dependency in the main app module needs to be removed.
         */

        'adminModule',
        'directivesModule',
        'checklistsModule',
        'clinicalAuditModule',
        'investigationsModule',
        'controlsModule',
        'riskRegisterModule',
        'formsModule',
        'equipmentModule',
        'configPortationModule',
        'reportableIncidentsModule',
        'notificationsModule',
        'benchmarkingModule',
        'notificationCentreModule',
    ])

        /**
         * Local Storage wrapper configuration
         */
        .config(function (localStorageServiceProvider) {
            localStorageServiceProvider.setPrefix('datix');
        })

        .filter('showAsUtc', function () {
            return function (input) {
                return moment(input).utc();
            };
        })

        .constant(
            'DEFAULT_RISK_REGISTER',
            4
        )

        .constant('LANGUAGE_DEFAULTS', {
            CODE: 'en_gb',
            DIRECTION: 'ltr'
        })

        .constant(
            'RECAPTCHA_PUBLIC_KEY',
            window.__env.recaptcha_public_key
        )

        .constant(
            'PRINCE_CLIENT_BASE_URL',
            'http://*************/datix/staging/index.php'
        )

        /**
         * Boolean representing whether the application should run in test mode
         */
        .constant(
            'TEST_MODE',
            window.__env.test_mode
        )

        // Even though MEDICATIONS V2 is now ALWAYS ON, this constant is still used
        // to control the EQUIPMENT (V1/V2) switch.
        .constant(
            'CARLTON_MEDICATIONS_ENABLED',
            window.__env.carlton_medications_enabled === 1
        )

        .constant(
            'DEFAULT_LANGUAGE_CODE',
            'en_gb'
        )

        .constant(
            'DEFAULT_LANGUAGE_PRINCE_CODE',
            'en_GB'
        )

        .constant(
            'SYSTEM_DATE_LOCALE',
            window.__env.date_locale
        )

        .constant('RECORD_TYPES', {
            RISK: 'risk',
            ALERT: 'safety-alert',
        })

        .constant('MODULE_KEYS', {
            ACTION: 'action_instance',
            CLINICAL_AUDIT: 'clinical_audit',
            CONTACTS_CONTACT: 'contact_instance',
            ENTERPRISE_RISK_MANAGER: 'risk_register',
            ENTERPRISE_RISK_MANAGER_RISK: 'risk_register_risk',
            ENTERPRISE_RISK_MANAGER_REVIEW: 'risk_register_review',
            INVESTIGATION_BASE: 'investigation',
            INVESTIGATION: 'investigation_instance',
            SAFETY_LEARNINGS: 'safety_learnings',
            SAFETY_LEARNINGS_LEARNING: 'safety_learnings_learning',
            RECOMMENDATIONS_AND_CONTROLS: 'control',
            SAFETY_ROUNDS: 'round',
            SAFETY_ROUNDS_INSTANCE: 'round_instance',
            CONTROLS: 'control_instance',
            RECOMMENDATIONS: 'control_recommendation_instance',
            REPORTABLE_INCIDENT: 'reportable_incident_instance',
            SAFETY_ROUND_TEMPLATE: 'round_template',
            SAFETY_ALERTS: 'safety_alerts',
            SAFETY_ALERTS_ALERT: 'safety_alerts_alert',
        })

        .constant('SECTION_HELP', 'help')

        .constant('MODULE_DOMAIN_MAP', {
            investigation_instance: 'investigations',
            risk_register_risk: 'risk-register',
            risk_register_review: 'risk-register',
            safety_learnings_learning: 'safety_learnings',
            clinical_audit_instance: 'clinical-audit',
            accreditation_programme: 'accreditation',
            accreditation_standard: 'accreditation',
            round_instance: 'roundings',
            round_summary: 'roundings',
            action_instance: 'actions',
            safety_alerts_alert: 'safety_alerts'
        })

        .constant('CARLTON_EXTENDED_PERMISSIONS', {
            'user_group': [
                'user_permission_edit_acl_rules_and_groups',
                'edit_locations_services_and_tags'
            ],
            'dataextraction':[
                'can_schedule_and_extract'
            ]
        })

        .constant('CARLTON_SYSTEM_ADMIN_PERMISSIONS', {
            'system_admin': [
                'enable_maintenance_mode',
            ],
        })

        .constant('RECORD_LOCKING_POLL_INTERVAL', 60000)

        .constant(
            'ACTION_SUBFORMS_ENABLED',
            window.__env.enable_action_subforms
        )

        .constant(
            'PRIVATE_ATTACHMENTS_ENABLED',
            window.__env.enable_private_attachments
        )


        .constant(
            'TEMPLATE_ATTACHMENTS_ENABLED',
            window.__env.enable_template_attachments
        )

        .constant(
            'AD_USERS_ENABLED',
            window.__env.active_directory_users_enabled
        )

        .constant(
            'NOTIFICATION_CENTRE_API_URL',
            window.__env.notification_centre_api_url
        )

        .constant(
            'NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED',
            window.__env.notification_centre_whitelist_enabled
        )

        .constant(
            'MEDICATIONS_V2_API_URL',
            window.__env.medications_v2_api_url
        )

        .constant(
            'CAPTURE_API_URL',
            window.__env.prince_api_base_url
        )

        .constant(
            'MEDICATION_LOCATION_FILTERING_ENABLED',
            window.__env.medication_location_filtering_enabled
        )

        .constant(
            'CENTRALADMIN_TARGET',
            window.__env.centraladmin_target
        )

        .constant(
            'DRAFTLOCATIONS_ENABLED',
            window.__env.draft_locations_enabled
        )

        .constant(
            'DRAFTSERVICES_ENABLED',
            window.__env.draft_services_enabled
        )

        .constant(
            'LOCATION_DELETE_PARENT_NODES',
            window.__env.location_delete_parent_nodes
        )
        .constant(
            'SERVICE_DELETE_PARENT_NODES',
            window.__env.service_delete_parent_nodes
        )
        .constant(
            'DCIQ_VERSION_NUMBER',
            window.__env.dciq_version_number
        )
        .constant(
            'INCLUDEESCALATIONMODEL_ENABLED',
            window.__env.include_escalation_model
        )
        .constant(
            'EMAILAUDIT_ENABLED',
            window.__env.email_audit_enabled
        )
        .constant(
            'PENDO_ENABLED',
            window.__env.pendo_enabled
        )
        .constant(
            'PENDO_API_KEY',
            window.__env.pendo_api_key
        )
        .constant(
            'ETLTABLESYNCUI_ENABLED',
            window.__env.etl_table_sync_ui_enabled
        )
        .constant(
            'CONTACTSYNCINGUI_ENABLED',
            window.__env.contact_syncing_ui_enabled
        )
        .constant(
            'ACTIONSYNCINGUI_ENABLED',
            window.__env.action_syncing_ui_enabled
        )
        .constant(
            'ERMEXPORT_ENABLED',
            window.__env.erm_export_enabled
        )
        .constant(
            'BJP_ENABLED',
            window.__env.bjp_enabled
        )
        .constant(
            'TODOLIST_ENABLED',
            window.__env.todolist_enabled 
        )

        /**
         * Routing and $http configuration
         */
        .config(function ($urlRouterProvider, $httpProvider, DEFAULT_RISK_REGISTER) {

            // Add XDebug parameter to API requests if in debug mode
            if (window.__env.debug_mode) {
                $httpProvider.interceptors.push('xdebugInterceptor');
            }

            // Default route (if no other route matches)
            $urlRouterProvider.otherwise(function ($injector) {
                var $state = $injector.get('$state');
                $state.go('root');
            });

            if (!localStorage.getItem('datix.selectedRiskRegister')) {
                localStorage.setItem('datix.selectedRiskRegister', DEFAULT_RISK_REGISTER);
            }

            /**
             * This setting forces $resource to use jQuery's style of query string serialisation. This
             * is required to properly generate the nested filter strings (e.g. filter[rating][current]=3).
             */
            $httpProvider.defaults.paramSerializer = '$httpParamSerializerJQLike';
            $httpProvider.defaults.withCredentials = true;

            if (!$httpProvider.defaults.headers.get) {
                $httpProvider.defaults.headers.get = {};
            }

            $httpProvider.defaults.headers.get['Cache-Control'] = 'no-cache';
            $httpProvider.defaults.headers.get['Pragma'] = 'no-cache';
        })

        /**
         * Angular Toastr configuration
         */
        .config(function (toastrConfig, TEST_MODE) {
            var timeOut = TEST_MODE ? 15000 : 5000;

            angular.extend(toastrConfig, {
                closeButton: true,
                positionClass: 'toast-top-right',
                preventOpenDuplicates: true,
                timeOut: timeOut
            });
        })

        /**
         * Bootstrap Datepicker configuration
         */
        .config(function (uibDatepickerConfig) {
            angular.extend(uibDatepickerConfig, {
                showWeeks: false,
                formatMonth: 'MM'
            });
        })

        /**
         * Bootstrap Datepicker Popup configuration
         */
        .config(function (uibDatepickerPopupConfig) {
            angular.extend(uibDatepickerPopupConfig, {
                datepickerPopup: 'L',
                closeText: 'Close'
            });
        })

        /**
         * Angular Bootstrap Accordion configuration. This is required to allow ui-sortable to be used
         * on accordion groups.
         */
        .config(['$provide', function ($provide) {
            $provide.decorator('uibAccordionDirective', function ($delegate) {
                var directive = $delegate[0];
                directive.replace = true;
                return $delegate;
            });
        }])

        .config(['$translateProvider', function ($translateProvider) {
            $translateProvider.useSanitizeValueStrategy('escapeParameters');
            $translateProvider.useLoader('$translatePartialLoader', {
                urlTemplate: window.__env.api_url + '{part}/{lang}'
            });
            $translateProvider.useLoaderCache(false);
            $translateProvider.preferredLanguage('en_GB');
            $translateProvider.useMissingTranslationHandler('DatixTranslationErrorHandler');
        }])

        .config(function ($httpProvider) {
            $httpProvider.interceptors.push('HttpInterceptor');
            $httpProvider.interceptors.push('AuthInterceptor');
            $httpProvider.interceptors.push('DataInterceptor');
        })

        /**
         * Set the test mode flag on $rootScope
         *
         * The strict true check is required to ensure that an unresolved build variable does not evaluate to true.
         */
        .run(function ($rootScope, TEST_MODE) {
            $rootScope.testMode = (true === TEST_MODE);
        })

        .run(function ($location) {
            if ($location.search().newToken) {
                sessionStorage.setItem('datix.jwtToken', $location.search().newToken);
                $location.search('newToken', null);
            }
        })

        .run(function() {
            // Removes the trial label from charts, which defaults to shown
            FusionCharts.options.creditLabel = false;
        })

        /**
         * Adds the ability to add a "redirectTo" property to a route's config to automatically redirect when that
         * route is matched.
         *
         * Note: a full route path must be used (e.g. "datix.risk_register.admin.objectives").
         *
         * e.g. Redirect from a base "Admin" route to one of the children marked as a default.
         *
         * @todo: remove once angular-ui-router 1.0+ is in use.
         */
        .run(function ($rootScope, $state) {
            $rootScope.$on('$stateChangeStart', function (evt, to, params) {
                if (to.redirectTo) {
                    evt.preventDefault();

                    const redirectTo = angular.isFunction(to.redirectTo) ? to.redirectTo() : to.redirectTo;
                    $state.go(redirectTo, params);
                }
            });
        })

        /**
         * Override the Bootstrap accordion group template, allowing clickable items to be added to the accordion header
         */
        .run(function ($templateCache, $rootScope, $injector) {
            $rootScope.permissionCheck = permissionCheck;
            $rootScope.accessCheck = accessCheck;

            $templateCache.put("template/accordion/accordion-group.html",
                "<div class=\"panel {{panelClass || 'panel-default'}}\">\n" +
                "  <div class=\"panel-heading\" ng-keypress=\"toggleOpen($event)\">\n" +
                "    <h4 class=\"panel-title\">\n" +
                "      <div tabindex=\"0\" class=\"accordion-toggle\" ng-click=\"toggleOpen()\" uib-accordion-transclude=\"heading\" onmouseover='this.style.cursor=\"pointer\"' onmouseout='this.style.cursor=\"default\"'><span ng-class=\"{'text-muted': isDisabled}\">{{heading}}</span></div>\n" +
                "    </h4>\n" +
                "  </div>\n" +
                "  <div class=\"panel-collapse collapse\" uib-collapse=\"!isOpen\">\n" +
                "	  <div class=\"panel-body\" ng-transclude></div>\n" +
                "  </div>\n" +
                "</div>\n" +
                "");

            // Override the template used for UI Bootstrap's pagination to add the `translate` directive to the nav links
            $templateCache.put("uib/template/pagination/pagination.html",
                "<ul class=\"pagination\">\n" +
                "  <li ng-if=\"::boundaryLinks\" ng-class=\"{disabled: noPrevious()||ngDisabled}\" class=\"pagination-first\"><a href ng-click=\"selectPage(1, $event)\" translate=\"{{::getText('first')}}\"></a></li>\n" +
                "  <li ng-if=\"::directionLinks\" ng-class=\"{disabled: noPrevious()||ngDisabled}\" class=\"pagination-prev\"><a href ng-click=\"selectPage(page - 1, $event)\" translate=\"{{::getText('previous')}}\"></a></li>\n" +
                "  <li ng-repeat=\"page in pages track by $index\" ng-class=\"{active: page.active,disabled: ngDisabled&&!page.active}\" class=\"pagination-page\"><a href ng-click=\"selectPage(page.number, $event)\">{{page.text}}</a></li>\n" +
                "  <li ng-if=\"::directionLinks\" ng-class=\"{disabled: noNext()||ngDisabled}\" class=\"pagination-next\"><a href ng-click=\"selectPage(page + 1, $event)\" translate=\"{{::getText('next')}}\"></a></li>\n" +
                "  <li ng-if=\"::boundaryLinks\" ng-class=\"{disabled: noNext()||ngDisabled}\" class=\"pagination-last\"><a href ng-click=\"selectPage(totalPages, $event)\"  translate=\"{{::getText('last')}}\"></a></li>\n" +
                "</ul>\n" +
                "");


            function accessCheck() {
                return $injector.invoke(function (ModuleService) {
                    return ModuleService.getAccess();
                });
            }

            /**
             *
             * @param module
             * @param permission
             * @param redirect
             * @param errorMessage
             * @param recordParam
             */
            function permissionCheck(module, permission, redirect, errorMessage, recordParam) {
                return $injector.invoke(function ($q, $state, PermissionService) {
                    try {
                        return PermissionService.get(module, recordParam)
                            .then(function (response) {
                                if (permission && !response.access[permission]) {
                                    if (false !== errorMessage) {
                                        var ermCodeStart = errorMessage.indexOf("{[");
                                        var ermCodeEnd = errorMessage.indexOf("]}");
                                        var ermErrorCode = '';
                                        if (ermCodeStart === 0 && ermCodeEnd > 0) {
                                            ermErrorCode = errorMessage.substring(1, ermCodeEnd+1) + ' ';
                                            errorMessage = errorMessage.substring(ermCodeEnd+2);
                                        }
                                        var message = (!errorMessage ? 'Access Denied' : ermErrorCode + 'Access Denied to ' + errorMessage);
                                        $rootScope.$emit('errorMessage', message);
                                    }

                                    if (angular.isObject(redirect)) {
                                        return $state.go(redirect.url, redirect.params);
                                    } else if (angular.isString(redirect)) {
                                        return $state.go(redirect);
                                    } else {
                                        return $q.reject();
                                    }
                                }

                                $rootScope.userPermissions = response.access;

                                if ($rootScope.delegated) {
                                    $rootScope.userPermissions.isAdmin = false;
                                }

                                $rootScope.userRoles = response.roles;

                                return response;
                            });
                    } catch (e) {
                        console.error(e);
                    }
                });
            }
        });
})(window.angular);
