(angular => {
    'use strict';

    angular
        .module('baseModule')
        .controller('AppHeaderDirectiveController', Controller);

    function Controller(
        $q,
        $window,
        $scope,
        $rootScope,
        PrinceService,
        PermissionService,
        NotificationPollingService,
        ModuleService,
        AclLogoutService,
        UserService,
        MedicationService,
        SystemConfigurationService,
        HEADER_EVENTS,
        BASE_CONTROLLER_EVENTS,
        CARLTON_EXTENDED_PERMISSIONS,
        CARLTON_SYSTEM_ADMIN_PERMISSIONS,
        TODOLIST_ENABLED
    ) {
        const vm = this;

        vm.mainNavLinks = null;
        vm.cdmNavItems = null;
        vm.logoUrl = null;
        vm.pendingUsersCounter = 0;
        vm.isNavCollapsed = true; // where collapsed means hamburger is visible and the menu is closed
        vm.logOut = logOut;
        vm.isAdmin = false;
        vm.isLocalAdmin = false;
        vm.showSystemAdmin = false;
        vm.isTodoListEnabled = TODOLIST_ENABLED === 1;

        $rootScope.$on(BASE_CONTROLLER_EVENTS.BODY_CLICKED, collapseNav);
        $rootScope.$on(BASE_CONTROLLER_EVENTS.UPDATE_PENDING_USERS_COUNT, NotificationPollingService.fetchPendingUsersCount);

        function init() {
            PermissionService.refreshModules().then(() => {
                return getNavLinks();
            }).then(navLinks => {
                vm.mainNavLinks = navLinks;
            });

            $q.all([
                UserService.currentUserIsLocalAdmin().then(isLocalAdmin => vm.isLocalAdmin = isLocalAdmin),

                UserService.currentUserIsAdmin().then(isAdmin => vm.isAdmin = isAdmin),

                UserService.currentUserAccessToAdminByPermissions(CARLTON_EXTENDED_PERMISSIONS)
                    .then(accessToAdmin => vm.showSystemAdmin = vm.showSystemAdmin || accessToAdmin),

                UserService.currentUserAccessToAdminByPermissions(CARLTON_SYSTEM_ADMIN_PERMISSIONS)
                    .then(accessToAdmin => vm.showSystemAdmin = vm.showSystemAdmin || accessToAdmin)
            ]).then(() => {
                getPrinceLinks();
            });

            Promise.all([
                UserService.getInactivityTimeoutConfig(),
                SystemConfigurationService.getConfiguration(),
            ]).then((result) => {
                const [timeoutConfig, systemConfig] = result;

                localStorage.setItem('inactivity-timeout-value-milliseconds', timeoutConfig.timeoutValueMilliseconds);
                localStorage.setItem('inactivity-timeout-message', timeoutConfig.timeoutMessage);
                localStorage.setItem('inactivity-timeout-ok', timeoutConfig.okButton);
                localStorage.setItem('inactivity-timeout-cancel', timeoutConfig.cancelButton);

                if (systemConfig.logoutRedirectTarget === 'dif1_incident') {
                    (
                        angular.isObject($rootScope.me)
                            ? Promise.resolve($rootScope.me.current.id)
                            : UserService.getCurrentUser().then(function (user) {
                                return user.id
                            })
                    ).then(function (userId) {
                        return AclLogoutService.logoutUrlForUserId(userId);
                    }).then(function (foundUrl) {
                        localStorage.setItem('inactivityContinueUrl', foundUrl.data.url);
                    });
                } else {
                    localStorage.setItem('inactivityContinueUrl', window.__env.logout_url);
                }
            });

            NotificationPollingService.startPolling(vm);

            vm.dropDownClicked = onNavItemClick;
            vm.navToggleClicked = navToggleClicked;
            vm.buildMySettingsUrl = buildMySettingsUrl;
        }

        function navToggleClicked() {
            vm.isNavCollapsed = !vm.isNavCollapsed;

            if (!vm.isNavCollapsed) {
                $rootScope.$broadcast(HEADER_EVENTS.MENU_OPENED);
            }
        }

        function onNavItemClick(link) {
            collapseNav();

            if (!angular.isObject(link) || !link.key) {
                return;
            }

            PermissionService
                .hasModuleAccess(link.key)
                .then(hasAccess => {
                    if (!hasAccess) {
                        $scope.$emit('errorMessage', 'COMMON.PERMISSION_DENIED');
                    }
                });
        }

        function buildMySettingsUrl() {
            return vm.mySettings.url.replace('{currentUrl}', encodeURIComponent($window.location.href));
        }

        function getPrinceLinks() {
            PrinceService
                .getNavigationLinks()
                .then(links => {
                    const medicationsLinkIndex = links.cdm.findIndex(link => link.key === 'medications');
                    if (medicationsLinkIndex !== -1) {
                        vm.medicationsLink = links.cdm[medicationsLinkIndex];
                        links.cdm.splice(medicationsLinkIndex, 1);
                    }

                    const equipmentsLinkIndex = links.cdm.findIndex(link => link.key === 'equipments');
                    if (equipmentsLinkIndex !== -1) {
                        vm.equipmentsLink = links.cdm[equipmentsLinkIndex];
                        links.cdm.splice(equipmentsLinkIndex, 1);
                    }

                    getCdmLinks(links);
                    getNavLinks().then(navLinks => {
                        vm.mainNavLinks = navLinks;
                    });

                    vm.cdmNavItems = vm.cdmNavItems.concat(links.cdm);

                    if (vm.isAdmin || vm.isLocalAdmin || vm.showSystemAdmin) {
                        vm.cdmNavItems.push({
                            translationKey: 'NAV.SYSTEM_ADMIN',
                            state: 'datix.admin'
                        });
                    }

                    vm.helpItem = links.help;
                    vm.covidResourceItem = links.covidResource;
                    vm.mySettings = links.mySettings;

                    if (window.__env.custom_logo_enabled) {
                        vm.logoUrl = links.logoUrl;
                    }
                });
        }

        function logOut() {
            AclLogoutService.logout();
        }

        function getToolkitModules(toolkit) {
            const newToolkit = {
                translationKey: toolkit.translationKey,
                links: []
            };

            angular.forEach(toolkit.links, value => {
                if (angular.isDefined(value.key)) {
                    if (PermissionService.canAccess(value.key) === true) {
                        value.isLicensed = PermissionService.canAccess(value.key);
                        newToolkit.links.push(value);
                    }
                } else {
                    value.enabled = true;
                    newToolkit.links.push(value);
                }
            });

            return newToolkit;
        }

        function getNavLinks() {
            const deferred = $q.defer();
            const toolkits = [
                {
                    translationKey: 'NAV.CAPTURE',
                    links: [
                        {
                            key: 'icon_wall',
                            translationKey: 'NAV.ICON_WALL',
                            callback: getIconWallUrl
                        },
                        {
                            key: 'prince_incident',
                            translationKey: 'NAV.INCIDENTS',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_claim',
                            translationKey: 'NAV.CLAIMS',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_feedback',
                            translationKey: 'NAV.FEEDBACK',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_mortality',
                            translationKey: 'NAV.MORTALITY',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_redress',
                            translationKey: 'NAV.REDRESS',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_safeguarding',
                            translationKey: 'NAV.SAFEGUARDING',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_dashboard',
                            translationKey: 'NAV.DASHBOARD',
                            callback: getPrinceUrl
                        },
                        {
                            key: 'prince_admin',
                            translationKey: 'NAV.CAPTURE.ADMIN',
                            callback: getPrinceUrl
                        }
                    ]
                },
                {
                    translationKey: 'NAV.EVALUATE',
                    links: [
                        {
                            key: 'risk_register',
                            translationKey: 'NAV.RISK_REGISTER',
                            state: 'datix.risk_register'
                        },
                        {
                            key: 'investigation',
                            translationKey: 'NAV.INVESTIGATIONS',
                            state: 'datix.investigations'
                        },
                        {
                            key: 'reportable_incident',
                            translationKey: 'NAV.REPORTABLE_INCIDENTS',
                            state: 'datix.reportable_incidents',
                        }
                    ]
                },
                {
                    translationKey: 'NAV.STRATEGY',
                    links: [
                        {
                            key: 'clinical_audit',
                            translationKey: 'NAV.CLINICAL_AUDIT',
                            state: 'datix.clinical_audit'
                        },
                        {
                            key: 'control',
                            translationKey: 'NAV.CONTROLS_AND_RECOMMENDATIONS',
                            state: 'datix.controls'
                        },
                        {
                            key: 'safety_learnings',
                            translationKey: 'NAV.SAFETY_LEARNINGS',
                            state: 'datix.safety_learnings'
                        },
                    ]
                },
                {
                    translationKey: 'NAV.IMPLEMENT',
                    links: [
                        {
                            translationKey: 'NAV.POLICIES_AND_GUIDELINES',
                            key: 'policies_and_guidelines',
                            callback: getPoliciesAndGuidelinesUrl
                        },
                        {
                            key: 'safety_alerts',
                            translationKey: 'NAV.SAFETY_ALERTS',
                            state: 'datix.safety_alerts.dashboard'
                        },
                    ]
                },
                {
                    translationKey: 'NAV.ASSESS',
                    links: [
                        {
                            key: 'accreditation',
                            translationKey: 'NAV.COMPLIANCE_ASSESSMENT',
                            state: 'datix.accreditation'
                        },
                        {
                            key: 'round',
                            translationKey: 'NAV.SAFETY_ROUNDS',
                            state: 'datix.rounding'
                        }
                    ]
                }
            ];
            const finalToolkits = [];

            angular.forEach(toolkits, function (value) {
                const toolkit = getToolkitModules(value);
                if (toolkit && toolkit.links.length > 0) {
                    finalToolkits.push(toolkit);
                }
            });

            deferred.resolve(finalToolkits);

            return deferred.promise;
        }

        function getPoliciesAndGuidelinesUrl() {
            return UserService.userModuleAccessCheck('policies_and_guidelines')
                .then(access => access ? ModuleService.getPoliciesAndGuidelinesUrl() : false)
                .then(url => url ? $window.location = url : false);
        }

        function getIconWallUrl() {
            ModuleService
                .getIconWallUrl()
                .then(url => {
                    $window.location = url;
                });
        }

        function getPrinceUrl(key) {
            ModuleService
                .getPrinceUrl(key)
                .then(url => {
                    $window.location = url;
                });
        }

        function getCdmLinks(princeLinks) {
            vm.cdmNavItems = [
                {
                    translationKey: 'NAV.ACTIONS_DASHBOARD',
                    state: 'datix.actions',
                },
                {
                    translationKey: 'NAV.DISTRIBUTIONS_LISTS',
                    state: 'datix.distribution_lists.dashboard',
                },
            ];

            if (vm.medicationsLink) {
                vm.cdmNavItems.push(vm.medicationsLink);
            }

            if (vm.isAdmin && vm.equipmentsLink){
                vm.cdmNavItems.push(vm.equipmentsLink);
            }

            vm.cdmNavItems.push(
                {
                    translationKey: 'nav.checklists',
                    state: 'datix.checklists',
                }
            );

            if (vm.isAdmin || !window.__env.admin_contacts_enabled) {
                vm.cdmNavItems.push(
                    {
                        translationKey: 'nav.contacts',
                        state: 'datix.contacts',
                    }
               );
            }
        }

        function collapseNav() {
            vm.isNavCollapsed = true;
        }

        init();
    }
})(window.angular);
