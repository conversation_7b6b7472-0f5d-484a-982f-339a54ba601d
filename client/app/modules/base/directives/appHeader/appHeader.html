<div class="app-header">
    <header>
        <div class="dummy-header"></div>
        <nav class="navbar">
            <div class="header-container">
                <div class="navbar-header">
                    <button type="button"
                        class="navbar-toggle"
                        ng-class="{ 'collapsed': vm.isNavCollapsed }"
                        ng-click="vm.navToggleClicked()"
                        aria-expanded="{{ !vm.isNavCollapsed }}">
                        <i class="fa fa-bars"></i>
                        <i class="fa fa-times"></i>
                    </button>
                    <a href="/" class="navbar-brand">
                        <img ng-if="!vm.logoUrl" src="images/RLDatix-logo-170x30.png" alt="RLDatix" />
                        <img ng-if="vm.logoUrl" ng-src="{{vm.logoUrl}}">
                    </a>
                </div>

                <div>
                    <div class="collapse navbar-collapse" ng-class="{ 'in': !vm.isNavCollapsed }">
                        <ul id="toolkit-nav" class="nav navbar-nav main-nav-list" ng-if="!vm.isPublic">
                            <li ng-repeat="navGroup in vm.mainNavLinks" ui-sref-active="active" class="dropdown">
                                <a class="dropdown-toggle"
                                   data-toggle="dropdown"
                                   role="button"
                                   aria-haspopup="true"
                                   aria-expanded="false"
                                >
                                    <span translate="{{ navGroup.translationKey }}"></span>
                                    <i class="fa fa-caret-down"></i>
                                </a>
                                <ul class="dropdown-menu module-links">
                                    <li ng-repeat="link in navGroup.links" ng-class="{'hidden': !link.isLicensed}">
                                        <a ng-if="link.state && link.isLicensed" ui-sref="{{ link.state }}" translate="{{ link.translationKey }}" ng-click="vm.dropDownClicked(link)"></a>
                                        <a ng-if="link.url && link.isLicensed" ng-href="{{ link.url }}" translate="{{ link.translationKey }}" ng-click="vm.dropDownClicked(link)"></a>
                                        <a ng-if="!link.isLicensed && (link.state || link.url)" translate="{{ link.translationKey }}" ng-click="vm.dropDownClicked(link)"></a>
                                        <button ng-if="link.callback" translate="{{ link.translationKey }}" ng-click="link.callback(link.key); vm.dropDownClicked(link)" class="btn btn-block"></button>
                                        <a ng-if="!link.state && !link.url && !link.callback" translate="{{ link.translationKey }}" ng-click="vm.dropDownClicked(link)"></a>
                                    </li>
                                </ul>
                            </li>
                        </ul>

                        <div class="utilities-nav">

                            <ul class="nav navbar-nav" ng-if="!vm.isPublic && vm.covidResourceItem">
                                <li>
                                    <a href="{{vm.covidResourceItem.url}}" title="{{ vm.covidResourceItem.translationKey | translate }}">
                                        <span class="navbar-resources" translate="{{ vm.covidResourceItem.translationKey }}"></span>
                                    </a>
                                </li>
                            </ul>

                            <ul class="nav navbar-nav" ng-if="!vm.isPublic && vm.pendingUsersCounter > 0">
                                <li id="pending_users_nav_icon">
                                    <a ui-sref="datix.users.pending" title="Pending users" ng-click="vm.dropDownClicked()">
                                    <span class="badge badge-warning">
                                        <span class="glyphicon glyphicon-user" aria-hidden="true"></span>
                                        {{vm.pendingUsersCounter}}
                                    </span>
                                    </a >
                                </li>
                            </ul>

                            <ul class="nav navbar-nav core-data-modules-nav" ng-if="!vm.isPublic">
                                <li class="dropdown" ui-sref-active="active">
                                    <a id="core-data-modules" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-plus ml-0"></i>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li ng-repeat="item in vm.cdmNavItems track by $index" ng-class="{'disabled': !item.isEnabled && item.moduleNavigationType === 'generic'}">
                                            <a ng-if="item.state" ui-sref="{{ item.state }}" translate="{{ item.translationKey }}" ng-click="vm.dropDownClicked()"></a>
                                            <a ng-if="item.url" ng-href="{{ item.url }}" translate="{{ item.translationKey }}" ng-click="vm.dropDownClicked()" ng-attr-target="{{ item.openInNewTab ? '_blank' : '_self' }}"></a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>

                            <!-- Help -->
                            <ul class="nav navbar-nav" ng-if="!vm.isPublic">
                                <li>
                                    <a href="{{vm.helpItem.url}}" title="{{vm.helpItem.translationKey | translate}}" target="_blank" ng-click="vm.dropDownClicked()">
                                        <i class="fa fa-question ml-0"></i>
                                    </a>
                                </li>
                            </ul>

                            <ul class="nav navbar-nav" ng-if="$root.me.current">
                                <li class="dropdown">
                                    <a
                                            uib-tooltip="{{ $root.me.current.forename + ' ' + $root.me.current.surname }}"
                                            tooltip-placement="bottom-right"
                                            id="user-navigation"
                                            class="dropdown-toggle user-name"
                                            data-toggle="dropdown"
                                            role="button"
                                            aria-haspopup="true"
                                            aria-expanded="false"
                                    >
                                <span>
                                    {{ $root.me.current.forename.charAt(0).toUpperCase() + $root.me.current.surname.charAt(0).toUpperCase() }}
                                </span>
                                        <i class="fa fa-caret-down"></i>
                                    </a>
                                    <ul class="dropdown-menu">
                                        <li ng-if="!vm.isPublic && vm.isTodoListEnabled">
                                            <a id="my-todo" translate="NAV.MY_TODO" ng-click="vm.dropDownClicked();" href="/todolist/"></a>
                                        </li>
                                        <li ng-if="!vm.isPublic">
                                            <a ui-sref="datix.my_preferences"
                                               id="my-preferences"
                                               translate="NAV.MY_PREFERENCES"
                                               ng-click="vm.dropDownClicked()"
                                            ></a>
                                        </li>
                                        <li ng-if="vm.mySettings.isEnabled && !vm.isPublic">
                                            <a ng-href="{{ vm.buildMySettingsUrl() }}"
                                               translate="{{ vm.mySettings.translationKey }}"
                                               ng-click="vm.dropDownClicked()"
                                            ></a>
                                        </li>
                                        <li>
                                            <button
                                                    ng-click="vm.logOut(); vm.dropDownClicked()"
                                                    translate="NAV.LOGOUT"
                                                    id="logout"
                                                    class="btn btn-block"
                                            ></button>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div ng-if="$root.me.current.delegated" class="delegate-user">
                    <span translate="USER.DELEGATIONS.CURRENT_LOGGED_IN"
                          translate-values="{user: $root.me.current.forename + ' ' + $root.me.current.surname}"
                    >
                    </span>
                </div>
            </div>
        </nav>
    </header>
</div>

