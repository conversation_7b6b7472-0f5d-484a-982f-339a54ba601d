<div>
    <form name="ermOverdue">
        <div class="form-group" ng-class="{ 'has-error': ermOverdue.ermOverdueDays.$invalid }">
            <label for="erm-overdue-days" translate="PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.OVERDUE_DAYS.LABEL"></label>
            <input id="erm-overdue-days"
                   name="ermOverdueDays"
                   type="number"
                   class="form-control"
                   min="1"
                   ng-model="vm.overdueDays"
                   ng-blur="vm.saveConfiguration()"
                   ng-disabled="vm.saving"
            >

            <p ng-if="ermOverdue.ermOverdueDays.$invalid" class="help-block" translate="PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.OVERDUE_DAYS.ERROR"></p>
        </div>
        <div class="form-group">
            <button
                class="btn btn-primary mt-10"
                ng-click="vm.saveConfiguration()"
                ng-disabled="vm.saving">
                <span translate="COMMON.SAVE"></span>
            </button>
        </div>
    </form>
</div>
