(angular => {
    'use strict';

    angular
        .module('notificationCentreModule')
        .controller('NotificationCentreErmOverdueSettingsController', Controller);

    function Controller($scope, NotificationCentreOverdueService, MODULE) {
        const vm = this;

        vm.module = MODULE;
        vm.overdueDays = vm.module.overdueConfiguration['overdue-days'];
        vm.saving = false;

        vm.saveConfiguration = () => {
            vm.saving = true;
            vm.module.overdueConfiguration = {
                'overdue-days': vm.overdueDays,
            };

            NotificationCentreOverdueService
                .updateModule(vm.module)
                .finally(() => {
                    vm.saving = false;
                });
        };
    }
})(window.angular);
