(angular => {
    'use strict';

    angular
        .module('notificationCentreModule')
        .controller('NotificationCentreListTemplatesController', Controller);


    function Controller(
        $scope,
        $state,
        $stateParams,
        NotificationCentreTemplateService,
        MAGMA_TABLE_EVENTS,
        FILTER_EVENTS,
        PAGINATION_PREFIX
    ) {
        const vm = this;
        const paginationKey = PAGINATION_PREFIX.concat('.notification-centre-templates');
        let perPage = parseInt(localStorage.getItem(paginationKey), 10) || 10;
        let pageNumber = 1;

        const linkTemplates = {
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INCIDENTS.OVERDUE': {
                config: '#/notification-centre/overdue/incidents',
                docs: 'documentation/Administration/Reference_Misc/Reference/CaptureAdmin_Configuration_Settings/Configuration_Settings_Incidents_Module.htm',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNEE': {
                config: '#/notification-centre/overdue/actions',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.OVERDUE_ASSIGNER': {
                config: '#/notification-centre/overdue/actions',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ACTIONS.REMINDER': {
                config: '#/notification-centre/overdue/actions',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.FEEDBACK.OVERDUE': {
                config: '#/notification-centre/overdue/feedback',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.MORTALITY_REVIEW.OVERDUE': {
                config: '#/notification-centre/overdue/mortality-review',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.INVESTIGATIONS.REMINDER': {
                config: '#/notification-centre/overdue/investigations',
            },
            'PLACEHOLDER.NOTIFICATION_CENTRE.TEMPLATE_TYPE.ERM.RISK_OVERDUE': {
                config: '#/notification-centre/overdue/erm',
            }
        };



        vm.schema = null;
        vm.templates = null;
        vm.selectedModuleId = null;
        vm.modules = [];
        vm.configLinkTranslation = null;
        vm.docsLinkTranslation = null;
        vm.overdueDays = null;
        vm.reminderDays = null;
        vm.isHidden = true;

        $scope.$on(MAGMA_TABLE_EVENTS.ROW_CLICK, (e, data) => {
            $state.go('datix.notification_centre.templates.edit', {
                templateId: data.record.id
            });
        });

        $scope.$on(MAGMA_TABLE_EVENTS.PAGINATION_CHANGE, (e, data) => {
            pageNumber = data.currentPage;

            if (data.perPage !== perPage) {
                pageNumber = 1;
                perPage = data.perPage;
                $scope.$emit(FILTER_EVENTS.UPDATED);
            }

            localStorage.setItem(paginationKey, data.perPage);
            loadTemplates(pageNumber, data.perPage);
        });

        function init() {
            const templateTypeId = $stateParams.templateTypeId;
            const schemaPromise = NotificationCentreTemplateService
                .getSchema()
                .then(schema => {
                    schema.options.key = 'notification-centre-templates';
                    schema.options.pagination = {
                        offsetMode: true,
                        totalItems: 0,
                        currentPage: 1,
                        perPage,
                        displayPages: 5,
                        initOffset: 0,
                        initLimit: 5,
                        mode: 'customHandler',
                    };
                    vm.schema = schema;
                });

            const modulePromise = NotificationCentreTemplateService
                .getModules()
                .then(modules => {
                    vm.modules = modules;
                    const selectedModule = modules.find(
                        (module) => {
                            return module.templateTypes.find(
                                templateType => templateType.id === $stateParams.templateTypeId
                            );
                        }
                    );
                    if (angular.isDefined(selectedModule) &&
                        NotificationCentreTemplateService.getDisabledNewTemplateButtonModules().includes(selectedModule.key)
                    ) {
                        vm.isHidden = false;
                    }
                    vm.overdueDays = selectedModule?.overdueConfiguration['overdue-days'];
                    vm.reminderDays = selectedModule?.overdueConfiguration['reminder-days'];
                });

            Promise.all([schemaPromise, modulePromise]).then(() => {
                loadTemplates(1, perPage, templateTypeId);
            });
        }

        function loadTemplates(page, perPage, templateTypeID) {
            return NotificationCentreTemplateService
                .getTemplatesOfType(page, perPage, templateTypeID)
                .then(templatesResponse => {
                    vm.templates = templatesResponse['hydra:member'];
                    vm.templateName = vm.templates[0].templateType.name;
                    vm.description = vm.templateName + '.DESCRIPTION';
                    vm.title = vm.templateName + '.TITLE';
                    if (vm.templateName in linkTemplates) {
                        if ('config' in linkTemplates[vm.templateName]) {
                            vm.configLinkTranslation = vm.templateName + '.CONFIG_LINK';
                            vm.configUrl = linkTemplates[vm.templateName].config;
                        }
                        if ('docs' in linkTemplates[vm.templateName]) {
                            vm.docsLinkTranslation = vm.templateName + '.DOCS_LINK';
                            vm.docsUrl = linkTemplates[vm.templateName].docs;
                        }
                    }
                    vm.schema.options.pagination.totalItems = templatesResponse['hydra:totalItems'];
                });
        }

        vm.addNewTemplate = () => {
            $state.go('datix.notification_centre.templates.new_with_template_type_id', {
                templateTypeId: $stateParams.templateTypeId
            });
        };

        init();
    }
})(window.angular);
