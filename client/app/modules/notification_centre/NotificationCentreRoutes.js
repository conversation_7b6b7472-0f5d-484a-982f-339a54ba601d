(angular => {
    'use strict';

    angular
        .module('notificationCentreModule')
        .config((
            $stateProvider,
            ACL_RESOLVES,
            NOTIFICATION_CENTRE_PATHS,
            DEFAULT_SIDEBAR_TEMPLATE_URL,
            NOTIFICATION_CENTRE_OVERDUE_MODULES
        ) => {
            if (!window.__env.notification_centre_enabled) {
                return;
            }

            $stateProvider
                .state('datix.notification_centre', {
                    redirectTo: 'datix.notification_centre.dashboard',
                    url: '/notification-centre',
                    data: {
                        ModuleKey: ['notification_centre', 'locations'],
                        ModuleTitle: 'PLACEHOLDER.NOTIFICATION_CENTRE.MODULE_TITLE',
                    },
                    resolve: {
                        ADMIN: ACL_RESOLVES.USER_IS_ADMIN,
                    },
                })
                .state('datix.notification_centre.dashboard', {
                    url: '',
                    data: {
                        BackState: {
                            state: 'datix.admin',
                            label: 'NAV.BACK_TO_ADMIN',
                        }
                    },
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/dashboard/dashboard.html',
                            controller: 'NotificationCentreDashboardController',
                            controllerAs: 'vm',
                        },
                        'sidebar@datix': {
                            templateUrl: DEFAULT_SIDEBAR_TEMPLATE_URL,
                            controller: 'NotificationCentreDashboardSidebarController',
                            controllerAs: 'vm'
                        },
                    },
                    resolve: {
                        ModulePermissions: modulePermissions,
                    },
                })
                .state('datix.notification_centre.templates', {
                    abstract: true,
                    url: '/email-templates',
                    data: {
                        BackState: {
                            state: 'datix.notification_centre.dashboard',
                            label: 'PLACEHOLDER.NOTIFICATION_CENTRE.NAV.VIEW_AND_CREATE',
                        }
                    },
                })
                .state('datix.notification_centre.list-templates', {
                    url: '/list-templates/{templateTypeId:int}',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/list-templates/list-templates.html',
                            controller: 'NotificationCentreListTemplatesController',
                            controllerAs: 'vm',
                        },
                        'sidebar@datix': {
                            templateUrl: DEFAULT_SIDEBAR_TEMPLATE_URL,
                            controller: 'NotificationCentreDashboardSidebarController',
                            controllerAs: 'vm',
                        },
                    },
                    data: {
                        BackState: {
                            state: 'datix.notification_centre.dashboard',
                            label: 'PLACEHOLDER.NOTIFICATION_CENTRE.NAV.VIEW_AND_CREATE',
                        }
                    },
                })
                .state('datix.notification_centre.templates.new', {
                    url: '/new',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/single-template/single-template.html',
                            controller: 'NotificationCentreSingleTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        CaptureTranslations: getCaptureTranslations,
                    }
                })
                .state('datix.notification_centre.templates.new_with_template_type_id', {
                    url: '/new/{templateTypeId:int}',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/single-template/single-template.html',
                            controller: 'NotificationCentreSingleTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        CaptureTranslation: getCaptureTranslations,
                    },
                })
                .state('datix.notification_centre.templates.edit', {
                    url: '/{templateId:int}',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/single-template/single-template.html',
                            controller: 'NotificationCentreSingleTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        CaptureTranslations: getCaptureTranslations,
                    },
                })
                .state('datix.notification_centre.overdue', {
                    url: '/overdue',
                    redirectTo: 'datix.notification_centre.overdue.incidents',
                    data: {
                        BackState: {
                            state: 'datix.notification_centre.dashboard',
                            label: 'PLACEHOLDER.NOTIFICATION_CENTRE.NAV.VIEW_AND_CREATE',
                        },
                    },
                    views: {
                        'sidebar@datix': {
                            templateUrl: DEFAULT_SIDEBAR_TEMPLATE_URL,
                            controller: 'NotificationCentreOverdueSidebarController',
                            controllerAs: 'vm',
                        },
                    },
                 })
                .state('datix.notification_centre.overdue.incidents', {
                   url: '/incidents',
                   views: {
                       'body@datix': {
                           templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/overdue-template/overdue-template.html',
                           controller: 'NotificationCentreOverdueTemplateController',
                           controllerAs: 'vm',
                       },
                   },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.INCIDENTS,
                        MODULE: getModule,
                    },
                })
                .state('datix.notification_centre.overdue.feedback', {
                    url: '/feedback',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/overdue-template/overdue-template.html',
                            controller: 'NotificationCentreOverdueTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.FEEDBACK,
                        MODULE: getModule,
                    },
                })
                .state('datix.notification_centre.overdue.erm', {
                    url: '/erm',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/erm-overdue-settings/erm-overdue-settings.html',
                            controller: 'NotificationCentreErmOverdueSettingsController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.ERM,
                        MODULE: getModule,
                    },
                })
                .state('datix.notification_centre.overdue.mortality-review', {
                    url: '/mortality-review',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/overdue-template/overdue-template.html',
                            controller: 'NotificationCentreOverdueTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.MORTALITY_REVIEW,
                        MODULE: getModule,
                    },
                })
                .state('datix.notification_centre.overdue.actions', {
                    url: '/actions',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/actions-overdue-template/action-overdue-template.html',
                            controller: 'NotificationCentreActionOverdueTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.ACTIONS,
                        MODULE: getModule,
                    },
                })
                .state('datix.notification_centre.overdue.investigations', {
                    url: '/investigations',
                    views: {
                        'body@datix': {
                            templateUrl: NOTIFICATION_CENTRE_PATHS.PAGES + '/investigations-overdue-template/investigation-overdue-template.html',
                            controller: 'NotificationCentreInvestigationOverdueTemplateController',
                            controllerAs: 'vm',
                        },
                    },
                    resolve: {
                        MODULE_KEY: () => NOTIFICATION_CENTRE_OVERDUE_MODULES.INVESTIGATIONS,
                        MODULE: getModule,
                    },
                });
            function modulePermissions($rootScope) {
                // @todo: implement
                // return $rootScope.permissionCheck('notification_centre');

                return true;
            }

            function getCaptureTranslations(TranslationService) {
                return TranslationService.loadCaptureNotificationCentreTranslations();
            }

            function getModule(MODULE_KEY, NotificationCentreOverdueService) {
                return NotificationCentreOverdueService
                    .getModule(MODULE_KEY.NOTIFICATION_CENTRE);
            }
        });
})(window.angular);
