;(angular => {
    'use strict';

    angular
        .module('notificationCentreModule')
        .controller('NotificationCentreOverdueSidebarController', Controller);

    function Controller() {
        const vm = this;

        vm.tabs = getModuleTabs();

        function getModuleTabs() {
            const baseState = 'datix.notification_centre.';

            return [
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ACTIONS.TITLE',
                    state: baseState + 'overdue.actions',
                },
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.ERM.TITLE',
                    state: baseState + 'overdue.erm',
                },
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.INVESTIGATIONS.TITLE',
                    state: baseState + 'overdue.investigations',
                },
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.INCIDENTS.TITLE',
                    state: baseState + 'overdue.incidents',
                },
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.FEEDBACK.TITLE',
                    state: baseState + 'overdue.feedback',
                },
                {
                    heading: 'PLACEHOLDER.NOTIFICATION_CENTRE.OVERDUE.MORTALITY_REVIEW.TITLE',
                    state: baseState + 'overdue.mortality-review',
                },
            ];
        }
    }
})(window.angular);
