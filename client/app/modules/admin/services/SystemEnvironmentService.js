;(angular => {
    'use strict';

    angular
        .module('adminModule')
        .service('SystemEnvironmentService', Service);

    function Service() {
        const _this = this;

        _this.getApiEnvironment = getApiEnvironment;
        _this.getFrontendEnvironment = getFrontendEnvironment;

        function getApiEnvironment() {
            return _this
                .resource
                .get()
                .$promise
                .then(response => response.environment);
        }


        function getFrontendEnvironment() {
            return Promise.resolve({
                //jshint ignore:start
                'API_URL': window.__env.api_url,
                'CONFIG_API_URL': window.__env.config_api_url,
                'SYSTEM_DATE_LOCALE': window.__env.date_locale,
                'DIF_TIMEOUT_MINS': window.__env.dif_timeout_mins,
                'RECAPTCHA_PUBLIC_KEY': window.__env.recaptcha_public_key,
                'TEST_MODE': window.__env.test_mode,
                'DISABLE_PENDING_USERS_SERVICE': window.__env.disable_pending_users_service,
                'LOGOUT_URL': window.__env.logout_url,
                'TOKEN_RENEW_API': window.__env.renew_token_url,
                'GRAPH_QL_ENDPOINT': window.__env.graph_ql_endpoint,
                'ENABLE_ACTION_SUBFORMS': window.__env.enable_action_subforms,
                'ENABLE_PRIVATE_ATTACHMENTS': window.__env.enable_private_attachments,
                'FEATURE_SA_HEALTH_REQUIREMENTS': window.__env.featureSaHealthRequirements,
                'ENABLE_EXPORT_IMPORT': window.__env.enable_import_export,
                'ENABLE_USER_DELEGATION': window.__env.enable_user_delegation,
                'LOCALAUTH_ENABLED': window.__env.enable_user_form,
                'ENABLE_RISK_NOTIFICATIONS': window.__env.enable_risk_notifications,
                'BENCHMARKING_ENABLED': window.__env.enable_benchmarking,
                'ENABLE_TEMPLATE_ATTACHMENTS': window.__env.enable_template_attachments,
                'CUSTOMLOGO_ENABLED': window.__env.custom_logo_enabled ,
                'DELEGATIONCARLTONACCESS_ENABLED': window.__env.delegation_carlton_access_enabled,
                'ADMINCONTACTS_ENABLED': window.__env.admin_contacts_enabled,
                'LOCALADMINAUDIT_ENABLED': window.__env.local_admin_audit_enabled,
                'ADMINAUDITLOG_ENABLED': window.__env.admin_audit_log_enabled,
                'RECOMMENDATIONAUDITLOG_ENABLED': window.__env.recommendation_audit_log_enabled,
                'NOTIFICATIONCENTRE_ENABLED': window.__env.notification_centre_enabled,
                'NOTIFICATION_CENTRE_API_URL': window.__env.notification_centre_api_url,
                'NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED': window.__env.notification_centre_whitelist_enabled,
                'PRINCE_API_BASE_URL': window.__env.prince_api_base_url,
                'EMAILREPORTS_ENABLED': window.__env.email_reports_enabled,
                'ADUSERS_ENABLED': window.__env.active_directory_users_enabled,
                'CARLTONMEDICATIONS_ENABLED': window.__env.carlton_medications_enabled,
                'MEDICATIONS_V2_API_URL': window.__env.medications_v2_api_url,
                'MEDICATIONLOCATIONFILTERING_ENABLED': window.__env.medication_location_filtering_enabled,
                'CENTRALADMIN_TARGET': window.__env.centraladmin_target,
                'DRAFTLOCATIONS_ENABLED': window.__env.draft_locations_enabled,
                'DRAFTSERVICES_ENABLED': window.__env.draft_services_enabled,
                'LOCATION_DELETE_PARENT_NODES': window.__env.location_delete_parent_nodes,
                'SERVICE_DELETE_PARENT_NODES': window.__env.service_delete_parent_nodes,
                'TODOLIST_ENABLED': window.__env.todolist_enabled,
                // jshint ignore:end
            });
        }
    }
})(window.angular);
