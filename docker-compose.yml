services:
  frontend:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

    image: "400799746524.dkr.ecr.eu-west-2.amazonaws.com/dciq/frontend:develop_latest"
    build:
      context: client
    ports:
      - "9999:80"
    volumes:
      - ./client/:/var/www/frontend/
      - ./client/docker/start.sh:/start.sh
    restart: always
    environment:
      - API_URL=https://localhost.datix/api/
      - CONFIG_API_URL=http://configapi.dev:8090/v1
      - SYSTEM_DATE_LOCALE=en-gb
      - MODE=DEV
      - TEST_MODE=false
      - RECAPTCHA_PUBLIC_KEY=6Lf2riYTAAAAALFLctqJJUForqZCVyLzHV6oautO
      - ENABLE_EXPORT_IMPORT=1
      - DIF_TIMEOUT_MINS=0
      - DISABLE_PENDING_USERS_SERVICE=0
      - LOGOUT_URL=https://localhost.datix/auth/logout
      - TOKEN_RENEW_API=https://localhost.datix/auth/refreshToken
      - GRAPH_QL_ENDPOINT=https://localhost.datix/iris/q
      - ENABLE_ACTION_SUBFORMS=1
      - FEATURE_SA_HEALTH_REQUIREMENTS=1
      - PUBLIC_URL=https://localhost.datix
      - ENABLE_PRIVATE_ATTACHMENTS=0
      - ENABLE_USER_DELEGATION=1
      - LOCALAUTH_ENABLED=1
      - ENABLE_RISK_NOTIFICATIONS=1
      - BENCHMARKING_ENABLED=1
      - CUSTOMLOGO_ENABLED=1
      - ADMINCONTACTS_ENABLED=1
      - LOCALADMINAUDIT_ENABLED=1
      - DELEGATIONCARLTONACCESS_ENABLED=1
      - ADMINAUDITLOG_ENABLED=1
      - RECOMMENDATIONAUDITLOG_ENABLED=1
      - ENABLE_TEMPLATE_ATTACHMENTS=1
      - NOTIFICATIONCENTRE_ENABLED=1
      - NOTIFICATION_CENTRE_API_URL=https://localhost.datix/notifications/api
      - PRINCE_API_BASE_URL=https://localhost.datix/capture/api
      - EMAILREPORTS_ENABLED=1
      - ADUSERS_ENABLED=1
      - CARLTONMEDICATIONS_ENABLED=1
      - MEDICATIONS_V2_API_URL=https://localhost.datix/medications/api
      - MEDICATIONLOCATIONFILTERING_ENABLED=1
      - CENTRALADMIN_TARGET=0
      - DRAFTLOCATIONS_ENABLED=1
      - DRAFTSERVICES_ENABLED=1
      - EXPOSE_CONFIG=1
      - LOCATION_DELETE_PARENT_NODES=1
      - SERVICE_DELETE_PARENT_NODES=1
      - DCIQ_VERSION_NUMBER=
      - INCLUDEESCALATIONMODEL_ENABLED=0
      - EMAILAUDIT_ENABLED=1
      - PENDO_ENABLED=0
      - ETLTABLESYNCUI_ENABLED=1
      - CONTACTSYNCINGUI_ENABLED=1
      - ACTIONSYNCINGUI_ENABLED=1
      - ENABLE_BJP=1
      - ERMEXPORT_ENABLED=1
      - NOTIFICATION_CENTRE_DOMAIN_WHITELIST_ENABLED=1
      - TODOLIST_ENABLED=1
    container_name: frontend

  api:
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1024M

    build:
      context: api
      dockerfile: nginx.Dockerfile
      args:
        - USE_ZSCALER_CERT=1
    environment:
      - PHP_HOSTNAME=carlton-php
      - PHP_PORT=9099
    ports:
      - "8080:8080"
    extra_hosts:
      - host.docker.internal:host-gateway
    container_name: api
    depends_on:
      - carlton-php

  carlton-php:
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1024M
    build:
      context: api
      dockerfile: php.Dockerfile
      args:
        - USE_ZSCALER_CERT=1
        - ENABLE_XDEBUG=1
        - XDEBUG_IDE_PORT=9001
        - XDEBUG_MODE=debug
        - XDEBUG_START_WITH_REQUEST=yes
    ports:
      - "9099:9099"
    volumes:
      - ./api/:/var/www/api/
      - /var/www/api/site/data/cache
      - /var/www/api/site/data/DoctrineORMModule/Proxy
      - /var/www/api/site/data/uploads
    restart: always
    environment:
      - MYSQL_DATABASE=datix-rms-api
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - KAFKA_PROXY_URL=http://kafka_proxy:9090
      - NOTIFICATIONCENTRE_URL=http://nginx:80
      - PRINCE_API_BASE_URL=http://capture:8020/api/
      - KAFKA_HOST=kafka:9092
      - CUSTOM_PHP_MAX_EXEC_TIME=30
      - CUSTOM_PHP_MEM_LIMIT=512
      # If enabled, docker compose up with profile observability.
      - OTEL_SDK_DISABLED=true
      - OTEL_SERVICE_NAME=api
      - OTEL_TRACES_EXPORTER=otlp
      - OTEL_PHP_TRACES_PROCESSOR=batch
      - OTEL_METRICS_EXPORTER=otlp
      - OTEL_LOGS_EXPORTER=otlp
      - OTEL_PHP_LOGS_PROCESSOR=batch
      - OTEL_EXPORTER_OTLP_PROTOCOL=http/protobuf
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel:4318
      - OTEL_PHP_EXPERIMENTAL_AUTO_ROOT_SPAN=true
    extra_hosts:
      - host.docker.internal:host-gateway
    container_name: carlton-php
    depends_on:
      kafka:
        condition: service_healthy
      mysql:
        condition: service_healthy
  mysql:
    image: public.ecr.aws/docker/library/mysql:8.0.35-debian
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 1024M
    ports:
      - "3306:3306"
    environment:
      MYSQL_DATABASE: datix-rms-api
      MYSQL_ROOT_PASSWORD: root
    restart: always
    container_name: mysql

    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8
      --collation-server=utf8_general_ci

    volumes:
      - mysql8-data:/var/lib/mysql
    healthcheck:
      test: [ "CMD-SHELL", "mysqladmin ping -u root --password=$$MYSQL_ROOT_PASSWORD" ]
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 30

  traefik:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

    image: traefik:v1.7.8
    restart: always
    container_name: traefik
    ports:
      - "80:80"
      - "443:443"
      - "8088:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./traefik/traefik.toml:/etc/traefik/traefik.toml
      - ./traefik/certs/:/certs/
    command:
      - '--configFile=/etc/traefik/traefik.toml \'
      - "--logLevel=INFO"

  auth:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

    restart: unless-stopped
    container_name: auth
    image: 400799746524.dkr.ecr.eu-west-2.amazonaws.com/dciq/auth:develop_latest
    environment:
      - METHOD=centrify
      - CENTRIFY_SIGN_ON_URL=https://aaq0489.my.idaptive.app/applogin/appKey/7ea543a7-5ef5-4118-ac2e-a80886231d80/customerId/AAQ0489
      - CENTRIFY_SIGN_OFF_URL=https://aaq0489.my.idaptive.app/security/logout
        # FOR Using the User Logout Functionality Comment the above 2 line and Uncomment the below ones. and recreate the auth container using docker-compose up --force-recreate auth
      # - CENTRIFY_SIGN_ON_URL=https://aaq0489.my.idaptive.app/applogin/appKey/7ea543a7-5ef5-4118-ac2e-a80886231d80/customerId/AAQ0489
      # - CENTRIFY_SIGN_OFF_URL=https://aaq0489.my.idaptive.app/applogout/appKey/7ea543a7-5ef5-4118-ac2e-a80886231d80/customerId/AAQ0489
      - CENTRIFY_CERT=MIIDkTCCAnmgAwIBAgIQBDfUleVD2UG4CvRGlSTXyjANBgkqhkiG9w0BAQsFADAkMSIwIAYDVQQDDBlDZW50cmlmeSBDdXN0b21lciBBQVEwNDg5MB4XDTE3MDMxMjE0NTcwMloXDTM5MDEwMTAwMDAwMFowRDFCMEAGA1UEAww5Q2VudHJpZnkgQ3VzdG9tZXIgQUFRMDQ4OSBBcHBsaWNhdGlvbiBTaWduaW5nIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiLngc3VGv0kTjwZQpQN3P0QW5ArWr2ZtBlao0sC28wfO/98EYyEP781zzQTddMu5oygi4NysMWdh7Tmh3QWkZRHX3TQnTIA6ZT/H4i8L1ycdSekZ+KwAIY8XeXN61i20NGa/HBhZdfDQMLxraofZiCXIavSIV2GtXtklQZOb6GgLifXQob7DtNNWM2YuVhEe6K0Ez4IRENwRWzSu6NxqVx5e/E0OnqONsEF6F+zD8Z6rMxHs/lQjDd6Bn3DPA5rWk8vhEokTanXp3oRIOxyRoyJGaPVGDyPEe3uz8Tpx6v1OlIuOVc1kEYXgkLWg6irZgwgQeo8WJBPiPnISsicBXwIDAQABo4GeMIGbMBMGCisGAQQBgqZwAQkEBQwDMS4wMBcGCisGAQQBgqZwAQMECQwHQUFRMDQ4OTAfBgNVHSMEGDAWgBTAq4qJfqoxj6ab9ZKtADUeCcBL+TAdBgNVHQ4EFgQUHOj1vflRfyADeYySBAovT1dAtw0wDgYDVR0PAQH/BAQDAgWgMBsGCisGAQQBgqZwAQQEDQwLQXBwbGljYXRpb24wDQYJKoZIhvcNAQELBQADggEBAE6f+xG+J9VEx1BQL4Ac6v+1yQdVALpMqh5qCp+Sp04EC6Q1hPiKEvsG4+ln8FBRIRlgl12LoNCcEvsvpumy7kuIlBNqklHl8YUfXyePRpdHTbLFqOoBBZEsbFgILLmn3lsNBf5HRJmxxLE1VIsANQJHbjU1XmcfyKNUVQSPrDFpKpQxesuN5OQ9xd5w/y3E7kvThZMKNLxAin6INKOZaNp9n7jNf4p7YZXdA9nGYEG0P5uhYD+yWD8sIhE9uoqA9pFWleYIEYRB6iXYU7g3yC8SAkt2pyKqoLaElWtOS5ICqC7N6LCLT3w/Z+hG/zI7uK8TGzURSh36OsmrtPmRD34=
      - ADFS_SIGN_ON_URL=https://sts.datix.co.uk/adfs/ls
      - ADFS_SIGN_OFF_URL=https://sts.datix.co.uk/adfs/ls/?wa=wsignout1.0
      - ADFS_CALLBACK_URL=https://localhost.datix/auth/saml/acs
      - ADFS_ISSUER_URL=https://localhost.datix
      - ADFS_CERT=
      - JWT_USERNAME=jwtUser
      - JWT_PASSWORD=Pa55word
      - CAPTURE_API_BASE_URL=http://capture:8020
      - JWT_SECRET=df70874k3SWuZ3AMvCakyGHFvDaBLpqUT5Crv6QDHcflc4U7l3k0Ap6pI1RyXHFFEUmne8rLVVmWEAc0hVfx3g=
      - JWT_ISSUER=DATIX
      - JWT_ALGORITHM=HS256
      - CARLTON_API_BASE_URL=http://api:8080
      - DEFAULT_REDIRECT=https://localhost.datix/
      - BASE_URL=https://localhost.datix
    ports:
      - "3000:3000"
     # volumes:
     #  - ../authservice/:/opt/app

  mongo:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

    image: mongo:latest
    ports:
      - "27017:27017"
    restart: unless-stopped
    container_name: mongo
    volumes:
      - mongo-data:/data/db

  importexport:
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 512M

    image: 400799746524.dkr.ecr.eu-west-2.amazonaws.com/dciq/import-export-service:latest
    environment:
      - PORT=8010
      - BUILD_NUMBER=OVERRIDE
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_IMPORTEXPORT_DATABASE=importexport
      - SQLSERVER_HOST=mssql
      - SQLSERVER_USER=sa
      - SQLSERVER_PASSWORD=C1aws0nite
      - SQLSERVER_DATABASE=prince160
      - LOG_LEVEL=info
      - SYSTEM_LOGS=false
      - SQLSERVER_RETRIES=3
      - SQLSERVER_RETRY_INTERVAL=3000
      - SQLSERVER_BATCH_INSERT_NUMBER=30
      - API_HOST=https://traefik:443
      - MODE=dev
      - DISABLED_CARLTON_MODULES_KEYS=clinical_audit_instance,accreditation_programme,accreditation_standard,round_instance
      - NODE_MAX_MEMORY=470
      - S3_ACCESS_KEY=COOLIO
      - S3_SECRET=BECAUSEIGOTITLIKETHAT
      - S3_BUCKET=dev
      - S3_UPLOADS_FOLDER=importexport
      - S3_REGION="us-east-1”
      - MINIO_ENABLED=1
      - MINIO_ENDPOINT=http://s3:6969
    ports:
      - "8010:8010"
    restart: unless-stopped
    container_name: importexport

  zookeeper:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 512M

    image: zookeeper:3.4.9
    restart: unless-stopped
    hostname: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_PORT: 2181
      ZOO_SERVERS: server.1=zookeeper:2888:3888
    container_name: zookeeper

  kafka:
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1024M

    image: confluentinc/cp-kafka:4.0.0
    hostname: kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:9092"
      KAFKA_ZOOKEEPER_CONNECT: "zookeeper:2181"
      KAFKA_BROKER_ID: 1
      KAFKA_LOG4J_LOGGERS: "kafka.controller=INFO,kafka.producer.async.DefaultEventHandler=INFO,state.change.logger=INFO"
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_LOG_RETENTION_MINUTES: 10
    depends_on:
      - zookeeper
    volumes:
      - kafka-data:/var/lib/kafka/data
    restart: unless-stopped
    container_name: kafka
    healthcheck:
      test: nc -z localhost 9092 || exit -1
      start_period: 15s
      interval: 5s
      timeout: 10s
      retries: 10

  kafka_proxy:
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M

    image: 400799746524.dkr.ecr.eu-west-2.amazonaws.com/dciq/kafka_proxy:develop_latest
    ports:
      - "9090:9090"
    environment:
      HTTP_PORT: 9090
      LOG_LEVEL: debug
      LOG_FORMAT: json
      KAFKA_HOSTS: kafka:9092
      PRODUCER_TIMEOUT: 3000 # time to wait for kafka producer ack
    restart: unless-stopped
    container_name: kafka_proxy

  medications:
    deploy:
      resources:
        limits:
          cpus: "0.5"
          memory: 256M

    image: "400799746524.dkr.ecr.eu-west-2.amazonaws.com/dciq/medications-dotnet-local:latest"
    hostname: "medications"
    ports:
      - "8001:80"
    environment:
      ASPNETCORE_URLS: "http://*:80"
      ENABLE_API_DOCUMENTATION: "true"
      MONGO_CONNECTION_STRING: "mongodb://mongo:27017"
      KAFKA_CONNECTION_STRING: "kafka:9092"
      MONGO_DATABASE_STRING: "iq-medications"
      SERVICE_NAME: "medications"
      KAFKA_CONSUMER_GROUP: "medication_group"
      DOMAIN: "medications_v2"
      MUTATIONS_SYNC_API_KEY: "TooManySecrets"
      JWT_SECRET: "df70874k3SWuZ3AMvCakyGHFvDaBLpqUT5Crv6QDHcflc4U7l3k0Ap6pI1RyXHFFEUmne8rLVVmWEAc0hVfx3g="
      JWT_ISSUER: "RLDatix"
      JWT_AUDIENCE: "RLDatix"
      JWT_AUTH_DEV_OVERRIDE: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOjY0LCJpc0FkbWluIjp0cnVlLCJhdXRoZW50aWNhdGVkVXNlcklkIjo2NCwianRpIjoiYmFlNzE4NWMtOGIwZC00MzdhLWExNjMtMmI1ZDQ3Yzc5ZDk0IiwiaWF0IjoxNTkyMjQ2MzAyLCJleHAiOjE1OTIyNDk5MDIsImxhbmd1YWdlIjoiZW4ifQ.VilfxTxdgLqUW0PqzvzauAzSIisqXGeUyfRMN3U-IBs"
      JWT_PERMISSIONS_DEV_OVERRIDE: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJtZWRpY2F0aW9uIjp7ImxvY2FsX2NvbnRyb2xsZXIiOnRydWUsInN1YnNjcmlwdGlvbl9jb250cm9sbGVyIjp0cnVlLCJsb2NhbF9jb250cm9sbGVyX2xvY2F0aW9uIjp7ImlkIjoxLCJsYWJlbCI6Ikhvc3BpdGFsIDEifSwibG9jYXRpb25fZmlsdGVyIjp0cnVlfSwiZXF1aXBtZW50Ijp7ImxvY2FsX2NvbnRyb2xsZXIiOnRydWUsInN1YnNjcmlwdGlvbl9jb250cm9sbGVyIjp0cnVlLCJsb2NhbF9jb250cm9sbGVyX2xvY2F0aW9uIjp7ImlkIjoxLCJsYWJlbCI6Ikhvc3BpdGFsIDEifSwibG9jYXRpb25fZmlsdGVyIjp0cnVlfSwianRpIjoiZjExYzNiNGQtODM4OC00ZWNjLWJiYjktZmRlMzFiOGY3NjJjIiwiaWF0IjoxNTkyMjQ2MzYxLCJleHAiOjE1OTIyNDk5NjF9.Noz1N28Lk3FhheREQgegjr77lkuOS6qxMBLaNjeKsUc"
    container_name: "medications"

  mongo-medication-seed:
    build: ./mongo-seed
    environment:
      MONGO_CONTAINER: "mongo"
      MONGO_DATABASE: "iq-medications"
      MONGO_COLLECTION: "medications_v2"
      MONGO_JSON: "medication-global.json"
    depends_on:
      - "mongo"

  mongo-equipment-seed:
    build: ./mongo-seed
    environment:
      MONGO_CONTAINER: "mongo"
      MONGO_DATABASE: "iq-medications"
      MONGO_COLLECTION: "equipment_v2"
      MONGO_JSON: "equipment-global.json"
    depends_on:
      - "mongo"

  otel:
    image: grafana/otel-lgtm
    profiles: ["observability"]
    ports:
      - 3010:3000
      - 4317:4317
      - 4318:4318
    container_name: "otel"
    depends_on:
      - "carlton-php"

networks:
  default:
      external: true
      name: projectcarlton_default

volumes:
  mongo-data:
  mysql-data:
  mysql8-data:
  kafka-data:
