name: Build-Push-DockerScout

on:
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [develop, release-candidate, main]
  push:
    branches: [develop, release-candidate, main]

permissions:
  contents: write
  id-token: write
  pull-requests: write

jobs:
  bump-version:
    runs-on: ubuntu-latest
    outputs:
      new_tag: ${{ steps.bump.outputs.new_tag }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.DCIQ_PAT }}

      - name: Bump Version
        id: bump
        uses: rld-engineering/gha-composite-actions/dciq/bump-semver@main
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

  Build-Push:
    needs: bump-version
    runs-on: ubuntu-latest
    timeout-minutes: 20

    strategy:
      matrix:
        service:
          - name: carlton-php
            context: api
            file: php.Dockerfile
            php: true
            target: prod
          - name: api
            context: api
            file: nginx.Dockerfile
            target: prod
          - name: frontend
            context: client

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.DCIQ_PAT }}

      - name: Build and Push Docker Images with DockerScout
        uses: rld-engineering/gha-composite-actions/dciq/build-push-scout@main
        with:
          project_name: dciq
          aws_account_id: "************"
          aws_region: "eu-west-2"
          dockerhub_username: ${{ secrets.DOCKERHUB_USERNAME }}
          dockerhub_token: ${{ secrets.DOCKERHUB_ACCESS_TOKEN }}
          scout_username: ${{ secrets.SCOUT_USERNAME }}
          scout_token: ${{ secrets.SCOUT_TOKEN }}
          dciq_pat: ${{ secrets.DCIQ_PAT }}
          images: ${{ toJson(matrix.service) }}
          image_tag: ${{ needs.bump-version.outputs.new_tag }}

  mergedown-RC-develop:
    needs: Build-Push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/release-candidate'
    steps:
      - name: Merge release-candidate to develop
        uses: rld-engineering/gha-composite-actions/dciq/mergedown@main
        with:
          token: ${{ secrets.DCIQ_PAT }}

  gitposeidon-update:
    needs: Build-Push
    runs-on: ubuntu-latest
    if: >
      (github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true &&
      (github.event.pull_request.base.ref == 'develop' || github.event.pull_request.base.ref == 'release-candidate' || github.event.pull_request.base.ref == 'main')) ||
      (github.event_name == 'push' && (github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/release-candidate' || github.ref == 'refs/heads/main'))
    steps:
      - name: Update Poseidon Template
        uses: rld-engineering/gha-composite-actions/dciq/poseidon@main
        with:
          git_sha: ${{ github.sha }}
          ssh_private_key: ${{ secrets.BITBUCKET_SSH_KEY }}
          template_map: |
            api=templates/deployments/carlton.tmpl
            carlton-php=templates/deployments/carlton.tmpl
            carlton-php=templates/migrations/carltonmigrate.tmpl
            carlton-php=templates/deployments/carltonkeycloakcron.tmpl
            frontend=templates/deployments/carlton.tmpl