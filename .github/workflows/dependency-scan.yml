name: Dependency Scan

on:
  push:
    branches: [develop, release-candidate, main]
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  scan-dependencies:
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repository
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.29.0
        with:
          scan-type: "fs"
          scan-ref: "."
          ignore-unfixed: true
          format: "table"
          #severity: "CRITICAL,HIGH"
          vuln-type: "library"
          skip-dirs: "node_modules"
