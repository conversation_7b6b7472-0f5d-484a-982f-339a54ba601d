name: P<PERSON><PERSON><PERSON> (level=max)

on:
  pull_request:
    types: [opened, synchronize, reopened]

  push:
    branches: [develop, release-candidate, main]

jobs:
  phpstan:
    runs-on: ubuntu-latest
    name: <PERSON><PERSON><PERSON><PERSON>an On Changed Files

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: mbstring, dom, json, libxml, tokenizer, xml, xmlwriter
          tools: composer:v2.2

      - name: Find and Install Dependencies with Composer
        uses: ramsey/composer-install@v3
        with:
          dependency-versions: locked
          composer-options: "--dev"
          working-directory: ./api/site
        env:
          COMPOSER_AUTH: '{"github-oauth": {"github.com": "${{ secrets.DCIQ_PAT }}"}}'

      - name: Get changed PHP files
        id: php_files
        run: |
          echo "Changed PHP files:"
          git fetch origin ${{ github.event.pull_request.base.ref }} --depth=1
          FILES=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }} HEAD | grep '\.php$' || true)
          echo "$FILES"
          echo "php_files<<EOF" >> $GITHUB_OUTPUT
          echo "$FILES" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Run PHPStan
        if: steps.php_files.outputs.php_files != ''
        uses: php-actions/phpstan@v3
        with:
          version: latest
          php_version: 8.3
          level: max
          error_format: github
          path: ${{ steps.php_files.outputs.php_files }}
