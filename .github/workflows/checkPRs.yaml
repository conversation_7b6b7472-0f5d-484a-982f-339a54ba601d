name: Check Merge Conflicts

on:
    pull_request:
        types: [opened, synchronize, reopened]

jobs:
    check-conflicts:
        runs-on: ubuntu-latest
        permissions:
            contents: write
            pull-requests: write

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  ref: develop
                  fetch-depth: 0


            - name: Get open pull requests
              id: get-prs
              run: |
                  prs=$(gh pr list --state open --json number --jq '.[].number')
                  prs=$(echo "$prs" | tr '\n' ' ')  # Ensure space-separated list
                  echo "prs=$prs" >> $GITHUB_ENV
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

            - name: Configure Git Identity
              run: |
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "Check Merge Conflicts"

            - name: Set PR number to environment
              run: echo "PR_NUMBER=${{ github.event.pull_request.number }}" >> $GITHUB_ENV

            - name: Check PRs for conflicts among each other
              run: |
                  pr1=$PR_NUMBER
                  left_branch_name=$(gh pr view $pr1 --json headRefName -q .headRefName)
                  left_branch_name_full_ref="origin/$left_branch_name"

                  for pr2 in $prs; do
                    if [ "$pr1" -ne "$pr2" ]; then

                      echo "🔍 Checking merge conflict between PR #$pr1 and PR #$pr2..."
                      right_branch_name=$(gh pr view $pr2 --json headRefName -q .headRefName)
                      right_branch_name_full_ref="origin/$right_branch_name"

                      git checkout $left_branch_name_full_ref

                      git merge --no-commit --no-ff $right_branch_name_full_ref | tee /dev/null
                      merge_status=${PIPESTATUS[0]}

                      mergeable=$(gh pr view $pr2 --json mergeable -q .mergeable)

                      if [ "$mergeable" = "MERGEABLE" ]; then
                        if [ $merge_status -ne 0 ]; then
                          echo "❌ PR #$pr1 and PR #$pr2 have conflicts!"
                          echo "❌ Conflicting branches: #$left_branch_name_full_ref and PR #$right_branch_name_full_ref"
                          echo "⚠️ **Check Merge Conflicts** PR #$pr1 has merge conflicts with PR #$pr2. Please resolve before merging."
                          exit 1;
                        else
                          echo "✅ PR #$pr1 and PR #$pr2 can merge together."
                          echo "✅ **Check Merge Conflicts** PR #$pr1 and PR #$pr2 can be merged together with no conflicts. 🚀"
                        fi
                      fi

                      git merge --abort

                    fi
                  done
              env:
                  GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}


