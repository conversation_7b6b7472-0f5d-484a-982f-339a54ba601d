name: SonarQube Scan with PHP Unit Tests

on:
  push:
    branches: [develop, release-candidate, main]
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

jobs:
  sonarqube:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: mbstring, dom, json, libxml, protobuf, opentelemetry, tokenizer, xml, xmlwriter
          tools: composer:v2.2

      - name: Find and Install Dependencies with Composer
        uses: ramsey/composer-install@v3
        with:
          dependency-versions: locked
          composer-options: "--dev"
          working-directory: ./api/site
        env:
          COMPOSER_AUTH: '{"github-oauth": {"github.com": "${{ secrets.DCIQ_PAT }}"}}'

      - name: Run PHPUnit Tests with Coverage
        working-directory: ./api/site
        run: |
          php -d xdebug.mode=coverage ./vendor/bin/phpunit \
            --configuration phpunit.xml.dist \
            --testsuite Unit \
            --coverage-clover=coverage.xml

      - name: SonarQ<PERSON> Scan
        uses: SonarSource/sonarqube-scan-action@v4.2.1
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TEST_TOKEN }}
          SONAR_HOST_URL: ${{ vars.SONAR_TEST_HOST }}
