###############################################################################
# Build Stage: CLI Development Environment
###############################################################################
FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/php:8.3.22 AS cli

ARG USE_ZSCALER_CERT
ARG XDEBUG_IDE_PORT

USER root

RUN dnf upgrade -y && \
    dnf install -y --nodocs \
      autoconf \
      automake \
      bzip2-devel \
      diffutils \
      file \
      fontconfig-devel \
      freetype-devel \
      make \
      libtool \
      gcc \
      gcc-c++ \
      ghostscript \
      glib2-devel \
      libcurl-devel \
      libicu-devel \
      libtiff-devel \
      libX11-devel \
      libxml2-devel \
      libzip-devel \
      libwebp-devel \
      openldap-devel \
      openldap \
      openssl-devel \
      zlib-devel \
      openldap \
      openldap-devel && \
    dnf clean all

# Add Zscaler Root Certificate
COPY docker/ZscalerRootCertificate-2048-SHA256.crt /etc/pki/ca-trust/source/anchors/
RUN if [ "$USE_ZSCALER_CERT" = "1" ]; then update-ca-trust extract; fi

COPY docker/php-fpm/enable_xdebug.sh /tmp/enable_xdebug.sh
RUN chmod +x /tmp/enable_xdebug.sh && \
    /tmp/enable_xdebug.sh $XDEBUG_IDE_PORT coverage yes

COPY docker/php-fpm/php-cli.ini /usr/local/etc/php/conf.d/php-cli.ini

WORKDIR /app

###############################################################################
# Build Stage: PHP builder
###############################################################################
FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/php:8.3.22 AS builder

ARG USE_ZSCALER_CERT
ARG ENABLE_XDEBUG
ARG XDEBUG_PORT
ARG XDEBUG_MODE
ARG XDEBUG_START_WITH_REQUEST

USER root

# Add Zscaler Root Certificate
COPY docker/ZscalerRootCertificate-2048-SHA256.crt /etc/pki/ca-trust/source/anchors/
RUN if [ "$USE_ZSCALER_CERT" = "1" ]; then update-ca-trust extract; fi

RUN dnf upgrade -y && \
    dnf install -y --nodocs \
      autoconf \
      automake \
      bzip2-devel \
      diffutils \
      file \
      fontconfig-devel \
      freetype-devel \
      make \
      libtool \
      gcc \
      gcc-c++ \
      ghostscript \
      glib2-devel \
      libcurl-devel \
      libicu-devel \
      libtiff-devel \
      libX11-devel \
      libxml2-devel \
      libzip-devel \
      libwebp-devel \
      openldap-devel \
      openldap \
      openssl-devel \
      zlib-devel \
      openldap \
      openldap-devel \
      unixODBC unixODBC-devel && \
    dnf clean all

RUN curl https://packages.microsoft.com/keys/microsoft.asc | gpg --dearmor -o /etc/pki/rpm-gpg/microsoft.gpg && \
    curl https://packages.microsoft.com/config/rhel/9/prod.repo -o /etc/yum.repos.d/mssql-tools.repo && \
    ACCEPT_EULA=Y dnf -y install --nodocs mssql-tools msodbcsql17 && \
    dnf clean all && \
    echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc &&\
    pecl channel-update pecl.php.net && \
    pecl install mongodb-1.19.4 sqlsrv pdo_sqlsrv-5.10.1 protobuf opentelemetry && \
    printf "extension=sqlsrv\n" > /usr/local/etc/php/conf.d/30-sqlsrv.ini && \
    printf "extension=pdo_sqlsrv\n" > /usr/local/etc/php/conf.d/30-pdo_sqlsrv.ini && \
    printf "extension=mongodb\n" > /usr/local/etc/php/conf.d/mongodb.ini && \
    printf "extension=opentelemetry\n" > /usr/local/etc/php/conf.d/opentelemetry.ini && \
    printf "extension=protobuf\n" > /usr/local/etc/php/conf.d/protobuf.ini

COPY docker/php-fpm/enable_xdebug.sh /tmp/enable_xdebug.sh
RUN if [ "$ENABLE_XDEBUG" = "1" ]; then chmod +x /tmp/enable_xdebug.sh && /tmp/enable_xdebug.sh $XDEBUG_PORT $XDEBUG_MODE $XDEBUG_START_WITH_REQUEST; fi

###############################################################################
# Production Stage
###############################################################################
FROM ************.dkr.ecr.eu-west-2.amazonaws.com/ironbank/php:8.3.22 AS prod

USER root

COPY --from=builder --chown=root:root /usr/local/bin /usr/local/bin
COPY --from=builder --chown=root:root /usr/local/php /usr/local/php
COPY --from=builder --chown=root:root /usr/local/etc/php/conf.d/ /usr/local/etc/php/conf.d/
COPY --from=builder --chown=root:root /usr/local/include/php /usr/local/include/php
COPY --from=builder --chown=root:root /usr/local/lib/php /usr/local/lib/php

# Copy SQL Server components
COPY --from=builder --chown=root:root /opt /opt
COPY --from=builder --chown=root:root /usr/lib64/libmsodbcsql* /usr/lib64/
COPY --from=builder --chown=root:root /usr/lib64/libodbc* /usr/lib64/
COPY --from=builder --chown=root:root /etc/odbcinst.ini /etc/
COPY --from=builder --chown=root:root /etc/odbc.ini /etc/

WORKDIR /var/www/dciq-capture

COPY docker/php-fpm/.user.ini /usr/local/etc/php/conf.d/user.ini
COPY docker/php-fpm/www.ini /tmp/www.ini

# Copy application code
COPY  --chmod=755 --chown=php-fpm:php-fpm . .
RUN chmod 755 /var/www/dciq-capture
RUN chown php-fpm:php-fpm /var/www/dciq-capture

# Copy and configure startup scripts
COPY --chmod=775 ./docker/php-fpm/*.sh /
COPY --chmod=755 ./docker/php-fpm/*.php /tmp

RUN rm -rf /var/www/dciq-capture/app/framework/_doctrineProxie && \
    rm -rf /var/www/dciq-capture/app/framework/Cache && \
    rm -rf /var/www/dciq-capture/app/framework/_htmlPurifier && \
    mkdir -p /var/www/dciq-capture/app/framework/_doctrineProxies \
             /var/www/dciq-capture/app/framework/Cache \
             /var/www/dciq-capture/app/framework/_htmlPurifier

RUN chmod -R 775 /var/www/dciq-capture/app/framework/_doctrineProxies && \
    chown -R php-fpm:php-fpm /var/www/dciq-capture/app/framework/_doctrineProxies && \
    chmod -R 775 /var/www/dciq-capture/app/framework/Cache && \
    chown -R php-fpm:php-fpm /var/www/dciq-capture/app/framework/Cache && \
    chmod -R 775 /var/www/dciq-capture/app/framework/_htmlPurifier && \
    chown -R php-fpm:php-fpm /var/www/dciq-capture/app/framework/_htmlPurifier

RUN chmod -R 755 /usr/local/etc/php/conf.d && \
    chown -R php-fpm:php-fpm /usr/local/etc/php/conf.d

RUN sed -i 's|listen = 9000|listen = 9098|' /usr/local/etc/php-fpm.d/zz-docker.conf

# Complete PHP-FPM logging suppression
RUN cat /tmp/www.ini >> /usr/local/etc/php-fpm.d/www.conf

EXPOSE 9098

USER php-fpm

ENTRYPOINT ["/logging-wrapper.sh", "/start.sh"]
